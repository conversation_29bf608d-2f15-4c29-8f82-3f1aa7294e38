#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime

def test_environment():
    print("🔍 环境检查开始...")
    print(f"📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 当前目录: {os.getcwd()}")
    
    # 检查selenium
    try:
        import selenium
        print(f"✅ Selenium版本: {selenium.__version__}")
    except ImportError:
        print("❌ Selenium未安装")
        return False
    
    # 检查ChromeDriver
    if os.path.exists('./chromedriver'):
        print("✅ ChromeDriver文件存在")
    else:
        print("❌ ChromeDriver文件不存在")
        return False
    
    # 检查输出目录
    output_dir = "./images/front_system/履约执行/"
    if os.path.exists(output_dir):
        print(f"✅ 输出目录存在: {output_dir}")
    else:
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 创建输出目录: {output_dir}")
    
    print("🎉 环境检查完成，可以开始截图")
    return True

if __name__ == "__main__":
    test_environment()
