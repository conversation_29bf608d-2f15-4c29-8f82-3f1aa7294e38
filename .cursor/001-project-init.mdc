---
description: 
globs: 
alwaysApply: false
---
# 项目初始化阶段 MCP 规则

## 规则目标
确保项目初始化阶段的工作按照敏捷开发规范有序进行，包括需求分析、MRD文档编写等。

## 前置条件
1. 已获取项目基本需求
2. 已建立项目团队
3. 已配置开发环境

## 执行步骤

### 1. 需求收集与分析
```mcp
{
  "name": "需求收集分析",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "创建需求收集文档",
      "path": "./docs/requirements/",
      "template": "requirement-template.md",
      "validation": "文档是否包含用户故事、验收标准"
    },
    {
      "action": "进行需求分析会议",
      "participants": ["产品经理", "技术负责人", "业务分析师"],
      "output": "会议纪要与行动项"
    },
    {
      "action": "整理需求优先级",
      "method": "MoSCoW方法",
      "output": "需求优先级矩阵"
    }
  ]
}
```

### 2. MRD文档编写
```mcp
{
  "name": "MRD文档编写",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "创建MRD文档",
      "path": "./docs/mrd.md",
      "sections": [
        "产品概述",
        "市场分析",
        "用户分析",
        "产品功能",
        "技术可行性",
        "风险评估"
      ]
    },
    {
      "action": "MRD评审",
      "reviewers": ["产品经理", "技术负责人", "项目经理"],
      "output": "评审意见与修改建议"
    },
    {
      "action": "MRD定稿",
      "validation": "文档完整性检查"
    }
  ]
}
```

### 3. 项目计划制定
```mcp
{
  "name": "项目计划制定",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "创建项目计划",
      "template": "agile-project-plan.md",
      "content": [
        "Sprint规划",
        "里程碑设定",
        "资源分配",
        "风险管理"
      ]
    },
    {
      "action": "建立项目仓库",
      "tasks": [
        "初始化Git仓库",
        "配置.gitignore",
        "设置分支策略",
        "配置CI/CD"
      ]
    }
  ]
}
```

## 验收标准
1. MRD文档完整且经过评审
2. 项目计划已制定并获得相关方确认
3. 项目仓库配置完成且可用

## 输出物
1. ./docs/mrd.md
2. ./docs/requirements/*
3. 项目计划文档
4. 配置完成的项目仓库

## 下一阶段
完成本阶段后，进入 [002-prd-design.mdc](mdc:002-prd-design.mdc) 阶段。

