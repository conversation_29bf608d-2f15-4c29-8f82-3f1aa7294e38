---
description: 
globs: 
alwaysApply: false
---
# 前端开发阶段 MCP 规则

## 规则目标
完成前端页面代码开发和调试，确保符合设计规范和性能要求。

## 前置条件
1. 已完成 [002-prd-design.mdc](mdc:002-prd-design.mdc) 阶段
2. UI设计稿已完成并评审通过
3. 前端技术栈已确定

## 执行步骤

### 1. 前端项目初始化
```mcp
{
  "name": "前端项目初始化",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "创建项目结构",
      "path": "./admin/front",
      "template": "vue3-typescript-template",
      "config": {
        "typescript": true,
        "vue3": true,
        "elementPlus": true,
        "eslint": true,
        "prettier": true
      }
    },
    {
      "action": "依赖安装配置",
      "dependencies": {
        "vue": "^3.x",
        "vue-router": "^4.x",
        "element-plus": "^2.x",
        "typescript": "^4.x",
        "surely-vue": "latest",
        "monaco-editor": "latest"
      },
      "devDependencies": {
        "webpack": "^5.x",
        "babel": "^7.x",
        "less": "^4.x",
        "postcss": "^8.x",
        "eslint": "^8.x",
        "husky": "^7.x"
      }
    }
  ]
}
```

### 2. 组件开发
```mcp
{
  "name": "组件开发",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "基础组件开发",
      "components": [
        "布局组件",
        "导航组件",
        "表单组件",
        "表格组件",
        "弹窗组件"
      ],
      "standards": {
        "typescript": "严格类型检查",
        "comments": "JSDoc规范注释",
        "testing": "单元测试覆盖"
      }
    },
    {
      "action": "业务组件开发",
      "components": [
        "规则配置组件",
        "监控面板组件",
        "数据展示组件",
        "报表组件"
      ],
      "features": [
        "数据绑定",
        "状态管理",
        "事件处理",
        "异步加载"
      ]
    },
    {
      "action": "页面开发",
      "pages": [
        "登录页面",
        "首页仪表盘",
        "规则管理页面",
        "监控告警页面",
        "报表分析页面"
      ]
    }
  ]
}
```

### 3. 前端调试与优化
```mcp
{
  "name": "前端调试与优化",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "代码质量检查",
      "tools": [
        "ESLint",
        "TypeScript检查",
        "Prettier格式化"
      ]
    },
    {
      "action": "性能优化",
      "tasks": [
        "代码分割",
        "懒加载配置",
        "缓存策略",
        "打包优化"
      ]
    },
    {
      "action": "兼容性测试",
      "browsers": [
        "Chrome最新版",
        "Firefox最新版",
        "Edge最新版"
      ]
    },
    {
      "action": "单元测试",
      "framework": "Jest",
      "coverage": {
        "statements": 80,
        "branches": 70,
        "functions": 80,
        "lines": 80
      }
    }
  ]
}
```

### 4. 资源文件处理
```mcp
{
  "name": "资源文件处理",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "图片资源处理",
      "steps": [
        {
          "action": "创建图片目录",
          "path": "./admin/front/src/assets/images",
          "subDirs": [
            "icons",
            "backgrounds",
            "logos"
          ]
        },
        {
          "action": "图片下载与优化",
          "tasks": [
            {
              "type": "download",
              "source": "远程图片URL",
              "target": "./admin/front/src/assets/images/${category}/${filename}",
              "optimization": {
                "compress": true,
                "format": ["webp", "original"],
                "responsive": true
              }
            }
          ],
          "validation": {
            "fileTypes": ["png", "jpg", "jpeg", "gif", "webp", "svg"],
            "maxSize": "500KB",
            "naming": "kebab-case"
          }
        }
      ]
    },
    {
      "action": "样式文件处理",
      "steps": [
        {
          "action": "创建样式目录",
          "path": "./admin/front/src/assets/styles",
          "structure": [
            "base/",
            "components/",
            "layouts/",
            "themes/",
            "variables/",
            "mixins/"
          ]
        },
        {
          "action": "样式文件下载与处理",
          "tasks": [
            {
              "type": "download",
              "source": "远程CSS URL",
              "target": "./admin/front/src/assets/styles/${category}/${filename}.less",
              "processing": {
                "format": "less",
                "autoprefix": true,
                "minimize": true,
                "scopeName": "[data-v-component]"
              }
            }
          ],
          "validation": {
            "syntax": "less",
            "lint": "stylelint",
            "naming": "kebab-case"
          }
        }
      ]
    },
    {
      "action": "JavaScript文件处理",
      "steps": [
        {
          "action": "创建脚本目录",
          "path": "./admin/front/src/assets/scripts",
          "structure": [
            "utils/",
            "helpers/",
            "plugins/",
            "vendors/"
          ]
        },
        {
          "action": "脚本文件下载与处理",
          "tasks": [
            {
              "type": "download",
              "source": "远程JS URL",
              "target": "./admin/front/src/assets/scripts/${category}/${filename}.ts",
              "processing": {
                "transpile": true,
                "typescript": true,
                "lint": "eslint",
                "minimize": true
              }
            }
          ],
          "validation": {
            "syntax": "typescript",
            "lint": "eslint",
            "naming": "camelCase"
          }
        }
      ]
    },
    {
      "action": "资源版本控制",
      "steps": [
        {
          "action": "生成资源清单",
          "output": "./admin/front/src/assets/manifest.json",
          "content": {
            "images": "图片资源清单",
            "styles": "样式资源清单",
            "scripts": "脚本资源清单"
          }
        },
        {
          "action": "配置资源加载",
          "tasks": [
            {
              "type": "webpack",
              "config": {
                "assets": {
                  "publicPath": "/assets/",
                  "hashName": true,
                  "optimization": true
                }
              }
            }
          ]
        }
      ]
    }
  ]
}
```

### 5. 资源使用规范
```mcp
{
  "name": "资源使用规范",
  "type": "sequential-thinking",
  "rules": [
    {
      "category": "图片资源",
      "rules": [
        "使用 webp 格式作为主要图片格式",
        "提供适当的后备格式（png/jpg）",
        "使用 lazy-loading 属性",
        "提供 alt 文本",
        "使用响应式图片 srcset"
      ]
    },
    {
      "category": "样式资源",
      "rules": [
        "使用 CSS Modules 或 Scoped CSS",
        "遵循 BEM 命名规范",
        "使用 CSS 变量进行主题配置",
        "优先使用 flex/grid 布局",
        "实现响应式设计"
      ]
    },
    {
      "category": "脚本资源",
      "rules": [
        "使用 TypeScript 类型声明",
        "遵循 ESLint 规范",
        "使用 ES6+ 特性",
        "实现按需加载",
        "添加适当的注释"
      ]
    }
  ]
}
```

## 验收标准
1. 所有页面和组件符合设计规范
2. TypeScript类型检查通过
3. ESLint检查无错误
4. 单元测试覆盖率达标
5. 性能指标满足要求
6. 跨浏览器兼容性测试通过
7. 所有资源文件正确下载并优化
8. 资源清单完整且正确
9. 资源加载性能达标

## 输出物
1. ./admin/front/* 前端代码
2. 单元测试报告
3. 性能测试报告
4. 代码质量报告
5. 资源清单文件
6. 资源优化报告

## 下一阶段
完成本阶段后，进入 [004-backend-dev.mdc](mdc:004-backend-dev.mdc) 阶段。



