---
description:
globs:
alwaysApply: false
---
# 部署阶段 MCP 规则

## 规则目标
完成系统在WSL Docker环境中的部署和上线。

## 前置条件
1. 已完成 [004-backend-dev.mdc](./004-backend-dev.mdc) 阶段
2. WSL环境已配置
3. Docker环境已安装

## 执行步骤

### 1. 环境准备
```mcp
{
  "name": "环境准备",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "WSL配置检查",
      "checks": [
        "WSL版本检查",
        "内存配置检查",
        "磁盘空间检查"
      ]
    },
    {
      "action": "Docker环境检查",
      "checks": [
        "Docker版本检查",
        "Docker Compose检查",
        "网络配置检查"
      ]
    },
    {
      "action": "中间件准备",
      "services": [
        {
          "name": "MySQL",
          "version": "8.0.41",
          "port": 3306
        },
        {
          "name": "Redis",
          "version": "latest",
          "port": 6379
        },
        {
          "name": "RabbitMQ",
          "version": "latest",
          "port": 5672
        },
        {
          "name": "Nacos",
          "version": "latest",
          "port": 8848
        }
      ]
    }
  ]
}
```

### 2. 容器化配置
```mcp
{
  "name": "容器化配置",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "Dockerfile编写",
      "components": [
        {
          "name": "前端Dockerfile",
          "base": "node:latest",
          "path": "./admin/front/Dockerfile"
        },
        {
          "name": "后端Dockerfile",
          "base": "openjdk:11",
          "path": "./admin/backend/Dockerfile"
        }
      ]
    },
    {
      "action": "Docker Compose配置",
      "file": "docker-compose.yml",
      "services": [
        "frontend",
        "backend",
        "mysql",
        "redis",
        "rabbitmq",
        "nacos"
      ]
    },
    {
      "action": "环境变量配置",
      "configs": [
        ".env.development",
        ".env.production"
      ]
    }
  ]
}
```

### 3. 部署流程
```mcp
{
  "name": "部署流程",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "数据库初始化",
      "tasks": [
        "创建数据库",
        "执行建表脚本",
        "导入基础数据"
      ]
    },
    {
      "action": "服务部署",
      "sequence": [
        "启动Nacos",
        "启动MySQL",
        "启动Redis",
        "启动RabbitMQ",
        "部署后端服务",
        "部署前端服务"
      ]
    },
    {
      "action": "服务检查",
      "checks": [
        "健康检查",
        "日志检查",
        "接口测试",
        "功能验证"
      ]
    }
  ]
}
```

### 4. 监控配置
```mcp
{
  "name": "监控配置",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "日志配置",
      "components": [
        "应用日志",
        "访问日志",
        "错误日志"
      ]
    },
    {
      "action": "监控配置",
      "metrics": [
        "CPU使用率",
        "内存使用率",
        "磁盘使用率",
        "网络流量"
      ]
    },
    {
      "action": "告警配置",
      "rules": [
        "服务不可用告警",
        "资源超限告警",
        "性能异常告警"
      ]
    }
  ]
}
```

## 验收标准
1. 所有服务成功部署且运行正常
2. 系统功能测试通过
3. 性能指标满足要求
4. 监控告警配置生效
5. 日志收集正常

## 输出物
1. 部署文档
2. 运维手册
3. 监控dashboard
4. 部署脚本
5. 环境配置文件

## 完成标志
系统在WSL Docker环境中成功部署并稳定运行。
