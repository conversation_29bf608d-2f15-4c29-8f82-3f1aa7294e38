# Vue前端开发Cursor规则

## 基础提示模板

### Vue3组件生成
```
作为资深Vue3前端开发专家，请基于以下技术栈生成[组件名称]组件：
- Vue 3.x (Composition API)
- TypeScript
- Element Plus UI库
- Pinia状态管理
- Vue Router
- Less样式预处理器
- ESLint配置

技术要求：
- 符合Vue3最佳实践
- 使用TypeScript类型定义
- 良好的组件封装性
- 性能优化考虑
- 响应式设计支持
- 代码可维护性

业务需求：
[描述业务需求]

组件功能点：
1. [功能点1]
2. [功能点2]
3. [功能点3]
```

### API集成实现
```
请实现基于Axios的API请求服务，用于[功能描述]，要求：
- 请求拦截器设置
- 响应拦截器设置
- 统一错误处理
- 取消请求支持
- 请求重试机制
- 请求缓存策略
- 鉴权Token处理
- 请求超时控制

API接口描述：
[描述API接口信息]

错误处理策略：
[描述错误处理策略]
```

### 表单组件生成
```
请设计并实现一个[表单名称]表单组件，基于Element Plus，要求：
- 完整的表单验证
- 多种输入控件组合
- 动态表单项控制
- 表单联动逻辑
- 表单数据初始化
- 表单提交处理
- 本地存储支持
- 响应式布局适配

表单字段：
[列出表单字段及其验证规则]

提交流程：
[描述表单提交流程]
```

### 列表与分页组件
```
请实现一个基于Element Plus的数据列表组件，带有分页、筛选和排序功能，要求：
- 服务端分页实现
- 多条件筛选
- 列表排序
- 批量操作支持
- 行内编辑功能
- 列显示控制
- 导出功能
- 响应式适配

数据结构：
[描述列表数据结构]

主要功能点：
[描述列表主要功能点]
```

## 高级应用场景

### 微前端架构实现
```
请基于qiankun框架设计[应用名称]的微前端架构方案，要求：
- 主应用配置
- 子应用接入规范
- 应用间通信机制
- 共享依赖管理
- 路由管理策略
- 权限控制集成
- 样式隔离方案
- 性能优化考虑

应用架构：
[描述应用架构和模块划分]

技术选型：
[描述各子应用的技术栈]
```

### 状态管理设计
```
请基于Pinia设计[应用名称]的状态管理方案，要求：
- Store模块设计
- 状态持久化策略
- 异步操作处理
- TypeScript类型支持
- 性能优化考虑
- 插件集成
- 与组件的连接模式
- 开发调试支持

业务模块：
[描述主要业务模块]

状态数据结构：
[描述关键状态的数据结构]
```

### 主题与国际化实现
```
请设计并实现[应用名称]的主题切换和国际化方案，要求：
- 动态主题切换
- 主题变量管理
- 暗色/亮色模式支持
- 多语言支持
- 语言包管理
- 文本格式化处理
- 方向性支持(RTL)
- 本地存储首选项

技术实现：
[描述技术实现方案]

支持的语言：
[列出需要支持的语言]
```

## 代码质量规范

### TypeScript类型定义
```
请为以下接口/组件创建TypeScript类型定义：

```typescript
[相关代码]
```

要求：
- 完整的类型定义
- 接口扩展考虑
- 泛型应用
- 类型保护
- 字面量类型和联合类型应用
- 类型工具应用
- 可选属性处理
- 只读属性标记
```

### 组件优化模板
```
请分析并优化以下Vue组件的性能：

```vue
[Vue组件代码]
```

优化方向：
- 不必要的渲染检查
- 计算属性优化
- 响应式数据拆分
- 组件拆分建议
- 事件处理优化
- v-once/v-memo使用
- 懒加载策略
- 虚拟列表考虑
```

### 单元测试模板
```
为[组件名]编写完整的单元测试，使用Vue Test Utils和Jest，要求：
- 组件渲染测试
- Props验证测试
- 事件触发测试
- 用户交互模拟
- 异步操作测试
- 快照测试
- 边界情况测试
- Mocking外部依赖

组件功能：
[描述组件功能]

测试场景：
[描述需要测试的场景]
```

## 性能优化指南

### 首屏加载优化
```
请分析并优化[应用名称]的首屏加载性能，当前情况：
- 首屏加载时间：[当前时间]
- 资源总大小：[资源大小]
- 请求数量：[请求数]
- 关键渲染路径：[描述]

请提供以下优化建议：
- 资源分割策略
- 代码分割方案
- 预加载/预取策略
- 图片优化方案
- 关键CSS提取
- 服务端渲染/静态生成考虑
- 缓存策略
- CDN部署建议
```

### 长列表渲染优化
```
请优化以下大数据量列表的渲染性能：

```vue
[列表渲染代码]
```

优化方向：
- 虚拟滚动实现
- 分段渲染
- 懒加载策略
- 组件复用优化
- 不可变数据处理
- DOM操作优化
- 防抖/节流应用
- Web Worker考虑
```

### 内存泄漏排查
```
请分析以下Vue组件可能的内存泄漏问题：

```vue
[组件代码]
```

排查方向：
- 事件监听器未移除
- 闭包引用问题
- 定时器未清除
- 大对象引用未释放
- 第三方库资源未释放
- 全局状态累积
- DOM引用未清理
- 观察者模式应用问题
```

## 安全最佳实践

### 前端安全加固
```
请审查并加固以下前端代码的安全性：

```vue
[前端代码]
```

安全加固方向：
- XSS防护
- CSRF防护
- 敏感信息处理
- 输入验证
- URL参数处理
- API请求安全
- 本地存储安全
- 第三方组件安全性检查
```

### 权限控制实现
```
请设计并实现[应用名称]的前端权限控制系统，要求：
- 基于角色的访问控制
- 菜单权限控制
- 按钮/操作权限控制
- 路由权限控制
- 数据权限控制
- 动态权限加载
- 权限缓存策略
- 无权限时的友好提示

权限模型：
[描述权限模型]

用户角色：
[描述用户角色]
``` 