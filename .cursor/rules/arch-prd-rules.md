# 架构师PRD与架构设计文档生成规则

## PRD（产品需求文档）生成模板
```
作为一名资深架构师，请根据以下业务背景和目标，生成一份标准的PRD（产品需求文档）：

业务背景：
[简要描述业务背景和市场需求]

目标与范围：
[明确本次产品/项目的目标和范围]

核心需求：
1. [核心需求1]
2. [核心需求2]
3. [核心需求3]

用户角色与场景：
- 用户角色：[列出主要用户角色]
- 典型场景：[描述关键使用场景]

功能列表：
- [功能1]
- [功能2]
- [功能3]

非功能需求：
- 性能要求：[描述性能目标]
- 安全要求：[描述安全目标]
- 可用性要求：[描述可用性目标]
- 兼容性要求：[描述兼容性目标]

里程碑与计划：
- 关键里程碑：[列出主要时间节点]
- 交付计划：[描述交付节奏]

风险与假设：
- 主要风险：[列出潜在风险]
- 关键假设：[列出前提假设]

请输出结构化、条理清晰、便于团队理解和执行的PRD文档。
```

## 架构设计文档生成模板
```
作为一名企业级架构师，请根据以下需求，生成一份标准的架构设计文档：

1. 项目背景与目标
   - 业务背景：[简要描述]
   - 技术目标：[明确技术目标]

2. 总体架构设计
   - 架构风格：[如微服务、单体、事件驱动等]
   - 技术选型：[列出现有和推荐的技术栈]
   - 系统边界与模块划分：[简要说明]
   - 关键设计原则：[如高可用、可扩展、安全等]

3. 主要模块设计
   - 模块1：[名称与职责]
   - 模块2：[名称与职责]
   - 模块3：[名称与职责]

4. 数据架构设计
   - 数据库选型与结构：[简要说明]
   - 数据流与同步策略：[简要说明]
   - 数据安全与合规性：[简要说明]

5. 接口与集成设计
   - 内部接口设计：[主要接口说明]
   - 外部系统集成：[对接系统与方式]
   - API规范与安全：[简要说明]

6. 非功能性设计
   - 性能与扩展性：[设计要点]
   - 安全性设计：[设计要点]
   - 运维与监控：[设计要点]
   - 灾备与容错：[设计要点]

7. 部署架构
   - 部署拓扑图：[简要说明]
   - 环境规划：[开发、测试、生产等环境说明]
   - 自动化运维策略：[CI/CD、自动化脚本等]

8. 风险评估与应对
   - 主要技术风险：[列举并给出应对措施]
   - 项目实施风险：[列举并给出应对措施]

9. 里程碑与交付计划
   - 关键里程碑：[列出主要时间节点]
   - 交付计划：[描述交付节奏]

10. 参考资料与附录
   - 相关文档链接
   - 术语表
   - 其他补充说明

请输出结构化、专业、便于团队评审和落地的架构设计文档。
``` 