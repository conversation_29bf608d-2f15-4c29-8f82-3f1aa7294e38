/**
 * Cursor规则系统初始化脚本
 * 
 * 该脚本在Cursor启动时执行，初始化规则系统并注册相关事件处理程序
 */

const fs = require('fs');
const path = require('path');
const autoSuggest = require('./auto-suggest');

// 初始化函数
function initialize() {
  console.log('正在初始化Cursor企业级规则系统...');
  
  try {
    // 检查配置文件是否存在
    const configPath = path.join(__dirname, 'config.json');
    if (!fs.existsSync(configPath)) {
      console.error('配置文件不存在，规则系统初始化失败');
      return false;
    }
    
    // 加载配置
    const config = require('./config.json');
    
    // 验证规则文件是否都存在
    for (const rule of config.rules) {
      const rulePath = path.join(__dirname, rule.file);
      if (!fs.existsSync(rulePath)) {
        console.error(`规则文件不存在: ${rule.file}`);
        return false;
      }
    }
    
    // 注册事件监听器
    registerEventListeners();
    
    console.log('Cursor企业级规则系统初始化成功');
    console.log(`已加载 ${config.rules.length} 条规则`);
    
    return true;
  } catch (error) {
    console.error('规则系统初始化失败:', error.message);
    return false;
  }
}

// 注册事件监听器
function registerEventListeners() {
  // 监听用户输入事件
  cursor.on('userInput', (event) => {
    handleUserInput(event.input, event.context);
  });
  
  // 监听文件切换事件
  cursor.on('fileChanged', (event) => {
    handleFileChanged(event.file);
  });
  
  // 其他可能的事件...
}

// 处理用户输入
function handleUserInput(input, context) {
  // 根据输入和上下文匹配规则
  const matchedRule = autoSuggest.matchRule(context, input);
  
  if (matchedRule) {
    // 获取规则内容
    const ruleResult = autoSuggest.applyRule(matchedRule.name);
    
    if (ruleResult.success) {
      // 加载配置决定是否自动应用
      const config = require('./config.json');
      
      if (config.settings.autoApply) {
        // 自动应用规则
        applyRuleToEditor(ruleResult.content);
      } else {
        // 显示建议
        showSuggestion(matchedRule.name, ruleResult.content);
      }
    }
  }
}

// 处理文件切换
function handleFileChanged(file) {
  // 创建上下文对象
  const context = {
    currentFile: {
      path: file.path,
      language: file.language,
      content: file.content
    }
  };
  
  // 匹配规则
  const matchedRule = autoSuggest.matchRule(context, '');
  
  if (matchedRule) {
    // 显示文件类型相关的规则提示
    showFileTypeHint(matchedRule.name);
  }
}

// 应用规则到编辑器（这里是示例实现，实际实现取决于Cursor API）
function applyRuleToEditor(content) {
  cursor.suggest(content);
}

// 显示规则建议（这里是示例实现，实际实现取决于Cursor API）
function showSuggestion(ruleName, content) {
  cursor.showNotification({
    title: '规则建议',
    message: `检测到您可能需要 "${ruleName}" 规则`,
    actions: [
      {
        label: '应用',
        callback: () => applyRuleToEditor(content)
      },
      {
        label: '忽略',
        callback: () => {}
      }
    ]
  });
}

// 显示文件类型提示（这里是示例实现，实际实现取决于Cursor API）
function showFileTypeHint(ruleName) {
  cursor.showStatusBarMessage(`提示: 当前文件适用 "${ruleName}" 规则，可使用触发词自动应用`);
}

// 导出模块
module.exports = {
  initialize
};

// 自动初始化
initialize();