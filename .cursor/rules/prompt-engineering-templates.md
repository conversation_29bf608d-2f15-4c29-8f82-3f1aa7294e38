# Cursor通用提示工程模板

## 角色与任务定义模板

### 专家角色模板
```
作为一名专业的[技术领域]专家，精通[技术栈]，请帮我[任务描述]。

技术要求：
- [框架/库/技术栈列表]
- [版本信息]
- [企业规范]

业务上下文：
[业务场景描述]

请提供：
1. [期望输出内容1]
2. [期望输出内容2]
3. [期望输出内容3]
```

### 代码分析模板
```
作为一名资深的代码分析专家，请分析以下代码：

```[语言]
[粘贴代码]
```

请提供以下分析：
1. 代码的主要功能和目的
2. 潜在的性能问题
3. 可能的安全漏洞
4. 代码质量和可维护性评估
5. 改进建议，包括具体的代码示例
```

### 问题诊断模板
```
作为一名[技术领域]问题诊断专家，请帮我解决以下错误：

错误信息：
```
[错误信息/堆栈跟踪]
```

环境信息：
- 操作系统：[OS信息]
- 运行时环境：[运行时版本]
- 相关依赖：[依赖库及版本]
- 代码片段：
```[语言]
[相关代码]
```

我已经尝试过的解决方案：
1. [尝试1]
2. [尝试2]

请提供：
1. 错误根因分析
2. 解决方案，包括代码修改建议
3. 防止此类错误的最佳实践
```

## 代码生成模板

### 功能实现模板
```
请实现一个[功能名称]功能，要求如下：

功能描述：
[详细的功能描述]

输入参数：
- [参数1]: [类型] - [描述]
- [参数2]: [类型] - [描述]

预期输出：
- [输出描述]

技术要求：
- 语言：[编程语言]
- 框架：[框架名称]
- 其他约束：[性能/内存/兼容性等要求]

示例用例：
- 输入：[示例输入]
- 预期输出：[示例输出]

请提供完整、可运行的代码实现，并包含必要的注释。
```

### 组件设计模板
```
请设计并实现一个[组件名称]组件，该组件需要满足以下需求：

功能需求：
1. [功能1]
2. [功能2]
3. [功能3]

技术规范：
- 框架：[框架名称和版本]
- 设计模式：[期望使用的设计模式]
- 接口定义：[接口约束]
- 代码风格：[代码规范]

性能要求：
- [描述性能期望]

可扩展性考虑：
- [描述未来可能的扩展]

请提供：
1. 组件设计思路和架构
2. 完整的代码实现
3. 使用示例
4. 测试建议
```

### 重构优化模板
```
请帮我重构以下代码，提高其可读性、性能和可维护性：

```[语言]
[粘贴代码]
```

重构目标：
1. [目标1，如提高性能]
2. [目标2，如增强可读性]
3. [目标3，如解决特定问题]

重构约束：
- [约束1，如保持API兼容性]
- [约束2，如特定性能要求]

请提供：
1. 重构后的完整代码
2. 重构思路和改进点解释
3. 预期的改进效果
```

## 文档生成模板

### API文档模板
```
请为以下API端点生成标准RESTful API文档：

```[语言]
[API代码片段]
```

文档要求：
- 符合OpenAPI/Swagger规范
- 包含详细的请求/响应模型
- 提供请求示例
- 说明可能的错误码和处理
- 包含授权要求

特殊说明：
[任何特殊情况或限制]
```

### 技术方案模板
```
请为[项目/功能名称]编写一份技术方案文档，内容包括：

1. 背景和目标
   - 业务背景
   - 技术挑战
   - 目标和成功标准

2. 方案设计
   - 整体架构
   - 核心模块设计
   - 接口设计
   - 数据模型设计
   - 算法和流程

3. 技术选型
   - 框架和库选择理由
   - 技术栈兼容性分析
   - 替代方案比较

4. 实施计划
   - 阶段划分
   - 关键里程碑
   - 风险识别与缓解

5. 评估和监控
   - 性能指标
   - 监控方案
   - 持续优化策略

方案约束：
[列出时间、资源、兼容性等约束]
```

### 用户指南模板
```
请为[软件/功能名称]编写一份用户指南，该指南应面向[目标用户群体]，并满足以下要求：

1. 概述
   - 功能简介
   - 适用场景
   - 主要优势

2. 快速入门
   - 安装/配置步骤
   - 基本使用流程
   - 示例场景

3. 功能详解
   - [功能1]详细说明
   - [功能2]详细说明
   - 高级特性

4. 常见问题解答
   - 常见错误及解决方法
   - 使用技巧
   - 限制条件

5. 参考资料
   - 术语表
   - 配置参数说明
   - 相关资源链接

特殊要求：
- 使用简洁明了的语言
- 包含截图和视觉指引
- 提供实际操作示例
```

## 测试相关模板

### 单元测试模板
```
请为以下代码生成全面的单元测试：

```[语言]
[粘贴代码]
```

测试要求：
- 使用[测试框架]
- 测试覆盖率目标：[覆盖率要求]
- 包含正常流程测试
- 包含边界条件测试
- 包含异常情况测试
- 使用模拟(Mock)处理外部依赖

特殊测试场景：
1. [特殊场景1]
2. [特殊场景2]
```

### 性能测试方案模板
```
请为[系统/功能名称]设计一份性能测试方案，要求包含：

1. 测试目标
   - 性能指标定义
   - 基准和期望值
   - 瓶颈假设

2. 测试环境
   - 硬件配置
   - 网络环境
   - 软件版本
   - 测试数据规模

3. 测试场景
   - 并发用户模拟
   - 业务流程路径
   - 数据变化模式
   - 长时间稳定性测试

4. 监控方案
   - 关键指标监控
   - 日志分析策略
   - 报警机制

5. 分析与报告
   - 结果收集方法
   - 分析维度
   - 报告格式

测试工具：
- [推荐使用的性能测试工具]
- [监控工具]
```

### 安全测试模板
```
请为[系统/应用名称]制定一份安全测试计划，关注以下安全风险：

1. 漏洞识别
   - OWASP Top 10 漏洞检查
   - 特定技术栈相关漏洞
   - API安全测试
   - 认证与授权测试

2. 安全测试方法
   - 静态代码分析
   - 动态应用安全测试
   - 渗透测试策略
   - 模糊测试方案

3. 测试场景
   - 身份认证绕过
   - 越权访问
   - 数据泄露风险
   - 注入攻击测试
   - 会话管理测试

4. 敏感数据保护
   - 数据加密验证
   - 传输安全测试
   - 存储安全测试

5. 报告与修复
   - 风险评级方法
   - 修复优先级策略
   - 修复验证流程

推荐工具：
- [安全测试工具列表]
```

## 开发效率模板

### 快速原型模板
```
请为[功能/项目名称]创建一个快速原型，帮助验证核心概念：

原型目标：
- [描述原型要验证的核心概念]
- [描述预期的交互流程]

技术栈：
- 前端：[前端框架/库]
- 后端：[后端框架/库]
- 数据存储：[数据存储解决方案]

功能范围：
1. [核心功能1]
2. [核心功能2]
3. [核心功能3]

简化假设：
- [可以简化的方面1]
- [可以简化的方面2]

请提供：
1. 最小可行产品的核心代码
2. 关键交互流程
3. 部署和运行指南
```

### 代码审查模板
```
请对以下代码进行全面审查，并提供改进建议：

```[语言]
[粘贴代码]
```

审查维度：
1. 代码质量
   - 代码风格与规范
   - 命名与可读性
   - 注释完整性

2. 功能实现
   - 功能完整性
   - 业务逻辑正确性
   - 边缘情况处理

3. 性能效率
   - 算法选择
   - 资源使用效率
   - 潜在性能瓶颈

4. 安全性
   - 常见漏洞检查
   - 敏感数据处理
   - 输入验证和输出编码

5. 可维护性
   - 代码结构
   - 复杂度评估
   - 可测试性

请提供具体、可操作的改进建议，并按优先级排序。
```

### 技术选型模板
```
请为[项目/功能名称]进行技术选型分析，帮助我选择最合适的技术栈：

项目背景：
- [项目简介]
- [业务特点]
- [团队情况]

功能需求：
1. [功能需求1]
2. [功能需求2]
3. [功能需求3]

非功能需求：
- 性能要求：[描述性能要求]
- 安全要求：[描述安全要求]
- 可扩展性：[描述扩展性要求]
- 维护性：[描述维护性要求]

备选技术：
- 方案A：[技术组合A]
- 方案B：[技术组合B]
- 方案C：[技术组合C]

请提供：
1. 各方案的优缺点分析
2. 对比评估表格(包含各维度评分)
3. 最终推荐和理由
4. 实施建议
``` 