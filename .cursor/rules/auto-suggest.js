/**
 * Cursor规则自动建议脚本
 * 
 * 该脚本根据用户输入和当前文件上下文，自动应用适当的规则模板
 */

// 规则匹配函数
function matchRule(context, userInput) {
  // 加载配置
  const config = require('./config.json');
  const rules = config.rules;
  const settings = config.settings;
  
  // 如果未启用自动建议，返回null
  if (!settings.autoSuggest) {
    return null;
  }
  
  let matchedRules = [];
  
  // 基于文件类型匹配
  if (context.currentFile) {
    const filePath = context.currentFile.path;
    
    for (const rule of rules) {
      if (rule.filePatterns) {
        for (const pattern of rule.filePatterns) {
          if (matchPattern(filePath, pattern)) {
            matchedRules.push({
              rule: rule,
              score: 1.0
            });
            break;
          }
        }
      }
    }
  }
  
  // 基于用户输入触发词匹配
  if (userInput) {
    const inputLower = userInput.toLowerCase();
    
    for (const rule of rules) {
      if (rule.triggers) {
        for (const trigger of rule.triggers) {
          if (inputLower.includes(trigger.toLowerCase())) {
            // 检查是否已经匹配过这条规则
            const existingRule = matchedRules.find(r => r.rule.name === rule.name);
            
            if (existingRule) {
              // 增加匹配分数
              existingRule.score += 0.5;
            } else {
              matchedRules.push({
                rule: rule,
                score: 0.8
              });
            }
            break;
          }
        }
      }
    }
  }
  
  // 根据分数排序
  matchedRules.sort((a, b) => b.score - a.score);
  
  // 应用优先级排序
  if (settings.priorityOrder && matchedRules.length > 1) {
    matchedRules.sort((a, b) => {
      const indexA = settings.priorityOrder.indexOf(a.rule.name);
      const indexB = settings.priorityOrder.indexOf(b.rule.name);
      
      if (indexA === -1 && indexB === -1) return 0;
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      
      return indexA - indexB;
    });
  }
  
  // 返回最匹配的规则
  return matchedRules.length > 0 ? matchedRules[0].rule : null;
}

// 辅助函数: 简单的通配符模式匹配
function matchPattern(filePath, pattern) {
  // 转换通配符为正则表达式
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*\*/g, '.*')
    .replace(/\*/g, '[^/]*');
  
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(filePath);
}

// 规则应用函数
function applyRule(ruleName) {
  const config = require('./config.json');
  const rules = config.rules;
  
  // 查找规则
  const rule = rules.find(r => r.name === ruleName);
  if (!rule) {
    return { success: false, message: `找不到规则: ${ruleName}` };
  }
  
  // 读取规则文件内容
  try {
    const fs = require('fs');
    const path = require('path');
    
    const rulePath = path.join(__dirname, rule.file);
    const ruleContent = fs.readFileSync(rulePath, 'utf8');
    
    return {
      success: true,
      name: rule.name,
      content: ruleContent
    };
  } catch (error) {
    return {
      success: false,
      message: `读取规则文件失败: ${error.message}`
    };
  }
}

module.exports = {
  matchRule,
  applyRule
}; 