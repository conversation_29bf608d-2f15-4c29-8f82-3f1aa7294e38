# 履约执行模块修正版操作指南

## 🔧 重要修正
**导航路径修正**: 不再访问【个人工作台】，直接访问【履约执行】模块

## 🎯 目标
完成履约执行模块17个页面的截图和测试（移除个人工作台）

## 📸 修正后的截图清单（17个页面）

### 基础页面（3个）
1. ✅ **登录页面.png** - 已完成
2. ⏳ **首页.png** - 登录成功后的系统首页
3. ⏳ **合同管理列表.png** - 首页 → 履约执行 → 合同管理

### 合同操作（2个）
4. ⏳ **合同编辑抽屉.png** - 合同列表 → 点击编辑按钮
5. ⏳ **合同创建抽屉.png** - 合同列表 → 点击创建合同按钮

### 合同详情（6个）
6. ⏳ **合同详情页面.png** - 合同列表 → 点击合同名称
7. ⏳ **合同内容信息.png** - 合同详情 → 合同内容信息标签
8. ⏳ **支付节点信息.png** - 合同详情 → 支付节点信息标签
9. ⏳ **节点确认记录.png** - 合同详情 → 节点确认记录标签
10. ⏳ **合同附件信息.png** - 合同详情 → 合同附件信息标签
11. ⏳ **合同审批流程.png** - 合同详情 → 合同审批流程标签

### 功能操作（6个）
12. ⏳ **支付节点确认弹窗.png** - 支付节点页面 → 点击确认按钮
13. ⏳ **支付状态确认弹窗.png** - 支付节点页面 → 点击状态确认
14. ⏳ **确认记录详情.png** - 确认记录页面 → 点击查看详情
15. ⏳ **确认记录审核.png** - 确认记录页面 → 点击审核按钮
16. ⏳ **附件上传界面.png** - 附件页面 → 点击上传按钮
17. ⏳ **附件预览界面.png** - 附件页面 → 点击预览按钮

## 🚀 修正后的执行方案

### 方案A：自动化脚本（推荐）
```bash
cd docs
python3 crawl_contract_execution.py
```

**脚本修正内容**:
- ✅ 移除个人工作台导航
- ✅ 直接导航到履约执行模块
- ✅ 更新截图总数为17个
- ✅ 修正导航路径逻辑

### 方案B：浏览器辅助脚本
```javascript
// 修正后的浏览器辅助脚本
class ScreenshotHelper {
    constructor() {
        this.shots = [
            "首页.png - 当前页面直接截图",
            "合同管理列表.png - 点击左侧'履约执行' → '合同管理'",
            "合同编辑抽屉.png - 点击'编辑'按钮",
            "合同创建抽屉.png - 点击'创建合同'",
            "合同详情页面.png - 点击合同名称",
            "合同内容信息.png - 点击'合同内容信息'标签",
            "支付节点信息.png - 点击'支付节点信息'标签",
            "支付节点确认弹窗.png - 点击'确认'按钮",
            "支付状态确认弹窗.png - 点击'状态确认'",
            "节点确认记录.png - 点击'节点确认记录'标签",
            "确认记录详情.png - 点击'查看详情'",
            "确认记录审核.png - 点击'审核'按钮",
            "合同附件信息.png - 点击'合同附件信息'标签",
            "附件上传界面.png - 点击'上传附件'",
            "附件预览界面.png - 点击'预览'按钮",
            "合同审批流程.png - 点击'合同审批流程'标签"
        ];
        this.index = 0;
    }
    
    start() {
        console.log("🚀 履约执行模块截图助手（修正版）");
        console.log("📋 总共需要截图16个页面（已完成登录页面）");
        this.show();
    }
    
    show() {
        if (this.index >= this.shots.length) {
            console.log("🎉 所有截图完成！");
            return;
        }
        console.log(`\n📸 [${this.index + 1}/16] ${this.shots[this.index]}`);
        console.log("💾 保存到: docs/images/front_system/履约执行/");
        console.log("⏸️  截图后运行: next()");
    }
    
    next() {
        this.index++;
        this.show();
    }
}

window.helper = new ScreenshotHelper();
helper.start();
```

### 方案C：手动操作步骤

#### 详细操作流程
1. **登录系统** ✅ 已完成
2. **截图首页** - 当前页面直接截图
3. **进入履约执行** - 点击左侧菜单"履约执行"
4. **进入合同管理** - 点击"合同管理"子菜单
5. **截图列表页** - 截图合同管理列表页面
6. **测试编辑功能** - 点击编辑按钮，截图编辑抽屉
7. **测试创建功能** - 点击创建按钮，截图创建抽屉
8. **进入合同详情** - 点击合同名称，截图详情页面
9. **测试各标签页** - 逐个点击标签页截图
10. **测试弹窗功能** - 点击各种操作按钮截图弹窗

## 🔍 验证修正结果

### 运行验证脚本
```bash
cd docs
python3 verify_screenshots.py
```

**预期结果**: 显示17/17完成

### 更新PRD文档
```bash
python3 update_prd_with_screenshots.py
```

## ⚠️ 重要注意事项

### 导航路径变更
- ❌ 旧路径: 首页 → 个人工作台 → 合同管理
- ✅ 新路径: 首页 → 履约执行 → 合同管理

### 截图数量变更
- ❌ 原计划: 18个截图
- ✅ 修正后: 17个截图（移除个人工作台）

### 脚本更新内容
- ✅ 自动截图脚本已更新导航逻辑
- ✅ 验证脚本已更新截图清单
- ✅ 浏览器辅助脚本已更新操作流程
- ✅ PRD文档已移除个人工作台章节

## 🎯 执行建议

### 立即执行
1. **使用修正后的自动化脚本**（最推荐）
2. **使用修正后的浏览器辅助脚本**
3. **按照修正后的手动操作流程**

### 验证完成
- 确认17个截图文件全部生成
- 运行验证脚本确认质量
- 更新PRD文档补充测试结果

---
**修正完成时间**: 2024-12-XX
**预计执行时间**: 60分钟
**完成标志**: 17/17截图完成，PRD文档更新
