#!/bin/bash

# 履约执行模块一键截图脚本
# 使用方法: chmod +x run_screenshot.sh && ./run_screenshot.sh

echo "🚀 履约执行模块一键截图工具"
echo "================================"

# 检查当前目录
if [ ! -f "crawl_contract_execution.py" ]; then
    echo "❌ 请在 docs 目录下运行此脚本"
    exit 1
fi

# 检查Python环境
echo "🔍 检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 未找到Python环境"
    exit 1
fi

echo "✅ Python命令: $PYTHON_CMD"

# 检查pip
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
elif command -v pip &> /dev/null; then
    PIP_CMD="pip"
else
    echo "❌ 未找到pip"
    exit 1
fi

echo "✅ Pip命令: $PIP_CMD"

# 安装依赖
echo ""
echo "📦 安装依赖包..."
$PIP_CMD install selenium pillow

# 检查ChromeDriver
echo ""
echo "🔍 检查ChromeDriver..."
if [ ! -f "chromedriver" ]; then
    echo "⚠️  未找到ChromeDriver"
    echo "💡 请下载ChromeDriver并放在当前目录"
    echo "   下载地址: https://chromedriver.chromium.org/"
    
    read -p "是否已经下载了ChromeDriver? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 请先下载ChromeDriver"
        exit 1
    fi
fi

# 创建输出目录
echo ""
echo "📁 创建输出目录..."
mkdir -p "./images/front_system/履约执行/"

# 显示菜单
echo ""
echo "📋 请选择截图方式:"
echo "1. 自动截图 (推荐)"
echo "2. 浏览器辅助截图"
echo "3. 验证现有截图"
echo "4. 查看使用说明"

read -p "请输入选项 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🤖 开始自动截图..."
        $PYTHON_CMD crawl_contract_execution.py
        
        echo ""
        echo "🔍 验证截图结果..."
        $PYTHON_CMD verify_screenshots.py
        ;;
    2)
        echo ""
        echo "🌐 浏览器辅助截图模式"
        echo "请按以下步骤操作:"
        echo "1. 打开浏览器访问: http://183.136.206.207:44099/login"
        echo "2. 登录账号: heqiang / Abc123456789!"
        echo "3. 按F12打开开发者工具"
        echo "4. 切换到Console标签页"
        echo "5. 复制并运行 browser_screenshot_guide.js 中的代码"
        echo ""
        echo "📄 脚本文件: browser_screenshot_guide.js"
        
        if [ -f "browser_screenshot_guide.js" ]; then
            echo "✅ 脚本文件已存在"
        else
            echo "❌ 脚本文件不存在"
        fi
        ;;
    3)
        echo ""
        echo "🔍 验证现有截图..."
        $PYTHON_CMD verify_screenshots.py
        ;;
    4)
        echo ""
        echo "📖 使用说明"
        echo "==========="
        echo ""
        echo "🎯 目标: 为履约执行模块截图18个页面"
        echo ""
        echo "📋 截图清单:"
        echo "   1. 登录页面.png"
        echo "   2. 首页.png"
        echo "   3. 个人工作台.png"
        echo "   4. 合同管理列表.png"
        echo "   5. 合同编辑抽屉.png"
        echo "   6. 合同创建抽屉.png"
        echo "   7. 合同详情页面.png"
        echo "   8. 合同内容信息.png"
        echo "   9. 支付节点信息.png"
        echo "   10. 支付节点确认弹窗.png"
        echo "   11. 支付状态确认弹窗.png"
        echo "   12. 节点确认记录.png"
        echo "   13. 确认记录详情.png"
        echo "   14. 确认记录审核.png"
        echo "   15. 合同附件信息.png"
        echo "   16. 附件上传界面.png"
        echo "   17. 附件预览界面.png"
        echo "   18. 合同审批流程.png"
        echo ""
        echo "🔧 环境要求:"
        echo "   - Python 3.x"
        echo "   - Chrome浏览器"
        echo "   - ChromeDriver"
        echo "   - 网络连接"
        echo ""
        echo "📁 输出目录: docs/images/front_system/履约执行/"
        echo ""
        echo "🌐 测试环境:"
        echo "   地址: http://183.136.206.207:44099/login"
        echo "   账号: heqiang"
        echo "   密码: Abc123456789!"
        ;;
    *)
        echo "❌ 无效选项"
        exit 1
        ;;
esac

echo ""
echo "🏁 脚本执行完成"
