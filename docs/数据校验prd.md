<<<<<<< HEAD
# 数据校验产品需求文档(PRD)

## 1. 产品概述

### 1.1 产品背景
系统中的数据存在数据字段内容重名或者为空的问题，影响项目推进。具体表现在
1.合同名称重名问题
2.合同编号为空问题
3.商务负责人为空问题
4.技术负责人为空问题
5.合同里程碑名称重名问题
6.里程碑编号为空问题
7.需求名称重名问题
8.需求编号为空问题
9.报价名称重名问题
10.报价编号为空
这些问题导致数据管理混乱，影响业务流程正常运转。

### 1.2 开发目的
开发"数据校验"功能模块，辅助管理员快速定位并查找有问题的数据，提高数据质量管理效率，确保业务流程顺畅进行。

### 1.3 解决的问题
- 自动检测并标识数据异常
- 提供简单直观的操作界面
- 减少人工查错的工作量
- 提高数据质量和准确性

## 2. 目标用户

数据管理员：负责系统数据维护和管理的人员，需要定期检查数据问题并进行修复。

## 3. 核心功能模块

### 3.1 模块A：MAXkb AI对话工作流（数据管理员操作界面）

#### 3.1.1 功能描述
- 提供自然语言对话式交互界面
- 支持查询各类型问题数据
- 展示查询结果
- 仅支持查询功能，不包含修改操作

#### 3.1.2 界面设计


界面组成：
1. **对话输入框**：用户可以输入自然语言查询，如"查找合同名称重名的数据"
2. **查询结果展示区**：以表格形式展示查询到的异常数据


#### 3.1.3 交互流程
1. 用户登录Dkm系统进入数据校验模块
2. 在对话框输入查询需求（自然语言或选择预设问题类型）
3. 系统解析需求，调用后端API查询相关数据
4. 结果以表格形式展示，包含问题类型、数据ID、字段名、字段值等

### 3.2 模块B：后端查询API

#### 3.2.1 功能描述
- 提供RESTful API接口供前端调用
- 支持10种核心数据问题的查询逻辑
- 查询结果格式化
- 查询性能优化，避免长时间阻塞
- 权限控制和数据安全处理
- 支持导出Excel报表

#### 3.2.2 API接口设计

##### 仅保留Excel导出接口

1. **市场合同数据校验Excel导出接口**
   - 路径: `/mpf/check-market-contract-excel`
   - 请求方法: POST
   - 描述: 校验市场合同表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/市场合同表数据完整性检查_20230617123045.xlsx"
     }
     ```

2. **报价管理数据校验Excel导出接口**
   - 路径: `/mpf/check-quotation-excel`
   - 请求方法: POST
   - 描述: 校验报价管理表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/报价管理表数据完整性检查_20230617123145.xlsx"
     }
     ```

3. **合同里程碑数据校验Excel导出接口**
   - 路径: `/mpf/check-contract-milestone-excel`
   - 请求方法: POST
   - 描述: 校验合同里程碑表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/合同里程碑表数据完整性检查_20230617123245.xlsx"
     }
     ```

4. **需求管理数据校验Excel导出接口**
   - 路径: `/mpf/check-requirement-excel`
   - 请求方法: POST
   - 描述: 校验需求管理表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/需求管理表数据完整性检查_20230617123345.xlsx"
     }
     ```

#### 3.2.3 技术实现
- 采用微服务架构，单独部署数据校验服务

## 4. 具体校验场景实现

### 4.1 支持的数据校验场景
| 序号 | 校验场景 | 涉及表 | 涉及字段 |
|------|---------|--------|----------|
| 1 | 合同名称重名 | pms_market_contract | name |
| 2 | 合同编号为空 | pms_market_contract | number |
| 3 | 商务负责人为空 | pms_market_contract | commerce_rsp_user |
| 4 | 技术负责人为空 | pms_market_contract | tech_rsp_user |
| 5 | 合同里程碑名称重名 | pms_contract_milestone | milestone_name |
| 6 | 里程碑编号为空 | pms_contract_milestone | number |
| 7 | 需求名称重名 | pms_requirement_mangement | requirement_name |
| 8 | 需求编号为空 | pms_requirement_mangement | requirement_number |
| 9 | 报价名称重名 | pmsx_quotation_management | quotation_name |
| 10 | 报价编号为空 | pmsx_quotation_management | quotation_id |

### 4.2 校验规则实现
* **重名检查**：同一表中同一字段值相同的记录（排除特殊场景）
* **空值检查**：对必填字段进行NULL值或空字符串检查
* **数据一致性**：检查跨表数据关联一致性

## 5. 用户流程

### 5.1 典型用例
1. **数据管理员日常检查流程**
   - 进入数据校验模块
   - 选择或输入需要检查的问题类型
   - 查看检查结果
   - 人工记录问题数据信息，联系相关人员处理

### 5.2 对话示例
```
用户：查找所有合同名称重名的数据
系统：已为您查询到5条合同名称重名的数据，详情请下载Excel报表...

用户：查询技术负责人为空的合同
系统：已为您查询到12条技术负责人为空的合同数据，详情请下载Excel报表...
```

## 6. 技术实现建议

### 6.1 系统架构


#### 前端技术栈

- MAXkb AI组件

#### 后端技术栈
- Spring Boot微服务
- MySQL数据库

### 6.2 对接时序图
系统交互时序图详见 [数据校验时序图.puml](数据校验时序图.puml) 文件。

```plantuml
!include 数据校验时序图.puml
```

## 7. 产品开发规划

### 7.1 开发优先级
1. **高优先级**（第一阶段）
   - 核心校验规则实现
   - 基础查询API开发
   - MAXkb AI对话工作流编排


### 7.2 待开发功能模块
- [ ] 基础数据校验API接口
- [ ] MAXkb AI对话工作流编排


## 8. 风险与考量

### 8.1 性能风险
- 大量数据查询可能导致系统响应缓慢
- 解决方案：实现查询结果分页、缓存以及异步处理机制

### 8.2 用户体验
- 自然语言理解准确性
- 解决方案：提供预设问题类型，结合自然语言处理技术

### 8.3 数据安全
- 敏感信息保护
- 解决方案：严格的权限控制和数据脱敏处理

## 9. 附录

### 9.1 相关数据模型
模块已引用到的数据表：
- pms_market_contract（市场合同表）
- pmsx_quotation_management（报价管理表）
- pms_contract_milestone（合同里程碑表）
- pms_requirement_mangement（需求管理表）

### 9.2 数据模型关系图
数据模型关系图详见 [数据模型.puml](数据模型.puml) 文件。

```plantuml
!include 数据模型.puml
```


=======
# 数据校验产品需求文档(PRD)

## 1. 产品概述

### 1.1 产品背景
系统中的数据存在数据字段内容重名或者为空的问题，影响项目推进。具体表现在
1.合同名称重名问题
2.合同编号为空问题
3.商务负责人为空问题
4.技术负责人为空问题
5.合同里程碑名称重名问题
6.里程碑编号为空问题
7.需求名称重名问题
8.需求编号为空问题
9.报价名称重名问题
10.报价编号为空
这些问题导致数据管理混乱，影响业务流程正常运转。

### 1.2 开发目的
开发"数据校验"功能模块，辅助管理员快速定位并查找有问题的数据，提高数据质量管理效率，确保业务流程顺畅进行。

### 1.3 解决的问题
- 自动检测并标识数据异常
- 提供简单直观的操作界面
- 减少人工查错的工作量
- 提高数据质量和准确性

## 2. 目标用户

数据管理员：负责系统数据维护和管理的人员，需要定期检查数据问题并进行修复。

## 3. 核心功能模块

### 3.1 模块A：MAXkb AI对话工作流（数据管理员操作界面）

#### 3.1.1 功能描述
- 提供自然语言对话式交互界面
- 支持查询各类型问题数据
- 展示查询结果
- 仅支持查询功能，不包含修改操作

#### 3.1.2 界面设计


界面组成：
1. **对话输入框**：用户可以输入自然语言查询，如"查找合同名称重名的数据"
2. **查询结果展示区**：以表格形式展示查询到的异常数据


#### 3.1.3 交互流程
1. 用户登录Dkm系统进入数据校验模块
2. 在对话框输入查询需求（自然语言或选择预设问题类型）
3. 系统解析需求，调用后端API查询相关数据
4. 结果以表格形式展示，包含问题类型、数据ID、字段名、字段值等

### 3.2 模块B：后端查询API

#### 3.2.1 功能描述
- 提供RESTful API接口供前端调用
- 支持10种核心数据问题的查询逻辑
- 查询结果格式化
- 查询性能优化，避免长时间阻塞
- 权限控制和数据安全处理
- 支持导出Excel报表

#### 3.2.2 API接口设计

##### 仅保留Excel导出接口

1. **市场合同数据校验Excel导出接口**
   - 路径: `/mpf/check-market-contract-excel`
   - 请求方法: POST
   - 描述: 校验市场合同表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/市场合同表数据完整性检查_20230617123045.xlsx"
     }
     ```

2. **报价管理数据校验Excel导出接口**
   - 路径: `/mpf/check-quotation-excel`
   - 请求方法: POST
   - 描述: 校验报价管理表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/报价管理表数据完整性检查_20230617123145.xlsx"
     }
     ```

3. **合同里程碑数据校验Excel导出接口**
   - 路径: `/mpf/check-contract-milestone-excel`
   - 请求方法: POST
   - 描述: 校验合同里程碑表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/合同里程碑表数据完整性检查_20230617123245.xlsx"
     }
     ```

4. **需求管理数据校验Excel导出接口**
   - 路径: `/mpf/check-requirement-excel`
   - 请求方法: POST
   - 描述: 校验需求管理表数据的有效性，并导出Excel格式报告
   - 响应示例:
     ```
     {
       "code": 200,
       "message": "校验完成",
       "result": "http://10.60.16.205:7012/download/需求管理表数据完整性检查_20230617123345.xlsx"
     }
     ```

#### 3.2.3 技术实现
- 采用微服务架构，单独部署数据校验服务

## 4. 具体校验场景实现

### 4.1 支持的数据校验场景
| 序号 | 校验场景 | 涉及表 | 涉及字段 |
|------|---------|--------|----------|
| 1 | 合同名称重名 | pms_market_contract | name |
| 2 | 合同编号为空 | pms_market_contract | number |
| 3 | 商务负责人为空 | pms_market_contract | commerce_rsp_user |
| 4 | 技术负责人为空 | pms_market_contract | tech_rsp_user |
| 5 | 合同里程碑名称重名 | pms_contract_milestone | milestone_name |
| 6 | 里程碑编号为空 | pms_contract_milestone | number |
| 7 | 需求名称重名 | pms_requirement_mangement | requirement_name |
| 8 | 需求编号为空 | pms_requirement_mangement | requirement_number |
| 9 | 报价名称重名 | pmsx_quotation_management | quotation_name |
| 10 | 报价编号为空 | pmsx_quotation_management | quotation_id |

### 4.2 校验规则实现
* **重名检查**：同一表中同一字段值相同的记录（排除特殊场景）
* **空值检查**：对必填字段进行NULL值或空字符串检查
* **数据一致性**：检查跨表数据关联一致性

## 5. 用户流程

### 5.1 典型用例
1. **数据管理员日常检查流程**
   - 进入数据校验模块
   - 选择或输入需要检查的问题类型
   - 查看检查结果
   - 人工记录问题数据信息，联系相关人员处理

### 5.2 对话示例
```
用户：查找所有合同名称重名的数据
系统：已为您查询到5条合同名称重名的数据，详情请下载Excel报表...

用户：查询技术负责人为空的合同
系统：已为您查询到12条技术负责人为空的合同数据，详情请下载Excel报表...
```

## 6. 技术实现建议

### 6.1 系统架构


#### 前端技术栈

- MAXkb AI组件

#### 后端技术栈
- Spring Boot微服务
- MySQL数据库

### 6.2 对接时序图
系统交互时序图详见 [数据校验时序图.puml](数据校验时序图.puml) 文件。

```plantuml
!include 数据校验时序图.puml
```

## 7. 产品开发规划

### 7.1 开发优先级
1. **高优先级**（第一阶段）
   - 核心校验规则实现
   - 基础查询API开发
   - MAXkb AI对话工作流编排


### 7.2 待开发功能模块
- [ ] 基础数据校验API接口
- [ ] MAXkb AI对话工作流编排


## 8. 风险与考量

### 8.1 性能风险
- 大量数据查询可能导致系统响应缓慢
- 解决方案：实现查询结果分页、缓存以及异步处理机制

### 8.2 用户体验
- 自然语言理解准确性
- 解决方案：提供预设问题类型，结合自然语言处理技术

### 8.3 数据安全
- 敏感信息保护
- 解决方案：严格的权限控制和数据脱敏处理

## 9. 附录

### 9.1 相关数据模型
模块已引用到的数据表：
- pms_market_contract（市场合同表）
- pmsx_quotation_management（报价管理表）
- pms_contract_milestone（合同里程碑表）
- pms_requirement_mangement（需求管理表）

### 9.2 数据模型关系图
数据模型关系图详见 [数据模型.puml](数据模型.puml) 文件。

```plantuml
!include 数据模型.puml
```


>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
