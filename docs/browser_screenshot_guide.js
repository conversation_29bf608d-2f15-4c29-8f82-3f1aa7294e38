// 履约执行模块浏览器截图脚本
// 在浏览器开发者工具的Console中运行此脚本

class ContractScreenshotHelper {
    constructor() {
        this.screenshots = [];
        this.currentStep = 0;
        this.totalSteps = 18;
        this.baseUrl = 'http://183.136.206.207:44099';
    }

    // 显示进度
    showProgress(message) {
        console.log(`📸 [${this.currentStep}/${this.totalSteps}] ${message}`);
        this.currentStep++;
    }

    // 等待元素出现
    async waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素未找到: ${selector}`));
                } else {
                    setTimeout(checkElement, 100);
                }
            };
            checkElement();
        });
    }

    // 等待页面加载
    async waitForPageLoad(delay = 2000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    // 滚动到页面顶部
    scrollToTop() {
        window.scrollTo(0, 0);
    }

    // 提示用户截图
    promptScreenshot(filename, description) {
        this.scrollToTop();
        this.showProgress(`请截图: ${filename}`);
        console.log(`📝 描述: ${description}`);
        console.log(`💾 保存路径: docs/images/front_system/履约执行/${filename}`);
        console.log(`⏸️  截图完成后，按回车键继续...`);
        return new Promise(resolve => {
            const handler = (event) => {
                if (event.key === 'Enter') {
                    document.removeEventListener('keydown', handler);
                    resolve();
                }
            };
            document.addEventListener('keydown', handler);
        });
    }

    // 点击元素
    async clickElement(selector, description) {
        try {
            const element = await this.waitForElement(selector);
            element.click();
            console.log(`✅ 点击成功: ${description}`);
            await this.waitForPageLoad();
            return true;
        } catch (error) {
            console.error(`❌ 点击失败: ${description} - ${error.message}`);
            return false;
        }
    }

    // 主要截图流程
    async startScreenshotProcess() {
        console.log('🚀 开始履约执行模块截图流程');
        console.log('📋 总共需要截图 18 个页面');
        console.log('💡 每次提示时，请手动截图并按回车键继续');
        console.log('');

        try {
            // 1. 登录页面
            if (window.location.pathname.includes('login')) {
                await this.promptScreenshot('登录页面.png', '系统登录界面');
            }

            // 2. 首页
            await this.promptScreenshot('首页.png', '登录成功后的首页');

            // 3. 个人工作台
            await this.clickElement('//span[contains(text(), "个人工作台")]', '个人工作台菜单');
            await this.promptScreenshot('个人工作台.png', '个人工作台主页面');

            // 4. 合同管理列表
            await this.clickElement('//span[contains(text(), "合同管理")]', '合同管理菜单');
            await this.promptScreenshot('合同管理列表.png', '合同管理列表页面');

            // 5. 合同编辑抽屉
            if (await this.clickElement('//button[contains(text(), "编辑")]', '编辑按钮')) {
                await this.promptScreenshot('合同编辑抽屉.png', '合同编辑界面');
                await this.clickElement('//button[contains(text(), "取消")]', '取消按钮');
            }

            // 6. 合同创建抽屉
            if (await this.clickElement('//button[contains(text(), "创建合同")]', '创建合同按钮')) {
                await this.promptScreenshot('合同创建抽屉.png', '合同创建界面');
                await this.clickElement('//button[contains(text(), "取消")]', '取消按钮');
            }

            // 7. 合同详情页面
            if (await this.clickElement('//td//a', '合同名称链接')) {
                await this.promptScreenshot('合同详情页面.png', '合同详情主页面');

                // 8. 合同内容信息
                await this.clickElement('//span[contains(text(), "合同内容信息")]', '合同内容信息标签');
                await this.promptScreenshot('合同内容信息.png', '合同基本信息标签页');

                // 9. 支付节点信息
                await this.clickElement('//span[contains(text(), "支付节点信息")]', '支付节点信息标签');
                await this.promptScreenshot('支付节点信息.png', '支付节点信息标签页');

                // 10. 支付节点确认弹窗
                if (await this.clickElement('//button[contains(text(), "支付节点确认")]', '支付节点确认按钮')) {
                    await this.promptScreenshot('支付节点确认弹窗.png', '支付节点确认界面');
                    await this.clickElement('//button[contains(text(), "取消")]', '取消按钮');
                }

                // 11. 支付状态确认弹窗
                if (await this.clickElement('//button[contains(text(), "支付状态确认")]', '支付状态确认按钮')) {
                    await this.promptScreenshot('支付状态确认弹窗.png', '支付状态确认界面');
                    await this.clickElement('//button[contains(text(), "取消")]', '取消按钮');
                }

                // 12. 节点确认记录
                await this.clickElement('//span[contains(text(), "节点确认记录")]', '节点确认记录标签');
                await this.promptScreenshot('节点确认记录.png', '节点确认记录标签页');

                // 13. 确认记录详情
                if (await this.clickElement('//button[contains(text(), "查看详情")]', '查看详情按钮')) {
                    await this.promptScreenshot('确认记录详情.png', '确认记录详情界面');
                    await this.clickElement('//button[contains(text(), "关闭")]', '关闭按钮');
                }

                // 14. 确认记录审核
                if (await this.clickElement('//button[contains(text(), "审核")]', '审核按钮')) {
                    await this.promptScreenshot('确认记录审核.png', '确认记录审核界面');
                    await this.clickElement('//button[contains(text(), "取消")]', '取消按钮');
                }

                // 15. 合同附件信息
                await this.clickElement('//span[contains(text(), "合同附件信息")]', '合同附件信息标签');
                await this.promptScreenshot('合同附件信息.png', '合同附件信息标签页');

                // 16. 附件上传界面
                if (await this.clickElement('//button[contains(text(), "上传附件")]', '上传附件按钮')) {
                    await this.promptScreenshot('附件上传界面.png', '附件上传界面');
                    await this.clickElement('//button[contains(text(), "取消")]', '取消按钮');
                }

                // 17. 附件预览界面
                if (await this.clickElement('//button[contains(text(), "预览")]', '预览按钮')) {
                    await this.promptScreenshot('附件预览界面.png', '附件预览界面');
                    await this.clickElement('//button[contains(text(), "关闭")]', '关闭按钮');
                }

                // 18. 合同审批流程
                await this.clickElement('//span[contains(text(), "合同审批流程")]', '合同审批流程标签');
                await this.promptScreenshot('合同审批流程.png', '合同审批流程标签页');
            }

            console.log('🎉 所有截图流程完成！');
            console.log('📁 请检查 docs/images/front_system/履约执行/ 目录下的截图文件');

        } catch (error) {
            console.error('❌ 截图流程出错:', error);
        }
    }

    // 显示使用说明
    showInstructions() {
        console.log(`
📖 履约执行模块截图助手使用说明

🔧 准备工作:
1. 登录系统: ${this.baseUrl}/login
2. 账号: heqiang
3. 密码: Abc123456789!
4. 打开浏览器开发者工具 (F12)
5. 切换到 Console 标签页

🚀 开始截图:
在 Console 中运行以下命令:
const helper = new ContractScreenshotHelper();
helper.startScreenshotProcess();

📸 截图说明:
- 每次提示时，请使用截图工具截取完整的浏览器窗口
- 截图完成后按回车键继续下一步
- 文件保存到: docs/images/front_system/履约执行/
- 使用提示的文件名保存

⚠️  注意事项:
- 确保页面完全加载后再截图
- 保持浏览器窗口大小一致
- 截图要包含完整的页面内容
        `);
    }
}

// 创建实例并显示说明
const contractHelper = new ContractScreenshotHelper();
contractHelper.showInstructions();

// 使用方法:
// 1. 复制此脚本到浏览器控制台
// 2. 运行: contractHelper.startScreenshotProcess()
