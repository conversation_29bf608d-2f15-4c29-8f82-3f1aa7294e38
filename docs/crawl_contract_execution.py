#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import os
import sys
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContractExecutionCrawler:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.screenshot_count = 0
        self.total_screenshots = 17
        self.failed_screenshots = []
        self.success_screenshots = []
        self.start_time = datetime.now()
        self.setup_driver()

    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')

        # 使用当前目录下的chromedriver
        service = Service('./chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, 15)

    def login(self, username, password):
        """登录系统"""
        try:
            self.driver.get("http://183.136.206.207:44099/login")
            time.sleep(5)

            # 截图登录页面
            self.take_screenshot("./docs/images/front_system/履约执行/登录页面.png")

            # 等待页面完全加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            # 尝试多种方式查找用户名输入框
            username_selectors = [
                (By.NAME, "username"),
                (By.ID, "username"),
                (By.XPATH, "//input[@placeholder='用户名']"),
                (By.XPATH, "//input[@placeholder='请输入用户名']"),
                (By.XPATH, "//input[contains(@class, 'username')]"),
                (By.XPATH, "//input[@type='text']")
            ]

            username_input = None
            for selector in username_selectors:
                try:
                    username_input = self.driver.find_element(*selector)
                    if username_input.is_displayed():
                        break
                except:
                    continue

            if not username_input:
                logger.error("找不到用户名输入框")
                return False

            username_input.clear()
            username_input.send_keys(username)
            time.sleep(1)

            # 尝试多种方式查找密码输入框
            password_selectors = [
                (By.NAME, "password"),
                (By.ID, "password"),
                (By.XPATH, "//input[@placeholder='密码']"),
                (By.XPATH, "//input[@placeholder='请输入密码']"),
                (By.XPATH, "//input[contains(@class, 'password')]"),
                (By.XPATH, "//input[@type='password']")
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(*selector)
                    if password_input.is_displayed():
                        break
                except:
                    continue

            if not password_input:
                logger.error("找不到密码输入框")
                return False

            password_input.clear()
            password_input.send_keys(password)
            time.sleep(1)

            # 尝试多种方式查找登录按钮
            login_selectors = [
                (By.XPATH, "//button[contains(text(), '登录')]"),
                (By.XPATH, "//button[contains(text(), '登 录')]"),
                (By.XPATH, "//button[contains(text(), 'Login')]"),
                (By.XPATH, "//input[@type='submit']"),
                (By.XPATH, "//button[@type='submit']"),
                (By.XPATH, "//button[contains(@class, 'login')]")
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(*selector)
                    if login_button.is_displayed():
                        break
                except:
                    continue

            if not login_button:
                logger.error("找不到登录按钮")
                return False

            login_button.click()

            # 等待组织选择页面出现
            time.sleep(5)

            # 检查是否出现组织选择页面
            try:
                # 查找确定按钮（组织选择页面）
                confirm_selectors = [
                    (By.XPATH, "//button[contains(text(), '确定')]"),
                    (By.XPATH, "//button[contains(text(), '确 定')]"),
                    (By.XPATH, "//button[contains(text(), 'OK')]"),
                    (By.XPATH, "//button[@type='button' and contains(@class, 'primary')]"),
                    (By.XPATH, "//span[contains(text(), '确定')]/parent::button")
                ]

                confirm_button = None
                for selector in confirm_selectors:
                    try:
                        confirm_button = self.driver.find_element(*selector)
                        if confirm_button.is_displayed():
                            logger.info("找到确定按钮，准备选择组织")
                            break
                    except:
                        continue

                if confirm_button:
                    # 尝试多种方式点击确定按钮
                    try:
                        # 方法1: 直接点击
                        confirm_button.click()
                        logger.info("已点击确定按钮选择组织")
                    except Exception as e1:
                        try:
                            # 方法2: 使用JavaScript点击
                            self.driver.execute_script("arguments[0].click();", confirm_button)
                            logger.info("使用JavaScript点击确定按钮")
                        except Exception as e2:
                            try:
                                # 方法3: 查找模态框中的确定按钮
                                modal_confirm = self.driver.find_element(By.XPATH, "//div[contains(@class, 'ant-modal')]//button[contains(text(), '确定')]")
                                modal_confirm.click()
                                logger.info("点击模态框中的确定按钮")
                            except Exception as e3:
                                logger.error(f"所有点击方法都失败: {e1}, {e2}, {e3}")

                    time.sleep(5)
                else:
                    logger.info("未找到确定按钮，可能已经直接登录成功")

            except Exception as e:
                logger.info(f"组织选择处理异常: {e}")

            # 等待登录成功，检查是否跳转到主页
            logger.info("等待页面跳转...")

            # 多次检查登录状态
            for i in range(10):  # 最多等待20秒
                time.sleep(2)
                current_url = self.driver.current_url
                page_title = self.driver.title

                logger.info(f"检查第{i+1}次 - URL: {current_url}, Title: {page_title}")

                # 检查是否登录成功的多种条件
                if ("login" not in current_url.lower() and
                    ("工作台" in page_title or "首页" in page_title or "dashboard" in current_url.lower())):
                    logger.info("登录成功！")
                    # 截图首页
                    self.take_screenshot("./docs/images/front_system/履约执行/首页.png", "登录成功后的首页")
                    return True

                # 检查是否还在组织选择页面
                if "选择" in page_title or "organization" in current_url.lower():
                    logger.info("仍在组织选择页面，继续等待...")
                    continue

                # 检查是否有错误信息
                try:
                    error_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '错误') or contains(text(), '失败') or contains(text(), 'error')]")
                    if error_elements:
                        logger.error(f"发现错误信息: {[elem.text for elem in error_elements[:3]]}")
                except:
                    pass

            # 最终检查
            current_url = self.driver.current_url
            if "login" not in current_url.lower():
                logger.info("最终检查：登录可能成功")
                self.take_screenshot("./docs/images/front_system/履约执行/首页.png", "登录后的页面")
                return True
            else:
                logger.error("登录失败，仍在登录相关页面")
                # 截图当前页面用于调试
                self.take_screenshot("./docs/images/front_system/履约执行/登录失败页面.png", "登录失败时的页面状态")
                return False

        except Exception as e:
            logger.error(f"登录失败: {e}")
            return False

    def take_screenshot(self, filename, description=""):
        """截图"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            # 滚动到页面顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            # 等待页面稳定
            time.sleep(2)

            self.driver.save_screenshot(filename)
            self.screenshot_count += 1
            self.success_screenshots.append(filename)

            progress = (self.screenshot_count / self.total_screenshots) * 100
            logger.info(f"✅ 截图成功 ({self.screenshot_count}/{self.total_screenshots}, {progress:.1f}%): {os.path.basename(filename)}")
            if description:
                logger.info(f"   描述: {description}")

        except Exception as e:
            self.failed_screenshots.append({"filename": filename, "error": str(e)})
            logger.error(f"❌ 截图失败: {os.path.basename(filename)} - {e}")

    def print_progress_summary(self):
        """打印进度总结"""
        elapsed_time = datetime.now() - self.start_time
        logger.info(f"\n📊 截图进度总结:")
        logger.info(f"   总计: {self.total_screenshots} 个截图")
        logger.info(f"   成功: {len(self.success_screenshots)} 个")
        logger.info(f"   失败: {len(self.failed_screenshots)} 个")
        logger.info(f"   耗时: {elapsed_time.total_seconds():.1f} 秒")

        if self.failed_screenshots:
            logger.info(f"\n❌ 失败的截图:")
            for failed in self.failed_screenshots:
                logger.info(f"   - {failed['filename']}: {failed['error']}")

        if len(self.success_screenshots) == self.total_screenshots:
            logger.info(f"\n🎉 所有截图完成！")
        else:
            logger.info(f"\n⚠️  还有 {self.total_screenshots - len(self.success_screenshots)} 个截图未完成")

    def navigate_to_contract_execution_module(self):
        """导航到履约执行模块"""
        try:
            # 等待页面加载
            time.sleep(5)

            # 尝试多种方式查找履约执行菜单
            execution_selectors = [
                (By.XPATH, "//span[contains(text(), '履约执行')]"),
                (By.XPATH, "//a[contains(text(), '履约执行')]"),
                (By.XPATH, "//div[contains(text(), '履约执行')]"),
                (By.XPATH, "//*[contains(text(), '履约执行')]"),
                (By.XPATH, "//li[contains(@class, 'menu-item')]//span[contains(text(), '履约执行')]"),
                (By.XPATH, "//div[contains(@class, 'menu')]//span[contains(text(), '履约执行')]")
            ]

            execution_menu = None
            for selector in execution_selectors:
                try:
                    elements = self.driver.find_elements(*selector)
                    for element in elements:
                        if element.is_displayed():
                            execution_menu = element
                            logger.info(f"找到履约执行菜单: {element.text}")
                            break
                    if execution_menu:
                        break
                except:
                    continue

            if not execution_menu:
                logger.error("找不到履约执行菜单")
                # 尝试截图当前页面用于调试
                self.take_screenshot("./docs/images/front_system/履约执行/找不到履约执行菜单.png", "调试用截图")
                return False

            execution_menu.click()
            time.sleep(3)

            logger.info("成功导航到履约执行模块")
            return True

        except Exception as e:
            logger.error(f"导航到履约执行模块失败: {e}")
            return False

    def navigate_to_contract_management(self):
        """导航到合同管理"""
        try:
            # 等待页面加载
            time.sleep(3)

            # 尝试多种方式查找合同管理菜单
            contract_selectors = [
                (By.XPATH, "//span[contains(text(), '合同管理')]"),
                (By.XPATH, "//a[contains(text(), '合同管理')]"),
                (By.XPATH, "//div[contains(text(), '合同管理')]"),
                (By.XPATH, "//*[contains(text(), '合同管理')]")
            ]

            contract_menu = None
            for selector in contract_selectors:
                try:
                    elements = self.driver.find_elements(*selector)
                    for element in elements:
                        if element.is_displayed():
                            contract_menu = element
                            break
                    if contract_menu:
                        break
                except:
                    continue

            if not contract_menu:
                logger.error("找不到合同管理菜单")
                return False

            contract_menu.click()
            time.sleep(5)

            # 截图合同管理列表
            self.take_screenshot("./docs/images/front_system/履约执行/合同管理列表.png")

            logger.info("成功导航到合同管理")
            return True

        except Exception as e:
            logger.error(f"导航到合同管理失败: {e}")
            return False

    def crawl_contract_detail_pages(self):
        """爬取合同详情页面的所有标签页"""
        try:
            # 查找第一个合同并点击进入详情
            contract_links = self.driver.find_elements(By.XPATH, "//a[contains(@href, 'contractManageDetail')]")
            if not contract_links:
                # 尝试其他方式查找合同链接
                contract_links = self.driver.find_elements(By.XPATH, "//td//a")

            if not contract_links:
                logger.error("找不到合同链接")
                return False

            # 点击第一个合同
            contract_links[0].click()
            time.sleep(5)

            # 截图合同详情主页面
            self.take_screenshot("./docs/images/front_system/履约执行/合同详情页面.png")

            # 合同详情页面的标签页列表
            detail_tabs = [
                {"name": "合同内容信息", "xpath": "//span[contains(text(), '合同内容信息')]"},
                {"name": "支付节点信息", "xpath": "//span[contains(text(), '支付节点信息')]"},
                {"name": "节点确认记录", "xpath": "//span[contains(text(), '节点确认记录')]"},
                {"name": "合同附件信息", "xpath": "//span[contains(text(), '合同附件信息')]"},
                {"name": "合同审批流程", "xpath": "//span[contains(text(), '合同审批流程')]"},
            ]

            for tab in detail_tabs:
                try:
                    logger.info(f"正在访问标签页: {tab['name']}")

                    # 点击标签页
                    tab_element = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, tab['xpath']))
                    )
                    tab_element.click()
                    time.sleep(3)

                    # 截图
                    screenshot_path = f"./docs/images/front_system/履约执行/{tab['name']}.png"
                    self.take_screenshot(screenshot_path)

                    # 如果是支付节点信息页面，尝试点击一些操作按钮
                    if tab['name'] == "支付节点信息":
                        self.capture_payment_node_operations()

                    # 如果是节点确认记录页面，尝试查看详情
                    if tab['name'] == "节点确认记录":
                        self.capture_confirm_record_operations()

                    # 如果是合同附件信息页面，尝试上传操作
                    if tab['name'] == "合同附件信息":
                        self.capture_attachment_operations()

                    time.sleep(2)

                except Exception as e:
                    logger.error(f"访问标签页 {tab['name']} 失败: {e}")
                    continue

            return True

        except Exception as e:
            logger.error(f"爬取合同详情页面失败: {e}")
            return False

    def capture_payment_node_operations(self):
        """截图支付节点相关操作"""
        try:
            # 查找支付节点确认按钮
            confirm_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '支付节点确认')]")
            if confirm_buttons:
                confirm_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/支付节点确认弹窗.png")

                # 关闭弹窗
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '取消')] | //button[contains(@class, 'close')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

            # 查找支付状态确认按钮
            status_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '支付状态确认')]")
            if status_buttons:
                status_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/支付状态确认弹窗.png")

                # 关闭弹窗
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '取消')] | //button[contains(@class, 'close')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

        except Exception as e:
            logger.error(f"截图支付节点操作失败: {e}")

    def capture_confirm_record_operations(self):
        """截图确认记录相关操作"""
        try:
            # 查找查看详情按钮
            detail_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '查看详情')] | //a[contains(text(), '详情')]")
            if detail_buttons:
                detail_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/确认记录详情.png")

                # 关闭详情
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '关闭')] | //button[contains(text(), '取消')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

            # 查找审核按钮
            audit_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '审核')] | //button[contains(text(), '审批')]")
            if audit_buttons:
                audit_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/确认记录审核.png")

                # 关闭审核界面
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '取消')] | //button[contains(@class, 'close')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

        except Exception as e:
            logger.error(f"截图确认记录操作失败: {e}")

    def capture_attachment_operations(self):
        """截图附件相关操作"""
        try:
            # 查找上传附件按钮
            upload_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '上传附件')] | //button[contains(text(), '上传')]")
            if upload_buttons:
                upload_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/附件上传界面.png")

                # 关闭上传界面
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '取消')] | //button[contains(@class, 'close')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

            # 查找预览按钮
            preview_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '预览')] | //a[contains(text(), '预览')]")
            if preview_buttons:
                preview_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/附件预览界面.png")

                # 关闭预览
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '关闭')] | //button[contains(@class, 'close')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

        except Exception as e:
            logger.error(f"截图附件操作失败: {e}")

    def capture_contract_list_operations(self):
        """截图合同列表相关操作"""
        try:
            # 查找编辑按钮
            edit_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '编辑')]")
            if edit_buttons:
                edit_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/合同编辑抽屉.png")

                # 关闭编辑抽屉
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '取消')] | //button[contains(@class, 'close')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

            # 查找创建合同按钮
            create_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '创建合同')] | //button[contains(text(), '新增')]")
            if create_buttons:
                create_buttons[0].click()
                time.sleep(2)
                self.take_screenshot("./docs/images/front_system/履约执行/合同创建抽屉.png")

                # 关闭创建抽屉
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '取消')] | //button[contains(@class, 'close')]")
                if close_buttons:
                    close_buttons[0].click()
                    time.sleep(1)

        except Exception as e:
            logger.error(f"截图合同列表操作失败: {e}")

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    logger.info("🚀 开始履约执行模块自动截图")
    logger.info(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🎯 目标: 完成 18 个页面截图")
    logger.info(f"🌐 测试环境: http://183.136.206.207:44099/login")
    logger.info(f"👤 测试账号: heqiang")

    crawler = ContractExecutionCrawler()

    try:
        # 登录
        logger.info("\n🔐 步骤1: 登录系统")
        if crawler.login("heqiang", "Abc123456789!"):
            # 截图首页
            logger.info("\n📸 步骤2: 截图首页")
            crawler.take_screenshot("./docs/images/front_system/履约执行/首页.png", "登录成功后的首页")

            # 导航到履约执行模块
            logger.info("\n🏠 步骤3: 导航到履约执行模块")
            if crawler.navigate_to_contract_execution_module():
                # 导航到合同管理
                logger.info("\n📋 步骤4: 进入合同管理")
                if crawler.navigate_to_contract_management():
                    # 截图合同列表操作
                    logger.info("\n📸 步骤5: 截图合同列表操作")
                    crawler.capture_contract_list_operations()

                    # 爬取合同详情页面
                    logger.info("\n📸 步骤6: 截图合同详情页面")
                    crawler.crawl_contract_detail_pages()

                    logger.info("\n✅ 履约执行模块截图流程完成")
                else:
                    logger.error("❌ 无法进入合同管理")
            else:
                logger.error("❌ 无法导航到履约执行模块")
        else:
            logger.error("❌ 登录失败")

    except Exception as e:
        logger.error(f"❌ 执行过程中发生错误: {e}")
    finally:
        # 打印最终报告
        crawler.print_progress_summary()
        crawler.close()
        logger.info(f"\n🏁 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def check_environment():
    """检查运行环境"""
    logger.info("🔍 检查运行环境...")

    # 检查ChromeDriver
    if not os.path.exists('./chromedriver'):
        logger.error("❌ 找不到 chromedriver 文件")
        logger.info("💡 请下载 ChromeDriver 并放在当前目录下")
        return False

    # 检查selenium
    try:
        import selenium
        logger.info(f"✅ Selenium 版本: {selenium.__version__}")
    except ImportError:
        logger.error("❌ 未安装 selenium")
        logger.info("💡 请运行: pip3 install selenium")
        return False

    # 检查输出目录
    output_dir = "./docs/images/front_system/履约执行/"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"📁 创建输出目录: {output_dir}")
    else:
        logger.info(f"📁 输出目录已存在: {output_dir}")

    logger.info("✅ 环境检查完成")
    return True

if __name__ == "__main__":
    if check_environment():
        main()
    else:
        logger.error("❌ 环境检查失败，无法继续执行")
        sys.exit(1)
