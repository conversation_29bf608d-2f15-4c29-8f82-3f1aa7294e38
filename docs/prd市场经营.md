# 市场经营模块产品设计文档

## 1. 模块概述

市场经营模块是系统的核心业务模块之一，主要包含以下功能：
- 线索管理
- 需求管理
- 报价管理
- 合同管理
- 客户管理
- 报表中心

## 2. 功能页面详细说明

### 2.1 市场经营一览

![市场经营一览](./images/front_system/市场经营/市场经营_市场经营一览.png)

#### 页面布局
- 顶部：系统导航栏
- 左侧：功能菜单树
- 主区域：市场经营数据概览

#### 组件说明
1. 数据统计卡片
   - 线索数量
   - 需求数量
   - 报价数量
   - 合同数量
   - 预测收入

2. 图表展示
   - 销售漏斗图
   - 月度趋势图
   - 区域分布图

#### 事件操作
- 点击数据卡片：跳转到对应功能模块
- 图表交互：支持时间范围筛选、数据钻取

#### API接口
- GET /api/market/overview/statistics
- GET /api/market/overview/funnel
- GET /api/market/overview/trend
- GET /api/market/overview/region

### 2.2 线索管理

#### 2.2.1 线索池

![线索池](./images/front_system/市场经营/市场经营_线索持.png)

#### 页面布局
- 顶部：搜索栏、操作按钮
- 中部：线索列表
- 底部：分页器

#### 组件说明
1. 搜索条件
   - 线索名称
   - 客户名称
   - 状态
   - 时间范围

2. 操作按钮
   - 新建线索
   - 批量分配
   - 导出

3. 列表字段
   - 线索名称
   - 客户名称
   - 联系人
   - 联系电话
   - 状态
   - 创建时间
   - 操作列

#### 事件操作
- 点击新建：打开新建线索表单
- 点击分配：打开分配弹窗
- 点击导出：下载Excel文件
- 点击编辑：打开编辑表单
- 点击删除：二次确认后删除

#### API接口
- GET /api/leads/list
- POST /api/leads/create
- PUT /api/leads/update
- DELETE /api/leads/delete
- POST /api/leads/assign
- GET /api/leads/export

### 2.3 需求管理

#### 2.3.1 需求列表

![需求管理](./images/front_system/市场经营/市场经营_需求管理.png)

#### 页面布局
- 顶部：搜索栏、操作按钮
- 中部：需求列表
- 底部：分页器

#### 组件说明
1. 搜索条件
   - 需求编号
   - 需求名称
   - 客户名称
   - 状态
   - 时间范围

2. 操作按钮
   - 新建需求
   - 批量操作
   - 导出

3. 列表字段
   - 需求编号
   - 需求名称
   - 客户名称
   - 负责人
   - 状态
   - 创建时间
   - 操作列

#### 事件操作
- 点击新建：打开新建需求表单
- 点击编辑：打开编辑表单
- 点击删除：二次确认后删除
- 点击导出：下载Excel文件

#### API接口
- GET /api/requirements/list
- POST /api/requirements/create
- PUT /api/requirements/update
- DELETE /api/requirements/delete
- GET /api/requirements/export

### 2.4 报价管理

#### 2.4.1 报价列表

![报价管理](./images/front_system/市场经营/市场经营_报价管理.png)

#### 页面布局
- 顶部：搜索栏、操作按钮
- 中部：报价列表
- 底部：分页器

#### 组件说明
1. 搜索条件
   - 报价编号
   - 报价名称
   - 客户名称
   - 状态
   - 时间范围

2. 操作按钮
   - 新建报价
   - 批量操作
   - 导出

3. 列表字段
   - 报价编号
   - 报价名称
   - 客户名称
   - 负责人
   - 状态
   - 创建时间
   - 操作列

#### 事件操作
- 点击新建：打开新建报价表单
- 点击编辑：打开编辑表单
- 点击删除：二次确认后删除
- 点击导出：下载Excel文件

#### API接口
- GET /api/quotes/list
- POST /api/quotes/create
- PUT /api/quotes/update
- DELETE /api/quotes/delete
- GET /api/quotes/export

### 2.5 合同管理

#### 2.5.1 合同列表

![合同管理](./images/front_system/市场经营/市场经营_合同管理.png)

#### 页面布局
- 顶部：搜索栏、操作按钮
- 中部：合同列表
- 底部：分页器

#### 组件说明
1. 搜索条件
   - 合同编号
   - 合同名称
   - 客户名称
   - 状态
   - 时间范围

2. 操作按钮
   - 新建合同
   - 批量操作
   - 导出

3. 列表字段
   - 合同编号
   - 合同名称
   - 客户名称
   - 负责人
   - 状态
   - 创建时间
   - 操作列

#### 事件操作
- 点击新建：打开新建合同表单
- 点击编辑：打开编辑表单
- 点击删除：二次确认后删除
- 点击导出：下载Excel文件

#### API接口
- GET /api/contracts/list
- POST /api/contracts/create
- PUT /api/contracts/update
- DELETE /api/contracts/delete
- GET /api/contracts/export

## 3. 技术实现

### 3.1 前后端对接时序图

#### 3.1.1 线索管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面
    participant C as Controller
    participant S as Service
    participant R as Redis
    participant D as Database

    F->>C: GET /api/leads/list
    C->>S: LeadService.getList()
    S->>R: 查询缓存
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询数据库
        D-->>S: 返回数据
        S->>R: 更新缓存
    end
    S-->>C: 返回数据
    C-->>F: 返回结果
```

### 3.2 数据库设计

#### 3.2.1 线索表(leads)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| lead_name | varchar(100) | 线索名称 |
| customer_name | varchar(100) | 客户名称 |
| contact_name | varchar(50) | 联系人 |
| contact_phone | varchar(20) | 联系电话 |
| status | tinyint | 状态 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

#### 3.2.2 需求表(requirements)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| req_no | varchar(50) | 需求编号 |
| req_name | varchar(100) | 需求名称 |
| customer_name | varchar(100) | 客户名称 |
| owner | varchar(50) | 负责人 |
| status | tinyint | 状态 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

#### 3.2.3 报价表(quotes)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| quote_no | varchar(50) | 报价编号 |
| quote_name | varchar(100) | 报价名称 |
| customer_name | varchar(100) | 客户名称 |
| owner | varchar(50) | 负责人 |
| status | tinyint | 状态 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

#### 3.2.4 合同表(contracts)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| contract_no | varchar(50) | 合同编号 |
| contract_name | varchar(100) | 合同名称 |
| customer_name | varchar(100) | 客户名称 |
| owner | varchar(50) | 负责人 |
| status | tinyint | 状态 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

### 3.3 物理模型图

```mermaid
erDiagram
    LEADS ||--o{ REQUIREMENTS : "关联"
    REQUIREMENTS ||--o{ QUOTES : "关联"
    QUOTES ||--o{ CONTRACTS : "关联"
    
    LEADS {
        bigint id PK
        varchar lead_name
        varchar customer_name
        varchar contact_name
        varchar contact_phone
        tinyint status
        datetime create_time
        datetime update_time
    }
    
    REQUIREMENTS {
        bigint id PK
        varchar req_no
        varchar req_name
        varchar customer_name
        varchar owner
        tinyint status
        datetime create_time
        datetime update_time
    }
    
    QUOTES {
        bigint id PK
        varchar quote_no
        varchar quote_name
        varchar customer_name
        varchar owner
        tinyint status
        datetime create_time
        datetime update_time
    }
    
    CONTRACTS {
        bigint id PK
        varchar contract_no
        varchar contract_name
        varchar customer_name
        varchar owner
        tinyint status
        datetime create_time
        datetime update_time
    }
```

## 4. 前端实现

### 4.1 页面组件结构

#### 4.1.1 市场经营一览
- 文件路径：`src/views/market/Overview.vue`
- 主要组件：
  - MarketOverview.vue
  - StatisticsCard.vue
  - SalesFunnel.vue
  - TrendChart.vue
  - RegionMap.vue

#### 4.1.2 线索管理
- 文件路径：`src/views/market/leads/`
- 主要组件：
  - LeadList.vue
  - LeadForm.vue
  - LeadAssign.vue
  - LeadSearch.vue

#### 4.1.3 需求管理
- 文件路径：`src/views/market/requirements/`
- 主要组件：
  - RequirementList.vue
  - RequirementForm.vue
  - RequirementSearch.vue

#### 4.1.4 报价管理
- 文件路径：`src/views/market/quotes/`
- 主要组件：
  - QuoteList.vue
  - QuoteForm.vue
  - QuoteSearch.vue

#### 4.1.5 合同管理
- 文件路径：`src/views/market/contracts/`
- 主要组件：
  - ContractList.vue
  - ContractForm.vue
  - ContractSearch.vue

### 4.2 前端状态管理

```typescript
// store/modules/market.ts
interface MarketState {
  leads: Lead[];
  requirements: Requirement[];
  quotes: Quote[];
  contracts: Contract[];
  statistics: MarketStatistics;
}

const state: MarketState = {
  leads: [],
  requirements: [],
  quotes: [],
  contracts: [],
  statistics: {
    leadCount: 0,
    requirementCount: 0,
    quoteCount: 0,
    contractCount: 0,
    predictedIncome: 0
  }
};
```

## 5. 后端实现

### 5.1 项目结构

```
cos/
├── cos-api/                 # API接口定义
├── cos-app/                 # 应用服务实现
├── cos-common/             # 公共模块
└── cos-project-management/  # 项目管理模块
```

### 5.2 核心接口实现

#### 5.2.1 线索管理接口

```java
@RestController
@RequestMapping("/api/leads")
public class LeadController {
    @Autowired
    private LeadService leadService;
    
    @GetMapping("/list")
    public Result<PageResult<LeadVO>> list(LeadQueryDTO query) {
        return Result.success(leadService.getList(query));
    }
    
    @PostMapping("/create")
    public Result<Long> create(@RequestBody LeadDTO lead) {
        return Result.success(leadService.create(lead));
    }
    
    @PutMapping("/update")
    public Result<Void> update(@RequestBody LeadDTO lead) {
        leadService.update(lead);
        return Result.success();
    }
    
    @DeleteMapping("/delete/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        leadService.delete(id);
        return Result.success();
    }
}
```

### 5.3 数据库表关系

#### 5.3.1 线索表(leads)
```sql
CREATE TABLE leads (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    lead_name VARCHAR(100) NOT NULL COMMENT '线索名称',
    customer_name VARCHAR(100) NOT NULL COMMENT '客户名称',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-未处理，1-已分配，2-已转化，3-已失效',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer_name (customer_name),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线索表';
```

#### 5.3.2 需求表(requirements)
```sql
CREATE TABLE requirements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    req_no VARCHAR(50) NOT NULL COMMENT '需求编号',
    req_name VARCHAR(100) NOT NULL COMMENT '需求名称',
    customer_name VARCHAR(100) NOT NULL COMMENT '客户名称',
    owner VARCHAR(50) NOT NULL COMMENT '负责人',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-草稿，1-待审核，2-已审核，3-已拒绝',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_req_no (req_no),
    INDEX idx_customer_name (customer_name),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='需求表';
```

### 5.4 缓存设计

#### 5.4.1 Redis缓存键设计
```java
public class CacheKeys {
    // 线索相关缓存
    public static final String LEAD_LIST = "market:lead:list:%s";  // %s为查询条件hash
    public static final String LEAD_DETAIL = "market:lead:detail:%d";  // %d为线索ID
    
    // 需求相关缓存
    public static final String REQ_LIST = "market:req:list:%s";
    public static final String REQ_DETAIL = "market:req:detail:%d";
    
    // 报价相关缓存
    public static final String QUOTE_LIST = "market:quote:list:%s";
    public static final String QUOTE_DETAIL = "market:quote:detail:%d";
    
    // 合同相关缓存
    public static final String CONTRACT_LIST = "market:contract:list:%s";
    public static final String CONTRACT_DETAIL = "market:contract:detail:%d";
}
```

### 5.5 字段映射关系

#### 5.5.1 线索管理字段映射

| 前端字段 | 后端字段 | 数据库字段 | 转换逻辑 |
|---------|---------|-----------|---------|
| leadName | leadName | lead_name | 直接映射 |
| customerName | customerName | customer_name | 直接映射 |
| contactName | contactName | contact_name | 直接映射 |
| contactPhone | contactPhone | contact_phone | 直接映射 |
| status | status | status | 状态码转换：0-未处理，1-已分配，2-已转化，3-已失效 |
| createTime | createTime | create_time | 时间格式化：yyyy-MM-dd HH:mm:ss |

#### 5.5.2 需求管理字段映射

| 前端字段 | 后端字段 | 数据库字段 | 转换逻辑 |
|---------|---------|-----------|---------|
| reqNo | reqNo | req_no | 自动生成：REQ + 年月日 + 4位序号 |
| reqName | reqName | req_name | 直接映射 |
| customerName | customerName | customer_name | 直接映射 |
| owner | owner | owner | 直接映射 |
| status | status | status | 状态码转换：0-草稿，1-待审核，2-已审核，3-已拒绝 |
| createTime | createTime | create_time | 时间格式化：yyyy-MM-dd HH:mm:ss |

## 6. 部署架构

### 6.1 系统架构图

```mermaid
graph TD
    A[前端应用] --> B[Nginx]
    B --> C[后端服务]
    C --> D[Redis缓存]
    C --> E[MySQL数据库]
    C --> F[文件存储]
```

### 6.2 部署配置

#### 6.2.1 Nginx配置
```nginx
server {
    listen 80;
    server_name market.example.com;
    
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 6.2.2 应用配置
```yaml
spring:
  datasource:
    url: *************************************************************************
    username: root
    password: ${MYSQL_PASSWORD}
  
  redis:
    host: localhost
    port: 6379
    password: ${REDIS_PASSWORD}
    
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
```
