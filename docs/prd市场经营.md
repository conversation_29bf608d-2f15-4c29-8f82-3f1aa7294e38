# 市场经营模块产品设计文档

## 1. 模块概述

市场经营模块是系统的核心业务模块之一，主要包含以下功能：
- 线索管理（线索池、线索录入）
- 需求管理
- 报价管理
- 合同管理
- 客户管理
- 里程碑管理
- 我的草稿
- 帮助中心
- 报表中心（需求报表、报价报表、合同报表、里程碑报表）

## 2. 功能页面详细说明

### 2.1 市场经营一览

![市场经营一览](./images/front_system/市场经营/市场经营_市场经营一览.png)

#### 页面布局
- 顶部：系统导航栏，包含用户信息、消息通知等
- 左侧：功能菜单树，展示市场经营各子模块
- 主区域：市场经营数据概览仪表板

#### 组件说明
1. 数据统计卡片区域
   - 线索数量统计卡片
   - 需求数量统计卡片
   - 报价数量统计卡片
   - 合同数量统计卡片
   - 预测收入统计卡片

2. 图表展示区域
   - 销售漏斗图：展示从线索到合同的转化情况
   - 月度趋势图：展示各业务指标的月度变化趋势
   - 区域分布图：展示业务在不同地区的分布情况

#### 事件操作
- 点击数据卡片：跳转到对应功能模块的列表页面
- 图表交互：支持时间范围筛选、数据钻取、图表缩放
- 刷新按钮：重新加载最新数据

#### API接口
- GET /api/market/overview/statistics - 获取统计数据
- GET /api/market/overview/funnel - 获取销售漏斗数据
- GET /api/market/overview/trend - 获取趋势图数据
- GET /api/market/overview/region - 获取区域分布数据

### 2.2 线索管理

#### 2.2.1 线索池

![线索池](./images/front_system/市场经营/市场经营_线索持.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：线索列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 线索名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、未处理、已分配、已转化、已失效）
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建线索按钮（主要按钮）
   - 批量分配按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 线索列表表格
   - 复选框列（支持全选/反选）
   - 线索名称（可点击查看详情）
   - 客户名称
   - 联系人姓名
   - 联系电话
   - 线索状态（带状态标签）
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、分配）

#### 线索池子页面

![全部线索](./images/front_system/市场经营/市场经营_线索池_全部线索.png)

![分配给我](./images/front_system/市场经营/市场经营_线索池_分配给我.png)

![预测收入](./images/front_system/市场经营/市场经营_线索池_预测收入.png)

#### 事件操作
- 点击新建线索：打开新建线索表单弹窗
- 点击批量分配：打开批量分配弹窗，可选择分配人员
- 点击导出：根据当前筛选条件导出Excel文件
- 点击编辑：打开编辑线索表单弹窗
- 点击删除：显示确认对话框，确认后删除
- 点击分配：打开单个线索分配弹窗
- 点击线索名称：跳转到线索详情页面

#### API接口
- GET /api/leads/list - 获取线索列表（支持分页和筛选）
- POST /api/leads/create - 创建新线索
- PUT /api/leads/update - 更新线索信息
- DELETE /api/leads/delete - 删除线索
- POST /api/leads/assign - 分配线索
- GET /api/leads/export - 导出线索数据

#### 2.2.2 线索录入

![线索录入](./images/front_system/市场经营/市场经营_线索录入.png)

#### 页面布局
- 顶部：页面标题和操作按钮
- 主体：线索录入表单
- 底部：保存、取消按钮

#### 组件说明
1. 基本信息区域
   - 线索名称（必填）
   - 客户名称（必填，支持下拉选择或新建）
   - 联系人姓名（必填）
   - 联系电话（必填）
   - 联系邮箱
   - 线索来源（下拉选择）
   - 线索优先级（高、中、低）

2. 详细信息区域
   - 线索描述（富文本编辑器）
   - 预计金额
   - 预计成交时间
   - 所属行业
   - 客户规模

3. 附件上传区域
   - 支持多文件上传
   - 文件类型限制
   - 文件大小限制

#### 新建线索弹窗

![新建线索](./images/front_system/市场经营/市场经营_线索录入_新建线索.png)

#### 事件操作
- 点击保存：验证表单数据，提交创建线索
- 点击取消：关闭表单，返回线索池
- 文件上传：支持拖拽上传和点击上传
- 客户选择：支持搜索现有客户或新建客户

#### API接口
- POST /api/leads/create - 创建线索
- GET /api/customers/list - 获取客户列表
- POST /api/customers/create - 创建新客户
- POST /api/files/upload - 上传附件

### 2.3 需求管理

#### 2.3.1 需求列表

![需求管理](./images/front_system/市场经营/市场经营_需求管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：需求列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 需求编号输入框
   - 需求名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、草稿、待审核、已审核、已拒绝、已报名、已分发）
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建需求按钮（主要按钮）
   - 批量分发按钮（需选中数据）
   - 批量报名按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 需求列表表格
   - 复选框列（支持全选/反选）
   - 需求编号（自动生成，可点击查看详情）
   - 需求名称
   - 客户名称
   - 负责人
   - 需求状态（带状态标签）
   - 预计金额
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、分发、报名、查看详情）

#### 需求管理子页面

![基本信息](./images/front_system/市场经营/市场经营_需求管理_基本信息.png)

![基本信息新建](./images/front_system/市场经营/市场经营_需求管理_基本信息_新建.png)

![创建需求](./images/front_system/市场经营/市场经营_需求管理_创建需求.png)

![双方签约主体信息](./images/front_system/市场经营/市场经营_需求管理_双方签约主体信息.png)

![报价基本信息](./images/front_system/市场经营/市场经营_需求管理_报价基本信息.png)

![附件](./images/front_system/市场经营/市场经营_需求管理_附件.png)

#### 事件操作
- 点击新建需求：打开新建需求表单页面
- 点击编辑：打开编辑需求表单页面
- 点击删除：显示确认对话框，确认后删除
- 点击分发：打开分发弹窗，选择分发对象
- 点击报名：确认报名参与该需求
- 点击导出：根据当前筛选条件导出Excel文件
- 点击需求编号：跳转到需求详情页面

#### API接口
- GET /api/requirements/list - 获取需求列表（支持分页和筛选）
- POST /api/requirements/create - 创建新需求
- PUT /api/requirements/update - 更新需求信息
- DELETE /api/requirements/delete - 删除需求
- POST /api/requirements/distribute - 分发需求
- POST /api/requirements/apply - 报名需求
- GET /api/requirements/export - 导出需求数据

### 2.4 报价管理

#### 2.4.1 报价列表

![报价管理](./images/front_system/市场经营/市场经营_报价管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：报价列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 报价编号输入框
   - 报价名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、草稿、待审核、已审核、已发出、已中标、已失效）
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建报价按钮（主要按钮）
   - 批量发出按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 报价列表表格
   - 复选框列（支持全选/反选）
   - 报价编号（自动生成，可点击查看详情）
   - 报价名称
   - 客户名称
   - 负责人
   - 报价状态（带状态标签）
   - 报价金额
   - 有效期
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、发出、确认、查看详情）

#### 报价管理子页面

![基本信息](./images/front_system/市场经营/市场经营_报价管理_基本信息.png)

![基本信息创建](./images/front_system/市场经营/市场经营_报价管理_基本信息_创建.png)

![创建报价](./images/front_system/市场经营/市场经营_报价管理_创建.png)

![创建报价详情](./images/front_system/市场经营/市场经营_报价管理_创建报价.png)

![双方签约主体信息](./images/front_system/市场经营/市场经营_报价管理_创建双方签约主体信息.png)

#### 事件操作
- 点击新建报价：打开新建报价表单页面
- 点击编辑：打开编辑报价表单页面
- 点击删除：显示确认对话框，确认后删除
- 点击发出：确认发出报价给客户
- 点击确认：确认报价单内容
- 点击导出：根据当前筛选条件导出Excel文件
- 点击报价编号：跳转到报价详情页面

#### API接口
- GET /api/quotes/list - 获取报价列表（支持分页和筛选）
- POST /api/quotes/create - 创建新报价
- PUT /api/quotes/update - 更新报价信息
- DELETE /api/quotes/delete - 删除报价
- POST /api/quotes/send - 发出报价
- POST /api/quotes/confirm - 确认报价
- GET /api/quotes/export - 导出报价数据

### 2.5 合同管理

#### 2.5.1 合同列表

![合同管理](./images/front_system/市场经营/市场经营_合同管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：合同列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 合同编号输入框
   - 合同名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、草稿、待审核、已审核、已签署、执行中、已完成、已终止）
   - 时间范围选择器（创建时间、签署时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建合同按钮（主要按钮）
   - 批量审核按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 合同列表表格
   - 复选框列（支持全选/反选）
   - 合同编号（自动生成，可点击查看详情）
   - 合同名称
   - 客户名称
   - 负责人
   - 合同状态（带状态标签）
   - 合同金额
   - 签署时间
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、审核、查看详情）

#### 合同管理子页面

![合同创建](./images/front_system/市场经营/市场经营_合同管理_创建.png)

#### 事件操作
- 点击新建合同：打开新建合同表单页面
- 点击编辑：打开编辑合同表单页面
- 点击删除：显示确认对话框，确认后删除
- 点击审核：打开审核弹窗，填写审核意见
- 点击导出：根据当前筛选条件导出Excel文件
- 点击合同编号：跳转到合同详情页面

#### API接口
- GET /api/contracts/list - 获取合同列表（支持分页和筛选）
- POST /api/contracts/create - 创建新合同
- PUT /api/contracts/update - 更新合同信息
- DELETE /api/contracts/delete - 删除合同
- POST /api/contracts/approve - 审核合同
- GET /api/contracts/export - 导出合同数据

### 2.6 客户管理

![客户管理](./images/front_system/市场经营/市场经营_客户管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：客户列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 客户名称输入框
   - 客户类型下拉选择器
   - 客户等级下拉选择器
   - 所属行业下拉选择器
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建客户按钮（主要按钮）
   - 导出按钮
   - 刷新按钮

3. 客户列表表格
   - 复选框列（支持全选/反选）
   - 客户名称（可点击查看详情）
   - 客户类型
   - 客户等级
   - 所属行业
   - 联系人
   - 联系电话
   - 创建时间
   - 操作列（编辑、删除、查看详情）

#### 事件操作
- 点击新建客户：打开新建客户表单弹窗
- 点击编辑：打开编辑客户表单弹窗
- 点击删除：显示确认对话框，确认后删除
- 点击导出：根据当前筛选条件导出Excel文件
- 点击客户名称：跳转到客户详情页面

#### API接口
- GET /api/customers/list - 获取客户列表（支持分页和筛选）
- POST /api/customers/create - 创建新客户
- PUT /api/customers/update - 更新客户信息
- DELETE /api/customers/delete - 删除客户
- GET /api/customers/export - 导出客户数据

### 2.7 里程碑管理

![里程碑管理](./images/front_system/市场经营/市场经营_里程碑管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：里程碑列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 里程碑名称输入框
   - 合同名称输入框
   - 状态下拉选择器（全部、未开始、进行中、已完成、已逾期）
   - 时间范围选择器（计划时间、实际时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建里程碑按钮（主要按钮）
   - 批量更新按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 里程碑列表表格
   - 复选框列（支持全选/反选）
   - 里程碑名称（可点击查看详情）
   - 合同名称
   - 计划开始时间
   - 计划完成时间
   - 实际完成时间
   - 完成状态（带状态标签）
   - 负责人
   - 操作列（编辑、删除、更新状态、查看详情）

#### 事件操作
- 点击新建里程碑：打开新建里程碑表单弹窗
- 点击编辑：打开编辑里程碑表单弹窗
- 点击删除：显示确认对话框，确认后删除
- 点击更新状态：打开状态更新弹窗
- 点击导出：根据当前筛选条件导出Excel文件
- 点击里程碑名称：跳转到里程碑详情页面

#### API接口
- GET /api/milestones/list - 获取里程碑列表（支持分页和筛选）
- POST /api/milestones/create - 创建新里程碑
- PUT /api/milestones/update - 更新里程碑信息
- DELETE /api/milestones/delete - 删除里程碑
- PUT /api/milestones/status - 更新里程碑状态
- GET /api/milestones/export - 导出里程碑数据

### 2.8 我的草稿

![我的草稿](./images/front_system/市场经营/市场经营_我的草稿.png)

#### 页面布局
- 顶部：搜索栏、筛选条件区域
- 中部：草稿列表表格
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 草稿类型下拉选择器（全部、线索、需求、报价、合同）
   - 草稿名称输入框
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 草稿列表表格
   - 草稿类型（带类型标签）
   - 草稿名称（可点击继续编辑）
   - 创建时间
   - 最后修改时间
   - 操作列（继续编辑、删除）

#### 事件操作
- 点击草稿名称：跳转到对应的编辑页面继续编辑
- 点击继续编辑：跳转到对应的编辑页面
- 点击删除：显示确认对话框，确认后删除草稿

#### API接口
- GET /api/drafts/list - 获取草稿列表（支持分页和筛选）
- DELETE /api/drafts/delete - 删除草稿

### 2.9 帮助中心

![帮助中心](./images/front_system/市场经营/市场经营_帮助中心.png)

#### 页面布局
- 左侧：帮助分类导航树
- 右侧：帮助内容展示区域

#### 组件说明
1. 分类导航树
   - 常见问题
   - 操作指南
   - 功能介绍
   - 联系我们

2. 内容展示区域
   - 帮助文章标题
   - 帮助文章内容（支持富文本）
   - 相关链接
   - 反馈按钮

#### 事件操作
- 点击分类：展开/收起子分类
- 点击帮助文章：显示文章详细内容
- 点击反馈：打开反馈表单

#### API接口
- GET /api/help/categories - 获取帮助分类
- GET /api/help/articles - 获取帮助文章
- POST /api/help/feedback - 提交反馈

### 2.10 报表中心

#### 2.10.1 需求报表

![需求报表](./images/front_system/市场经营/市场经营_需求报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 状态多选框
   - 部门选择器
   - 客户类型选择器

2. 数据展示区域
   - 需求统计表格
   - 需求趋势图表
   - 需求状态分布饼图

#### API接口
- GET /api/reports/requirements - 获取需求报表数据
- GET /api/reports/requirements/export - 导出需求报表

#### 2.10.2 报价报表

![报价报表](./images/front_system/市场经营/市场经营_报价报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 状态多选框
   - 金额范围选择器
   - 客户类型选择器

2. 数据展示区域
   - 报价统计表格
   - 报价金额趋势图
   - 报价成功率分析图

#### API接口
- GET /api/reports/quotes - 获取报价报表数据
- GET /api/reports/quotes/export - 导出报价报表

#### 2.10.3 合同报表

![合同报表](./images/front_system/市场经营/市场经营_合同报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 合同状态多选框
   - 金额范围选择器
   - 客户类型选择器

2. 数据展示区域
   - 合同统计表格
   - 合同金额趋势图
   - 合同执行状态分析图

#### API接口
- GET /api/reports/contracts - 获取合同报表数据
- GET /api/reports/contracts/export - 导出合同报表

#### 2.10.4 里程碑报表

![里程碑报表](./images/front_system/市场经营/市场经营_里程碑报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 里程碑状态多选框
   - 合同选择器
   - 负责人选择器

2. 数据展示区域
   - 里程碑统计表格
   - 里程碑完成率趋势图
   - 里程碑逾期分析图

#### API接口
- GET /api/reports/milestones - 获取里程碑报表数据
- GET /api/reports/milestones/export - 导出里程碑报表

## 3. 技术实现

### 3.1 前后端对接时序图

#### 3.1.1 线索管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(LeadList.vue)
    participant C as LeadController
    participant S as LeadService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: GET /api/leads/list
    Note over F,C: 前端文件: src/views/market/leads/LeadList.vue<br/>调用函数: getLeadList()
    C->>S: LeadService.getList()
    Note over C,S: Controller: LeadController.list()<br/>Service: LeadService.getList()
    S->>R: 查询缓存 market:lead:list:hash
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询数据库表 pms_lead_management
        Note over S,D: SQL: SELECT * FROM pms_lead_management WHERE ...
        D-->>S: 返回数据
        S->>R: 更新缓存 market:lead:list:hash
    end
    S-->>C: 返回数据
    C-->>F: 返回结果
```

#### 3.1.2 需求管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(RequirementList.vue)
    participant C as RequirementController
    participant S as RequirementService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: POST /api/requirements/create
    Note over F,C: 前端文件: src/views/market/requirements/RequirementForm.vue<br/>调用函数: createRequirement()
    C->>S: RequirementService.create()
    Note over C,S: Controller: RequirementMangementController.create()<br/>Service: RequirementMangementService.save()
    S->>D: 插入数据到 pms_requirement_mangement
    Note over S,D: SQL: INSERT INTO pms_requirement_mangement (...)
    D-->>S: 返回插入结果
    S->>R: 清除相关缓存 market:req:list:*
    S-->>C: 返回创建结果
    C-->>F: 返回结果
```

#### 3.1.3 报价管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(QuoteList.vue)
    participant C as QuotationController
    participant S as QuotationService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: POST /api/quotes/send
    Note over F,C: 前端文件: src/views/market/quotes/QuoteForm.vue<br/>调用函数: sendQuote()
    C->>S: QuotationService.sendOut()
    Note over C,S: Controller: QuotationManagementController.sendOut()<br/>Service: QuotationManagementService.sendOut()
    S->>D: 更新报价状态到 pms_quotation_management
    Note over S,D: SQL: UPDATE pms_quotation_management SET status = 'SENT'
    D-->>S: 返回更新结果
    S->>R: 更新缓存 market:quote:detail:id
    S-->>C: 返回发送结果
    C-->>F: 返回结果
```

#### 3.1.4 合同管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(ContractList.vue)
    participant C as MarketContractController
    participant S as MarketContractService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: GET /api/contracts/list
    Note over F,C: 前端文件: src/views/market/contracts/ContractList.vue<br/>调用函数: getContractList()
    C->>S: MarketContractService.getList()
    Note over C,S: Controller: MarketContractController.list()<br/>Service: MarketContractService.getList()
    S->>R: 查询缓存 market:contract:list:hash
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询数据库表 pms_market_contract
        Note over S,D: SQL: SELECT * FROM pms_market_contract WHERE ...
        D-->>S: 返回数据
        S->>R: 更新缓存 market:contract:list:hash
    end
    S-->>C: 返回数据
    C-->>F: 返回结果
```

### 3.2 数据库设计

#### 3.2.1 线索管理表(pms_lead_management)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| lead_name | varchar | 255 | NO | | 线索名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| contact_person | varchar | 100 | YES | | 联系人 |
| contact_phone | varchar | 20 | YES | | 联系电话 |
| contact_email | varchar | 100 | YES | | 联系邮箱 |
| lead_source | varchar | 50 | YES | | 线索来源 |
| lead_priority | varchar | 20 | YES | | 线索优先级 |
| lead_status | varchar | 20 | YES | | 线索状态 |
| description | text | | YES | | 线索描述 |
| estimated_amount | decimal | 15,2 | YES | | 预计金额 |
| estimated_close_date | datetime | | YES | | 预计成交时间 |
| industry | varchar | 100 | YES | | 所属行业 |
| company_size | varchar | 50 | YES | | 客户规模 |
| assigned_to | bigint | 20 | YES | | 分配给 |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.2 需求管理表(pms_requirement_mangement)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| requirement_no | varchar | 50 | NO | | 需求编号 |
| requirement_name | varchar | 255 | NO | | 需求名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| requirement_type | varchar | 50 | YES | | 需求类型 |
| requirement_status | varchar | 20 | YES | | 需求状态 |
| description | text | | YES | | 需求描述 |
| estimated_amount | decimal | 15,2 | YES | | 预计金额 |
| estimated_start_date | datetime | | YES | | 预计开始时间 |
| estimated_end_date | datetime | | YES | | 预计结束时间 |
| priority | varchar | 20 | YES | | 优先级 |
| responsible_person | bigint | 20 | YES | | 负责人 |
| customer_contact | varchar | 100 | YES | | 客户联系人 |
| customer_phone | varchar | 20 | YES | | 客户电话 |
| attachment_urls | text | | YES | | 附件URLs |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.3 报价管理表(pms_quotation_management)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| quotation_no | varchar | 50 | NO | | 报价编号 |
| quotation_name | varchar | 255 | NO | | 报价名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| requirement_id | bigint | 20 | YES | | 关联需求ID |
| quotation_status | varchar | 20 | YES | | 报价状态 |
| total_amount | decimal | 15,2 | YES | | 报价总金额 |
| valid_until | datetime | | YES | | 有效期至 |
| quotation_date | datetime | | YES | | 报价日期 |
| description | text | | YES | | 报价说明 |
| terms_conditions | text | | YES | | 条款条件 |
| responsible_person | bigint | 20 | YES | | 负责人 |
| customer_contact | varchar | 100 | YES | | 客户联系人 |
| customer_phone | varchar | 20 | YES | | 客户电话 |
| attachment_urls | text | | YES | | 附件URLs |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.4 合同管理表(pms_market_contract)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| contract_no | varchar | 50 | NO | | 合同编号 |
| contract_name | varchar | 255 | NO | | 合同名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| quotation_id | bigint | 20 | YES | | 关联报价ID |
| contract_status | varchar | 20 | YES | | 合同状态 |
| contract_amount | decimal | 15,2 | YES | | 合同金额 |
| sign_date | datetime | | YES | | 签署日期 |
| start_date | datetime | | YES | | 开始日期 |
| end_date | datetime | | YES | | 结束日期 |
| description | text | | YES | | 合同描述 |
| terms_conditions | text | | YES | | 条款条件 |
| responsible_person | bigint | 20 | YES | | 负责人 |
| customer_contact | varchar | 100 | YES | | 客户联系人 |
| customer_phone | varchar | 20 | YES | | 客户电话 |
| attachment_urls | text | | YES | | 附件URLs |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.5 客户管理表(pms_customer_management)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| customer_name | varchar | 255 | NO | | 客户名称 |
| customer_type | varchar | 50 | YES | | 客户类型 |
| customer_level | varchar | 20 | YES | | 客户等级 |
| industry | varchar | 100 | YES | | 所属行业 |
| company_size | varchar | 50 | YES | | 公司规模 |
| contact_person | varchar | 100 | YES | | 联系人 |
| contact_phone | varchar | 20 | YES | | 联系电话 |
| contact_email | varchar | 100 | YES | | 联系邮箱 |
| address | varchar | 500 | YES | | 地址 |
| description | text | | YES | | 客户描述 |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

### 3.3 前端页面字段与数据库字段映射关系

#### 3.3.1 线索管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| leadName | 线索名称 | lead_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| contactPerson | 联系人 | contact_person | String | 直接映射 |
| contactPhone | 联系电话 | contact_phone | String | 直接映射 |
| contactEmail | 联系邮箱 | contact_email | String | 直接映射 |
| leadSource | 线索来源 | lead_source | String | 下拉选择值映射 |
| leadPriority | 线索优先级 | lead_priority | String | 枚举值映射(高/中/低) |
| leadStatus | 线索状态 | lead_status | String | 枚举值映射 |
| description | 线索描述 | description | String | 富文本转换 |
| estimatedAmount | 预计金额 | estimated_amount | Number | 数字格式化 |
| estimatedCloseDate | 预计成交时间 | estimated_close_date | Date | 日期格式转换 |
| industry | 所属行业 | industry | String | 下拉选择值映射 |
| companySize | 客户规模 | company_size | String | 下拉选择值映射 |
| assignedTo | 分配给 | assigned_to | Number | 用户ID映射 |
| createTime | 创建时间 | create_time | Date | 时间格式化显示 |
| updateTime | 更新时间 | update_time | Date | 时间格式化显示 |

#### 3.3.2 需求管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| requirementNo | 需求编号 | requirement_no | String | 自动生成编号 |
| requirementName | 需求名称 | requirement_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| requirementType | 需求类型 | requirement_type | String | 下拉选择值映射 |
| requirementStatus | 需求状态 | requirement_status | String | 枚举值映射 |
| description | 需求描述 | description | String | 富文本转换 |
| estimatedAmount | 预计金额 | estimated_amount | Number | 数字格式化 |
| estimatedStartDate | 预计开始时间 | estimated_start_date | Date | 日期格式转换 |
| estimatedEndDate | 预计结束时间 | estimated_end_date | Date | 日期格式转换 |
| priority | 优先级 | priority | String | 枚举值映射(高/中/低) |
| responsiblePerson | 负责人 | responsible_person | Number | 用户ID映射 |
| customerContact | 客户联系人 | customer_contact | String | 直接映射 |
| customerPhone | 客户电话 | customer_phone | String | 直接映射 |
| attachmentUrls | 附件URLs | attachment_urls | Array | JSON数组转换 |

#### 3.3.3 报价管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| quotationNo | 报价编号 | quotation_no | String | 自动生成编号 |
| quotationName | 报价名称 | quotation_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| requirementId | 关联需求ID | requirement_id | Number | 外键关联 |
| quotationStatus | 报价状态 | quotation_status | String | 枚举值映射 |
| totalAmount | 报价总金额 | total_amount | Number | 数字格式化 |
| validUntil | 有效期至 | valid_until | Date | 日期格式转换 |
| quotationDate | 报价日期 | quotation_date | Date | 日期格式转换 |
| description | 报价说明 | description | String | 富文本转换 |
| termsConditions | 条款条件 | terms_conditions | String | 富文本转换 |
| responsiblePerson | 负责人 | responsible_person | Number | 用户ID映射 |
| customerContact | 客户联系人 | customer_contact | String | 直接映射 |
| customerPhone | 客户电话 | customer_phone | String | 直接映射 |
| attachmentUrls | 附件URLs | attachment_urls | Array | JSON数组转换 |

#### 3.3.4 合同管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| contractNo | 合同编号 | contract_no | String | 自动生成编号 |
| contractName | 合同名称 | contract_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| quotationId | 关联报价ID | quotation_id | Number | 外键关联 |
| contractStatus | 合同状态 | contract_status | String | 枚举值映射 |
| contractAmount | 合同金额 | contract_amount | Number | 数字格式化 |
| signDate | 签署日期 | sign_date | Date | 日期格式转换 |
| startDate | 开始日期 | start_date | Date | 日期格式转换 |
| endDate | 结束日期 | end_date | Date | 日期格式转换 |
| description | 合同描述 | description | String | 富文本转换 |
| termsConditions | 条款条件 | terms_conditions | String | 富文本转换 |
| responsiblePerson | 负责人 | responsible_person | Number | 用户ID映射 |
| customerContact | 客户联系人 | customer_contact | String | 直接映射 |
| customerPhone | 客户电话 | customer_phone | String | 直接映射 |
| attachmentUrls | 附件URLs | attachment_urls | Array | JSON数组转换 |

### 3.4 物理模型图

#### 3.4.1 市场经营模块ER图

```mermaid
erDiagram
    PMS_LEAD_MANAGEMENT {
        bigint id PK
        varchar lead_name
        varchar customer_name
        varchar contact_person
        varchar contact_phone
        varchar contact_email
        varchar lead_source
        varchar lead_priority
        varchar lead_status
        text description
        decimal estimated_amount
        datetime estimated_close_date
        varchar industry
        varchar company_size
        bigint assigned_to
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_REQUIREMENT_MANGEMENT {
        bigint id PK
        varchar requirement_no
        varchar requirement_name
        varchar customer_name
        varchar requirement_type
        varchar requirement_status
        text description
        decimal estimated_amount
        datetime estimated_start_date
        datetime estimated_end_date
        varchar priority
        bigint responsible_person
        varchar customer_contact
        varchar customer_phone
        text attachment_urls
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_QUOTATION_MANAGEMENT {
        bigint id PK
        varchar quotation_no
        varchar quotation_name
        varchar customer_name
        bigint requirement_id FK
        varchar quotation_status
        decimal total_amount
        datetime valid_until
        datetime quotation_date
        text description
        text terms_conditions
        bigint responsible_person
        varchar customer_contact
        varchar customer_phone
        text attachment_urls
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_MARKET_CONTRACT {
        bigint id PK
        varchar contract_no
        varchar contract_name
        varchar customer_name
        bigint quotation_id FK
        varchar contract_status
        decimal contract_amount
        datetime sign_date
        datetime start_date
        datetime end_date
        text description
        text terms_conditions
        bigint responsible_person
        varchar customer_contact
        varchar customer_phone
        text attachment_urls
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_CUSTOMER_MANAGEMENT {
        bigint id PK
        varchar customer_name
        varchar customer_type
        varchar customer_level
        varchar industry
        varchar company_size
        varchar contact_person
        varchar contact_phone
        varchar contact_email
        varchar address
        text description
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_REQUIREMENT_MANGEMENT ||--o{ PMS_QUOTATION_MANAGEMENT : "一对多"
    PMS_QUOTATION_MANAGEMENT ||--o{ PMS_MARKET_CONTRACT : "一对多"
    PMS_CUSTOMER_MANAGEMENT ||--o{ PMS_LEAD_MANAGEMENT : "一对多"
    PMS_CUSTOMER_MANAGEMENT ||--o{ PMS_REQUIREMENT_MANGEMENT : "一对多"
```

### 3.5 Redis缓存设计

#### 3.5.1 缓存Key设计规范

| 模块 | 缓存Key格式 | 过期时间 | 说明 |
|------|-------------|----------|------|
| 线索管理 | market:lead:list:{hash} | 30分钟 | 线索列表缓存 |
| 线索管理 | market:lead:detail:{id} | 1小时 | 线索详情缓存 |
| 需求管理 | market:req:list:{hash} | 30分钟 | 需求列表缓存 |
| 需求管理 | market:req:detail:{id} | 1小时 | 需求详情缓存 |
| 报价管理 | market:quote:list:{hash} | 30分钟 | 报价列表缓存 |
| 报价管理 | market:quote:detail:{id} | 1小时 | 报价详情缓存 |
| 合同管理 | market:contract:list:{hash} | 30分钟 | 合同列表缓存 |
| 合同管理 | market:contract:detail:{id} | 1小时 | 合同详情缓存 |
| 客户管理 | market:customer:list:{hash} | 1小时 | 客户列表缓存 |
| 客户管理 | market:customer:detail:{id} | 2小时 | 客户详情缓存 |
| 统计数据 | market:stats:overview | 15分钟 | 概览统计缓存 |
| 报表数据 | market:report:{type}:{hash} | 1小时 | 报表数据缓存 |

#### 3.5.2 缓存更新策略

1. **列表缓存更新**：当有新增、修改、删除操作时，清除对应的列表缓存
2. **详情缓存更新**：当有修改操作时，更新对应的详情缓存
3. **统计缓存更新**：当有业务数据变更时，清除统计缓存，下次访问时重新计算
4. **报表缓存更新**：定时任务每小时更新一次报表缓存

## 4. 总结

市场经营模块作为系统的核心业务模块，涵盖了从线索获取到合同签署的完整销售流程。通过详细的页面设计、完善的API接口、清晰的数据库设计和高效的缓存策略，为用户提供了完整的市场经营管理解决方案。

### 4.1 主要功能特点

1. **完整的销售流程管理**：从线索到合同的全流程跟踪
2. **灵活的数据筛选和搜索**：支持多维度的数据查询
3. **丰富的报表分析**：提供多种维度的数据分析和可视化
4. **高效的缓存机制**：提升系统响应速度和用户体验
5. **规范的前后端对接**：清晰的API设计和数据映射关系

### 4.2 技术架构优势

1. **前后端分离**：Vue.js前端 + Spring Boot后端
2. **数据库设计规范**：遵循数据库设计范式，支持业务扩展
3. **缓存策略完善**：Redis缓存提升系统性能
4. **API设计RESTful**：符合REST规范，易于维护和扩展
