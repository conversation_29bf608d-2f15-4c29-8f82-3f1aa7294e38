// 履约执行模块简化截图助手
// 请在登录成功后，在浏览器Console中运行此脚本

class SimpleScreenshotHelper {
    constructor() {
        this.screenshots = [
            {name: "首页.png", desc: "登录成功后的首页", action: "当前页面直接截图"},
            {name: "合同管理列表.png", desc: "合同管理列表页", action: "点击左侧'履约执行' → '合同管理'"},
            {name: "合同编辑抽屉.png", desc: "合同编辑界面", action: "点击任意合同的'编辑'按钮"},
            {name: "合同创建抽屉.png", desc: "合同创建界面", action: "点击'创建合同'按钮"},
            {name: "合同详情页面.png", desc: "合同详情主页", action: "点击任意合同名称"},
            {name: "合同内容信息.png", desc: "合同基本信息", action: "点击'合同内容信息'标签"},
            {name: "支付节点信息.png", desc: "支付节点管理", action: "点击'支付节点信息'标签"},
            {name: "支付节点确认弹窗.png", desc: "节点确认界面", action: "点击'确认'按钮"},
            {name: "支付状态确认弹窗.png", desc: "状态确认界面", action: "点击'状态确认'按钮"},
            {name: "节点确认记录.png", desc: "确认记录列表", action: "点击'节点确认记录'标签"},
            {name: "确认记录详情.png", desc: "记录详情界面", action: "点击'查看详情'按钮"},
            {name: "确认记录审核.png", desc: "审核界面", action: "点击'审核'按钮"},
            {name: "合同附件信息.png", desc: "附件管理", action: "点击'合同附件信息'标签"},
            {name: "附件上传界面.png", desc: "上传界面", action: "点击'上传附件'按钮"},
            {name: "附件预览界面.png", desc: "预览界面", action: "点击'预览'按钮"},
            {name: "合同审批流程.png", desc: "审批流程", action: "点击'合同审批流程'标签"}
        ];
        this.currentIndex = 0;
    }

    start() {
        console.log("🚀 履约执行模块截图助手");
        console.log("📋 总共需要截图16个页面（已完成登录页面，移除个人工作台）");
        console.log("💡 每次提示时，请手动截图并运行 next() 继续");
        console.log("");
        this.showCurrent();
    }

    showCurrent() {
        if (this.currentIndex >= this.screenshots.length) {
            console.log("🎉 所有截图完成！");
            console.log("📁 请检查 docs/images/front_system/履约执行/ 目录");
            console.log("🔍 运行验证: cd docs && python3 verify_screenshots.py");
            return;
        }

        const shot = this.screenshots[this.currentIndex];
        console.log(`\n📸 [${this.currentIndex + 1}/17] ${shot.name}`);
        console.log(`📝 描述: ${shot.desc}`);
        console.log(`🎯 操作: ${shot.action}`);
        console.log(`💾 保存路径: docs/images/front_system/履约执行/${shot.name}`);
        console.log(`⏸️  截图完成后，运行: next()`);
    }

    next() {
        this.currentIndex++;
        this.showCurrent();
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.showCurrent();
        }
    }

    goto(index) {
        if (index >= 1 && index <= this.screenshots.length) {
            this.currentIndex = index - 1;
            this.showCurrent();
        } else {
            console.log(`❌ 请输入1-${this.screenshots.length}之间的数字`);
        }
    }

    list() {
        console.log("📋 截图清单:");
        this.screenshots.forEach((shot, index) => {
            const status = index < this.currentIndex ? "✅" :
                          index === this.currentIndex ? "👉" : "⏳";
            console.log(`${status} ${index + 1}. ${shot.name} - ${shot.desc}`);
        });
        console.log(`\n当前进度: ${this.currentIndex}/${this.screenshots.length}`);
    }

    help() {
        console.log(`
📖 使用说明:
- start()     开始截图流程
- next()      下一个截图
- prev()      上一个截图
- goto(n)     跳转到第n个截图
- list()      查看完整清单
- help()      显示帮助

📸 截图要求:
- 分辨率: 1920x1080或更高
- 格式: PNG格式
- 范围: 完整浏览器窗口
- 质量: 页面完全加载，内容清晰

💾 保存位置: docs/images/front_system/履约执行/
        `);
    }
}

// 创建全局实例
window.helper = new SimpleScreenshotHelper();

console.log("✅ 截图助手已加载！");
console.log("🚀 运行 helper.start() 开始截图");
console.log("❓ 运行 helper.help() 查看帮助");
