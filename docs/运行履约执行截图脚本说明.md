# 履约执行模块自动截图脚本使用说明

## 📋 脚本概述

我已经为您创建了一个自动化截图脚本 `crawl_contract_execution.py`，可以自动访问履约执行模块的所有页面并进行截图。

## 🔧 环境准备

### 1. 检查依赖
确保已安装以下依赖：
```bash
pip install selenium
```

### 2. 检查ChromeDriver
脚本会使用当前目录下的 `chromedriver`，请确保：
- ChromeDriver 文件存在于 `docs/` 目录下
- ChromeDriver 版本与您的Chrome浏览器版本兼容

### 3. 检查网络连接
确保可以访问测试环境：http://***************:44099/login

## 🚀 运行脚本

### 方法1：直接运行Python脚本
```bash
cd docs
python crawl_contract_execution.py
```

### 方法2：使用Python3
```bash
cd docs
python3 crawl_contract_execution.py
```

## 📸 截图内容

脚本将自动截图以下页面：

### 基础页面
1. `登录页面.png` - 系统登录界面
2. `首页.png` - 登录成功后的首页
3. `个人工作台.png` - 个人工作台主页面
4. `合同管理列表.png` - 合同管理列表页面

### 合同详情页面
5. `合同详情页面.png` - 合同详情主页面
6. `合同内容信息.png` - 合同基本信息标签页
7. `支付节点信息.png` - 支付节点信息标签页
8. `节点确认记录.png` - 节点确认记录标签页
9. `合同附件信息.png` - 合同附件信息标签页
10. `合同审批流程.png` - 合同审批流程标签页

### 操作界面
11. `合同编辑抽屉.png` - 合同编辑界面
12. `合同创建抽屉.png` - 合同创建界面
13. `支付节点确认弹窗.png` - 支付节点确认界面
14. `支付状态确认弹窗.png` - 支付状态确认界面
15. `确认记录详情.png` - 确认记录详情界面
16. `确认记录审核.png` - 确认记录审核界面
17. `附件上传界面.png` - 附件上传界面
18. `附件预览界面.png` - 附件预览界面

## 📁 输出目录

所有截图将保存在：
```
docs/images/front_system/履约执行/
```

## 🔍 脚本执行流程

1. **启动浏览器** - 使用Chrome浏览器，分辨率1920x1080
2. **访问登录页面** - 截图登录页面
3. **自动登录** - 使用账号 heqiang / Abc123456789!
4. **截图首页** - 登录成功后截图
5. **导航到个人工作台** - 截图工作台页面
6. **进入合同管理** - 截图合同列表页面
7. **测试列表操作** - 截图编辑、创建等操作界面
8. **进入合同详情** - 点击第一个合同进入详情
9. **遍历所有标签页** - 逐一截图每个标签页
10. **测试操作功能** - 截图各种操作弹窗
11. **关闭浏览器** - 完成所有截图后关闭

## ⚠️ 注意事项

### 数据依赖
- 系统中需要有合同数据才能正常截图
- 确保测试账号有足够的权限访问所有功能
- 某些操作按钮可能因为数据状态而不显示

### 网络要求
- 确保网络连接稳定
- 页面加载时间可能较长，脚本已设置合理的等待时间

### 浏览器要求
- 使用Chrome浏览器
- 确保ChromeDriver版本兼容
- 脚本会自动设置浏览器参数

## 🐛 常见问题

### 问题1：找不到ChromeDriver
**错误信息**：`selenium.common.exceptions.WebDriverException`
**解决方案**：
1. 下载对应版本的ChromeDriver
2. 将ChromeDriver放在docs目录下
3. 确保文件有执行权限

### 问题2：登录失败
**错误信息**：`登录失败，仍在登录页面`
**解决方案**：
1. 检查网络连接
2. 确认账号密码正确
3. 检查登录页面元素是否变化

### 问题3：找不到页面元素
**错误信息**：`找不到xxx菜单`
**解决方案**：
1. 检查页面是否完全加载
2. 确认用户权限是否足够
3. 检查页面结构是否有变化

### 问题4：截图失败
**错误信息**：`截图失败`
**解决方案**：
1. 检查目录权限
2. 确保磁盘空间充足
3. 检查文件路径是否正确

## 📊 执行结果

### 成功标志
- 控制台输出 "履约执行模块截图完成"
- 在输出目录中看到所有截图文件
- 每个截图文件大小合理（通常几百KB到几MB）

### 失败处理
- 脚本会记录详细的错误日志
- 即使某个页面截图失败，也会继续执行其他页面
- 可以根据日志信息定位具体问题

## 🔄 重新运行

如果需要重新截图：
1. 删除现有截图文件（可选）
2. 重新运行脚本
3. 脚本会覆盖同名文件

## 📝 后续工作

截图完成后：
1. 检查所有截图文件是否生成
2. 验证截图内容是否完整清晰
3. 按照《截图完成后的文档更新指南.md》更新PRD文档
4. 将实际截图路径更新到文档中

## 🆘 技术支持

如果遇到问题：
1. 查看控制台错误日志
2. 检查网络和环境配置
3. 参考常见问题解决方案
4. 联系技术支持人员

---
**提示**：首次运行建议在测试环境中验证，确保脚本正常工作后再在正式环境中使用。
