# 履约执行模块详细截图操作指南

## 🎯 目标
完成履约执行模块18个页面的截图，当前进度：1/18

## 🔐 登录信息
- **地址**: http://183.136.206.207:44099/login
- **账号**: heqiang
- **密码**: Abc123456789!

## 📋 详细操作步骤

### 第一步：登录系统 ✅
- [x] 1. 登录页面.png - 已完成

### 第二步：基础页面截图（2个）

#### 2. 首页.png
**操作步骤**：
1. 登录成功后，当前页面就是首页
2. 等待页面完全加载
3. 截图整个浏览器窗口
4. 保存为：`docs/images/front_system/履约执行/首页.png`

**验证要点**：
- 显示系统名称和用户信息
- 主导航菜单完整显示
- 页面内容完全加载

#### 3. 个人工作台.png
**操作步骤**：
1. 点击顶部导航的"个人工作台"
2. 等待页面加载完成
3. 截图整个页面
4. 保存为：`docs/images/front_system/履约执行/个人工作台.png`

**验证要点**：
- 左侧菜单完整显示
- 工作台内容区域显示
- 页面标题正确

### 第三步：合同管理模块（15个）

#### 4. 合同管理列表.png
**操作步骤**：
1. 在个人工作台左侧菜单中找到"合同管理"
2. 点击"合同管理"进入列表页
3. 等待合同列表加载完成
4. 截图整个页面
5. 保存为：`docs/images/front_system/履约执行/合同管理列表.png`

**验证要点**：
- 合同列表数据显示
- 筛选条件区域显示
- 操作按钮完整
- 分页信息显示

#### 5. 合同编辑抽屉.png
**操作步骤**：
1. 在合同列表中找到任意一个合同
2. 点击该合同行的"编辑"按钮
3. 等待编辑抽屉打开
4. 截图整个页面（包含抽屉）
5. 保存为：`docs/images/front_system/履约执行/合同编辑抽屉.png`
6. 点击"取消"或"关闭"按钮关闭抽屉

**验证要点**：
- 编辑表单完整显示
- 所有字段可见
- 保存/取消按钮显示

#### 6. 合同创建抽屉.png
**操作步骤**：
1. 在合同列表页面找到"创建合同"或"新增"按钮
2. 点击创建按钮
3. 等待创建抽屉打开
4. 截图整个页面（包含抽屉）
5. 保存为：`docs/images/front_system/履约执行/合同创建抽屉.png`
6. 点击"取消"关闭抽屉

**验证要点**：
- 创建表单完整显示
- 必填字段标识清晰
- 提交/取消按钮显示

#### 7. 合同详情页面.png
**操作步骤**：
1. 在合同列表中点击任意合同的名称（通常是蓝色链接）
2. 等待跳转到合同详情页面
3. 等待页面完全加载
4. 截图整个页面
5. 保存为：`docs/images/front_system/履约执行/合同详情页面.png`

**验证要点**：
- 合同基本信息显示
- 左侧标签页导航显示
- 操作按钮区域显示

#### 8. 合同内容信息.png
**操作步骤**：
1. 在合同详情页面，点击左侧的"合同内容信息"标签
2. 等待内容加载
3. 截图整个页面
4. 保存为：`docs/images/front_system/履约执行/合同内容信息.png`

**验证要点**：
- 合同基本信息表单显示
- 所有字段和值显示
- 标签页高亮状态

#### 9. 支付节点信息.png
**操作步骤**：
1. 点击左侧的"支付节点信息"标签
2. 等待节点列表加载
3. 截图整个页面
4. 保存为：`docs/images/front_system/履约执行/支付节点信息.png`

**验证要点**：
- 支付节点列表显示
- 节点状态和进度显示
- 操作按钮显示

#### 10. 支付节点确认弹窗.png
**操作步骤**：
1. 在支付节点信息页面，找到"支付节点确认"按钮
2. 点击确认按钮
3. 等待弹窗打开
4. 截图整个页面（包含弹窗）
5. 保存为：`docs/images/front_system/履约执行/支付节点确认弹窗.png`
6. 点击"取消"关闭弹窗

#### 11. 支付状态确认弹窗.png
**操作步骤**：
1. 找到"支付状态确认"按钮
2. 点击状态确认按钮
3. 等待弹窗打开
4. 截图整个页面（包含弹窗）
5. 保存为：`docs/images/front_system/履约执行/支付状态确认弹窗.png`
6. 点击"取消"关闭弹窗

#### 12. 节点确认记录.png
**操作步骤**：
1. 点击左侧的"节点确认记录"标签
2. 等待确认记录列表加载
3. 截图整个页面
4. 保存为：`docs/images/front_system/履约执行/节点确认记录.png`

#### 13. 确认记录详情.png
**操作步骤**：
1. 在确认记录列表中找到"查看详情"按钮
2. 点击查看详情
3. 等待详情界面打开
4. 截图整个页面
5. 保存为：`docs/images/front_system/履约执行/确认记录详情.png`
6. 关闭详情界面

#### 14. 确认记录审核.png
**操作步骤**：
1. 找到"审核"按钮（如果有权限）
2. 点击审核按钮
3. 等待审核界面打开
4. 截图整个页面
5. 保存为：`docs/images/front_system/履约执行/确认记录审核.png`
6. 点击"取消"关闭

#### 15. 合同附件信息.png
**操作步骤**：
1. 点击左侧的"合同附件信息"标签
2. 等待附件列表加载
3. 截图整个页面
4. 保存为：`docs/images/front_system/履约执行/合同附件信息.png`

#### 16. 附件上传界面.png
**操作步骤**：
1. 在附件信息页面找到"上传附件"按钮
2. 点击上传按钮
3. 等待上传界面打开
4. 截图整个页面
5. 保存为：`docs/images/front_system/履约执行/附件上传界面.png`
6. 点击"取消"关闭

#### 17. 附件预览界面.png
**操作步骤**：
1. 在附件列表中找到文件名或"预览"按钮
2. 点击预览
3. 等待预览界面打开
4. 截图整个页面
5. 保存为：`docs/images/front_system/履约执行/附件预览界面.png`
6. 关闭预览界面

#### 18. 合同审批流程.png
**操作步骤**：
1. 点击左侧的"合同审批流程"标签
2. 等待流程图加载
3. 截图整个页面
4. 保存为：`docs/images/front_system/履约执行/合同审批流程.png`

## 🔍 完成验证

所有截图完成后，运行验证脚本：
```bash
cd docs
python3 verify_screenshots.py
```

期望结果：显示 18/18 (100%) 完成

## ⚠️ 注意事项

1. **数据依赖**：确保系统中有合同数据
2. **权限限制**：某些按钮可能因权限不显示
3. **网络稳定**：确保网络连接稳定
4. **页面加载**：每次操作后等待页面完全加载
5. **文件命名**：严格按照指定的文件名保存

## 📞 技术支持

如遇问题请：
1. 检查网络连接
2. 刷新页面重试
3. 确认用户权限
4. 联系技术支持

---
**预计完成时间**: 45-60分钟
**质量要求**: 所有截图清晰完整，验证脚本通过
