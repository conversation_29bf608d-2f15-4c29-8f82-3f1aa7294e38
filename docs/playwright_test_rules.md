<<<<<<< HEAD
# Playwright MCP 自动化测试用例与脚本生成规则

## 1. 测试用例编写规范

### 1.1 用例结构
- **用例名称**：简明描述测试目标
- **前置条件**：测试前需满足的环境或数据
- **测试步骤**：每一步操作详细描述
- **预期结果**：每一步或最终的期望页面/数据/状态
- **后置条件**：测试完成后的清理操作
- **优先级**：高/中/低
- **标签**：如冒烟、回归、功能、性能等

### 1.2 示例
```
用例名称：登录成功
前置条件：用户已注册
测试步骤：
  1. 打开登录页
  2. 输入正确用户名和密码
  3. 点击登录按钮
预期结果：跳转到首页，显示用户名
后置条件：无
优先级：高
标签：冒烟,功能
```

## 2. Playwright 脚本生成规范

### 2.1 目录结构建议
```
/tests
  ├── cases/         # 测试用例描述（如YAML/MD）
  ├── scripts/       # Playwright 脚本
  └── reports/       # 测试报告
```

### 2.2 脚本模板
```js
import { test, expect } from '@playwright/test';

test('登录成功', async ({ page }) => {
  await page.goto('https://example.com/login');
  await page.fill('#username', 'testuser');
  await page.fill('#password', 'password123');
  await page.click('button[type="submit"]');
  await expect(page).toHaveURL('https://example.com/home');
  await expect(page.locator('.user-info')).toContainText('testuser');
});
```

### 2.3 断言规范
- 使用 `expect` 断言页面跳转、元素可见、文本内容、接口返回等
- 断言应具体、可复现，避免模糊判断

### 2.4 数据准备与清理
- 推荐使用 API 或数据库脚本进行数据初始化和清理
- 用例独立，避免数据污染

## 3. 测试报告生成规则

### 3.1 报告生成方式
- 推荐使用 Playwright Test 自带的报告生成（如 `npx playwright test --reporter=html`）
- 支持多种格式：HTML、JSON、JUnit XML

### 3.2 报告文件命名与存放
- 报告统一存放于 `/tests/reports/` 目录
- 文件命名格式：`report-YYYYMMDD-HHMMSS.html`，如 `report-20240601-153000.html`
- 每次测试生成独立报告，便于追溯历史

### 3.3 报告内容要求
- 包含用例执行总览（通过/失败/跳过数）
- 失败用例详细堆栈与截图
- 关键步骤截图与日志
- 可选：集成 CI/CD 平台自动上传报告

## 4. 其他建议
- 用例、脚本、报告三者一一对应，便于追踪
- 定期回顾和优化测试用例与脚本
- 结合代码审查，提升用例质量

---
如需进一步细化规则或补充特定场景示例，请补充说明。
=======
# Playwright MCP 自动化测试用例与脚本生成规则

## 1. 测试用例编写规范

### 1.1 用例结构
- **用例名称**：简明描述测试目标
- **前置条件**：测试前需满足的环境或数据
- **测试步骤**：每一步操作详细描述
- **预期结果**：每一步或最终的期望页面/数据/状态
- **后置条件**：测试完成后的清理操作
- **优先级**：高/中/低
- **标签**：如冒烟、回归、功能、性能等

### 1.2 示例
```
用例名称：登录成功
前置条件：用户已注册
测试步骤：
  1. 打开登录页
  2. 输入正确用户名和密码
  3. 点击登录按钮
预期结果：跳转到首页，显示用户名
后置条件：无
优先级：高
标签：冒烟,功能
```

## 2. Playwright 脚本生成规范

### 2.1 目录结构建议
```
/tests
  ├── cases/         # 测试用例描述（如YAML/MD）
  ├── scripts/       # Playwright 脚本
  └── reports/       # 测试报告
```

### 2.2 脚本模板
```js
import { test, expect } from '@playwright/test';

test('登录成功', async ({ page }) => {
  await page.goto('https://example.com/login');
  await page.fill('#username', 'testuser');
  await page.fill('#password', 'password123');
  await page.click('button[type="submit"]');
  await expect(page).toHaveURL('https://example.com/home');
  await expect(page.locator('.user-info')).toContainText('testuser');
});
```

### 2.3 断言规范
- 使用 `expect` 断言页面跳转、元素可见、文本内容、接口返回等
- 断言应具体、可复现，避免模糊判断

### 2.4 数据准备与清理
- 推荐使用 API 或数据库脚本进行数据初始化和清理
- 用例独立，避免数据污染

## 3. 测试报告生成规则

### 3.1 报告生成方式
- 推荐使用 Playwright Test 自带的报告生成（如 `npx playwright test --reporter=html`）
- 支持多种格式：HTML、JSON、JUnit XML

### 3.2 报告文件命名与存放
- 报告统一存放于 `/tests/reports/` 目录
- 文件命名格式：`report-YYYYMMDD-HHMMSS.html`，如 `report-20240601-153000.html`
- 每次测试生成独立报告，便于追溯历史

### 3.3 报告内容要求
- 包含用例执行总览（通过/失败/跳过数）
- 失败用例详细堆栈与截图
- 关键步骤截图与日志
- 可选：集成 CI/CD 平台自动上传报告

## 4. 其他建议
- 用例、脚本、报告三者一一对应，便于追踪
- 定期回顾和优化测试用例与脚本
- 结合代码审查，提升用例质量

---
如需进一步细化规则或补充特定场景示例，请补充说明。
>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
