# 履约执行模块手动截图执行方案

## 🎯 当前状态
- ✅ 已完成：登录页面.png (1/18)
- ❌ 待完成：17个页面截图

## 🚀 立即执行方案

### 方案A：浏览器Console辅助（推荐）

#### 步骤1：准备工作
1. 打开Chrome浏览器
2. 访问：http://***************:44099/login
3. 手动登录：heqiang / Abc123456789!
4. 选择组织并点击确定

#### 步骤2：运行辅助脚本
1. 按F12打开开发者工具
2. 切换到Console标签页
3. 复制以下代码并粘贴运行：

```javascript
// 履约执行模块截图辅助脚本
class ScreenshotHelper {
    constructor() {
        this.screenshots = [
            {name: "首页.png", desc: "登录成功后的首页", action: "当前页面"},
            {name: "个人工作台.png", desc: "个人工作台主页", action: "点击个人工作台"},
            {name: "合同管理列表.png", desc: "合同管理列表页", action: "点击合同管理"},
            {name: "合同编辑抽屉.png", desc: "合同编辑界面", action: "点击编辑按钮"},
            {name: "合同创建抽屉.png", desc: "合同创建界面", action: "点击创建按钮"},
            {name: "合同详情页面.png", desc: "合同详情主页", action: "点击合同名称"},
            {name: "合同内容信息.png", desc: "合同基本信息", action: "点击合同内容信息标签"},
            {name: "支付节点信息.png", desc: "支付节点管理", action: "点击支付节点信息标签"},
            {name: "支付节点确认弹窗.png", desc: "节点确认界面", action: "点击确认按钮"},
            {name: "支付状态确认弹窗.png", desc: "状态确认界面", action: "点击状态确认按钮"},
            {name: "节点确认记录.png", desc: "确认记录列表", action: "点击节点确认记录标签"},
            {name: "确认记录详情.png", desc: "记录详情界面", action: "点击查看详情"},
            {name: "确认记录审核.png", desc: "审核界面", action: "点击审核按钮"},
            {name: "合同附件信息.png", desc: "附件管理", action: "点击合同附件信息标签"},
            {name: "附件上传界面.png", desc: "上传界面", action: "点击上传按钮"},
            {name: "附件预览界面.png", desc: "预览界面", action: "点击预览按钮"},
            {name: "合同审批流程.png", desc: "审批流程", action: "点击合同审批流程标签"}
        ];
        this.currentIndex = 0;
    }

    start() {
        console.log("🚀 履约执行模块截图助手");
        console.log("📋 总共需要截图17个页面");
        console.log("💡 每次提示时，请手动截图并按回车继续");
        this.nextScreenshot();
    }

    nextScreenshot() {
        if (this.currentIndex >= this.screenshots.length) {
            console.log("🎉 所有截图完成！");
            console.log("📁 请检查 docs/images/front_system/履约执行/ 目录");
            return;
        }

        const shot = this.screenshots[this.currentIndex];
        console.log(`\n📸 [${this.currentIndex + 1}/17] ${shot.name}`);
        console.log(`📝 描述: ${shot.desc}`);
        console.log(`🎯 操作: ${shot.action}`);
        console.log(`💾 保存路径: docs/images/front_system/履约执行/${shot.name}`);
        console.log("⏸️  截图完成后，运行: helper.next()");

        this.currentIndex++;
    }

    next() {
        this.nextScreenshot();
    }

    reset() {
        this.currentIndex = 0;
        console.log("🔄 重置到第一个截图");
        this.nextScreenshot();
    }

    list() {
        console.log("📋 截图清单:");
        this.screenshots.forEach((shot, index) => {
            const status = index < this.currentIndex ? "✅" : "⏳";
            console.log(`${status} ${index + 1}. ${shot.name} - ${shot.desc}`);
        });
    }
}

// 创建助手实例
const helper = new ScreenshotHelper();
console.log("💡 使用方法:");
console.log("   helper.start()  - 开始截图");
console.log("   helper.next()   - 下一个截图");
console.log("   helper.list()   - 查看清单");
console.log("   helper.reset()  - 重新开始");
```

#### 步骤3：开始截图
1. 运行：`helper.start()`
2. 按照提示进行截图
3. 每完成一个截图后运行：`helper.next()`

### 方案B：完全手动截图

#### 详细步骤清单

**1. 首页.png**
- 当前页面截图即可
- 确保显示完整的首页内容

**2. 个人工作台.png**
- 点击顶部导航的"个人工作台"
- 截图工作台主页面

**3. 合同管理列表.png**
- 在个人工作台左侧菜单点击"合同管理"
- 截图合同列表页面

**4. 合同编辑抽屉.png**
- 在合同列表中点击任意合同的"编辑"按钮
- 截图编辑抽屉界面
- 点击"取消"关闭

**5. 合同创建抽屉.png**
- 点击"创建合同"或"新增"按钮
- 截图创建抽屉界面
- 点击"取消"关闭

**6. 合同详情页面.png**
- 点击任意合同名称进入详情页
- 截图详情主页面

**7-18. 合同详情子页面**
- 在合同详情页面，依次点击左侧标签页截图
- 对于弹窗类型的，截图后记得关闭

## 📸 截图要求

### 技术规范
- **分辨率**: 1920x1080或更高
- **浏览器**: Chrome，100%缩放
- **格式**: PNG格式
- **范围**: 完整浏览器窗口

### 质量要求
- 页面完全加载
- 数据内容完整
- 文字清晰可读
- 界面元素完整

### 文件命名
- 使用清单中的确切文件名
- 保存到：`docs/images/front_system/履约执行/`

## 🔍 验证截图

完成后运行验证：
```bash
cd docs
python3 verify_screenshots.py
```

## 📞 遇到问题

如果遇到问题：
1. 检查网络连接
2. 确认账号权限
3. 刷新页面重试
4. 联系技术支持

---
**预计时间**: 30-45分钟
**完成标志**: 验证脚本显示18/18完成
