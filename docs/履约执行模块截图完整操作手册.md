# 履约执行模块截图完整操作手册

## 📋 概述

本手册提供了履约执行模块截图的完整操作指南，包含多种截图方式和详细的操作步骤。

## 🎯 目标

为履约执行模块的18个功能页面进行截图，用于完善产品需求文档(PRD)。

## 🔧 环境准备

### 系统要求
- 操作系统：Windows/macOS/Linux
- 浏览器：Chrome (推荐)
- Python：3.6+
- 网络：能访问测试环境

### 测试环境
- **地址**: http://183.136.206.207:44099/login
- **账号**: heqiang
- **密码**: Abc123456789!

## 📸 截图清单

| 序号 | 文件名 | 页面描述 | 访问路径 |
|------|--------|----------|----------|
| 1 | 登录页面.png | 系统登录界面 | 直接访问登录页 |
| 2 | 首页.png | 登录后首页 | 登录成功后 |
| 3 | 个人工作台.png | 工作台主页 | 首页 → 个人工作台 |
| 4 | 合同管理列表.png | 合同列表页 | 个人工作台 → 合同管理 |
| 5 | 合同编辑抽屉.png | 编辑界面 | 合同列表 → 编辑按钮 |
| 6 | 合同创建抽屉.png | 创建界面 | 合同列表 → 创建按钮 |
| 7 | 合同详情页面.png | 详情主页 | 合同列表 → 点击合同名称 |
| 8 | 合同内容信息.png | 基本信息 | 合同详情 → 合同内容信息 |
| 9 | 支付节点信息.png | 节点管理 | 合同详情 → 支付节点信息 |
| 10 | 支付节点确认弹窗.png | 节点确认 | 支付节点 → 确认按钮 |
| 11 | 支付状态确认弹窗.png | 状态确认 | 支付节点 → 状态确认 |
| 12 | 节点确认记录.png | 确认记录 | 合同详情 → 节点确认记录 |
| 13 | 确认记录详情.png | 记录详情 | 确认记录 → 查看详情 |
| 14 | 确认记录审核.png | 审核界面 | 确认记录 → 审核按钮 |
| 15 | 合同附件信息.png | 附件管理 | 合同详情 → 合同附件信息 |
| 16 | 附件上传界面.png | 上传界面 | 附件信息 → 上传按钮 |
| 17 | 附件预览界面.png | 预览界面 | 附件信息 → 预览按钮 |
| 18 | 合同审批流程.png | 审批流程 | 合同详情 → 合同审批流程 |

## 🚀 方式一：一键自动截图（推荐）

### 步骤1：环境准备
```bash
cd docs
chmod +x run_screenshot.sh
./run_screenshot.sh
```

### 步骤2：选择自动截图
在菜单中选择选项1，脚本将自动：
- 检查环境依赖
- 启动浏览器
- 自动登录
- 逐页截图
- 生成验证报告

### 步骤3：查看结果
截图完成后，检查 `docs/images/front_system/履约执行/` 目录。

## 🌐 方式二：浏览器辅助截图

### 步骤1：打开浏览器
1. 启动Chrome浏览器
2. 访问：http://183.136.206.207:44099/login
3. 登录账号：heqiang / Abc123456789!

### 步骤2：加载辅助脚本
1. 按F12打开开发者工具
2. 切换到Console标签页
3. 复制 `browser_screenshot_guide.js` 中的代码
4. 粘贴到Console并回车执行

### 步骤3：跟随提示截图
脚本会自动导航并提示截图，每次提示时：
1. 等待页面完全加载
2. 使用截图工具截取完整浏览器窗口
3. 保存为指定文件名
4. 按回车键继续

## 📱 方式三：完全手动截图

### 详细操作步骤

#### 第一阶段：基础页面（4个）

**1. 登录页面**
- 访问：http://183.136.206.207:44099/login
- 截图：`登录页面.png`
- 要点：显示完整登录界面

**2. 首页**
- 输入账号密码并登录
- 截图：`首页.png`
- 要点：显示登录后的主页面

**3. 个人工作台**
- 点击：个人工作台
- 截图：`个人工作台.png`
- 要点：显示工作台和左侧菜单

**4. 合同管理列表**
- 点击：合同管理
- 截图：`合同管理列表.png`
- 要点：显示合同列表和操作按钮

#### 第二阶段：操作界面（2个）

**5. 合同编辑抽屉**
- 点击任意合同的"编辑"按钮
- 截图：`合同编辑抽屉.png`
- 点击"取消"关闭

**6. 合同创建抽屉**
- 点击"创建合同"按钮
- 截图：`合同创建抽屉.png`
- 点击"取消"关闭

#### 第三阶段：合同详情（12个）

**7. 合同详情页面**
- 点击任意合同名称进入详情
- 截图：`合同详情页面.png`

**8. 合同内容信息**
- 点击"合同内容信息"标签
- 截图：`合同内容信息.png`

**9. 支付节点信息**
- 点击"支付节点信息"标签
- 截图：`支付节点信息.png`

**10. 支付节点确认弹窗**
- 点击"支付节点确认"按钮
- 截图：`支付节点确认弹窗.png`
- 点击"取消"关闭

**11. 支付状态确认弹窗**
- 点击"支付状态确认"按钮
- 截图：`支付状态确认弹窗.png`
- 点击"取消"关闭

**12. 节点确认记录**
- 点击"节点确认记录"标签
- 截图：`节点确认记录.png`

**13. 确认记录详情**
- 点击"查看详情"按钮
- 截图：`确认记录详情.png`
- 关闭详情窗口

**14. 确认记录审核**
- 点击"审核"按钮（如有权限）
- 截图：`确认记录审核.png`
- 点击"取消"关闭

**15. 合同附件信息**
- 点击"合同附件信息"标签
- 截图：`合同附件信息.png`

**16. 附件上传界面**
- 点击"上传附件"按钮
- 截图：`附件上传界面.png`
- 点击"取消"关闭

**17. 附件预览界面**
- 点击文件名或"预览"按钮
- 截图：`附件预览界面.png`
- 关闭预览窗口

**18. 合同审批流程**
- 点击"合同审批流程"标签
- 截图：`合同审批流程.png`

## 📏 截图规范

### 技术要求
- **分辨率**: 1920x1080或更高
- **浏览器**: Chrome浏览器，100%缩放
- **格式**: PNG格式
- **范围**: 完整浏览器窗口
- **大小**: 单个文件不超过5MB

### 质量要求
- 页面完全加载，无加载状态
- 数据内容完整显示
- 文字清晰可读
- 界面元素完整

### 命名规范
- 使用清单中的确切文件名
- 中文命名，便于识别
- 不添加时间戳或序号

## 🔍 验证和检查

### 自动验证
```bash
cd docs
python3 verify_screenshots.py
```

### 手动检查
1. 检查文件数量：应有18个PNG文件
2. 检查文件大小：每个文件应在100KB-5MB之间
3. 检查图片质量：打开每个文件确认清晰度
4. 检查内容完整：确认页面内容完整显示

## 📁 文件管理

### 存储位置
```
docs/images/front_system/履约执行/
├── 登录页面.png
├── 首页.png
├── 个人工作台.png
├── 合同管理列表.png
├── 合同编辑抽屉.png
├── 合同创建抽屉.png
├── 合同详情页面.png
├── 合同内容信息.png
├── 支付节点信息.png
├── 支付节点确认弹窗.png
├── 支付状态确认弹窗.png
├── 节点确认记录.png
├── 确认记录详情.png
├── 确认记录审核.png
├── 合同附件信息.png
├── 附件上传界面.png
├── 附件预览界面.png
└── 合同审批流程.png
```

### 备份建议
- 完成后创建备份副本
- 使用版本控制管理
- 定期检查文件完整性

## 🆘 常见问题

### 登录问题
- 检查网络连接
- 确认账号密码正确
- 清除浏览器缓存

### 页面加载问题
- 等待页面完全加载
- 刷新页面重试
- 检查网络稳定性

### 权限问题
- 某些按钮可能因权限不显示
- 联系管理员确认权限
- 记录无法访问的功能

### 截图质量问题
- 调整浏览器缩放为100%
- 使用高分辨率显示器
- 确保截图工具设置正确

## 📞 技术支持

如遇到问题，请：
1. 查看错误日志
2. 参考常见问题解决方案
3. 联系技术支持人员

---
**预计完成时间**: 60-90分钟
**建议执行人**: 产品经理或测试人员
**完成标志**: 18个截图文件全部生成且质量合格
