#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from datetime import datetime

class PRDUpdater:
    def __init__(self):
        self.prd_file = "prd履约执行.md"
        self.screenshot_dir = "./images/front_system/履约执行/"
        self.required_screenshots = [
            "登录页面.png",
            "首页.png", 
            "个人工作台.png",
            "合同管理列表.png",
            "合同编辑抽屉.png",
            "合同创建抽屉.png",
            "合同详情页面.png",
            "合同内容信息.png",
            "支付节点信息.png",
            "支付节点确认弹窗.png",
            "支付状态确认弹窗.png",
            "节点确认记录.png",
            "确认记录详情.png",
            "确认记录审核.png",
            "合同附件信息.png",
            "附件上传界面.png",
            "附件预览界面.png",
            "合同审批流程.png"
        ]
        
    def check_screenshots_exist(self):
        """检查截图文件是否存在"""
        existing_files = []
        missing_files = []
        
        for filename in self.required_screenshots:
            filepath = os.path.join(self.screenshot_dir, filename)
            if os.path.exists(filepath):
                existing_files.append(filename)
            else:
                missing_files.append(filename)
        
        return existing_files, missing_files
    
    def update_test_status(self, page_name, status="✅ 已完成"):
        """更新页面测试状态"""
        with open(self.prd_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找对应页面的测试状态行
        pattern = rf'(\*\*测试状态\*\*: )⏳ 待测试'
        replacement = rf'\1{status}'
        
        # 更新测试时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        time_pattern = r'(\*\*测试时间\*\*: )待补充'
        time_replacement = rf'\1{current_time}'
        
        content = re.sub(pattern, replacement, content)
        content = re.sub(time_pattern, time_replacement, content)
        
        with open(self.prd_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def add_missing_page_sections(self):
        """为缺失的页面添加测试结果模板"""
        page_templates = {
            "合同编辑抽屉.png": self.get_edit_drawer_template(),
            "合同创建抽屉.png": self.get_create_drawer_template(),
            "合同详情页面.png": self.get_detail_page_template(),
            "合同内容信息.png": self.get_content_info_template(),
            "支付节点信息.png": self.get_payment_node_template(),
            "支付节点确认弹窗.png": self.get_node_confirm_template(),
            "支付状态确认弹窗.png": self.get_status_confirm_template(),
            "节点确认记录.png": self.get_confirm_record_template(),
            "确认记录详情.png": self.get_record_detail_template(),
            "确认记录审核.png": self.get_record_audit_template(),
            "合同附件信息.png": self.get_attachment_info_template(),
            "附件上传界面.png": self.get_upload_interface_template(),
            "附件预览界面.png": self.get_preview_interface_template(),
            "合同审批流程.png": self.get_approval_flow_template()
        }
        
        with open(self.prd_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查哪些页面缺少测试结果模板
        for page_name, template in page_templates.items():
            page_title = page_name.replace('.png', '')
            if f"### {page_title}" not in content:
                # 在合适的位置插入模板
                content += f"\n\n{template}"
        
        with open(self.prd_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def get_edit_drawer_template(self):
        return """### 2.5 合同编辑抽屉

![合同编辑抽屉](./images/front_system/履约执行/合同编辑抽屉.png)

**访问路径**: 合同管理列表 → 点击编辑按钮
**功能定位**: 合同信息编辑界面

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 抽屉正常打开
- [ ] 表单数据回填正确
- [ ] 字段验证有效
- [ ] 保存功能正常
- [ ] 取消功能有效

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_create_drawer_template(self):
        return """### 2.6 合同创建抽屉

![合同创建抽屉](./images/front_system/履约执行/合同创建抽屉.png)

**访问路径**: 合同管理列表 → 点击创建合同按钮
**功能定位**: 新合同创建界面

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 抽屉正常打开
- [ ] 表单字段完整
- [ ] 必填项验证有效
- [ ] 创建功能正常
- [ ] 取消功能有效

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_detail_page_template(self):
        return """### 2.7 合同详情页面

![合同详情页面](./images/front_system/履约执行/合同详情页面.png)

**访问路径**: 合同管理列表 → 点击合同名称
**功能定位**: 合同详细信息展示和管理中心

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 页面正常加载
- [ ] 标签页切换正常
- [ ] 数据显示完整
- [ ] 操作按钮有效
- [ ] 工作流功能正常

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_content_info_template(self):
        return """### 2.8 合同内容信息

![合同内容信息](./images/front_system/履约执行/合同内容信息.png)

**访问路径**: 合同详情页面 → 合同内容信息标签
**功能定位**: 合同基本信息展示

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 基本信息显示完整
- [ ] 字段格式正确
- [ ] 关联信息准确
- [ ] 编辑功能正常

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_payment_node_template(self):
        return """### 2.9 支付节点信息

![支付节点信息](./images/front_system/履约执行/支付节点信息.png)

**访问路径**: 合同详情页面 → 支付节点信息标签
**功能定位**: 支付计划和进度管理

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 节点列表显示正常
- [ ] 进度计算准确
- [ ] 状态更新及时
- [ ] 操作功能有效

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_node_confirm_template(self):
        return """### 2.10 支付节点确认弹窗

![支付节点确认弹窗](./images/front_system/履约执行/支付节点确认弹窗.png)

**访问路径**: 支付节点信息 → 点击确认按钮
**功能定位**: 支付节点确认操作

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 弹窗正常显示
- [ ] 确认功能有效
- [ ] 数据提交正常
- [ ] 状态更新及时

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_status_confirm_template(self):
        return """### 2.11 支付状态确认弹窗

![支付状态确认弹窗](./images/front_system/履约执行/支付状态确认弹窗.png)

**访问路径**: 支付节点信息 → 点击状态确认按钮
**功能定位**: 支付状态确认操作

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 弹窗正常显示
- [ ] 状态选择有效
- [ ] 确认功能正常
- [ ] 数据更新及时

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_confirm_record_template(self):
        return """### 2.12 节点确认记录

![节点确认记录](./images/front_system/履约执行/节点确认记录.png)

**访问路径**: 合同详情页面 → 节点确认记录标签
**功能定位**: 确认记录历史查看

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 记录列表显示正常
- [ ] 时间排序正确
- [ ] 详情查看有效
- [ ] 审核功能正常

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_record_detail_template(self):
        return """### 2.13 确认记录详情

![确认记录详情](./images/front_system/履约执行/确认记录详情.png)

**访问路径**: 节点确认记录 → 点击查看详情
**功能定位**: 确认记录详细信息查看

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 详情信息完整
- [ ] 附件显示正常
- [ ] 操作记录准确
- [ ] 关闭功能有效

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_record_audit_template(self):
        return """### 2.14 确认记录审核

![确认记录审核](./images/front_system/履约执行/确认记录审核.png)

**访问路径**: 节点确认记录 → 点击审核按钮
**功能定位**: 确认记录审核操作

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 审核界面显示正常
- [ ] 审核意见填写有效
- [ ] 审核结果提交正常
- [ ] 状态更新及时

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_attachment_info_template(self):
        return """### 2.15 合同附件信息

![合同附件信息](./images/front_system/履约执行/合同附件信息.png)

**访问路径**: 合同详情页面 → 合同附件信息标签
**功能定位**: 合同相关文档管理

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 附件列表显示正常
- [ ] 上传功能有效
- [ ] 下载功能正常
- [ ] 预览功能可用
- [ ] 删除功能有效

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_upload_interface_template(self):
        return """### 2.16 附件上传界面

![附件上传界面](./images/front_system/履约执行/附件上传界面.png)

**访问路径**: 合同附件信息 → 点击上传按钮
**功能定位**: 文档上传操作

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 上传界面显示正常
- [ ] 文件选择有效
- [ ] 上传进度显示
- [ ] 上传完成提示
- [ ] 取消功能有效

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_preview_interface_template(self):
        return """### 2.17 附件预览界面

![附件预览界面](./images/front_system/履约执行/附件预览界面.png)

**访问路径**: 合同附件信息 → 点击预览按钮
**功能定位**: 文档在线预览

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 预览界面显示正常
- [ ] 文档内容清晰
- [ ] 缩放功能有效
- [ ] 下载功能正常
- [ ] 关闭功能有效

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def get_approval_flow_template(self):
        return """### 2.18 合同审批流程

![合同审批流程](./images/front_system/履约执行/合同审批流程.png)

**访问路径**: 合同详情页面 → 合同审批流程标签
**功能定位**: 工作流状态和操作

#### 实际测试结果
**测试状态**: ⏳ 待测试
**测试时间**: 待补充
**测试人员**: 产品经理+测试人员

**功能验证清单**:
- [ ] 流程图显示正常
- [ ] 当前节点高亮
- [ ] 审批历史完整
- [ ] 操作按钮有效
- [ ] 流程跳转正常

**发现的问题**:
（待测试后补充）

**改进建议**:
（待测试后补充）"""
    
    def run_update(self):
        """执行PRD文档更新"""
        print("🚀 开始更新PRD文档...")
        
        # 检查截图文件
        existing_files, missing_files = self.check_screenshots_exist()
        print(f"📊 截图完成情况: {len(existing_files)}/{len(self.required_screenshots)}")
        
        # 添加缺失的页面模板
        self.add_missing_page_sections()
        print("📝 已添加缺失的页面测试模板")
        
        # 更新已完成截图的测试状态
        for filename in existing_files:
            page_name = filename.replace('.png', '')
            self.update_test_status(page_name)
        
        print(f"✅ 已更新 {len(existing_files)} 个页面的测试状态")
        
        if missing_files:
            print(f"⏳ 还有 {len(missing_files)} 个页面待测试:")
            for filename in missing_files:
                print(f"   - {filename}")
        else:
            print("🎉 所有页面测试完成！")
        
        print("📄 PRD文档更新完成")

def main():
    updater = PRDUpdater()
    updater.run_update()

if __name__ == "__main__":
    main()
