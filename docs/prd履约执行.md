# 履约执行模块产品设计文档

## 1. 模块概述

履约执行模块是项目管理系统中负责合同履约过程管理的核心模块，主要包括合同管理、支付节点管理、合同变更申请、节点确认记录等功能。该模块确保合同从签署到执行完成的全生命周期管理。

### 1.1 模块功能架构

```mermaid
graph TB
    A[履约执行模块] --> B[合同管理]
    A --> C[支付节点管理]
    A --> D[合同变更管理]
    A --> E[节点确认管理]
    A --> F[合同附件管理]
    A --> G[审批流程管理]

    B --> B1[合同列表]
    B --> B2[合同详情]
    B --> B3[合同编辑]
    B --> B4[合同删除]

    C --> C1[支付节点配置]
    C --> C2[支付进度跟踪]
    C --> C3[节点状态管理]

    D --> D1[变更申请]
    D --> D2[变更审批]
    D --> D3[变更记录]

    E --> E1[节点确认]
    E --> E2[确认记录]
    E --> E3[审核流程]

    F --> F1[附件上传]
    F --> F2[附件管理]
    F --> F3[文档关联]

    G --> G1[流程发起]
    G --> G2[审批处理]
    G --> G3[流程跟踪]
```

### 1.2 业务流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant CM as 合同管理
    participant PN as 支付节点
    participant WF as 工作流
    participant NC as 节点确认
    participant AM as 附件管理

    U->>CM: 创建合同
    CM->>PN: 配置支付节点
    CM->>WF: 发起审批流程
    WF-->>CM: 审批通过
    CM->>U: 合同生效

    U->>PN: 支付节点到期
    PN->>NC: 发起节点确认
    NC->>WF: 确认审批
    WF-->>NC: 确认通过
    NC->>PN: 更新节点状态

    U->>AM: 上传支付凭证
    AM->>NC: 关联确认记录
    NC->>PN: 完成支付确认
```

## 2. 页面功能设计

### 2.1 合同管理列表页面

![合同管理列表](./images/front_system/履约执行/合同管理列表.png)

**访问路径**: 个人工作台 → 合同管理 → 合同管理列表
**测试账号**: heqiang / Abc123456789!
**测试环境**: http://***************:44099/login

#### 页面布局
- 顶部：页面标题和操作按钮区域
- 中部：筛选条件和搜索区域
- 主体：合同列表表格
- 底部：分页组件

#### 组件说明
1. 工具栏区域
   - 删除按钮：批量删除选中的合同
   - 筛选器：支持多条件筛选

2. 表格区域
   - 合同编号：点击可跳转到合同详情
   - 合同名称：点击可跳转到合同详情
   - 所属项目：点击可跳转到项目详情
   - 合同状态：显示当前合同状态
   - 合同类别：合同分类信息
   - 合同类型：合同类型信息
   - 供应商：签约供应商名称
   - 合同总金额：格式化显示金额
   - 合同支付进度：进度条显示支付百分比
   - 合同负责人：负责人姓名
   - 合同开始日期：YYYY-MM-DD格式
   - 合同结束日期：YYYY-MM-DD格式
   - 合同签订日期：YYYY-MM-DD格式
   - 创建时间：YYYY-MM-DD HH:mm:ss格式
   - 操作列：查看、编辑、删除按钮

3. 操作事件
   - 查看：跳转到合同详情页面
   - 编辑：打开编辑抽屉（仅草稿状态可编辑）
   - 删除：删除确认后删除合同（仅草稿状态可删除）
   - 批量删除：删除选中的多个合同

#### 合同编辑界面
![合同编辑抽屉](./images/front_system/履约执行/合同编辑抽屉.png)

#### 合同创建界面
![合同创建抽屉](./images/front_system/履约执行/合同创建抽屉.png)

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(ContractManageIndex.vue)
    participant A as API层(Api.js)
    participant C as ProjectContractController
    participant S as ProjectContractService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant P as 权限服务(PMI)
    participant M as 消息中心(MSC)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据
    A-->>F: 权限验证通过

    F->>A: getContractList() 获取合同列表
    A->>C: POST /pas/projectContract/userPage
    Note over A,C: 前端文件: ContractManageIndex.vue<br/>调用函数: tableOptions.api<br/>权限验证: 用户合同数据权限
    C->>S: ProjectContractService.userPage()
    Note over C,S: Controller: ProjectContractController<br/>Service: ProjectContractService
    S->>R: 查询缓存 contract:user:list:${userId}:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询用户合同数据
        Note over S,D: SQL: SELECT pc.*, p.project_name, u.user_name as principal_name<br/>FROM project_contract pc<br/>LEFT JOIN project p ON pc.project_id = p.id<br/>LEFT JOIN sys_user u ON pc.principal = u.id<br/>WHERE pc.is_deleted = 0 AND pc.principal = ?<br/>ORDER BY pc.create_time DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 contract:user:list:${userId}:${searchHash} (30分钟)
    end
    S-->>C: Page<ProjectContractVO>
    C-->>A: ResponseDTO<Page<ProjectContractVO>>
    A-->>F: 渲染合同列表表格

    F->>A: deleteContract() 删除合同
    A->>C: DELETE /pas/projectContract
    C->>S: ProjectContractService.remove()
    S->>D: 逻辑删除合同数据
    Note over S,D: SQL: UPDATE project_contract<br/>SET is_deleted = 1, update_time = NOW()<br/>WHERE id IN (?) AND data_status = 101
    D-->>S: 删除成功
    S->>R: 清除相关缓存 contract:user:list:${userId}:*
    S->>M: 发送删除通知消息
    Note over S,M: 消息类型: 合同删除通知<br/>接收人: 相关人员
    M-->>S: 消息发送成功
    S-->>C: 返回删除结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示删除成功提示，刷新列表

    F->>A: editContract() 编辑合同
    A->>F: 打开编辑抽屉 AddContractDrawer
    Note over A,F: 组件: AddContractDrawer<br/>传递参数: type='edit', editData=record
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 合同编号 | number | project_contract | number | 直接映射 | 合同的唯一编号标识 |
| 合同名称 | name | project_contract | name | 直接映射 | 合同的业务名称 |
| 所属项目 | projectName | project_contract + project | project_id → project_name | 关联查询 | 通过project_id关联项目表获取项目名称 |
| 合同状态 | dataStatus | project_contract | data_status | 枚举转换 | 101-草稿、102-待审核、103-已审核等 |
| 合同类别 | contractCategoryName | project_contract + dict | contract_category → category_name | 字典转换 | 通过字典表获取类别名称 |
| 合同类型 | contractTypeName | project_contract + dict | contract_type → type_name | 字典转换 | 通过字典表获取类型名称 |
| 供应商 | signedMainName | contract_supplier_signed_main | signed_main_name | 关联查询 | 关联供应商签约主体表 |
| 合同总金额 | contractMoney | project_contract | contract_money | 金额格式化 | DECIMAL类型，显示为货币格式 |
| 合同支付进度 | paymentSchedule | 计算字段 | (已支付金额/合同总金额)*100 | 百分比计算 | 基于支付节点计算支付进度 |
| 合同负责人 | principalName | project_contract + sys_user | principal → user_name | 关联查询 | 通过principal关联用户表获取姓名 |
| 合同开始日期 | startDate | project_contract | start_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 合同结束日期 | endDate | project_contract | end_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 合同签订日期 | signDate | project_contract | sign_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 创建时间 | createTime | project_contract | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 合同编号 | numberSearch | number | LIKE '%value%' | 模糊查询合同编号 |
| 合同名称 | nameSearch | name | LIKE '%value%' | 模糊查询合同名称 |
| 项目名称 | projectNameSearch | project_id → project_name | LIKE '%value%' | 通过关联查询项目名称 |
| 供应商名称 | signedMainNameSearch | signed_main_name | LIKE '%value%' | 模糊查询供应商名称 |
| 合同状态 | dataStatusSearch | data_status | = 'value' | 精确匹配状态值 |

#### 数据异常排查指南

**合同列表为空**：
1. 检查 project_contract 表的 is_deleted = 0 条件
2. 确认用户数据权限范围（principal字段）
3. 检查筛选条件是否过于严格

**支付进度显示异常**：
1. 检查 contract_pay_node 表中的支付节点数据
2. 确认支付金额计算逻辑
3. 检查支付状态更新机制

**关联项目显示异常**：
1. 检查 project 表中对应记录是否存在
2. 确认 project_id 字段值是否有效
3. 检查项目是否被删除

#### API接口
- POST /pas/projectContract/userPage - 获取我的合同分页列表
- DELETE /pas/projectContract - 批量删除合同
- GET /pas/projectContract/all/{id} - 获取合同所有信息
- PUT /pas/projectContract - 编辑合同信息
- POST /pas/projectContract - 创建合同

### 2.2 合同详情页面

![合同详情](./images/front_system/履约执行/合同详情页面.png)

**访问路径**: 合同管理列表 → 点击合同名称或查看按钮
**功能说明**: 展示合同的完整信息，包括基本信息、支付节点、确认记录等

#### 页面布局
- 顶部：合同基本信息和操作按钮
- 左侧：功能菜单导航
- 右侧：详细内容展示区域
- 底部：工作流操作按钮

#### 组件说明
1. 头部信息区域
   - 合同编号：显示合同唯一标识
   - 合同名称：显示合同标题
   - 操作按钮：编辑、发起流程等

2. 左侧菜单导航
   - 合同内容信息：基本信息展示
   - 支付节点信息：支付计划和进度
   - 节点确认记录：确认历史记录
   - 合同附件信息：相关文档管理
   - 合同审批流程：工作流状态

3. 内容展示区域
   - 根据左侧菜单选择显示对应内容
   - 支持表格、表单、文件列表等多种展示形式

4. 底部工作流区域
   - 审批按钮：同意、拒绝、退回等
   - 审批意见：填写审批意见
   - 流程图：显示审批流程状态

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(ContractManageDetail.vue)
    participant A as API层(Api.js)
    participant C as ProjectContractController
    participant S as ProjectContractService
    participant W as 工作流服务(WF)
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)

    F->>A: 页面初始化 onMounted()
    A->>C: GET /pas/projectContract/all/{id}
    Note over A,C: 前端文件: ContractManageDetail.vue<br/>调用函数: getData()<br/>获取合同所有信息
    C->>S: ProjectContractService.allInfo()
    Note over C,S: Controller: ProjectContractController<br/>Service: ProjectContractService
    S->>R: 查询缓存 contract:detail:${contractId}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询合同详细信息
        Note over S,D: SQL: SELECT pc.*, p.project_name, u.user_name as principal_name<br/>FROM project_contract pc<br/>LEFT JOIN project p ON pc.project_id = p.id<br/>LEFT JOIN sys_user u ON pc.principal = u.id<br/>WHERE pc.id = ? AND pc.is_deleted = 0
        D-->>S: 返回合同详情
        S->>D: 查询支付节点信息
        Note over S,D: SQL: SELECT * FROM contract_pay_node<br/>WHERE contract_id = ? AND is_deleted = 0<br/>ORDER BY node_order ASC
        D-->>S: 返回支付节点列表
        S->>D: 查询确认记录信息
        Note over S,D: SQL: SELECT cpnc.*, u.user_name as confirm_user_name<br/>FROM contract_pay_node_confirm cpnc<br/>LEFT JOIN sys_user u ON cpnc.confirm_user_id = u.id<br/>WHERE cpnc.contract_id = ? ORDER BY cpnc.create_time DESC
        D-->>S: 返回确认记录列表
        S->>D: 查询附件信息
        Note over S,D: SQL: SELECT * FROM document<br/>WHERE business_id = ? AND business_type = 'CONTRACT'<br/>AND is_deleted = 0 ORDER BY create_time DESC
        D-->>S: 返回附件列表
        S->>R: 更新缓存 contract:detail:${contractId} (1小时)
    end
    S-->>C: ProjectContractAllInfoVO
    C-->>A: ResponseDTO<ProjectContractAllInfoVO>
    A-->>F: 渲染合同详情页面

    F->>A: editContract() 编辑合同
    A->>C: PUT /pas/projectContract
    C->>S: ProjectContractService.edit()
    S->>D: 更新合同信息
    Note over S,D: SQL: UPDATE project_contract<br/>SET name = ?, contract_money = ?, start_date = ?,<br/>end_date = ?, description = ?, update_time = NOW()<br/>WHERE id = ? AND data_status = 101
    D-->>S: 更新成功
    S->>R: 清除缓存 contract:detail:${contractId}
    S->>M: 发送更新通知消息
    Note over S,M: 消息类型: 合同更新通知<br/>接收人: 相关人员
    M-->>S: 消息发送成功
    S-->>C: 返回更新结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示更新成功提示，刷新页面

    F->>A: startWorkflow() 发起审批流程
    A->>W: POST /wf/flowTemplateBusiness/one/start
    Note over A,W: 工作流类型: 合同审批<br/>业务ID: contractId<br/>申请人: 当前用户
    W-->>A: 返回流程实例ID
    A->>C: 更新合同工作流状态
    C->>S: 更新合同状态为待审核
    S->>D: 更新合同状态
    Note over S,D: SQL: UPDATE project_contract<br/>SET data_status = 102, workflow_instance_id = ?<br/>WHERE id = ?
    D-->>S: 更新成功
    S->>M: 发送审批通知消息
    Note over S,M: 消息类型: 合同审批通知<br/>接收人: 审批人
    M-->>S: 消息发送成功
    S-->>C: 返回结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示发起成功提示
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 合同基本信息 | basicData | project_contract | 所有基本字段 | 对象映射 | 合同主表的所有基本信息 |
| 支付节点列表 | payNodeList | contract_pay_node | 所有节点字段 | 数组映射 | 合同支付节点表的所有记录 |
| 确认记录列表 | confirmList | contract_pay_node_confirm | 所有确认字段 | 数组映射 | 节点确认记录表的所有记录 |
| 附件列表 | documentList | document | 所有文档字段 | 数组映射 | 文档表中关联的所有附件 |
| 工作流信息 | workflowInfo | workflow_instance | 工作流相关字段 | 对象映射 | 工作流实例表的相关信息 |

#### 数据异常排查指南

**合同详情加载失败**：
1. 检查合同ID是否有效
2. 确认用户是否有查看权限
3. 检查合同是否被删除

**支付节点信息异常**：
1. 检查 contract_pay_node 表数据完整性
2. 确认节点顺序是否正确
3. 检查节点状态更新逻辑

**工作流状态异常**：
1. 检查工作流服务是否正常
2. 确认 workflow_instance_id 是否正确
3. 检查工作流模板配置

#### API接口
- GET /pas/projectContract/all/{id} - 获取合同所有信息
- PUT /pas/projectContract - 编辑合同信息
- POST /wf/flowTemplateBusiness/one/start - 发起工作流

### 2.3 支付节点信息页面

![支付节点信息](./images/front_system/履约执行/支付节点信息.png)

**访问路径**: 合同详情页面 → 支付节点信息标签页
**功能说明**: 展示合同的支付节点配置、进度跟踪和状态管理

#### 页面布局
- 顶部：节点统计信息
- 中部：支付节点列表表格
- 底部：操作按钮区域

#### 组件说明
1. 统计信息区域
   - 节点总数：显示支付节点总数量
   - 已完成节点：已支付完成的节点数
   - 待支付节点：等待支付的节点数
   - 支付进度：整体支付进度百分比

2. 节点列表表格
   - 节点名称：支付节点的名称
   - 节点类型：支付类型（预付款、进度款、尾款等）
   - 计划支付日期：预计支付时间
   - 支付金额：该节点的支付金额
   - 支付比例：占合同总金额的比例
   - 节点状态：未开始、进行中、已完成、已逾期
   - 实际支付日期：实际完成支付的时间
   - 操作：确认支付、查看详情等

#### 支付节点确认界面
![支付节点确认弹窗](./images/front_system/履约执行/支付节点确认弹窗.png)

#### 支付状态确认界面
![支付状态确认弹窗](./images/front_system/履约执行/支付状态确认弹窗.png)

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(NodeInfo.vue)
    participant A as API层(Api.js)
    participant C as ContractPayNodeController
    participant S as ContractPayNodeService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)
    participant RMQ as RabbitMQ

    F->>A: 页面初始化 onMounted()
    A->>C: GET /pas/contractPayNode/list/{contractId}
    Note over A,C: 前端文件: NodeInfo.vue<br/>调用函数: getPayNodeList()<br/>获取支付节点列表
    C->>S: ContractPayNodeService.getByContractId()
    Note over C,S: Controller: ContractPayNodeController<br/>Service: ContractPayNodeService
    S->>R: 查询缓存 contract:paynode:list:${contractId}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询支付节点列表
        Note over S,D: SQL: SELECT cpn.*, u.user_name as responsible_name<br/>FROM contract_pay_node cpn<br/>LEFT JOIN sys_user u ON cpn.responsible_person = u.id<br/>WHERE cpn.contract_id = ? AND cpn.is_deleted = 0<br/>ORDER BY cpn.node_order ASC
        D-->>S: 返回节点列表
        S->>R: 更新缓存 contract:paynode:list:${contractId} (1小时)
    end
    S-->>C: List<ContractPayNodeVO>
    C-->>A: ResponseDTO<List<ContractPayNodeVO>>
    A-->>F: 渲染支付节点列表

    F->>A: updateNodeStatus() 更新节点状态
    A->>C: PUT /pas/contractPayNode/status
    C->>S: ContractPayNodeService.updateStatus()
    S->>D: 更新节点状态
    Note over S,D: SQL: UPDATE contract_pay_node<br/>SET node_status = ?, actual_pay_date = ?,<br/>update_time = NOW(), update_by = ?<br/>WHERE id = ?
    D-->>S: 更新成功
    S->>R: 清除缓存 contract:paynode:list:${contractId}

    alt 节点状态为已完成
        S->>RMQ: 发送支付完成消息
        Note over S,RMQ: 队列: payment.completed<br/>消息内容: 节点ID、合同ID、支付金额
        RMQ-->>S: 消息发送成功
        S->>M: 发送支付完成通知
        Note over S,M: 消息类型: 支付节点完成通知<br/>接收人: 合同负责人、财务人员
        M-->>S: 消息发送成功
    end

    S-->>C: 返回更新结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示更新成功提示，刷新列表

    F->>A: confirmPayment() 确认支付
    A->>C: POST /pas/contractPayNodeConfirm
    C->>S: ContractPayNodeConfirmService.create()
    S->>D: 创建确认记录
    Note over S,D: SQL: INSERT INTO contract_pay_node_confirm<br/>(pay_node_id, contract_id, confirm_type, confirm_status,<br/>confirm_user_id, confirm_comment, create_time)<br/>VALUES (?, ?, ?, ?, ?, ?, NOW())
    D-->>S: 插入成功
    S->>M: 发送确认通知
    Note over S,M: 消息类型: 支付确认通知<br/>接收人: 审核人员
    M-->>S: 消息发送成功
    S-->>C: 返回确认结果
    C-->>A: ResponseDTO<String> confirmId
    A-->>F: 显示确认成功提示
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 节点名称 | nodeName | contract_pay_node | node_name | 直接映射 | 支付节点的业务名称 |
| 节点类型 | nodeType | contract_pay_node | node_type | 枚举转换 | 1-预付款、2-进度款、3-尾款等 |
| 计划支付日期 | planPayDate | contract_pay_node | plan_pay_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 支付金额 | payAmount | contract_pay_node | pay_amount | 金额格式化 | DECIMAL类型，显示为货币格式 |
| 支付比例 | payRatio | contract_pay_node | pay_ratio | 百分比格式化 | 显示为百分比，如30% |
| 节点状态 | nodeStatus | contract_pay_node | node_status | 枚举转换 | 1-未开始、2-进行中、3-已完成、4-已逾期 |
| 实际支付日期 | actualPayDate | contract_pay_node | actual_pay_date | 日期格式化 | YYYY-MM-DD格式显示，可为空 |
| 支付凭证 | payVoucher | contract_pay_node | pay_voucher_url | 文件链接 | 支付凭证文件的URL链接 |
| 负责人 | responsiblePerson | contract_pay_node + sys_user | responsible_person → user_name | 关联查询 | 通过responsible_person关联用户表 |
| 节点描述 | nodeDescription | contract_pay_node | node_description | 直接映射 | 节点的详细描述信息 |

#### 数据异常排查指南

**节点列表为空**：
1. 检查 contract_pay_node 表是否有对应合同的数据
2. 确认 contract_id 参数是否正确
3. 检查节点是否被删除

**支付进度计算错误**：
1. 检查节点金额总和是否等于合同金额
2. 确认节点状态更新是否及时
3. 检查支付比例字段的数据精度

**节点状态更新失败**：
1. 检查用户是否有更新权限
2. 确认节点当前状态是否允许更新
3. 检查数据库约束条件

#### API接口
- GET /pas/contractPayNode/{nodeId} - 获取支付节点详情
- POST /pas/contractPayNode/list/contractId/{contractId} - 根据合同ID获取支付节点列表
- POST /pas/contractPayNode/status/confirm - 支付状态确认
- POST /pas/contractPayNode/page - 支付节点分页查询

### 2.4 节点确认记录页面

![节点确认记录](./images/front_system/履约执行/节点确认记录.png)

**访问路径**: 合同详情页面 → 节点确认记录标签页
**功能说明**: 展示支付节点的确认历史记录和审核状态

#### 页面布局
- 顶部：筛选条件区域
- 中部：确认记录列表表格
- 底部：分页组件

#### 组件说明
1. 筛选条件区域
   - 节点名称：下拉选择支付节点
   - 确认状态：下拉选择确认状态
   - 确认时间范围：日期范围选择器

2. 确认记录表格
   - 节点名称：关联的支付节点名称
   - 确认类型：确认操作的类型
   - 确认状态：待确认、已确认、已拒绝
   - 确认人：执行确认操作的用户
   - 确认时间：确认操作的时间
   - 确认意见：确认时填写的意见
   - 操作：查看详情、审核等

#### 确认记录详情界面
![确认记录详情](./images/front_system/履约执行/确认记录详情.png)

#### 确认记录审核界面
![确认记录审核](./images/front_system/履约执行/确认记录审核.png)

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 节点名称 | nodeName | contract_pay_node_confirm + contract_pay_node | pay_node_id → node_name | 关联查询 | 通过pay_node_id关联支付节点表 |
| 确认类型 | confirmType | contract_pay_node_confirm | confirm_type | 枚举转换 | 1-支付确认、2-完工确认、3-验收确认 |
| 确认状态 | confirmStatus | contract_pay_node_confirm | confirm_status | 枚举转换 | 1-待确认、2-已确认、3-已拒绝 |
| 确认人 | confirmUser | contract_pay_node_confirm + sys_user | confirm_user_id → user_name | 关联查询 | 通过confirm_user_id关联用户表 |
| 确认时间 | confirmTime | contract_pay_node_confirm | confirm_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 确认意见 | confirmComment | contract_pay_node_confirm | confirm_comment | 直接映射 | 确认时填写的意见内容 |
| 附件信息 | attachments | contract_pay_node_confirm | attachment_urls | JSON数组解析 | 解析附件URL数组 |

#### API接口
- POST /pas/contractPayNodeConfirm/page - 获取确认记录分页列表
- POST /pas/contractPayNodeConfirm - 创建确认记录
- PUT /pas/contractPayNodeConfirm - 编辑确认记录
- POST /pas/contractPayNodeConfirm/submit - 提交确认记录
- POST /pas/contractPayNodeConfirm/agree - 同意确认记录
- POST /pas/contractPayNodeConfirm/reject - 驳回确认记录
- GET /pas/contractPayNodeConfirm/{id} - 获取确认记录详情
- DELETE /pas/contractPayNodeConfirm - 批量删除确认记录

### 2.5 合同附件信息页面

![合同附件信息](./images/front_system/履约执行/合同附件信息.png)

**访问路径**: 合同详情页面 → 合同附件信息标签页
**功能说明**: 管理合同相关的所有附件文档

#### 页面布局
- 顶部：上传按钮和操作区域
- 中部：附件列表表格
- 底部：分页组件

#### 组件说明
1. 操作区域
   - 上传附件：支持多文件上传
   - 批量下载：下载选中的附件
   - 批量删除：删除选中的附件

2. 附件列表表格
   - 文件名称：附件的原始文件名
   - 文件类型：文件的MIME类型
   - 文件大小：格式化显示文件大小
   - 上传人：上传文件的用户
   - 上传时间：文件上传的时间
   - 文件描述：文件的说明信息
   - 操作：下载、预览、删除等

#### 附件上传界面
![附件上传界面](./images/front_system/履约执行/附件上传界面.png)

#### 附件预览界面
![附件预览界面](./images/front_system/履约执行/附件预览界面.png)

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 文件名称 | fileName | document | file_name | 直接映射 | 附件的原始文件名 |
| 文件类型 | fileType | document | file_type | 直接映射 | 文件的MIME类型 |
| 文件大小 | fileSize | document | file_size | 大小格式化 | 字节转换为KB、MB等 |
| 上传人 | uploadUser | document + sys_user | create_by → user_name | 关联查询 | 通过create_by关联用户表 |
| 上传时间 | uploadTime | document | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 文件描述 | fileDescription | document | description | 直接映射 | 文件的说明信息 |
| 文件URL | fileUrl | document | file_url | 直接映射 | 文件的访问链接 |
| 关联业务 | businessType | document | business_type | 枚举转换 | 关联的业务类型标识 |

#### API接口
- POST /res/file/upload - 上传文件
- POST /pas/document/create - 创建文档记录
- GET /pas/document/list/{dataId} - 获取业务关联的文档列表
- DELETE /pas/document/del/ids - 批量删除文档
- GET /res/file/download/{fileId} - 下载文件
- POST /pas/common/file - 创建附件关联
- DELETE /pas/common/file/{fileId} - 删除附件

## 3. 技术实现

### 3.1 第三方服务集成

#### 3.1.1 工作流服务(WF)

**服务说明**：负责合同审批流程管理

**集成接口**：
- POST /wf/flowTemplateBusiness/one/start - 启动合同审批流程
- GET /wf/processInstance/{instanceId} - 获取流程实例信息
- POST /wf/task/complete - 完成审批任务
- GET /wf/task/list - 获取待办任务列表

**使用场景**：
- 合同审批流程
- 变更申请审批
- 节点确认审批

#### 3.1.2 消息中心服务(MSC)

**服务说明**：负责系统消息通知

**集成接口**：
- POST /msc/message - 发送消息通知
- PUT /msc/message/read - 标记消息已读
- GET /msc/message/list - 获取消息列表

**使用场景**：
- 合同状态变更通知
- 支付节点到期提醒
- 审批结果通知

#### 3.1.3 文件服务(RES)

**服务说明**：负责文件上传下载管理

**集成接口**：
- POST /res/file/upload - 上传文件
- GET /res/file/download/{fileId} - 下载文件
- DELETE /res/file/{fileId} - 删除文件
- POST /res/file/batch - 批量文件操作

**使用场景**：
- 合同附件管理
- 支付凭证上传
- 确认文档管理

#### 3.1.4 权限服务(PMI)

**服务说明**：负责用户权限验证

**集成接口**：
- GET /pmi/power/function - 获取页面功能权限
- POST /pmi/power/data - 验证数据权限

**使用场景**：
- 页面按钮权限控制
- 数据访问权限验证

### 3.2 数据库设计

#### 3.2.1 合同主表(project_contract)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | - | 主键ID |
| number | VARCHAR | 50 | NOT NULL | - | 合同编号 |
| name | VARCHAR | 200 | NOT NULL | - | 合同名称 |
| project_id | VARCHAR | 32 | NOT NULL | - | 所属项目ID |
| contract_category | VARCHAR | 20 | NULL | - | 合同类别 |
| contract_type | VARCHAR | 20 | NULL | - | 合同类型 |
| contract_money | DECIMAL | 15,2 | NULL | 0.00 | 合同金额 |
| start_date | DATE | - | NULL | - | 合同开始日期 |
| end_date | DATE | - | NULL | - | 合同结束日期 |
| sign_date | DATE | - | NULL | - | 合同签订日期 |
| principal | VARCHAR | 32 | NULL | - | 合同负责人 |
| data_status | INT | - | NOT NULL | 101 | 合同状态 |
| workflow_instance_id | VARCHAR | 32 | NULL | - | 工作流实例ID |
| description | TEXT | - | NULL | - | 合同描述 |
| is_deleted | TINYINT | 1 | NOT NULL | 0 | 是否删除 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| create_by | VARCHAR | 32 | NOT NULL | - | 创建人 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| update_by | VARCHAR | 32 | NULL | - | 更新人 |

#### 3.2.2 支付节点表(contract_pay_node)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | - | 主键ID |
| contract_id | VARCHAR | 32 | NOT NULL | - | 合同ID |
| node_name | VARCHAR | 100 | NOT NULL | - | 节点名称 |
| node_type | INT | - | NOT NULL | - | 节点类型 |
| node_order | INT | - | NOT NULL | - | 节点顺序 |
| pay_amount | DECIMAL | 15,2 | NOT NULL | - | 支付金额 |
| pay_ratio | DECIMAL | 5,2 | NOT NULL | - | 支付比例 |
| plan_pay_date | DATE | - | NULL | - | 计划支付日期 |
| actual_pay_date | DATE | - | NULL | - | 实际支付日期 |
| node_status | INT | - | NOT NULL | 1 | 节点状态 |
| responsible_person | VARCHAR | 32 | NULL | - | 负责人 |
| node_description | TEXT | - | NULL | - | 节点描述 |
| pay_voucher_url | VARCHAR | 500 | NULL | - | 支付凭证URL |
| is_deleted | TINYINT | 1 | NOT NULL | 0 | 是否删除 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| create_by | VARCHAR | 32 | NOT NULL | - | 创建人 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| update_by | VARCHAR | 32 | NULL | - | 更新人 |

#### 3.2.3 节点确认记录表(contract_pay_node_confirm)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | - | 主键ID |
| pay_node_id | VARCHAR | 32 | NOT NULL | - | 支付节点ID |
| contract_id | VARCHAR | 32 | NOT NULL | - | 合同ID |
| confirm_type | INT | - | NOT NULL | - | 确认类型 |
| confirm_status | INT | - | NOT NULL | 1 | 确认状态 |
| confirm_user_id | VARCHAR | 32 | NOT NULL | - | 确认人ID |
| confirm_time | DATETIME | - | NULL | - | 确认时间 |
| confirm_comment | TEXT | - | NULL | - | 确认意见 |
| attachment_urls | JSON | - | NULL | - | 附件URL列表 |
| audit_user_id | VARCHAR | 32 | NULL | - | 审核人ID |
| audit_time | DATETIME | - | NULL | - | 审核时间 |
| audit_comment | TEXT | - | NULL | - | 审核意见 |
| is_deleted | TINYINT | 1 | NOT NULL | 0 | 是否删除 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| create_by | VARCHAR | 32 | NOT NULL | - | 创建人 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| update_by | VARCHAR | 32 | NULL | - | 更新人 |

### 3.3 Redis缓存设计

#### 3.3.1 缓存Key设计规范

| 缓存类型 | Key格式 | 过期时间 | 说明 |
|---------|---------|----------|------|
| 合同列表 | contract:user:list:{userId}:{searchHash} | 30分钟 | 用户合同列表缓存 |
| 合同详情 | contract:detail:{contractId} | 1小时 | 合同详细信息缓存 |
| 支付节点 | contract:paynode:list:{contractId} | 1小时 | 合同支付节点列表缓存 |
| 确认记录 | contract:confirm:list:{contractId} | 30分钟 | 节点确认记录列表缓存 |
| 附件列表 | contract:document:list:{contractId} | 1小时 | 合同附件列表缓存 |

#### 3.3.2 缓存更新策略

**写入策略**：
- 查询时如果缓存不存在，从数据库查询并写入缓存
- 数据更新时，删除相关缓存，下次查询时重新加载

**失效策略**：
- 合同信息更新时，清除合同详情缓存
- 支付节点状态更新时，清除节点列表缓存
- 确认记录创建时，清除确认记录列表缓存

### 3.4 RabbitMQ消息队列设计

#### 3.4.1 消息队列配置

| 队列名称 | 交换机 | 路由键 | 说明 |
|---------|--------|--------|------|
| payment.completed | contract.exchange | payment.completed | 支付完成消息 |
| contract.status.changed | contract.exchange | contract.status.* | 合同状态变更消息 |
| node.confirm.created | contract.exchange | node.confirm.created | 节点确认创建消息 |

#### 3.4.2 消息格式设计

**支付完成消息**：
```json
{
  "nodeId": "节点ID",
  "contractId": "合同ID",
  "payAmount": "支付金额",
  "actualPayDate": "实际支付日期",
  "timestamp": "消息时间戳"
}
```

**合同状态变更消息**：
```json
{
  "contractId": "合同ID",
  "oldStatus": "原状态",
  "newStatus": "新状态",
  "changeReason": "变更原因",
  "timestamp": "消息时间戳"
}
```

### 3.5 物理模型图

#### 3.5.1 履约执行模块ER图

```mermaid
erDiagram
    PROJECT ||--o{ PROJECT_CONTRACT : "包含"
    PROJECT_CONTRACT ||--o{ CONTRACT_PAY_NODE : "包含"
    CONTRACT_PAY_NODE ||--o{ CONTRACT_PAY_NODE_CONFIRM : "确认"
    PROJECT_CONTRACT ||--o{ DOCUMENT : "关联"
    PROJECT_CONTRACT ||--o{ WORKFLOW_INSTANCE : "关联"
    SYS_USER ||--o{ PROJECT_CONTRACT : "负责"
    SYS_USER ||--o{ CONTRACT_PAY_NODE : "负责"
    SYS_USER ||--o{ CONTRACT_PAY_NODE_CONFIRM : "确认"

    PROJECT {
        string id PK
        string project_name
        string project_code
        int project_status
        datetime create_time
    }

    PROJECT_CONTRACT {
        string id PK
        string number UK
        string name
        string project_id FK
        string contract_category
        string contract_type
        decimal contract_money
        date start_date
        date end_date
        date sign_date
        string principal FK
        int data_status
        string workflow_instance_id FK
        text description
        tinyint is_deleted
        datetime create_time
        string create_by
        datetime update_time
        string update_by
    }

    CONTRACT_PAY_NODE {
        string id PK
        string contract_id FK
        string node_name
        int node_type
        int node_order
        decimal pay_amount
        decimal pay_ratio
        date plan_pay_date
        date actual_pay_date
        int node_status
        string responsible_person FK
        text node_description
        string pay_voucher_url
        tinyint is_deleted
        datetime create_time
        string create_by
        datetime update_time
        string update_by
    }

    CONTRACT_PAY_NODE_CONFIRM {
        string id PK
        string pay_node_id FK
        string contract_id FK
        int confirm_type
        int confirm_status
        string confirm_user_id FK
        datetime confirm_time
        text confirm_comment
        json attachment_urls
        string audit_user_id FK
        datetime audit_time
        text audit_comment
        tinyint is_deleted
        datetime create_time
        string create_by
        datetime update_time
        string update_by
    }

    DOCUMENT {
        string id PK
        string business_id FK
        string business_type
        string file_name
        string file_type
        bigint file_size
        string file_url
        text description
        tinyint is_deleted
        datetime create_time
        string create_by
    }

    WORKFLOW_INSTANCE {
        string id PK
        string business_id FK
        string business_type
        string template_id
        int instance_status
        datetime create_time
        string create_by
    }

    SYS_USER {
        string id PK
        string user_name
        string real_name
        string email
        string phone
        int user_status
        datetime create_time
    }
```

### 3.6 技术架构图

#### 3.6.1 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js 3.x]
        B[Element Plus]
        C[Axios]
        D[Vuex]
    end

    subgraph "网关层"
        E[Spring Cloud Gateway]
        F[负载均衡]
        G[限流熔断]
    end

    subgraph "应用层"
        H[履约执行服务]
        I[工作流服务]
        J[消息中心服务]
        K[文件服务]
        L[权限服务]
    end

    subgraph "数据层"
        M[MySQL主库]
        N[MySQL从库]
        O[Redis缓存]
        P[RabbitMQ]
    end

    subgraph "基础设施"
        Q[Docker容器]
        R[Kubernetes]
        S[监控告警]
        T[日志收集]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> H
    E --> I
    E --> J
    E --> K
    E --> L

    H --> M
    H --> N
    H --> O
    H --> P

    I --> M
    J --> M
    K --> M
    L --> M

    Q --> R
    S --> R
    T --> R
```

#### 3.6.2 数据架构图

```mermaid
graph TB
    subgraph "数据源层"
        A[业务数据库]
        B[日志数据库]
        C[配置数据库]
    end

    subgraph "数据处理层"
        D[数据同步]
        E[数据清洗]
        F[数据转换]
    end

    subgraph "数据存储层"
        G[关系型数据库]
        H[缓存数据库]
        I[消息队列]
        J[文件存储]
    end

    subgraph "数据服务层"
        K[数据API]
        L[缓存服务]
        M[搜索服务]
        N[报表服务]
    end

    subgraph "数据应用层"
        O[业务应用]
        P[数据分析]
        Q[监控告警]
        R[报表展示]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F

    F --> G
    F --> H
    F --> I
    F --> J

    G --> K
    H --> L
    I --> M
    J --> N

    K --> O
    L --> O
    M --> P
    N --> R
```

#### 3.6.3 业务架构图

```mermaid
graph TB
    subgraph "业务域"
        A[合同管理域]
        B[支付管理域]
        C[确认管理域]
        D[文档管理域]
        E[流程管理域]
    end

    subgraph "核心能力"
        F[合同生命周期管理]
        G[支付节点管理]
        H[确认审核管理]
        I[文档版本管理]
        J[工作流引擎]
    end

    subgraph "支撑服务"
        K[用户权限服务]
        L[消息通知服务]
        M[文件存储服务]
        N[数据字典服务]
        O[审计日志服务]
    end

    subgraph "外部集成"
        P[财务系统]
        Q[项目管理系统]
        R[供应商系统]
        S[OA系统]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> J

    F --> K
    G --> L
    H --> M
    I --> N
    J --> O

    K --> P
    L --> Q
    M --> R
    N --> S
```

## 4. 总结

履约执行模块作为项目管理系统的核心模块，通过完整的合同生命周期管理、支付节点跟踪、确认审核流程等功能，确保了合同履约过程的规范化和可追溯性。

### 4.1 核心特性

1. **全生命周期管理**：从合同创建到执行完成的完整流程管理
2. **支付节点跟踪**：精确的支付计划和进度监控
3. **多级确认机制**：完善的确认和审核流程
4. **文档版本管理**：完整的附件和文档管理
5. **工作流集成**：灵活的审批流程配置

### 4.2 技术优势

1. **微服务架构**：模块化设计，易于扩展和维护
2. **缓存优化**：Redis缓存提升系统性能
3. **消息队列**：异步处理提高系统响应速度
4. **数据溯源**：完整的字段映射和异常排查机制
5. **第三方集成**：丰富的外部系统集成能力

### 4.3 业务价值

1. **提升效率**：自动化的流程管理减少人工操作
2. **风险控制**：多级审核机制降低业务风险
3. **数据透明**：实时的进度跟踪和状态监控
4. **合规管理**：完整的审计日志和操作记录
5. **决策支持**：丰富的数据分析和报表功能

该文档为履约执行模块的重新开发或重构提供了完整的技术指南，确保开发团队能够快速理解业务需求和技术实现方案。

## 5. 测试指导

### 5.1 测试环境信息

**测试环境地址**: http://***************:44099/login
**测试账号**: heqiang
**测试密码**: Abc123456789!

### 5.2 履约执行模块截图清单

以下是需要截图的所有页面，请按照顺序进行测试并截图：

#### 5.2.1 合同管理模块

| 序号 | 页面名称 | 访问路径 | 截图文件名 | 测试要点 |
|------|----------|----------|------------|----------|
| 1 | 合同管理列表 | 个人工作台 → 合同管理 | 合同管理列表.png | 验证列表展示、筛选、分页功能 |
| 2 | 合同详情-基本信息 | 合同列表 → 点击合同名称 → 合同内容信息 | 合同详情_基本信息.png | 验证合同基本信息展示 |
| 3 | 合同详情-支付节点 | 合同详情 → 支付节点信息 | 支付节点信息.png | 验证支付节点配置和进度 |
| 4 | 合同详情-确认记录 | 合同详情 → 节点确认记录 | 节点确认记录.png | 验证确认记录列表 |
| 5 | 合同详情-附件信息 | 合同详情 → 合同附件信息 | 合同附件信息.png | 验证附件管理功能 |
| 6 | 合同详情-审批流程 | 合同详情 → 合同审批流程 | 合同审批流程.png | 验证工作流状态 |
| 7 | 合同编辑抽屉 | 合同列表 → 编辑按钮 | 合同编辑抽屉.png | 验证合同编辑功能 |
| 8 | 合同创建抽屉 | 合同列表 → 创建合同按钮 | 合同创建抽屉.png | 验证合同创建功能 |

#### 5.2.2 支付节点管理

| 序号 | 页面名称 | 访问路径 | 截图文件名 | 测试要点 |
|------|----------|----------|------------|----------|
| 9 | 支付节点确认弹窗 | 支付节点信息 → 支付节点确认按钮 | 支付节点确认弹窗.png | 验证节点确认功能 |
| 10 | 支付状态确认弹窗 | 支付节点信息 → 支付状态确认按钮 | 支付状态确认弹窗.png | 验证状态确认功能 |
| 11 | 节点详情展示 | 支付节点信息 → 点击节点 | 节点详情展示.png | 验证节点详细信息 |

#### 5.2.3 确认记录管理

| 序号 | 页面名称 | 访问路径 | 截图文件名 | 测试要点 |
|------|----------|----------|------------|----------|
| 12 | 确认记录详情 | 节点确认记录 → 查看详情 | 确认记录详情.png | 验证确认记录详情 |
| 13 | 确认记录审核 | 节点确认记录 → 审核按钮 | 确认记录审核.png | 验证审核功能 |
| 14 | 确认历史记录 | 节点确认记录 → 历史记录 | 确认历史记录.png | 验证历史记录查看 |

#### 5.2.4 附件管理

| 序号 | 页面名称 | 访问路径 | 截图文件名 | 测试要点 |
|------|----------|----------|------------|----------|
| 15 | 附件上传界面 | 合同附件信息 → 上传附件 | 附件上传界面.png | 验证文件上传功能 |
| 16 | 附件预览界面 | 合同附件信息 → 预览按钮 | 附件预览界面.png | 验证文件预览功能 |
| 17 | 附件下载确认 | 合同附件信息 → 下载按钮 | 附件下载确认.png | 验证文件下载功能 |

### 5.3 测试步骤详解

#### 5.3.1 登录系统
1. 打开浏览器，访问 http://***************:44099/login
2. 输入用户名：heqiang
3. 输入密码：Abc123456789!
4. 点击登录按钮
5. 截图：登录成功后的首页

#### 5.3.2 进入履约执行模块
1. 在主导航中找到"个人工作台"
2. 点击进入个人工作台
3. 在左侧菜单中找到"合同管理"
4. 点击进入合同管理模块
5. 截图：合同管理列表页面

#### 5.3.3 测试合同列表功能
1. 验证合同列表数据展示
2. 测试筛选功能（按合同状态、项目名称等）
3. 测试搜索功能（按合同编号、合同名称）
4. 测试分页功能
5. 测试排序功能
6. 截图：包含数据的合同列表页面

#### 5.3.4 测试合同详情功能
1. 点击任意一个合同的名称或查看按钮
2. 进入合同详情页面
3. 验证左侧菜单导航
4. 逐一点击每个标签页：
   - 合同内容信息
   - 支付节点信息
   - 节点确认记录
   - 合同附件信息
   - 合同审批流程
5. 每个标签页都要截图

#### 5.3.5 测试操作功能
1. 测试合同编辑功能（如果有草稿状态的合同）
2. 测试支付节点确认功能
3. 测试支付状态确认功能
4. 测试附件上传功能
5. 测试工作流发起功能
6. 每个操作界面都要截图

### 5.4 截图要求

#### 5.4.1 截图规范
- **分辨率**: 建议使用1920x1080或更高分辨率
- **浏览器**: 建议使用Chrome浏览器
- **缩放比例**: 100%（避免页面变形）
- **截图范围**: 完整的浏览器窗口，包含地址栏
- **文件格式**: PNG格式
- **文件大小**: 单个文件不超过5MB

#### 5.4.2 文件命名规范
- 使用中文名称，便于识别
- 格式：`功能模块_页面名称.png`
- 示例：`合同管理_合同列表.png`

#### 5.4.3 存储路径
所有截图文件存储在：`docs/images/front_system/履约执行/`

### 5.5 测试注意事项

#### 5.5.1 数据准备
- 确保系统中有足够的测试数据
- 包含不同状态的合同（草稿、待审核、已审核等）
- 包含不同类型的支付节点
- 包含确认记录和附件数据

#### 5.5.2 权限验证
- 验证不同状态下的操作按钮显示/隐藏
- 验证用户权限对功能的影响
- 验证数据权限范围

#### 5.5.3 异常情况测试
- 测试空数据状态的页面展示
- 测试网络异常时的错误提示
- 测试操作失败时的错误处理

### 5.6 测试完成后的工作

#### 5.6.1 整理截图
1. 检查所有截图文件是否完整
2. 确认文件命名符合规范
3. 验证图片质量和清晰度

#### 5.6.2 更新文档
1. 将截图文件路径更新到PRD文档中
2. 补充实际测试中发现的功能细节
3. 记录测试过程中发现的问题

#### 5.6.3 问题反馈
如果在测试过程中发现以下问题，请及时反馈：
- 页面加载异常
- 功能操作失败
- 数据显示错误
- 界面布局问题
- 权限控制异常

通过以上详细的测试指导，可以确保履约执行模块的所有功能都得到充分的验证和文档化。

## 6. 自动化工具

### 6.1 自动截图脚本

为了提高截图效率，我们提供了多种自动化工具：

#### 6.1.1 Python自动化脚本
- **文件**: `docs/crawl_contract_execution.py`
- **功能**: 全自动浏览器操作和截图
- **使用**: `python3 crawl_contract_execution.py`
- **特点**:
  - 自动登录和导航
  - 进度跟踪和错误处理
  - 18个页面全自动截图
  - 详细的执行日志

#### 6.1.2 浏览器辅助脚本
- **文件**: `docs/browser_screenshot_guide.js`
- **功能**: 浏览器控制台辅助截图
- **使用**: 在浏览器Console中运行
- **特点**:
  - 无需安装额外软件
  - 交互式操作指导
  - 自动页面导航
  - 手动截图确认

#### 6.1.3 一键执行脚本
- **文件**: `docs/run_screenshot.sh`
- **功能**: 一键环境检查和截图执行
- **使用**: `./run_screenshot.sh`
- **特点**:
  - 自动环境检查
  - 依赖安装
  - 多种截图方式选择
  - 结果验证

### 6.2 截图验证工具

#### 6.2.1 自动验证脚本
- **文件**: `docs/verify_screenshots.py`
- **功能**: 自动检查截图文件质量
- **使用**: `python3 verify_screenshots.py`
- **检查项目**:
  - 文件存在性检查
  - 文件大小验证
  - 图片分辨率检查
  - 质量问题报告

#### 6.2.2 验证报告
自动生成两种格式的验证报告：
- **JSON格式**: `screenshot_report.json`
- **Markdown格式**: `screenshot_report.md`

### 6.3 工具使用流程

```mermaid
flowchart TD
    A[开始] --> B{选择截图方式}
    B -->|自动化| C[运行Python脚本]
    B -->|半自动| D[使用浏览器脚本]
    B -->|手动| E[按操作手册执行]

    C --> F[自动登录导航]
    D --> G[手动登录]
    E --> G

    F --> H[自动截图18个页面]
    G --> I[辅助导航截图]

    H --> J[运行验证脚本]
    I --> J

    J --> K{验证通过?}
    K -->|是| L[更新PRD文档]
    K -->|否| M[修复问题]
    M --> J

    L --> N[完成]
```

### 6.4 工具文件清单

| 文件名 | 类型 | 功能 | 使用场景 |
|--------|------|------|----------|
| crawl_contract_execution.py | Python脚本 | 全自动截图 | 技术人员使用 |
| browser_screenshot_guide.js | JavaScript | 浏览器辅助 | 非技术人员使用 |
| run_screenshot.sh | Shell脚本 | 一键执行 | Linux/Mac环境 |
| verify_screenshots.py | Python脚本 | 质量验证 | 截图完成后验证 |
| 履约执行模块截图完整操作手册.md | 文档 | 详细指导 | 手动操作参考 |
| 履约执行模块手动截图指南.md | 文档 | 快速指导 | 简化操作流程 |

## 7. 文档维护

### 7.1 更新机制

#### 7.1.1 定期更新
- **频率**: 每月检查一次
- **内容**: 页面变更、功能更新
- **责任人**: 产品经理

#### 7.1.2 版本控制
- **工具**: Git版本控制
- **分支**: 文档更新使用独立分支
- **合并**: 经过评审后合并到主分支

#### 7.1.3 变更记录
| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| 1.0 | 2024-12 | 初始版本创建 | 产品团队 |
| 1.1 | 待定 | 功能更新 | 待定 |

### 7.2 质量保证

#### 7.2.1 文档评审
- **技术评审**: 开发团队验证技术实现
- **业务评审**: 产品团队验证业务逻辑
- **测试评审**: 测试团队验证测试覆盖

#### 7.2.2 持续改进
- 收集用户反馈
- 定期优化文档结构
- 更新最佳实践

### 7.3 使用反馈

如果在使用过程中发现问题或有改进建议，请：
1. 记录具体问题和建议
2. 提交到项目管理系统
3. 联系文档维护团队

---

**文档版本**: 1.0
**最后更新**: 2024年12月
**维护团队**: 产品开发团队
**联系方式**: [团队邮箱]