# 履约执行模块测试清单

## 🔐 测试环境信息
- **测试地址**: http://183.136.206.207:44099/login
- **测试账号**: heqiang
- **测试密码**: Abc123456789!

## 📋 测试清单

### ✅ 第一阶段：登录和导航测试

- [ ] 1. 登录系统
  - 访问登录页面
  - 输入账号密码
  - 验证登录成功
  - 截图：`登录页面.png`、`首页.png`

- [ ] 2. 进入履约执行模块
  - 点击"个人工作台"
  - 找到"合同管理"菜单
  - 进入合同管理列表
  - 截图：`个人工作台.png`、`合同管理入口.png`

### ✅ 第二阶段：合同管理功能测试

- [ ] 3. 合同管理列表页面
  - 验证合同列表数据展示
  - 测试筛选功能
  - 测试搜索功能
  - 测试分页功能
  - 截图：`合同管理列表.png`

- [ ] 4. 合同详情页面
  - 点击合同名称进入详情
  - 验证页面布局
  - 验证左侧菜单导航
  - 截图：`合同详情页面.png`

- [ ] 5. 合同内容信息标签页
  - 点击"合同内容信息"标签
  - 验证基本信息展示
  - 验证字段完整性
  - 截图：`合同详情_基本信息.png`

### ✅ 第三阶段：支付节点功能测试

- [ ] 6. 支付节点信息标签页
  - 点击"支付节点信息"标签
  - 验证节点列表展示
  - 验证节点状态显示
  - 验证支付进度展示
  - 截图：`支付节点信息.png`

- [ ] 7. 支付节点确认功能
  - 点击"支付节点确认"按钮
  - 验证确认弹窗
  - 测试确认流程
  - 截图：`支付节点确认弹窗.png`

- [ ] 8. 支付状态确认功能
  - 点击"支付状态确认"按钮
  - 验证状态确认弹窗
  - 测试状态更新
  - 截图：`支付状态确认弹窗.png`

### ✅ 第四阶段：确认记录功能测试

- [ ] 9. 节点确认记录标签页
  - 点击"节点确认记录"标签
  - 验证确认记录列表
  - 验证筛选功能
  - 截图：`节点确认记录.png`

- [ ] 10. 确认记录详情
  - 点击查看详情按钮
  - 验证详情信息展示
  - 验证附件信息
  - 截图：`确认记录详情.png`

- [ ] 11. 确认记录审核
  - 测试审核功能（如有权限）
  - 验证审核流程
  - 验证状态变更
  - 截图：`确认记录审核.png`

### ✅ 第五阶段：附件管理功能测试

- [ ] 12. 合同附件信息标签页
  - 点击"合同附件信息"标签
  - 验证附件列表展示
  - 验证文件信息显示
  - 截图：`合同附件信息.png`

- [ ] 13. 附件上传功能
  - 点击上传附件按钮
  - 测试文件选择
  - 测试上传流程
  - 截图：`附件上传界面.png`

- [ ] 14. 附件预览功能
  - 点击文件名或预览按钮
  - 验证文件预览
  - 测试不同文件类型
  - 截图：`附件预览界面.png`

- [ ] 15. 附件下载功能
  - 点击下载按钮
  - 验证下载流程
  - 确认文件完整性
  - 截图：`附件下载确认.png`

### ✅ 第六阶段：工作流功能测试

- [ ] 16. 合同审批流程标签页
  - 点击"合同审批流程"标签
  - 验证流程图展示
  - 验证流程状态
  - 截图：`合同审批流程.png`

- [ ] 17. 发起流程功能
  - 点击"发起流程"按钮（如有权限）
  - 验证流程发起界面
  - 测试流程提交
  - 截图：`发起流程界面.png`

### ✅ 第七阶段：编辑功能测试

- [ ] 18. 合同编辑功能
  - 点击编辑按钮（草稿状态合同）
  - 验证编辑抽屉界面
  - 测试字段编辑
  - 测试保存功能
  - 截图：`合同编辑抽屉.png`

- [ ] 19. 合同创建功能
  - 点击创建合同按钮（如有权限）
  - 验证创建界面
  - 测试必填字段验证
  - 截图：`合同创建抽屉.png`

### ✅ 第八阶段：异常情况测试

- [ ] 20. 空数据状态测试
  - 测试无合同数据时的页面展示
  - 测试无支付节点时的展示
  - 测试无附件时的展示
  - 截图：`空数据状态.png`

- [ ] 21. 权限控制测试
  - 验证不同状态下按钮显示/隐藏
  - 验证操作权限限制
  - 验证数据权限范围
  - 截图：`权限控制展示.png`

## 📁 截图文件存储

所有截图文件请存储在以下路径：
```
docs/images/front_system/履约执行/合同管理/
```

## 📝 测试记录

### 测试完成情况
- 测试开始时间：_______
- 测试完成时间：_______
- 测试人员：_______
- 测试环境：_______

### 发现的问题
| 序号 | 问题描述 | 严重程度 | 截图文件 | 备注 |
|------|----------|----------|----------|------|
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |

### 测试总结
- 功能完整性：□ 完整 □ 基本完整 □ 不完整
- 界面友好性：□ 优秀 □ 良好 □ 一般 □ 较差
- 操作流畅性：□ 流畅 □ 基本流畅 □ 卡顿
- 数据准确性：□ 准确 □ 基本准确 □ 有误

### 改进建议
1. 
2. 
3. 

---
**注意事项**：
1. 每个功能点都要认真测试并截图
2. 发现问题及时记录
3. 截图要清晰完整
4. 文件命名要规范
5. 测试完成后及时整理文档
