<<<<<<< HEAD
@startuml 数据校验时序图

actor 用户
participant "Dkm平台\nAI对话框" as DkmAI
participant "Maxkb\n工作流引擎" as MaxkbWorkflow
participant "微服务平台\n数据校验服务" as MicroService
database "MySQL数据库" as MySQL

用户 -> DkmAI: 1.输入查询问题数据的请求
activate DkmAI

DkmAI -> MaxkbWorkflow: 2.调用工作流处理用户请求
activate MaxkbWorkflow

MaxkbWorkflow -> MicroService: 3.发起数据校验API请求
activate MicroService

MicroService -> MySQL: 4.查询相关表数据
activate MySQL
MySQL --> MicroService: 5.返回查询结果
deactivate MySQL

MicroService -> MicroService: 6.执行数据校验处理\n(检查重名/空值等)
MicroService -> MicroService: 7.生成Excel报表
MicroService --> MaxkbWorkflow: 8.返回Excel文件路径
deactivate MicroService

MaxkbWorkflow -> MaxkbWorkflow: 9.处理Excel文件信息
MaxkbWorkflow --> DkmAI: 10.返回Excel文件下载链接
deactivate MaxkbWorkflow

DkmAI --> 用户: 11.展示Excel下载链接
用户 -> DkmAI: 12.点击下载Excel文件
DkmAI --> 用户: 13.下载Excel报表文件
deactivate DkmAI

=======
@startuml 数据校验时序图

actor 用户
participant "Dkm平台\nAI对话框" as DkmAI
participant "Maxkb\n工作流引擎" as MaxkbWorkflow
participant "微服务平台\n数据校验服务" as MicroService
database "MySQL数据库" as MySQL

用户 -> DkmAI: 1.输入查询问题数据的请求
activate DkmAI

DkmAI -> MaxkbWorkflow: 2.调用工作流处理用户请求
activate MaxkbWorkflow

MaxkbWorkflow -> MicroService: 3.发起数据校验API请求
activate MicroService

MicroService -> MySQL: 4.查询相关表数据
activate MySQL
MySQL --> MicroService: 5.返回查询结果
deactivate MySQL

MicroService -> MicroService: 6.执行数据校验处理\n(检查重名/空值等)
MicroService -> MicroService: 7.生成Excel报表
MicroService --> MaxkbWorkflow: 8.返回Excel文件路径
deactivate MicroService

MaxkbWorkflow -> MaxkbWorkflow: 9.处理Excel文件信息
MaxkbWorkflow --> DkmAI: 10.返回Excel文件下载链接
deactivate MaxkbWorkflow

DkmAI --> 用户: 11.展示Excel下载链接
用户 -> DkmAI: 12.点击下载Excel文件
DkmAI --> 用户: 13.下载Excel报表文件
deactivate DkmAI

>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
@enduml 