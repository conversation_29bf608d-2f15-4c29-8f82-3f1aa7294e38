#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime
from PIL import Image
import json

class ScreenshotVerifier:
    def __init__(self):
        self.screenshot_dir = "./docs/images/front_system/履约执行/"
        self.required_screenshots = [
            "登录页面.png",
            "首页.png",
            "合同管理列表.png",
            "合同编辑抽屉.png",
            "合同创建抽屉.png",
            "合同详情页面.png",
            "合同内容信息.png",
            "支付节点信息.png",
            "支付节点确认弹窗.png",
            "支付状态确认弹窗.png",
            "节点确认记录.png",
            "确认记录详情.png",
            "确认记录审核.png",
            "合同附件信息.png",
            "附件上传界面.png",
            "附件预览界面.png",
            "合同审批流程.png"
        ]

    def check_file_exists(self):
        """检查文件是否存在"""
        print("🔍 检查截图文件是否存在...")
        missing_files = []
        existing_files = []

        for filename in self.required_screenshots:
            filepath = os.path.join(self.screenshot_dir, filename)
            if os.path.exists(filepath):
                existing_files.append(filename)
                print(f"✅ {filename}")
            else:
                missing_files.append(filename)
                print(f"❌ {filename}")

        print(f"\n📊 文件检查结果:")
        print(f"   存在: {len(existing_files)}/{len(self.required_screenshots)} 个文件")
        print(f"   缺失: {len(missing_files)} 个文件")

        if missing_files:
            print(f"\n❌ 缺失的文件:")
            for filename in missing_files:
                print(f"   - {filename}")

        return existing_files, missing_files

    def check_file_quality(self, existing_files):
        """检查文件质量"""
        print(f"\n🔍 检查截图文件质量...")
        quality_issues = []

        for filename in existing_files:
            filepath = os.path.join(self.screenshot_dir, filename)
            try:
                # 检查文件大小
                file_size = os.path.getsize(filepath)
                if file_size < 10000:  # 小于10KB可能有问题
                    quality_issues.append(f"{filename}: 文件太小 ({file_size} bytes)")
                elif file_size > 10000000:  # 大于10MB可能太大
                    quality_issues.append(f"{filename}: 文件太大 ({file_size/1024/1024:.1f} MB)")

                # 检查图片尺寸
                try:
                    with Image.open(filepath) as img:
                        width, height = img.size
                        if width < 800 or height < 600:
                            quality_issues.append(f"{filename}: 分辨率太低 ({width}x{height})")
                        elif width > 4000 or height > 3000:
                            quality_issues.append(f"{filename}: 分辨率太高 ({width}x{height})")

                        print(f"✅ {filename}: {width}x{height}, {file_size/1024:.1f}KB")
                except Exception as e:
                    quality_issues.append(f"{filename}: 无法读取图片 - {e}")

            except Exception as e:
                quality_issues.append(f"{filename}: 文件访问错误 - {e}")

        if quality_issues:
            print(f"\n⚠️  质量问题:")
            for issue in quality_issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ 所有文件质量检查通过")

        return quality_issues

    def generate_report(self, existing_files, missing_files, quality_issues):
        """生成检查报告"""
        report = {
            "检查时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "总文件数": len(self.required_screenshots),
            "存在文件数": len(existing_files),
            "缺失文件数": len(missing_files),
            "质量问题数": len(quality_issues),
            "存在的文件": existing_files,
            "缺失的文件": missing_files,
            "质量问题": quality_issues,
            "完成度": f"{len(existing_files)}/{len(self.required_screenshots)} ({len(existing_files)/len(self.required_screenshots)*100:.1f}%)"
        }

        # 保存JSON报告
        report_file = os.path.join(self.screenshot_dir, "screenshot_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 生成Markdown报告
        md_report = self.generate_markdown_report(report)
        md_file = os.path.join(self.screenshot_dir, "screenshot_report.md")
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_report)

        print(f"\n📄 报告已生成:")
        print(f"   JSON: {report_file}")
        print(f"   Markdown: {md_file}")

        return report

    def generate_markdown_report(self, report):
        """生成Markdown格式的报告"""
        md = f"""# 履约执行模块截图检查报告

## 📊 总体情况

- **检查时间**: {report['检查时间']}
- **总文件数**: {report['总文件数']}
- **存在文件数**: {report['存在文件数']}
- **缺失文件数**: {report['缺失文件数']}
- **质量问题数**: {report['质量问题数']}
- **完成度**: {report['完成度']}

## ✅ 存在的文件

| 序号 | 文件名 | 状态 |
|------|--------|------|
"""

        for i, filename in enumerate(report['存在的文件'], 1):
            md += f"| {i} | {filename} | ✅ 已完成 |\n"

        if report['缺失的文件']:
            md += f"\n## ❌ 缺失的文件\n\n"
            for i, filename in enumerate(report['缺失的文件'], 1):
                md += f"{i}. {filename}\n"

        if report['质量问题']:
            md += f"\n## ⚠️ 质量问题\n\n"
            for i, issue in enumerate(report['质量问题'], 1):
                md += f"{i}. {issue}\n"

        md += f"\n## 📝 建议\n\n"
        if report['缺失文件数'] > 0:
            md += f"- 请补充缺失的 {report['缺失文件数']} 个截图文件\n"
        if report['质量问题数'] > 0:
            md += f"- 请修复 {report['质量问题数']} 个质量问题\n"
        if report['缺失文件数'] == 0 and report['质量问题数'] == 0:
            md += f"- 🎉 所有截图文件完整且质量良好！\n"

        return md

    def run_verification(self):
        """运行完整的验证流程"""
        print("🚀 开始履约执行模块截图验证")
        print(f"📁 检查目录: {self.screenshot_dir}")
        print(f"🎯 预期文件数: {len(self.required_screenshots)}")
        print("-" * 50)

        # 检查目录是否存在
        if not os.path.exists(self.screenshot_dir):
            print(f"❌ 截图目录不存在: {self.screenshot_dir}")
            return False

        # 检查文件存在性
        existing_files, missing_files = self.check_file_exists()

        # 检查文件质量
        quality_issues = []
        if existing_files:
            quality_issues = self.check_file_quality(existing_files)

        # 生成报告
        report = self.generate_report(existing_files, missing_files, quality_issues)

        # 打印总结
        print(f"\n🏁 验证完成")
        print(f"📊 完成度: {report['完成度']}")

        if missing_files or quality_issues:
            print(f"⚠️  发现问题，请查看报告文件")
            return False
        else:
            print(f"🎉 所有截图文件完整且质量良好！")
            return True

def main():
    verifier = ScreenshotVerifier()
    success = verifier.run_verification()

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
