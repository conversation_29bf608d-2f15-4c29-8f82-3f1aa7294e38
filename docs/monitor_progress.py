#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import json
from datetime import datetime
from verify_screenshots import ScreenshotVerifier

class ProgressMonitor:
    def __init__(self):
        self.verifier = ScreenshotVerifier()
        self.last_count = 1  # 已有登录页面
        self.start_time = datetime.now()
        
    def check_progress(self):
        """检查当前进度"""
        existing_files, missing_files = self.verifier.check_file_exists()
        current_count = len(existing_files)
        
        if current_count > self.last_count:
            new_files = current_count - self.last_count
            print(f"🎉 发现 {new_files} 个新截图！")
            print(f"📊 当前进度: {current_count}/18 ({current_count/18*100:.1f}%)")
            
            # 显示新增的文件
            for filename in existing_files[-new_files:]:
                print(f"   ✅ {filename}")
            
            self.last_count = current_count
            
        return current_count, missing_files
    
    def show_status(self):
        """显示当前状态"""
        current_count, missing_files = self.check_progress()
        elapsed = datetime.now() - self.start_time
        
        print(f"\n📊 截图进度监控")
        print(f"⏰ 运行时间: {elapsed.total_seconds():.0f}秒")
        print(f"📸 已完成: {current_count}/18")
        print(f"⏳ 待完成: {len(missing_files)}")
        
        if missing_files:
            print(f"\n❌ 待截图文件:")
            for i, filename in enumerate(missing_files[:5], 1):
                print(f"   {i}. {filename}")
            if len(missing_files) > 5:
                print(f"   ... 还有 {len(missing_files)-5} 个文件")
        
        if current_count == 18:
            print(f"\n🎉 所有截图完成！")
            print(f"🔍 正在运行质量验证...")
            self.verifier.run_verification()
            return True
            
        return False
    
    def monitor(self, interval=10):
        """持续监控"""
        print("🚀 开始监控截图进度...")
        print(f"📁 监控目录: {self.verifier.screenshot_dir}")
        print(f"⏱️  检查间隔: {interval}秒")
        print("🛑 按 Ctrl+C 停止监控")
        
        try:
            while True:
                completed = self.show_status()
                if completed:
                    break
                    
                print(f"\n⏳ {interval}秒后再次检查...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print(f"\n🛑 监控已停止")
            final_count, _ = self.check_progress()
            print(f"📊 最终进度: {final_count}/18")

def main():
    monitor = ProgressMonitor()
    
    # 显示初始状态
    print("📋 履约执行模块截图进度监控")
    print("=" * 40)
    monitor.show_status()
    
    # 开始监控
    monitor.monitor()

if __name__ == "__main__":
    main()
