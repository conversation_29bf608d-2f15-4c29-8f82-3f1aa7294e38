<<<<<<< HEAD
import requests
from bs4 import BeautifulSoup
import html2text
import os
from urllib.parse import urljoin, urlparse
from selenium.webdriver.chrome.options import Options
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import tempfile
import time
from selenium.webdriver.common.by import By

# 登录信息
LOGIN_URL = "http://183.136.206.207:1199/login"
USERNAME = "heqiang"
PASSWORD = "Abc123456789!"
BASE_DOMAIN = "183.136.206.207"
START_URL = "http://183.136.206.207:1199/home?keepAliveKey=CwmLb&rootTabsKey=ecMIr"
SAVE_DIR = "./markdowns"

visited = set()

def login(session):
    # 1. 获取登录页面，获取csrf等隐藏字段（如有）
    resp = session.get(LOGIN_URL)
    soup = BeautifulSoup(resp.text, "html.parser")
    # 假设没有csrf，直接提交
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    # 2. 提交登录表单
    login_resp = session.post(LOGIN_URL, data=data)
    if "登录" in login_resp.text or "系统加载中" in login_resp.text:
        print("登录失败，请检查账号密码或表单字段")
        return False
    print("登录成功")
    return True

def is_valid(url):
    parsed = urlparse(url)
    return parsed.netloc == BASE_DOMAIN and url not in visited

def extract_internal_links(html):
    # 提取所有内部链接（只爬取本系统页面）
    soup = BeautifulSoup(html, "html.parser")
    links = set()
    for a in soup.find_all("a", href=True):
        href = a["href"]
        if href.startswith("/") and not href.startswith("//"):
            full_url = f"http://{BASE_DOMAIN}{href}"
            links.add(full_url)
        elif BASE_DOMAIN in href:
            links.add(href)
    return links

def crawl(url, session, save_dir):
    if url in visited:
        return
    visited.add(url)
    try:
        resp = session.get(url)
        resp.raise_for_status()
    except Exception as e:
        print(f"无法访问: {url}，原因: {e}")
        return

    soup = BeautifulSoup(resp.text, "html.parser")
    content = soup.body or soup
    html = str(content)
    md = html2text.html2text(html)

    # 保存为Markdown文件
    path = urlparse(url).path.strip("/")
    if not path:
        path = "index"
    md_file = os.path.join(save_dir, f"{path.replace('/', '_')}.md")
    os.makedirs(os.path.dirname(md_file), exist_ok=True)
    with open(md_file, "w", encoding="utf-8") as f:
        f.write(md)

    # 递归爬取所有内部子页面
    for link in extract_internal_links(resp.text):
        if is_valid(link):
            crawl(link, session, save_dir)

def selenium_login_and_get_driver():
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-software-rasterizer')
    options.add_argument('--single-process')
    options.add_argument('--disable-extensions')
    options.binary_location = "/usr/bin/chromium-browser"
    tmp_dir = tempfile.mkdtemp()
    print(f"Using user-data-dir: {tmp_dir}")
    options.add_argument(f'--user-data-dir={tmp_dir}')
    service = Service(CHROMEDRIVER_PATH)
    driver = webdriver.Chrome(service=service, options=options)
    driver.get(LOGIN_URL)
    time.sleep(2)
    driver.find_element(By.NAME, "username").send_keys(USERNAME)
    driver.find_element(By.NAME, "password").send_keys(PASSWORD)
    driver.find_element(By.TAG_NAME, "button").click()
    time.sleep(3)
    return driver

if __name__ == "__main__":
    session = requests.Session()
    if login(session):
        crawl(START_URL, session, SAVE_DIR)
=======
import requests
from bs4 import BeautifulSoup
import html2text
import os
from urllib.parse import urljoin, urlparse
from selenium.webdriver.chrome.options import Options
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import tempfile
import time
from selenium.webdriver.common.by import By

# 登录信息
LOGIN_URL = "http://183.136.206.207:1199/login"
USERNAME = "heqiang"
PASSWORD = "Abc123456789!"
BASE_DOMAIN = "183.136.206.207"
START_URL = "http://183.136.206.207:1199/home?keepAliveKey=CwmLb&rootTabsKey=ecMIr"
SAVE_DIR = "./markdowns"

visited = set()

def login(session):
    # 1. 获取登录页面，获取csrf等隐藏字段（如有）
    resp = session.get(LOGIN_URL)
    soup = BeautifulSoup(resp.text, "html.parser")
    # 假设没有csrf，直接提交
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    # 2. 提交登录表单
    login_resp = session.post(LOGIN_URL, data=data)
    if "登录" in login_resp.text or "系统加载中" in login_resp.text:
        print("登录失败，请检查账号密码或表单字段")
        return False
    print("登录成功")
    return True

def is_valid(url):
    parsed = urlparse(url)
    return parsed.netloc == BASE_DOMAIN and url not in visited

def extract_internal_links(html):
    # 提取所有内部链接（只爬取本系统页面）
    soup = BeautifulSoup(html, "html.parser")
    links = set()
    for a in soup.find_all("a", href=True):
        href = a["href"]
        if href.startswith("/") and not href.startswith("//"):
            full_url = f"http://{BASE_DOMAIN}{href}"
            links.add(full_url)
        elif BASE_DOMAIN in href:
            links.add(href)
    return links

def crawl(url, session, save_dir):
    if url in visited:
        return
    visited.add(url)
    try:
        resp = session.get(url)
        resp.raise_for_status()
    except Exception as e:
        print(f"无法访问: {url}，原因: {e}")
        return

    soup = BeautifulSoup(resp.text, "html.parser")
    content = soup.body or soup
    html = str(content)
    md = html2text.html2text(html)

    # 保存为Markdown文件
    path = urlparse(url).path.strip("/")
    if not path:
        path = "index"
    md_file = os.path.join(save_dir, f"{path.replace('/', '_')}.md")
    os.makedirs(os.path.dirname(md_file), exist_ok=True)
    with open(md_file, "w", encoding="utf-8") as f:
        f.write(md)

    # 递归爬取所有内部子页面
    for link in extract_internal_links(resp.text):
        if is_valid(link):
            crawl(link, session, save_dir)

def selenium_login_and_get_driver():
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-software-rasterizer')
    options.add_argument('--single-process')
    options.add_argument('--disable-extensions')
    options.binary_location = "/usr/bin/chromium-browser"
    tmp_dir = tempfile.mkdtemp()
    print(f"Using user-data-dir: {tmp_dir}")
    options.add_argument(f'--user-data-dir={tmp_dir}')
    service = Service(CHROMEDRIVER_PATH)
    driver = webdriver.Chrome(service=service, options=options)
    driver.get(LOGIN_URL)
    time.sleep(2)
    driver.find_element(By.NAME, "username").send_keys(USERNAME)
    driver.find_element(By.NAME, "password").send_keys(PASSWORD)
    driver.find_element(By.TAG_NAME, "button").click()
    time.sleep(3)
    return driver

if __name__ == "__main__":
    session = requests.Session()
    if login(session):
        crawl(START_URL, session, SAVE_DIR)
>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
