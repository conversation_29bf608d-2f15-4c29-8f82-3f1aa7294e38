<<<<<<< HEAD
import os
import time
import re
import html2text
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import tempfile

BASE_URL = "http://***************:1199"
START_URL = BASE_URL + "/home?keepAliveKey=CwmLb&rootTabsKey=ecMIr"
CHROMEDRIVER_PATH = "/mnt/c/workspace/mpf/docs/chromedriver"  # 请根据实际路径修改
SAVE_DIR = "./markdowns"
IMG_DIR = "./screenshots"
PRD_MD = "docs/prd.md"
USERNAME = "heqiang"
PASSWORD = "Abc123456789!"
visited = set()

def selenium_login_and_get_driver():
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.binary_location = "/usr/bin/chromium-browser"
    tmp_dir = tempfile.mkdtemp()
    print(f"Using user-data-dir: {tmp_dir}")
    options.add_argument(f'--user-data-dir={tmp_dir}')
    service = Service(CHROMEDRIVER_PATH)
    driver = webdriver.Chrome(service=service, options=options)
    driver.get(BASE_URL + "/login")
    time.sleep(2)
    driver.find_element(By.NAME, "username").send_keys(USERNAME)
    driver.find_element(By.NAME, "password").send_keys(PASSWORD)
    driver.find_element(By.TAG_NAME, "button").click()
    time.sleep(3)
    return driver

def save_screenshot(driver, url, img_dir):
    path = url.replace(BASE_URL, "").strip("/").replace("/", "_")
    if not path:
        path = "index"
    img_path = os.path.join(img_dir, f"{path}.png")
    os.makedirs(os.path.dirname(img_path), exist_ok=True)
    driver.save_screenshot(img_path)
    return img_path

def append_to_prd_md(page_title, img_path, url):
    with open(PRD_MD, "a", encoding="utf-8") as f:
        f.write(f"## {page_title}\n\n")
        f.write(f"页面地址: [{url}]({url})\n\n")
        f.write(f"![{page_title}]({img_path})\n\n")

def extract_links_from_html(html):
    links = re.findall(r'href=["\'](.*?)["\']', html)
    internal_links = []
    for link in links:
        if link.startswith("/") and not link.startswith("//"):
            full_link = BASE_URL + link
            internal_links.append(full_link)
        elif link.startswith(BASE_URL):
            internal_links.append(link)
    return list(set(internal_links))

def crawl(driver, url, save_dir, img_dir):
    if url in visited:
        return
    visited.add(url)
    driver.get(url)
    time.sleep(2)
    html = driver.page_source
    md = html2text.html2text(html)
    img_path = save_screenshot(driver, url, img_dir)
    path = url.replace(BASE_URL, "").strip("/").replace("/", "_")
    if not path:
        path = "index"
    md_file = os.path.join(save_dir, f"{path}.md")
    os.makedirs(os.path.dirname(md_file), exist_ok=True)
    with open(md_file, "w", encoding="utf-8") as f:
        f.write(md)
        f.write(f"\n\n![页面截图]({img_path})\n")
    append_to_prd_md(path, img_path, url)
    new_links = extract_links_from_html(html)
    for link in new_links:
        if BASE_URL in link and link not in visited:
            crawl(driver, link, save_dir, img_dir)

if __name__ == "__main__":
    driver = selenium_login_and_get_driver()
    crawl(driver, START_URL, SAVE_DIR, IMG_DIR)
    driver.quit()
=======
import os
import time
import re
import html2text
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import tempfile

BASE_URL = "http://***************:1199"
START_URL = BASE_URL + "/home?keepAliveKey=CwmLb&rootTabsKey=ecMIr"
CHROMEDRIVER_PATH = "/mnt/c/workspace/mpf/docs/chromedriver"  # 请根据实际路径修改
SAVE_DIR = "./markdowns"
IMG_DIR = "./screenshots"
PRD_MD = "docs/prd.md"
USERNAME = "heqiang"
PASSWORD = "Abc123456789!"
visited = set()

def selenium_login_and_get_driver():
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.binary_location = "/usr/bin/chromium-browser"
    tmp_dir = tempfile.mkdtemp()
    print(f"Using user-data-dir: {tmp_dir}")
    options.add_argument(f'--user-data-dir={tmp_dir}')
    service = Service(CHROMEDRIVER_PATH)
    driver = webdriver.Chrome(service=service, options=options)
    driver.get(BASE_URL + "/login")
    time.sleep(2)
    driver.find_element(By.NAME, "username").send_keys(USERNAME)
    driver.find_element(By.NAME, "password").send_keys(PASSWORD)
    driver.find_element(By.TAG_NAME, "button").click()
    time.sleep(3)
    return driver

def save_screenshot(driver, url, img_dir):
    path = url.replace(BASE_URL, "").strip("/").replace("/", "_")
    if not path:
        path = "index"
    img_path = os.path.join(img_dir, f"{path}.png")
    os.makedirs(os.path.dirname(img_path), exist_ok=True)
    driver.save_screenshot(img_path)
    return img_path

def append_to_prd_md(page_title, img_path, url):
    with open(PRD_MD, "a", encoding="utf-8") as f:
        f.write(f"## {page_title}\n\n")
        f.write(f"页面地址: [{url}]({url})\n\n")
        f.write(f"![{page_title}]({img_path})\n\n")

def extract_links_from_html(html):
    links = re.findall(r'href=["\'](.*?)["\']', html)
    internal_links = []
    for link in links:
        if link.startswith("/") and not link.startswith("//"):
            full_link = BASE_URL + link
            internal_links.append(full_link)
        elif link.startswith(BASE_URL):
            internal_links.append(link)
    return list(set(internal_links))

def crawl(driver, url, save_dir, img_dir):
    if url in visited:
        return
    visited.add(url)
    driver.get(url)
    time.sleep(2)
    html = driver.page_source
    md = html2text.html2text(html)
    img_path = save_screenshot(driver, url, img_dir)
    path = url.replace(BASE_URL, "").strip("/").replace("/", "_")
    if not path:
        path = "index"
    md_file = os.path.join(save_dir, f"{path}.md")
    os.makedirs(os.path.dirname(md_file), exist_ok=True)
    with open(md_file, "w", encoding="utf-8") as f:
        f.write(md)
        f.write(f"\n\n![页面截图]({img_path})\n")
    append_to_prd_md(path, img_path, url)
    new_links = extract_links_from_html(html)
    for link in new_links:
        if BASE_URL in link and link not in visited:
            crawl(driver, link, save_dir, img_dir)

if __name__ == "__main__":
    driver = selenium_login_and_get_driver()
    crawl(driver, START_URL, SAVE_DIR, IMG_DIR)
    driver.quit()
>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
