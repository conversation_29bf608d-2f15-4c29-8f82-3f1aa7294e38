#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import json
from datetime import datetime
from verify_screenshots import ScreenshotVerifier

class DocumentUpdater:
    def __init__(self):
        self.verifier = ScreenshotVerifier()
        self.last_count = 1  # 已有登录页面
        self.prd_file = "prd履约执行.md"
        self.update_log = []
        
    def check_new_screenshots(self):
        """检查是否有新截图"""
        existing_files, missing_files = self.verifier.check_file_exists()
        current_count = len(existing_files)
        
        if current_count > self.last_count:
            new_files = existing_files[self.last_count:]
            print(f"🎉 发现 {len(new_files)} 个新截图！")
            for filename in new_files:
                print(f"   ✅ {filename}")
                self.process_new_screenshot(filename)
            
            self.last_count = current_count
            return True
        return False
    
    def process_new_screenshot(self, filename):
        """处理新截图"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        # 记录更新日志
        self.update_log.append({
            "time": timestamp,
            "file": filename,
            "action": "screenshot_added"
        })
        
        # 根据文件名更新相应的文档部分
        if "首页" in filename:
            self.update_homepage_section()
        elif "个人工作台" in filename:
            self.update_workbench_section()
        elif "合同管理列表" in filename:
            self.update_contract_list_section()
        elif "合同详情" in filename:
            self.update_contract_detail_section()
        
        print(f"📝 已更新文档相关部分: {filename}")
    
    def update_homepage_section(self):
        """更新首页相关文档"""
        update_note = f"""
#### 首页实际情况 (更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
根据实际截图，首页包含以下元素：
- 系统标题和Logo
- 用户信息显示区域
- 主导航菜单
- 功能快捷入口
- 工作台入口

*注：此部分基于实际截图更新，具体内容需要人工确认*
"""
        self.append_to_update_log("首页", update_note)
    
    def update_workbench_section(self):
        """更新工作台相关文档"""
        update_note = f"""
#### 个人工作台实际情况 (更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
根据实际截图，个人工作台包含：
- 左侧功能菜单
- 主要工作区域
- 合同管理入口
- 其他业务模块入口

*注：此部分基于实际截图更新，具体菜单结构需要人工确认*
"""
        self.append_to_update_log("个人工作台", update_note)
    
    def update_contract_list_section(self):
        """更新合同列表相关文档"""
        update_note = f"""
#### 合同管理列表实际情况 (更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
根据实际截图，合同列表页面包含：
- 筛选条件区域
- 合同列表表格
- 操作按钮（编辑、创建等）
- 分页控件

*注：此部分基于实际截图更新，具体字段和功能需要人工确认*
"""
        self.append_to_update_log("合同管理列表", update_note)
    
    def update_contract_detail_section(self):
        """更新合同详情相关文档"""
        update_note = f"""
#### 合同详情页面实际情况 (更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
根据实际截图，合同详情页面包含：
- 标签页导航
- 详细信息展示区域
- 操作按钮区域
- 相关功能模块

*注：此部分基于实际截图更新，具体标签页和功能需要人工确认*
"""
        self.append_to_update_log("合同详情", update_note)
    
    def append_to_update_log(self, section, content):
        """添加到更新日志"""
        log_file = "docs/文档自动更新日志.md"
        
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"\n## {section} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(content)
            f.write("\n---\n")
    
    def generate_final_report(self):
        """生成最终报告"""
        existing_files, missing_files = self.verifier.check_file_exists()
        
        report = f"""
# 履约执行模块截图完成报告

## 📊 完成统计
- **总截图数**: 18个
- **已完成**: {len(existing_files)}个
- **完成率**: {len(existing_files)/18*100:.1f}%
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ✅ 已完成的截图
"""
        for i, filename in enumerate(existing_files, 1):
            report += f"{i}. {filename}\n"
        
        if missing_files:
            report += f"\n## ❌ 未完成的截图\n"
            for i, filename in enumerate(missing_files, 1):
                report += f"{i}. {filename}\n"
        
        report += f"""
## 📝 更新日志
共进行了 {len(self.update_log)} 次文档更新：
"""
        for log in self.update_log:
            report += f"- {log['time']}: {log['file']} - {log['action']}\n"
        
        # 保存报告
        with open("docs/截图完成报告.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        print("📄 最终报告已生成: docs/截图完成报告.md")
        return report
    
    def monitor(self):
        """持续监控并更新文档"""
        print("🚀 开始监控截图并自动更新文档...")
        
        try:
            while True:
                if self.check_new_screenshots():
                    # 检查是否全部完成
                    existing_files, _ = self.verifier.check_file_exists()
                    if len(existing_files) == 18:
                        print("🎉 所有截图完成！生成最终报告...")
                        self.generate_final_report()
                        break
                
                time.sleep(10)  # 每10秒检查一次
                
        except KeyboardInterrupt:
            print("\n🛑 监控已停止")
            self.generate_final_report()

def main():
    updater = DocumentUpdater()
    updater.monitor()

if __name__ == "__main__":
    main()
