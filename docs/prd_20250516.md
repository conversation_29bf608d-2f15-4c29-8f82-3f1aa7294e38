<<<<<<< HEAD
# 项目管理平台产品设计文档

## 1. 产品概述

### 1.1 产品简介
项目管理平台是一款完整的企业级项目管理系统，提供全面的项目管理、资源调配、成本控制、合同管理等功能，帮助企业高效管理项目全生命周期。系统分为管理员后台和用户前台两个部分，满足不同角色用户的使用需求。

### 1.2 系统架构
系统采用前后端分离架构:
- 前端：Vue3 + Ant Design Vue + Vben + Less + Webpack + Qiankun微服务方案
- 后端：Spring Boot + Spring Cloud + MyBatis + Nacos + RabbitMQ

## 2. 系统角色与用户

### 2.1 角色划分
- 系统管理员：拥有系统的全部配置与管理权限
- 项目管理人员：负责项目创建、计划制定、跟踪与管理
- 财务人员：负责项目成本、预算、收入管理
- 合同管理人员：负责合同创建与管理
- 资源管理人员：负责人员、设备等资源调配
- 普通用户：项目参与者，使用系统进行日常工作

### 2.2 访问入口
- 管理员后台：http://183.136.206.207:1189/login (账号: admin，密码: orion2023!@#$)
- 用户前台：http://183.136.206.207:1199/login (账号: heqiang，密码: Abc123456789!)

## 3. 功能模块

### 3.1 系统管理模块
- 用户管理：用户增删改查、角色分配
  - 用户列表页面 ![用户列表页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_39_PM.png)
    - **主要功能**: 查看用户列表、搜索用户、添加用户、编辑用户信息、删除用户、分配角色。
    - **调用API（推测）**:
      - `GET /api/admin/users` (获取用户列表)
      - `POST /api/admin/users` (新增用户)
      - `PUT /api/admin/users/{userId}` (更新用户)
      - `DELETE /api/admin/users/{userId}` (删除用户)
      - `GET /api/admin/roles` (获取角色列表以供分配)
- 角色管理：角色创建、权限分配
- 菜单管理：系统菜单配置
- 权限管理：功能权限、数据权限设置
- 系统配置：系统参数设置、日志管理

### 3.2 项目管理模块
- 项目立项：项目创建、项目信息管理
  - 项目仪表盘/列表页面 (前台) ![前台项目管理仪表盘](./images/front_system/精益管理一体化平台-05-15-2025_08_13_PM.png)
    - **主要功能**: 展示用户相关的项目列表、项目概览、快速入口到项目详情或创建新项目。
    - **调用API（推测）**:
      - `GET /api/u/projects` (获取用户相关的项目列表)
      - `GET /api/u/projects/{projectId}/summary` (获取项目摘要)
  - 项目创建/编辑页面 (前台) ![前台项目创建表单](./images/front_system/精益管理一体化平台-05-15-2025_08_12_PM.png)
    - **主要功能**: 填写项目基本信息、选择项目模板、设置项目成员、提交项目立项申请。
    - **调用API（推测）**:
      - `POST /api/u/projects` (创建新项目)
      - `PUT /api/u/projects/{projectId}` (更新项目信息)
      - `GET /api/u/project-templates` (获取项目模板列表)
      - `GET /api/u/users` (获取用户列表以供选择项目成员)
- 项目库管理：项目分类、项目状态管理
- 项目计划：计划制定、WBS管理、甘特图显示
- 项目状态监控：进度监控、里程碑管理
- 项目风险管理：风险识别、风险控制
- 工作报告：日报、周报管理

### 3.3 资源管理模块
- 人力资源管理：人员池、技术人员配置
- 岗位管理：岗位设置、角色配置
- 工作量管理：工时记录、工作量统计
- 资源调配：资源分配、调度管理

### 3.4 成本控制模块
- 预算管理：预算编制、预算控制
- 成本管理：成本核算、成本分析
- 收入计划：收入预测、收入管理
- 财务管理：账务管理、财务报表

### 3.5 合同管理模块
- 合同创建：合同起草、合同模板
- 合同跟踪：合同执行状态跟踪
- 合同变更：合同变更管理
- 合同分析：合同执行情况分析

### 3.6 采购管理模块
- 供应商管理：供应商信息维护
  - 供应商列表页面  
    ![前台供应商管理列表](./images/front_system/采购管理_供应商信息报表_公司级合格供应商.png)  
    ![后台供应商列表页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_37_PM.png)
    - **主要功能**: 查看供应商列表、添加新供应商、编辑供应商详情、管理供应商合同/产品。
    - **调用API（推测）**:
      - `GET /api/admin/suppliers` (获取供应商列表)
      - `POST /api/admin/suppliers` (创建新供应商)
      - `GET /api/admin/suppliers/{supplierId}` (获取供应商详情)
      - `PUT /api/admin/suppliers/{supplierId}` (更新供应商信息)
      - `DELETE /api/admin/suppliers/{supplierId}` (删除供应商)
    - **时序图 (获取列表)**:
      ```plantuml
      @startuml
      actor User
      participant "Frontend" as FE
      participant "Backend API" as BE
      database "Database" as DB

      User -> FE: 查看供应商列表
      FE -> BE: GET /api/admin/suppliers
      BE -> DB: 查询供应商数据
      DB --> BE: 返回供应商数据
      BE --> FE: [供应商列表]
      FE -> User: 显示供应商列表
      @enduml
      ```
    - **数据模型 (供应商实体)**:
      ```plantuml
      @startuml
      object Supplier {
        supplierId (PK)
        supplierName
        contactName
        email
        phone
        address
        status
        supplierType
        qualificationLevel
        registrationDate
        lastUpdated
      }
      @enduml
      ```
    - **页面元素**:
      - 供应商搜索框：按名称、联系人、资质等级等字段搜索
      - 供应商分类筛选：公司级合格供应商、资审中供应商、不良行为供应商等
      - 供应商列表表格：显示供应商名称、联系人、联系方式、资质等级等
      - 操作按钮：新增、编辑、删除、查看详情
      - 批量操作：导入、导出、批量审核
  
  - 供应商详情页面  
    ![前台供应商详情与评估](./images/front_system/采购管理_供应商管理_技术配置合同管理.png)
    - **主要功能**: 
      - 查看供应商详细信息
      - 浏览历史合作记录
      - 管理产品目录
      - 供应商评估打分
      - 技术人员配置管理
    - **调用API（推测）**:
      - `GET /api/admin/suppliers/{supplierId}` (获取供应商详情)
      - `GET /api/admin/suppliers/{supplierId}/products` (获取供应商产品)
      - `GET /api/admin/suppliers/{supplierId}/history` (获取历史合作记录)
      - `POST /api/admin/suppliers/{supplierId}/evaluate` (提交供应商评估)
      - `GET /api/admin/suppliers/{supplierId}/technical-staff` (获取技术人员配置)
    - **页面元素**:
      - 供应商基本信息卡片：基本信息、联系方式、资质等级等
      - 合作历史记录表：历史合作项目、合同履行情况、评价等
      - 产品服务列表：供应商提供的产品及服务清单
      - 技术人员配置表格：技术人员资质、专业方向、工作经历等
      - 评估打分面板：多维度评估打分界面

  - 技术配置人员管理页面  
    ![前台技术人员配置管理](./images/front_system/采购管理_技术配置人员管理.png)
    - **主要功能**: 
      - 技术人员资质审核
      - 专业方向管理
      - 人员使用记录
      - 能力评估管理
    - **调用API（推测）**:
      - `GET /api/admin/technical-staff` (获取技术人员列表)
      - `POST /api/admin/technical-staff` (新增技术人员)
      - `PUT /api/admin/technical-staff/{staffId}` (更新技术人员信息)
      - `GET /api/admin/technical-staff/{staffId}/records` (获取使用记录)
    - **页面元素**:
      - 搜索筛选区：按姓名、专业方向、供应商筛选
      - 技术人员列表：姓名、专业、级别、所属供应商等
      - 资质证书附件管理：上传下载资质附件
      - 历史使用记录：参与项目、工作表现、客户评价等

- 采购管理：采购计划、采购执行
  - 采购合同管理页面  
    ![前台采购合同管理界面](./images/front_system/采购管理_框架合同列表.png)
    - **主要功能**: 
      - 合同列表管理
      - 框架合同与总价合同管理
      - 合同状态跟踪
      - 合同文档管理
    - **调用API（推测）**:
      - `GET /api/admin/procurement/contracts` (获取采购合同列表)
      - `POST /api/admin/procurement/contracts` (创建采购合同)
      - `PUT /api/admin/procurement/contracts/{contractId}` (更新合同信息)
      - `GET /api/admin/procurement/contracts/{contractId}/documents` (获取合同文档)
    - **页面元素**:
      - 合同搜索区：按合同编号、供应商、状态等搜索
      - 合同类型切换：框架合同、总价合同切换视图
      - 合同列表表格：合同编号、名称、供应商、金额、签约日期、状态等
      - 操作按钮：查看详情、编辑、文档管理、状态更新

  - 采购订单管理页面  
    ![前台采购订单管理界面](./images/front_system/采购管理_集采订单管理.png)
    - **主要功能**: 
      - 订单创建与管理
      - 订单状态跟踪
      - 订单审批流程
      - 收货与付款管理
    - **调用API（推测）**:
      - `GET /api/admin/procurement/orders` (获取采购订单列表)
      - `POST /api/admin/procurement/orders` (创建采购订单)
      - `PUT /api/admin/procurement/orders/{orderId}/status` (更新订单状态)
      - `POST /api/admin/procurement/orders/{orderId}/receive` (记录收货)
      - `POST /api/admin/procurement/orders/{orderId}/payment` (记录付款)
    - **页面元素**:
      - 订单搜索区：按订单号、供应商、状态等搜索
      - 订单列表表格：订单编号、关联合同、物料/服务、金额、交付日期、状态等
      - 状态流转图：直观显示订单当前状态
      - 操作按钮：查看详情、编辑、收货确认、付款记录

  - 合同变更与索赔管理页面  
    ![前台合同变更与索赔管理](./images/front_system/采购管理_变更_索赔_终止.png)
    - **主要功能**: 
      - 合同变更申请与审批
      - 索赔管理
      - 合同终止管理
      - 争议处理
    - **调用API（推测）**:
      - `GET /api/admin/procurement/contracts/{contractId}/changes` (获取变更记录)
      - `POST /api/admin/procurement/contracts/{contractId}/changes` (提交变更申请)
      - `POST /api/admin/procurement/contracts/{contractId}/claims` (提交索赔)
      - `POST /api/admin/procurement/contracts/{contractId}/terminate` (申请终止合同)
    - **页面元素**:
      - 合同基本信息区：显示当前合同关键信息
      - 变更记录列表：历史变更内容、原因、状态等
      - 索赔记录表格：索赔原因、金额、处理进度等
      - 终止申请流程图：终止流程各环节状态
      - 表单区：提交新的变更/索赔/终止申请

- 采购数据分析：采购状态可视化
  - 采购数据分析看板  
    ![前台采购数据分析仪表盘](./images/front_system/采购管理_供应商管理_技术配置人员管理_数据分析.png)
    - **主要功能**: 
      - 采购总额统计与分析
      - 供应商分布分析
      - 品类分析
      - 交付时间统计
      - 异常订单预警
    - **调用API（推测）**:
      - `GET /api/admin/procurement/dashboard` (获取采购看板数据)
      - `GET /api/admin/procurement/analytics/suppliers` (获取供应商分析数据)
      - `GET /api/admin/procurement/analytics/categories` (获取品类分析数据)
      - `GET /api/admin/procurement/analytics/delivery` (获取交付分析数据)
      - `GET /api/admin/procurement/alerts` (获取异常订单预警)
    - **页面元素**:
      - 关键指标卡片：采购总额、平均交付周期、合同履约率、供应商数量等
      - 供应商分布饼图：不同类型供应商的分布比例
      - 品类分布柱状图：各采购品类的金额占比
      - 交付时间趋势图：按时间段的交付周期变化
      - 异常订单预警列表：逾期交付、质量问题等异常情况
      - 数据筛选器：时间范围、供应商类型、合同类型等筛选条件

### 3.7 绩效管理模块
- 绩效模板：绩效考核模板管理
- 绩效库：绩效指标管理
- 绩效报告：绩效分析报告

### 3.8 文档管理模块
- 文档模板库：文档模板管理
- 项目文档：项目相关文档管理
- 证书标准：证书与标准文档管理

### 3.9 个人工作台
- 待办事项：个人任务待办
- 日常工作：日常工作管理
- 个人报告：个人工作报告

### 3.10 市场经营模块

市场经营模块是项目管理平台的核心业务模块之一，主要负责客户关系管理、商机跟踪、市场分析和竞争情报收集，为企业营销决策提供数据支持。

#### 3.10.1 客户管理

客户管理子模块实现对客户全生命周期的管理，提供客户信息维护、客户分类、客户关系跟踪等功能。

- **客户列表页面**  
  ![前台客户关系管理界面](./images/front_system/精益管理一体化平台-05-15-2025_08_13_PM.png)  
  ![后台客户列表页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_38_PM.png)
  - **主要功能**:
    - 客户列表显示与分页
    - 按名称、行业、状态等条件筛选客户
    - 添加新客户
    - 批量导入/导出客户信息
    - 客户分类与标签管理
  - **调用API（推测）**:
    - `GET /api/admin/clients` (获取客户列表)
    - `POST /api/admin/clients` (创建新客户)
    - `GET /api/admin/clients/{clientId}` (获取客户详情)
    - `PUT /api/admin/clients/{clientId}` (更新客户信息)
    - `DELETE /api/admin/clients/{clientId}` (删除客户)
    - `POST /api/admin/clients/import` (批量导入客户)
    - `GET /api/admin/clients/export` (导出客户数据)
  - **时序图 (添加客户)**:
    ```plantuml
    @startuml
    actor User
    participant "Frontend" as FE
    participant "Backend API" as BE
    database "Database" as DB

    User -> FE: 点击"添加客户"按钮
    FE -> User: 显示客户信息表单
    User -> FE: 填写客户信息并提交
    FE -> BE: POST /api/admin/clients (客户数据)
    BE -> BE: 校验数据
    alt 数据有效
      BE -> DB: 插入新客户记录
      DB --> BE: 插入成功
      BE --> FE: {status: "success", clientId: "newId"}
      FE -> User: 显示成功消息，并可能跳转到客户详情或刷新列表
    else 数据无效
      BE --> FE: {status: "error", errors: [...]} 
      FE -> User: 显示错误信息
    end
    @enduml
    ```
  - **数据模型 (客户实体)**:
    ```plantuml
    @startuml
    object Client {
      clientId (PK)
      clientName
      contactPerson
      email
      phone
      address
      industry
      source
      status
      createdDate
      updatedDate
      tags
      customerLevel
      lastContactDate
      nextFollowUpDate
    }
    @enduml
    ```
  - **页面元素**:
    - 客户搜索框：按名称、联系人、行业等字段搜索
    - 高级筛选区：行业、地区、来源、客户等级筛选
    - 客户列表表格：显示客户名称、联系人、联系方式、所属行业、客户来源等
    - 操作按钮：新增、编辑、删除、查看详情
    - 批量操作：导入、导出、批量删除、批量分配

- **客户详情页面**  
  ![前台客户详情与联系记录](./images/front_system/精益管理一体化平台-05-15-2025_08_11_PM.png)
  - **主要功能**:
    - 查看客户基本信息
    - 管理联系人信息
    - 记录客户沟通历史
    - 维护客户关联项目
    - 客户文档管理
    - 客户评级与标签
  - **调用API（推测）**:
    - `GET /api/admin/clients/{clientId}` (获取客户详情)
    - `GET /api/admin/clients/{clientId}/contacts` (获取客户联系记录)
    - `POST /api/admin/clients/{clientId}/contacts` (添加联系记录)
    - `GET /api/admin/clients/{clientId}/projects` (获取客户关联项目)
    - `PUT /api/admin/clients/{clientId}/tags` (更新客户标签)
    - `PUT /api/admin/clients/{clientId}/level` (更新客户等级)
  - **页面元素**:
    - 客户信息卡片：基本信息、联系方式、行业分类等
    - 联系记录时间线：按时间顺序显示所有沟通记录
    - 联系人列表：客户的多位联系人信息及职位
    - 关联项目列表：与该客户相关的所有项目及状态
    - 客户标签管理：可视化标签编辑区
    - 客户评级选择器：设置客户重要程度

- **客户统计分析页面**  
  ![前台客户统计分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_38_PM.png)
  - **主要功能**:
    - 客户行业分布统计
    - 客户来源渠道分析
    - 客户增长趋势图表
    - 客户价值评估
    - 客户活跃度分析
  - **调用API（推测）**:
    - `GET /api/admin/clients/statistics/industry` (获取行业分布数据)
    - `GET /api/admin/clients/statistics/source` (获取来源渠道数据)
    - `GET /api/admin/clients/statistics/growth` (获取增长趋势数据)
    - `GET /api/admin/clients/statistics/value` (获取客户价值分析)
  - **页面元素**:
    - 概览数据卡片：总客户数、新增客户数、活跃客户数、流失客户数
    - 行业分布饼图：按行业显示客户占比
    - 来源渠道柱状图：不同渠道获客数量对比
    - 客户增长趋势线图：按月/季度/年显示客户增长情况
    - 客户等级分布：不同等级客户的数量与占比
    - 数据筛选器：时间范围、客户类型、地区等筛选条件

#### 3.10.2 商机管理

商机管理子模块支持对潜在业务机会的跟踪和转化，包括商机录入、跟进、评估和转化为项目等功能。

- **商机列表页面**  
  ![前台商机跟踪管理界面](./images/front_system/精益管理一体化平台-05-15-2025_08_02_PM.png)
  - **主要功能**:
    - 商机列表展示与筛选
    - 新增商机
    - 商机状态管理
    - 商机优先级设置
    - 商机转化跟踪
  - **调用API（推测）**:
    - `GET /api/admin/opportunities` (获取商机列表)
    - `POST /api/admin/opportunities` (创建新商机)
    - `PUT /api/admin/opportunities/{opportunityId}` (更新商机信息)
    - `PUT /api/admin/opportunities/{opportunityId}/status` (更新商机状态)
    - `PUT /api/admin/opportunities/{opportunityId}/priority` (更新商机优先级)
    - `POST /api/admin/opportunities/{opportunityId}/convert` (将商机转化为项目)
  - **数据模型 (商机实体)**:
    ```plantuml
    @startuml
    object Opportunity {
      opportunityId (PK)
      title
      description
      expectedValue
      probability
      status
      priority
      source
      clientId (FK)
      contactId (FK)
      ownerId (FK)
      createdDate
      updatedDate
      expectedClosingDate
      followUpDate
      stageId (FK)
    }
    @enduml
    ```
  - **页面元素**:
    - 商机搜索筛选区：按状态、优先级、客户、负责人筛选
    - 商机列表表格：商机名称、客户、预期价值、状态、负责人等
    - 商机看板视图切换：可在表格和看板视图间切换
    - 商机添加按钮：快速创建新商机
    - 操作按钮：编辑、删除、状态更新、转为项目

- **商机详情页面**  
  ![前台商机详细信息界面](./images/front_system/精益管理一体化平台-05-15-2025_08_01_PM.png)
  - **主要功能**:
    - 查看商机基本信息
    - 追踪商机跟进记录
    - 商机阶段管理
    - 商机关联文档
    - 商机相关活动
  - **调用API（推测）**:
    - `GET /api/admin/opportunities/{opportunityId}` (获取商机详情)
    - `GET /api/admin/opportunities/{opportunityId}/activities` (获取跟进活动)
    - `POST /api/admin/opportunities/{opportunityId}/activities` (添加跟进记录)
    - `PUT /api/admin/opportunities/{opportunityId}/stage` (更新商机阶段)
    - `POST /api/admin/opportunities/{opportunityId}/documents` (上传商机文档)
  - **页面元素**:
    - 商机信息卡片：基本信息、预期价值、成功概率等
    - 商机阶段进度条：直观显示当前阶段及流程
    - 跟进记录时间线：所有跟进活动的历史记录
    - 相关文档列表：报价单、方案文档等相关材料
    - 关联客户信息：客户基本情况及联系方式

- **销售漏斗页面**  
  ![前台销售漏斗分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_57_PM.png)
  - **主要功能**:
    - 销售漏斗可视化
    - 各阶段商机数量和金额统计
    - 转化率分析
    - 销售周期分析
    - 赢单/输单原因分析
  - **调用API（推测）**:
    - `GET /api/admin/opportunities/funnel` (获取销售漏斗数据)
    - `GET /api/admin/opportunities/conversion-rates` (获取转化率数据)
    - `GET /api/admin/opportunities/cycle-time` (获取销售周期数据)
    - `GET /api/admin/opportunities/win-loss` (获取赢单/输单分析)
  - **页面元素**:
    - 漏斗图：各阶段商机数量和金额的漏斗可视化
    - 统计卡片：总商机数、平均销售周期、平均成交金额、整体转化率
    - 阶段转化率图表：各阶段间的转化百分比
    - 时间分析图表：销售周期长度分布
    - 赢单/输单原因饼图：归类的原因占比分析

#### 3.10.3 市场分析

市场分析子模块提供市场数据可视化和分析功能，帮助决策者了解市场趋势和机会。

- **市场分析仪表盘**  
  ![前台市场分析数据仪表盘](./images/front_system/精益管理一体化平台-05-15-2025_07_38_PM.png)
  - **主要功能**:
    - 市场趋势总览
    - 客户行业分布分析
    - 区域市场分析
    - 销售漏斗分析
    - 业务增长趋势
  - **调用API（推测）**:
    - `GET /api/admin/market/dashboard` (获取市场分析仪表盘数据)
    - `GET /api/admin/market/trends` (获取市场趋势数据)
    - `GET /api/admin/market/regions` (获取区域市场数据)
    - `GET /api/admin/market/conversion-rates` (获取转化率数据)
    - `GET /api/admin/market/growth` (获取业务增长数据)
  - **页面元素**:
    - 业务概览卡片：各项关键指标的当前值和环比变化
    - 行业分布热力图：客户行业分布可视化
    - 销售漏斗图：各阶段商机数量和转化率
    - 区域市场地图：不同地区业务量的地图展示
    - 增长趋势线图：按时间展示的业务增长曲线
    - 筛选器：时间范围、产品线、销售团队等筛选维度

- **市场预测分析页面**  
  ![前台市场预测与规划界面](./images/front_system/精益管理一体化平台-05-15-2025_07_42_PM.png)
  - **主要功能**:
    - 销售预测分析
    - 市场潜力评估
    - 季度/年度目标设定
    - 市场发展趋势预测
    - 业绩目标跟踪
  - **调用API（推测）**:
    - `GET /api/admin/market/forecasts` (获取销售预测数据)
    - `GET /api/admin/market/potential` (获取市场潜力评估)
    - `POST /api/admin/market/targets` (设置销售目标)
    - `GET /api/admin/market/targets/progress` (获取目标完成进度)
  - **页面元素**:
    - 预测分析图表：基于历史数据的销售预测曲线
    - 目标设定面板：输入和调整销售目标的界面
    - 目标完成度进度条：各目标的实时完成情况
    - 市场机会热图：潜在市场机会的可视化展示
    - 趋势线与预测带：含置信区间的业务发展预测

#### 3.10.4 竞争对手分析

竞争对手分析子模块帮助企业收集和分析竞争情报，为制定竞争策略提供依据。

- **竞争对手列表页面**  
  ![前台竞争对手管理界面](./images/front_system/精益管理一体化平台-05-15-2025_07_47_PM.png)
  - **主要功能**:
    - 竞争对手基本信息管理
    - 竞争对手产品/服务记录
    - 竞争对手动态跟踪
    - 竞争情报收集
  - **调用API（推测）**:
    - `GET /api/admin/competitors` (获取竞争对手列表)
    - `POST /api/admin/competitors` (创建新竞争对手)
    - `PUT /api/admin/competitors/{competitorId}` (更新竞争对手信息)
    - `GET /api/admin/competitors/{competitorId}/products` (获取竞争对手产品)
    - `POST /api/admin/competitors/intelligence` (记录竞争情报)
  - **数据模型 (竞争对手实体)**:
    ```plantuml
    @startuml
    object Competitor {
      competitorId (PK)
      name
      description
      website
      foundedYear
      headquartersLocation
      employeeCount
      marketShare
      strengthPoints
      weaknessPoints
      threatsToUs
      opportunitiesForUs
      createdDate
      updatedDate
    }
    @enduml
    ```
  - **页面元素**:
    - 竞争对手列表表格：名称、市场份额、优劣势等
    - 竞争对手添加按钮：创建新竞争对手记录
    - 情报收集入口：快速记录最新竞争情报
    - 筛选选项：按行业、地区、规模等筛选竞争对手
    - 操作按钮：编辑、删除、查看详情

- **竞争对手详情分析页面**  
  ![前台竞争对手详情分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_46_PM.png)
  - **主要功能**:
    - 竞争对手详细信息查看
    - 产品/服务对比分析
    - 价格策略分析
    - 市场动态记录
    - SWOT分析
  - **调用API（推测）**:
    - `GET /api/admin/competitors/{competitorId}` (获取竞争对手详情)
    - `GET /api/admin/competitors/{competitorId}/products` (获取产品对比)
    - `GET /api/admin/competitors/{competitorId}/pricing` (获取价格策略)
    - `GET /api/admin/competitors/{competitorId}/news` (获取市场动态)
    - `GET /api/admin/competitors/{competitorId}/swot` (获取SWOT分析)
  - **页面元素**:
    - 竞争对手信息卡：基本信息、市场地位、发展历程
    - 产品对比表格：我方与竞争对手产品功能对比
    - 价格策略分析图：价格定位和策略变化趋势
    - 市场动态时间线：重要事件和新闻
    - SWOT分析矩阵：直观展示优势、劣势、机会和威胁

- **市场份额分析页面**  
  ![前台市场份额分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_41_PM.png)
  - **主要功能**:
    - 市场份额可视化
    - 市场格局分析
    - 竞争态势演变
    - 行业集中度分析
    - 细分市场分析
  - **调用API（推测）**:
    - `GET /api/admin/market-share` (获取市场份额数据)
    - `GET /api/admin/market-share/trends` (获取市场份额趋势)
    - `GET /api/admin/market-share/segments` (获取细分市场数据)
    - `GET /api/admin/market/concentration` (获取行业集中度数据)
  - **页面元素**:
    - 市场份额饼图：各竞争者市场份额占比
    - 趋势变化线图：市场份额随时间变化情况
    - 细分市场柱状图：各细分市场的份额分布
    - 竞争格局雷达图：从多维度比较主要竞争者
    - 筛选器：产品线、地区、时间范围等筛选维度

### 3.10.5 营销活动管理

营销活动管理子模块支持企业策划、执行和评估各类营销活动，实现营销过程的全程管理。

- **活动列表页面**  
  ![前台营销活动管理界面](./images/front_system/精益管理一体化平台-05-15-2025_07_39_PM.png)
  - **主要功能**:
    - 营销活动计划管理
    - 活动执行追踪
    - 活动日历视图
    - 活动资源分配
    - 活动状态监控
  - **调用API（推测）**:
    - `GET /api/admin/campaigns` (获取营销活动列表)
    - `POST /api/admin/campaigns` (创建新活动)
    - `PUT /api/admin/campaigns/{campaignId}` (更新活动信息)
    - `GET /api/admin/campaigns/calendar` (获取活动日历数据)
    - `PUT /api/admin/campaigns/{campaignId}/status` (更新活动状态)
  - **数据模型 (营销活动实体)**:
    ```plantuml
    @startuml
    object Campaign {
      campaignId (PK)
      name
      description
      type
      targetAudience
      startDate
      endDate
      budget
      actualCost
      status
      expectedROI
      actualROI
      channels
      goals
      createdBy (FK)
      createdDate
      updatedDate
    }
    @enduml
    ```
  - **页面元素**:
    - 活动列表表格：名称、类型、时间、预算、状态等
    - 日历/甘特图视图：可视化活动时间安排
    - 活动添加按钮：创建新营销活动
    - 状态筛选器：按计划中、进行中、已完成等状态筛选
    - 资源分配图表：展示各活动的资源分配情况

- **活动详情与效果分析页面**  
  ![前台活动效果分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_37_PM.png)
  - **主要功能**:
    - 活动详情查看
    - KPI指标追踪
    - 转化漏斗分析
    - 投资回报率计算
    - 活动效果评估
  - **调用API（推测）**:
    - `GET /api/admin/campaigns/{campaignId}` (获取活动详情)
    - `GET /api/admin/campaigns/{campaignId}/kpis` (获取KPI指标)
    - `GET /api/admin/campaigns/{campaignId}/funnel` (获取转化漏斗数据)
    - `GET /api/admin/campaigns/{campaignId}/roi` (获取ROI分析)
    - `POST /api/admin/campaigns/{campaignId}/evaluation` (提交效果评估)
  - **页面元素**:
    - 活动基本信息卡：目标、预算、时间、负责人等
    - KPI仪表盘：关键指标的目标值与实际值对比
    - 转化漏斗图：从曝光到转化的各环节数据
    - ROI计算器与图表：投入产出比分析
    - 活动效果评分面板：多维度评估活动效果

### 3.11 财务管理模块
#### 3.11.1 账务管理
- 账务总览页面  
  ![前台财务数据仪表盘](./images/front_system/精益管理一体化平台-05-15-2025_08_10_PM.png)  
  ![后台账务总览页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_36_PM.png)
  - **主要功能**: 展示关键财务指标、应收/应付账款摘要、现金流概览。
  - **调用API（推测）**:
    - `GET /api/admin/finance/overview` (获取财务概览)
    - `GET /api/admin/finance/receivables` (获取应收账款)
    - `GET /api/admin/finance/payables` (获取应付账款)
    - `GET /api/admin/finance/cashflow` (获取现金流数据)
  - **时序图 (获取财务概览)**:
    ```plantuml
    @startuml
    actor User
    participant "Frontend" as FE
    participant "Backend API" as BE
    database "Database" as DB

    User -> FE: 访问账务总览页面
    FE -> BE: GET /api/admin/finance/overview
    BE -> DB: 查询并聚合财务数据 (例如：总收入、总支出、利润等)
    DB --> BE: 返回聚合后的财务概览数据
    BE --> FE: [财务概览数据]
    FE -> User: 在仪表盘上显示关键指标和图表
    @enduml
    ```
  - **数据模型 (核心财务实体)**:
    ```plantuml
    @startuml
    entity Account {
      + accountId (PK)
      --
      accountName
      accountType (e.g., Asset, Liability, Equity, Revenue, Expense)
      balance
    }

    entity Transaction {
      + transactionId (PK)
      --
      date
      description
      amount
      type (Debit/Credit)
      accountId (FK)
    }

    entity FinancialReport {
        + reportId (PK)
        --
        reportType (e.g., Balance Sheet, Income Statement)
        periodStartDate
        periodEndDate
        generatedDate
        data (JSON/Text)
    }
    Transaction -- Account : belongs to
    @enduml
    ```

#### 3.11.2 收入管理
- 收入计划页面  
  ![前台收入计划管理界面](./images/front_system/精益管理一体化平台-05-15-2025_07_59_PM.png)
  - **主要功能**: 制定收入计划、跟踪实际收入、差异分析、调整预测。
  - **调用API（推测）**:
    - `GET /api/admin/finance/income-plans` (获取收入计划列表)
    - `POST /api/admin/finance/income-plans` (创建收入计划)
    - `PUT /api/admin/finance/income-plans/{planId}` (更新收入计划)
    - `GET /api/admin/finance/income-plans/{planId}/analysis` (获取计划与实际差异分析)

- 应收账款管理页面  
  ![前台应收账款跟踪界面](./images/front_system/精益管理一体化平台-05-15-2025_07_56_PM.png)
  - **主要功能**: 查看应收账款、账龄分析、催收管理、收款记录。
  - **调用API（推测）**:
    - `GET /api/admin/finance/receivables` (获取应收账款列表)
    - `GET /api/admin/finance/receivables/aging` (获取账龄分析)
    - `POST /api/admin/finance/receivables/{receivableId}/collection` (记录催收活动)
    - `POST /api/admin/finance/receivables/{receivableId}/payment` (记录收款)

#### 3.11.3 成本管理
- 成本分析页面  
  ![前台成本分析与控制界面](./images/front_system/精益管理一体化平台-05-15-2025_07_55_PM.png)
  - **主要功能**: 查看成本构成、成本趋势分析、成本控制点识别、成本优化建议。
  - **调用API（推测）**:
    - `GET /api/admin/finance/costs` (获取成本数据)
    - `GET /api/admin/finance/costs/analysis` (获取成本分析)
    - `GET /api/admin/finance/costs/trends` (获取成本趋势)
    - `GET /api/admin/finance/costs/optimization` (获取成本优化建议)

- 预算管理页面  
  ![前台预算编制与审批界面](./images/front_system/精益管理一体化平台-05-15-2025_07_54_PM.png)
  - **主要功能**: 创建预算、预算审批流程、预算执行跟踪、预算调整。
  - **调用API（推测）**:
    - `GET /api/admin/finance/budgets` (获取预算列表)
    - `POST /api/admin/finance/budgets` (创建预算)
    - `PUT /api/admin/finance/budgets/{budgetId}` (更新预算)
    - `GET /api/admin/finance/budgets/{budgetId}/tracking` (获取预算执行情况)

## 4. 业务流程

### 4.1 项目立项流程
1. 创建项目申请
2. 项目信息填写
3. 项目审批
4. 项目立项
5. 项目初始化

### 4.2 项目计划管理流程
1. 编制项目计划
2. 计划审批
3. 计划发布
4. 计划执行
5. 执行监控
6. 计划调整

### 4.3 资源调配流程
1. 资源需求提出
2. 资源审批
3. 资源分配
4. 资源使用
5. 资源回收

### 4.4 合同管理流程
1. 合同需求
2. 合同起草
3. 合同审批
4. 合同签订
5. 合同执行
6. 合同变更
7. 合同归档

### 4.5 成本管理流程
1. 预算编制
2. 预算审批
3. 成本控制
4. 成本分析
5. 成本报告

## 5. 技术架构

### 5.1 前端架构
- 前端框架：Vue3
- UI组件库：Ant Design Vue
- 微前端框架：Qiankun
- 构建工具：Webpack
- 样式：Less
- 状态管理：Pinia
- 路由管理：Vue Router

### 5.2 后端架构
- 开发框架：Spring Boot, Spring Cloud
- 持久层：MyBatis
- 服务注册与发现：Nacos
- 消息队列：RabbitMQ
- 缓存：Redis
- 数据库：MySQL
- 安全框架：Spring Security

### 5.3 微服务模块划分
- pms-app：应用服务与系统入口
- pms-project：项目管理服务
- pms-cost：成本管理服务
- pms-project-contract：合同管理服务
- pms-api：API接口服务
- pms-interface：接口定义服务
- pms-project-management：项目管理服务
- pms-production：生产管理服务
- pms-finance：财务管理服务
- pms-common：公共服务

## 6. 非功能性需求

### 6.1 性能需求
- 系统响应时间：页面加载<3秒
- 数据处理能力：支持100+并发用户访问
- 数据存储容量：支持大规模项目数据存储

### 6.2 安全性需求
- 用户认证与授权
- 数据加密与保护
- 安全审计与日志
- 权限控制与数据隔离

### 6.3 可用性需求
- 系统可用率>99.9%
- 系统维护与升级不影响正常业务
- 多浏览器兼容支持

### 6.4 扩展性需求
- 支持业务规模扩展
- 支持功能模块扩展
- 支持与第三方系统集成

## 7. 数据模型

系统主要包含以下核心数据实体：
- 用户与角色
- 项目与项目计划
- 资源与资源分配
- 合同与合同条款
- 成本与预算
- 工作报告与日志

## 8. 界面设计原则

- 界面简洁清晰，符合企业应用风格
- 操作流程符合用户习惯，减少学习成本
- 重要信息突出显示，提高用户感知效率
- 表单设计合理，减少用户输入负担
- 数据可视化展示，提升信息获取效率

## 9. 接口设计

系统主要对内提供REST API接口，遵循RESTful设计规范，支持JSON格式的数据交换。

## 10. 部署与发布

### 10.1 部署环境
- 操作系统：Linux
- Web服务器：Nginx
- 应用服务器：Spring Boot内置Tomcat
- 数据库服务器：MySQL
- 中间件：Redis, RabbitMQ, Nacos

### 10.2 发布策略
- 开发环境：持续集成与测试
- 测试环境：功能与性能测试
- 生产环境：灰度发布与全量发布

## 11. API接口列表

系统提供标准的RESTful API接口，主要分为以下几类：

### 11.1 用户与权限管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 用户登录 | POST | /api/auth/login | 用户登录认证 |
| 获取用户信息 | GET | /api/admin/users/{userId} | 获取用户详细信息 |
| 用户列表 | GET | /api/admin/users | 获取用户列表 |
| 新增用户 | POST | /api/admin/users | 创建新用户 |
| 更新用户 | PUT | /api/admin/users/{userId} | 更新用户信息 |
| 删除用户 | DELETE | /api/admin/users/{userId} | 删除用户 |
| 角色列表 | GET | /api/admin/roles | 获取角色列表 |
| 权限列表 | GET | /api/admin/permissions | 获取权限列表 |

### 11.2 项目管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 项目列表 | POST | /api/u/projects/list | 获取项目列表 |
| 项目分页列表 | POST | /api/u/projects/page | 获取分页项目列表 |
| 项目详情 | GET | /api/u/projects/{projectId} | 获取项目详细信息 |
| 创建项目 | POST | /api/u/projects | 创建新项目 |
| 更新项目 | PUT | /api/u/projects/{projectId} | 更新项目信息 |
| 删除项目 | DELETE | /api/u/projects/{projectId} | 删除项目 |
| 项目仪表盘 | GET | /api/u/projects/dashboard | 获取项目仪表盘数据 |
| 项目计划列表 | GET | /api/u/projects/{projectId}/plans | 获取项目计划列表 |
| 项目里程碑列表 | GET | /api/u/projects/{projectId}/milestones | 获取项目里程碑列表 |

### 11.3 合同管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 合同列表 | POST | /api/admin/contracts/page | 获取合同分页列表 |
| 合同详情 | GET | /api/admin/contracts/{contractId} | 获取合同详细信息 |
| 创建合同 | POST | /api/admin/contracts | 创建新合同 |
| 更新合同 | PUT | /api/admin/contracts/{contractId} | 更新合同信息 |
| 删除合同 | DELETE | /api/admin/contracts/{contractId} | 删除合同 |
| 合同里程碑列表 | GET | /api/admin/contracts/{contractId}/milestones | 获取合同里程碑列表 |
| 合同金额统计 | GET | /api/admin/contracts/statistics | 获取合同金额统计 |

### 11.4 客户管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 客户列表 | GET | /api/admin/clients | 获取客户列表 |
| 客户详情 | GET | /api/admin/clients/{clientId} | 获取客户详细信息 |
| 创建客户 | POST | /api/admin/clients | 创建新客户 |
| 更新客户 | PUT | /api/admin/clients/{clientId} | 更新客户信息 |
| 删除客户 | DELETE | /api/admin/clients/{clientId} | 删除客户 |
| 客户联系记录 | GET | /api/admin/clients/{clientId}/contacts | 获取客户联系记录 |
| 客户关联项目 | GET | /api/admin/clients/{clientId}/projects | 获取客户关联项目 |

### 11.5 采购管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 供应商列表 | GET | /api/admin/suppliers | 获取供应商列表 |
| 供应商详情 | GET | /api/admin/suppliers/{supplierId} | 获取供应商详细信息 |
| 创建供应商 | POST | /api/admin/suppliers | 创建新供应商 |
| 更新供应商 | PUT | /api/admin/suppliers/{supplierId} | 更新供应商信息 |
| 删除供应商 | DELETE | /api/admin/suppliers/{supplierId} | 删除供应商 |
| 采购申请列表 | GET | /api/admin/procurement/requests | 获取采购申请列表 |
| 采购订单列表 | GET | /api/admin/procurement/orders | 获取采购订单列表 |
| 采购看板数据 | GET | /api/admin/procurement/dashboard | 获取采购看板数据 |

### 11.6 财务管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 财务概览 | GET | /api/admin/finance/overview | 获取财务概览 |
| 应收账款列表 | GET | /api/admin/finance/receivables | 获取应收账款列表 |
| 应付账款列表 | GET | /api/admin/finance/payables | 获取应付账款列表 |
| 收入计划列表 | GET | /api/admin/finance/income-plans | 获取收入计划列表 |
| 预算列表 | GET | /api/admin/finance/budgets | 获取预算列表 |
| 成本数据 | GET | /api/admin/finance/costs | 获取成本数据 |
| 现金流数据 | GET | /api/admin/finance/cashflow | 获取现金流数据 |

## 12. 前后台对接时序图

### 12.1 用户登录认证时序图

```plantuml
@startuml
actor 用户
participant "前端应用" as Frontend
participant "后端认证服务" as AuthService
database "用户数据库" as DB

用户 -> Frontend: 1.输入用户名和密码
Frontend -> AuthService: 2.发送登录请求 POST /api/auth/login
AuthService -> DB: 3.验证用户凭证
DB --> AuthService: 4.返回用户数据
AuthService -> AuthService: 5.生成JWT令牌
AuthService --> Frontend: 6.返回令牌和用户信息
Frontend -> Frontend: 7.保存令牌到本地存储
Frontend --> 用户: 8.登录成功，重定向到主页
@enduml
```

### 12.2 项目创建流程时序图

```plantuml
@startuml
actor 用户
participant "前端应用" as Frontend
participant "项目服务" as ProjectService
participant "用户服务" as UserService
database "项目数据库" as ProjectDB
database "用户数据库" as UserDB

用户 -> Frontend: 1.打开项目创建表单
Frontend -> UserService: 2.获取用户列表 GET /api/admin/users
UserService -> UserDB: 3.查询用户数据
UserDB --> UserService: 4.返回用户数据
UserService --> Frontend: 5.返回用户列表
Frontend --> 用户: 6.显示项目创建表单和可选择的用户

用户 -> Frontend: 7.填写项目信息并提交
Frontend -> ProjectService: 8.发送创建请求 POST /api/u/projects
ProjectService -> ProjectService: 9.数据验证
ProjectService -> ProjectDB: 10.保存项目数据
ProjectDB --> ProjectService: 11.返回创建结果
ProjectService --> Frontend: 12.返回创建结果
Frontend --> 用户: 13.显示项目创建成功提示
@enduml
```

### 12.3 数据校验时序图

```plantuml
@startuml
actor 用户
participant "Dkm平台\nAI对话框" as DkmAI
participant "Maxkb\n工作流引擎" as MaxkbWorkflow
participant "微服务平台\n数据校验服务" as MicroService
database "MySQL数据库" as MySQL

用户 -> DkmAI: 1.输入查询问题数据的请求
activate DkmAI

DkmAI -> MaxkbWorkflow: 2.调用工作流处理用户请求
activate MaxkbWorkflow

MaxkbWorkflow -> MicroService: 3.发起数据校验API请求
activate MicroService

MicroService -> MySQL: 4.查询相关表数据
activate MySQL
MySQL --> MicroService: 5.返回查询结果
deactivate MySQL

MicroService -> MicroService: 6.执行数据校验处理\n(检查重名/空值等)
MicroService --> MaxkbWorkflow: 7.返回校验结果
deactivate MicroService

MaxkbWorkflow -> MaxkbWorkflow: 8.格式化结果数据
MaxkbWorkflow --> DkmAI: 9.返回处理结果
deactivate MaxkbWorkflow

DkmAI --> 用户: 10.以对话形式展示问题数据
deactivate DkmAI
@enduml
```

## 13. 数据库表物理模型图

### 13.1 合同管理模块数据模型

```plantuml
@startuml

!define TABLE(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define PK(x) <b><color:#b8861b>x</color></b>
!define FK(x) <color:#aaaaaa>x</color>

TABLE(pms_market_contract, "pms_market_contract\n市场合同表") {
  PK(id) : varchar(64) '主键
  number : varchar(64) '合同编号
  name : varchar(64) '合同名称
  contract_type : varchar(64) '合同类型
  quote_id : varchar(64) '定价id
  quote_number : varchar(64) '报价编号
  tech_rsp_user : varchar(64) '技术负责人
  tech_rsp_dept : varchar(64) '承担部门
  contract_amt : decimal(20, 6) '合同金额
  currency : varchar(64) '币种
  tak_effect_date : datetime '生效日期
  end_date : datetime '终止日期
  content : varchar(1024) '主要内容
  quality_end_date : datetime '质保到期日
  quality_amt : decimal(20, 6) '质保金额
  sign_time : datetime '签订时间
  begin_time : datetime '开始时间
  end_time : datetime '结束时间
}

TABLE(pms_contract_milestone, "pms_contract_milestone\n合同里程碑表") {
  PK(id) : varchar(64) '主键
  milestone_name : varchar(64) '里程碑名称
  milestone_type : varchar(64) '里程碑类型1-里程碑 0-子里程碑
  parent_id : varchar(64) '关联里程碑Id
  tech_rsp_user : varchar(64) '技术负责人
  bus_rsp_user : varchar(64) '商务负责人
  plan_accept_date : datetime '合同约定验收日期
  milestone_amt : decimal(20, 2) '合同约定验收金额
  number : varchar(64) '里程碑编号
  actual_accept_date : datetime '实际验收日期
  actual_milestone_amt : decimal(10, 0) '实际验收金额
  FK(contract_id) : varchar(64) '合同id
  contract_number : varchar(64) '合同编号
  tax_rate : decimal(20, 2) '税率
}

TABLE(pmsx_quotation_management, "pmsx_quotation_management\n报价管理表") {
  PK(id) : varchar(64) '主键
  quotation_id : varchar(64) '报价单编码
  requirement_id : varchar(64) '需求ID
  quote_content : text '报价内容
  quote_plan_detail : text '报价方案详情
  quote_amt : decimal(20, 6) '报价金额
  currency : varchar(64) '报出币种
  floor_price : decimal(20, 6) '底线价格
  issue_time : datetime '报价发出时间
  issuer : varchar(64) '发出报价用户
  result : varchar(64) '报价结果
  quotation_status : varchar(64) '报价状态
}

TABLE(pms_requirement_mangement, "pms_requirement_mangement\n需求管理表") {
  PK(id) : varchar(64) '主键
  requirement_number : varchar(255) '需求编号
  requirement_name : varchar(64) '需求标题
  res_source : varchar(64) '需求来源
  bid_opening_tm : datetime '开标时间
  cust_person : varchar(64) '客户
  cust_person_name : varchar(64) '客户名称
  cust_scope : varchar(64) '客户范围
  cust_con_person : varchar(64) '客户主要联系人
  business_person : varchar(64) '商务接口人
  tech_res : varchar(64) '技术接口人(技术负责人)
  project_status : varchar(64) '需求状态
  response_status : varchar(64) '响应状态
}

TABLE(pms_client, "pms_client\n客户信息表") {
  PK(clientId) : varchar(64) '客户ID
  clientName : varchar(255) '客户名称
  contactPerson : varchar(64) '联系人
  email : varchar(64) '邮箱
  phone : varchar(64) '电话
  address : varchar(255) '地址
  industry : varchar(64) '所属行业
  source : varchar(64) '客户来源
  status : int '客户状态
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_supplier, "pms_supplier\n供应商信息表") {
  PK(supplierId) : varchar(64) '供应商ID
  supplierName : varchar(255) '供应商名称
  contactName : varchar(64) '联系人
  email : varchar(64) '邮箱
  phone : varchar(64) '电话
  address : varchar(255) '地址
  status : int '状态
  registrationDate : datetime '注册日期
  lastUpdated : datetime '最后更新日期
}

pms_market_contract "1" -- "n" pms_contract_milestone : 包含
pmsx_quotation_management "1" -- "1" pms_market_contract : 关联
pms_requirement_mangement "1" -- "n" pmsx_quotation_management : 包含
pms_client "1" -- "n" pms_market_contract : 签订
pms_client "1" -- "n" pms_requirement_mangement : 提出

@enduml
```

### 13.2 财务管理模块数据模型

```plantuml
@startuml

!define TABLE(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define PK(x) <b><color:#b8861b>x</color></b>
!define FK(x) <color:#aaaaaa>x</color>

TABLE(pms_account, "pms_account\n账户表") {
  PK(accountId) : varchar(64) '账户ID
  accountName : varchar(64) '账户名称
  accountType : varchar(32) '账户类型
  balance : decimal(20,6) '余额
  currency : varchar(16) '币种
  status : int '状态
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_transaction, "pms_transaction\n交易表") {
  PK(transactionId) : varchar(64) '交易ID
  date : datetime '交易日期
  description : varchar(255) '交易描述
  amount : decimal(20,6) '交易金额
  type : varchar(16) '交易类型
  FK(accountId) : varchar(64) '关联账户ID
  status : int '状态
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_budget, "pms_budget\n预算表") {
  PK(budgetId) : varchar(64) '预算ID
  name : varchar(64) '预算名称
  period : varchar(32) '预算周期
  startDate : datetime '开始日期
  endDate : datetime '结束日期
  amount : decimal(20,6) '预算金额
  actualAmount : decimal(20,6) '实际支出
  status : int '状态
  FK(departmentId) : varchar(64) '部门ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_income_plan, "pms_income_plan\n收入计划表") {
  PK(planId) : varchar(64) '计划ID
  name : varchar(64) '计划名称
  planDate : datetime '计划日期
  planAmount : decimal(20,6) '计划金额
  actualAmount : decimal(20,6) '实际金额
  status : int '状态
  FK(projectId) : varchar(64) '项目ID
  FK(contractId) : varchar(64) '合同ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_cost, "pms_cost\n成本表") {
  PK(costId) : varchar(64) '成本ID
  name : varchar(64) '成本名称
  amount : decimal(20,6) '成本金额
  costDate : datetime '成本发生日期
  costType : varchar(32) '成本类型
  status : int '状态
  FK(projectId) : varchar(64) '项目ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_receivable, "pms_receivable\n应收账款表") {
  PK(receivableId) : varchar(64) '应收ID
  amount : decimal(20,6) '应收金额
  dueDate : datetime '到期日期
  status : int '状态
  FK(clientId) : varchar(64) '客户ID
  FK(contractId) : varchar(64) '合同ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_payable, "pms_payable\n应付账款表") {
  PK(payableId) : varchar(64) '应付ID
  amount : decimal(20,6) '应付金额
  dueDate : datetime '到期日期
  status : int '状态
  FK(supplierId) : varchar(64) '供应商ID
  FK(procurementId) : varchar(64) '采购ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

pms_account "1" -- "n" pms_transaction : 包含
pms_income_plan "1" -- "1" pms_receivable : 对应
pms_budget "1" -- "n" pms_cost : 控制
pms_cost "n" -- "1" pms_payable : 产生

@enduml
```

## 14. 前台系统与第三方系统对接时序图

### 14.1 项目管理系统与CRM系统对接时序图

```plantuml
@startuml
participant "项目管理前台系统" as PMS
participant "项目管理API网关" as Gateway
participant "CRM系统API" as CRM
database "CRM数据库" as CRMDB

PMS -> Gateway: 1.请求客户信息 GET /api/external/crm/clients
Gateway -> CRM: 2.转发请求至CRM API
CRM -> CRMDB: 3.查询客户数据
CRMDB --> CRM: 4.返回客户数据
CRM --> Gateway: 5.返回客户数据结果
Gateway --> PMS: 6.格式化并返回客户数据
PMS -> PMS: 7.展示客户信息

PMS -> Gateway: 8.同步新增客户 POST /api/external/crm/clients
Gateway -> CRM: 9.转发客户创建请求
CRM -> CRMDB: 10.保存客户数据
CRMDB --> CRM: 11.返回保存结果
CRM --> Gateway: 12.返回创建结果
Gateway --> PMS: 13.返回同步结果
@enduml
```

### 14.2 项目管理系统与财务系统对接时序图

```plantuml
@startuml
participant "项目管理前台系统" as PMS
participant "项目管理API网关" as Gateway
participant "财务系统API" as Finance
database "财务系统数据库" as FinanceDB

PMS -> Gateway: 1.请求合同收款信息 GET /api/external/finance/contracts/{contractId}/payments
Gateway -> Finance: 2.转发请求至财务系统API
Finance -> FinanceDB: 3.查询收款记录
FinanceDB --> Finance: 4.返回收款数据
Finance --> Gateway: 5.返回收款记录
Gateway --> PMS: 6.格式化并返回收款信息
PMS -> PMS: 7.展示合同收款情况

PMS -> Gateway: 8.同步应收账款信息 POST /api/external/finance/receivables
Gateway -> Finance: 9.转发应收账款创建请求
Finance -> FinanceDB: 10.保存应收账款数据
FinanceDB --> Finance: 11.返回保存结果
Finance --> Gateway: 12.返回创建结果
Gateway --> PMS: 13.返回同步结果
@enduml
```

### 14.3 项目管理系统与人力资源系统对接时序图

```plantuml
@startuml
participant "项目管理前台系统" as PMS
participant "项目管理API网关" as Gateway
participant "HR系统API" as HR
database "HR系统数据库" as HRDB

PMS -> Gateway: 1.请求员工信息 GET /api/external/hr/employees
Gateway -> HR: 2.转发请求至HR系统API
HR -> HRDB: 3.查询员工数据
HRDB --> HR: 4.返回员工数据
HR --> Gateway: 5.返回员工信息
Gateway --> PMS: 6.格式化并返回员工信息
PMS -> PMS: 7.展示可选员工列表

PMS -> Gateway: 8.同步项目人员工时数据 POST /api/external/hr/timesheet
Gateway -> HR: 9.转发工时数据
HR -> HRDB: 10.保存工时记录
HRDB --> HR: 11.返回保存结果
HR --> Gateway: 12.返回同步结果
Gateway --> PMS: 13.显示同步完成状态
@enduml
```

## 15. 系统模块与页面设计

### 15.1 用户前台系统模块结构

```
用户前台系统
├── 登录页面
├── 主控制台
│   ├── 个人工作台
│   │   ├── 待办事项
│   │   ├── 日常工作
│   │   └── 个人报告
│   └── 数据概览
├── 项目管理
│   ├── 项目仪表盘
│   ├── 项目列表
│   ├── 项目详情
│   │   ├── 项目信息
│   │   ├── 项目计划
│   │   ├── 项目成员
│   │   ├── 项目文档
│   │   └── 项目风险
│   └── 工作报告
├── 资源管理
│   ├── 人力资源
│   ├── 工作量管理
│   └── 资源调配
├── 成本控制
│   ├── 预算管理
│   ├── 成本管理
│   └── 收入计划
├── 合同管理
│   ├── 合同列表
│   ├── 合同详情
│   └── 合同跟踪
├── 采购管理
│   ├── 供应商管理
│   ├── 采购申请
│   ├── 采购订单
│   └── 采购看板
├── 市场经营
│   ├── 客户管理
│   └── 市场分析
├── 财务管理
│   ├── 账务总览
│   ├── 收入管理
│   └── 成本管理
└── 绩效管理
    ├── 绩效模板
    ├── 绩效库
    └── 绩效报告
```

### 15.2 管理员后台系统模块结构

```
管理员后台系统
├── 登录页面
├── 系统管理
│   ├── 用户管理
│   ├── 角色管理
│   ├── 菜单管理
│   ├── 权限管理
│   └── 系统配置
├── 项目库管理
│   ├── 项目分类
│   └── 项目状态管理
├── 资源池管理
│   ├── 人力资源池
│   └── 岗位管理
├── 财务管理
│   ├── 财务报表
│   ├── 账务管理
│   └── 财务配置
├── 合同模板管理
│   ├── 合同模板库
│   └── 合同条款库
├── 文档管理
│   ├── 文档模板库
│   ├── 证书标准库
│   └── 文档分类
└── 系统监控
    ├── 操作日志
    ├── 登录日志
    └── 系统性能
=======
# 项目管理平台产品设计文档

## 1. 产品概述

### 1.1 产品简介
项目管理平台是一款完整的企业级项目管理系统，提供全面的项目管理、资源调配、成本控制、合同管理等功能，帮助企业高效管理项目全生命周期。系统分为管理员后台和用户前台两个部分，满足不同角色用户的使用需求。

### 1.2 系统架构
系统采用前后端分离架构:
- 前端：Vue3 + Ant Design Vue + Vben + Less + Webpack + Qiankun微服务方案
- 后端：Spring Boot + Spring Cloud + MyBatis + Nacos + RabbitMQ

## 2. 系统角色与用户

### 2.1 角色划分
- 系统管理员：拥有系统的全部配置与管理权限
- 项目管理人员：负责项目创建、计划制定、跟踪与管理
- 财务人员：负责项目成本、预算、收入管理
- 合同管理人员：负责合同创建与管理
- 资源管理人员：负责人员、设备等资源调配
- 普通用户：项目参与者，使用系统进行日常工作

### 2.2 访问入口
- 管理员后台：http://183.136.206.207:1189/login (账号: admin，密码: orion2023!@#$)
- 用户前台：http://183.136.206.207:1199/login (账号: heqiang，密码: Abc123456789!)

## 3. 功能模块

### 3.1 系统管理模块
- 用户管理：用户增删改查、角色分配
  - 用户列表页面 ![用户列表页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_39_PM.png)
    - **主要功能**: 查看用户列表、搜索用户、添加用户、编辑用户信息、删除用户、分配角色。
    - **调用API（推测）**:
      - `GET /api/admin/users` (获取用户列表)
      - `POST /api/admin/users` (新增用户)
      - `PUT /api/admin/users/{userId}` (更新用户)
      - `DELETE /api/admin/users/{userId}` (删除用户)
      - `GET /api/admin/roles` (获取角色列表以供分配)
- 角色管理：角色创建、权限分配
- 菜单管理：系统菜单配置
- 权限管理：功能权限、数据权限设置
- 系统配置：系统参数设置、日志管理

### 3.2 项目管理模块
- 项目立项：项目创建、项目信息管理
  - 项目仪表盘/列表页面 (前台) ![前台项目管理仪表盘](./images/front_system/精益管理一体化平台-05-15-2025_08_13_PM.png)
    - **主要功能**: 展示用户相关的项目列表、项目概览、快速入口到项目详情或创建新项目。
    - **调用API（推测）**:
      - `GET /api/u/projects` (获取用户相关的项目列表)
      - `GET /api/u/projects/{projectId}/summary` (获取项目摘要)
  - 项目创建/编辑页面 (前台) ![前台项目创建表单](./images/front_system/精益管理一体化平台-05-15-2025_08_12_PM.png)
    - **主要功能**: 填写项目基本信息、选择项目模板、设置项目成员、提交项目立项申请。
    - **调用API（推测）**:
      - `POST /api/u/projects` (创建新项目)
      - `PUT /api/u/projects/{projectId}` (更新项目信息)
      - `GET /api/u/project-templates` (获取项目模板列表)
      - `GET /api/u/users` (获取用户列表以供选择项目成员)
- 项目库管理：项目分类、项目状态管理
- 项目计划：计划制定、WBS管理、甘特图显示
- 项目状态监控：进度监控、里程碑管理
- 项目风险管理：风险识别、风险控制
- 工作报告：日报、周报管理

### 3.3 资源管理模块
- 人力资源管理：人员池、技术人员配置
- 岗位管理：岗位设置、角色配置
- 工作量管理：工时记录、工作量统计
- 资源调配：资源分配、调度管理

### 3.4 成本控制模块
- 预算管理：预算编制、预算控制
- 成本管理：成本核算、成本分析
- 收入计划：收入预测、收入管理
- 财务管理：账务管理、财务报表

### 3.5 合同管理模块
- 合同创建：合同起草、合同模板
- 合同跟踪：合同执行状态跟踪
- 合同变更：合同变更管理
- 合同分析：合同执行情况分析

### 3.6 采购管理模块
- 供应商管理：供应商信息维护
  - 供应商列表页面  
    ![前台供应商管理列表](./images/front_system/采购管理_供应商信息报表_公司级合格供应商.png)  
    ![后台供应商列表页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_37_PM.png)
    - **主要功能**: 查看供应商列表、添加新供应商、编辑供应商详情、管理供应商合同/产品。
    - **调用API（推测）**:
      - `GET /api/admin/suppliers` (获取供应商列表)
      - `POST /api/admin/suppliers` (创建新供应商)
      - `GET /api/admin/suppliers/{supplierId}` (获取供应商详情)
      - `PUT /api/admin/suppliers/{supplierId}` (更新供应商信息)
      - `DELETE /api/admin/suppliers/{supplierId}` (删除供应商)
    - **时序图 (获取列表)**:
      ```plantuml
      @startuml
      actor User
      participant "Frontend" as FE
      participant "Backend API" as BE
      database "Database" as DB

      User -> FE: 查看供应商列表
      FE -> BE: GET /api/admin/suppliers
      BE -> DB: 查询供应商数据
      DB --> BE: 返回供应商数据
      BE --> FE: [供应商列表]
      FE -> User: 显示供应商列表
      @enduml
      ```
    - **数据模型 (供应商实体)**:
      ```plantuml
      @startuml
      object Supplier {
        supplierId (PK)
        supplierName
        contactName
        email
        phone
        address
        status
        supplierType
        qualificationLevel
        registrationDate
        lastUpdated
      }
      @enduml
      ```
    - **页面元素**:
      - 供应商搜索框：按名称、联系人、资质等级等字段搜索
      - 供应商分类筛选：公司级合格供应商、资审中供应商、不良行为供应商等
      - 供应商列表表格：显示供应商名称、联系人、联系方式、资质等级等
      - 操作按钮：新增、编辑、删除、查看详情
      - 批量操作：导入、导出、批量审核
  
  - 供应商详情页面  
    ![前台供应商详情与评估](./images/front_system/采购管理_供应商管理_技术配置合同管理.png)
    - **主要功能**: 
      - 查看供应商详细信息
      - 浏览历史合作记录
      - 管理产品目录
      - 供应商评估打分
      - 技术人员配置管理
    - **调用API（推测）**:
      - `GET /api/admin/suppliers/{supplierId}` (获取供应商详情)
      - `GET /api/admin/suppliers/{supplierId}/products` (获取供应商产品)
      - `GET /api/admin/suppliers/{supplierId}/history` (获取历史合作记录)
      - `POST /api/admin/suppliers/{supplierId}/evaluate` (提交供应商评估)
      - `GET /api/admin/suppliers/{supplierId}/technical-staff` (获取技术人员配置)
    - **页面元素**:
      - 供应商基本信息卡片：基本信息、联系方式、资质等级等
      - 合作历史记录表：历史合作项目、合同履行情况、评价等
      - 产品服务列表：供应商提供的产品及服务清单
      - 技术人员配置表格：技术人员资质、专业方向、工作经历等
      - 评估打分面板：多维度评估打分界面

  - 技术配置人员管理页面  
    ![前台技术人员配置管理](./images/front_system/采购管理_技术配置人员管理.png)
    - **主要功能**: 
      - 技术人员资质审核
      - 专业方向管理
      - 人员使用记录
      - 能力评估管理
    - **调用API（推测）**:
      - `GET /api/admin/technical-staff` (获取技术人员列表)
      - `POST /api/admin/technical-staff` (新增技术人员)
      - `PUT /api/admin/technical-staff/{staffId}` (更新技术人员信息)
      - `GET /api/admin/technical-staff/{staffId}/records` (获取使用记录)
    - **页面元素**:
      - 搜索筛选区：按姓名、专业方向、供应商筛选
      - 技术人员列表：姓名、专业、级别、所属供应商等
      - 资质证书附件管理：上传下载资质附件
      - 历史使用记录：参与项目、工作表现、客户评价等

- 采购管理：采购计划、采购执行
  - 采购合同管理页面  
    ![前台采购合同管理界面](./images/front_system/采购管理_框架合同列表.png)
    - **主要功能**: 
      - 合同列表管理
      - 框架合同与总价合同管理
      - 合同状态跟踪
      - 合同文档管理
    - **调用API（推测）**:
      - `GET /api/admin/procurement/contracts` (获取采购合同列表)
      - `POST /api/admin/procurement/contracts` (创建采购合同)
      - `PUT /api/admin/procurement/contracts/{contractId}` (更新合同信息)
      - `GET /api/admin/procurement/contracts/{contractId}/documents` (获取合同文档)
    - **页面元素**:
      - 合同搜索区：按合同编号、供应商、状态等搜索
      - 合同类型切换：框架合同、总价合同切换视图
      - 合同列表表格：合同编号、名称、供应商、金额、签约日期、状态等
      - 操作按钮：查看详情、编辑、文档管理、状态更新

  - 采购订单管理页面  
    ![前台采购订单管理界面](./images/front_system/采购管理_集采订单管理.png)
    - **主要功能**: 
      - 订单创建与管理
      - 订单状态跟踪
      - 订单审批流程
      - 收货与付款管理
    - **调用API（推测）**:
      - `GET /api/admin/procurement/orders` (获取采购订单列表)
      - `POST /api/admin/procurement/orders` (创建采购订单)
      - `PUT /api/admin/procurement/orders/{orderId}/status` (更新订单状态)
      - `POST /api/admin/procurement/orders/{orderId}/receive` (记录收货)
      - `POST /api/admin/procurement/orders/{orderId}/payment` (记录付款)
    - **页面元素**:
      - 订单搜索区：按订单号、供应商、状态等搜索
      - 订单列表表格：订单编号、关联合同、物料/服务、金额、交付日期、状态等
      - 状态流转图：直观显示订单当前状态
      - 操作按钮：查看详情、编辑、收货确认、付款记录

  - 合同变更与索赔管理页面  
    ![前台合同变更与索赔管理](./images/front_system/采购管理_变更_索赔_终止.png)
    - **主要功能**: 
      - 合同变更申请与审批
      - 索赔管理
      - 合同终止管理
      - 争议处理
    - **调用API（推测）**:
      - `GET /api/admin/procurement/contracts/{contractId}/changes` (获取变更记录)
      - `POST /api/admin/procurement/contracts/{contractId}/changes` (提交变更申请)
      - `POST /api/admin/procurement/contracts/{contractId}/claims` (提交索赔)
      - `POST /api/admin/procurement/contracts/{contractId}/terminate` (申请终止合同)
    - **页面元素**:
      - 合同基本信息区：显示当前合同关键信息
      - 变更记录列表：历史变更内容、原因、状态等
      - 索赔记录表格：索赔原因、金额、处理进度等
      - 终止申请流程图：终止流程各环节状态
      - 表单区：提交新的变更/索赔/终止申请

- 采购数据分析：采购状态可视化
  - 采购数据分析看板  
    ![前台采购数据分析仪表盘](./images/front_system/采购管理_供应商管理_技术配置人员管理_数据分析.png)
    - **主要功能**: 
      - 采购总额统计与分析
      - 供应商分布分析
      - 品类分析
      - 交付时间统计
      - 异常订单预警
    - **调用API（推测）**:
      - `GET /api/admin/procurement/dashboard` (获取采购看板数据)
      - `GET /api/admin/procurement/analytics/suppliers` (获取供应商分析数据)
      - `GET /api/admin/procurement/analytics/categories` (获取品类分析数据)
      - `GET /api/admin/procurement/analytics/delivery` (获取交付分析数据)
      - `GET /api/admin/procurement/alerts` (获取异常订单预警)
    - **页面元素**:
      - 关键指标卡片：采购总额、平均交付周期、合同履约率、供应商数量等
      - 供应商分布饼图：不同类型供应商的分布比例
      - 品类分布柱状图：各采购品类的金额占比
      - 交付时间趋势图：按时间段的交付周期变化
      - 异常订单预警列表：逾期交付、质量问题等异常情况
      - 数据筛选器：时间范围、供应商类型、合同类型等筛选条件

### 3.7 绩效管理模块
- 绩效模板：绩效考核模板管理
- 绩效库：绩效指标管理
- 绩效报告：绩效分析报告

### 3.8 文档管理模块
- 文档模板库：文档模板管理
- 项目文档：项目相关文档管理
- 证书标准：证书与标准文档管理

### 3.9 个人工作台
- 待办事项：个人任务待办
- 日常工作：日常工作管理
- 个人报告：个人工作报告

### 3.10 市场经营模块

市场经营模块是项目管理平台的核心业务模块之一，主要负责客户关系管理、商机跟踪、市场分析和竞争情报收集，为企业营销决策提供数据支持。

#### 3.10.1 客户管理

客户管理子模块实现对客户全生命周期的管理，提供客户信息维护、客户分类、客户关系跟踪等功能。

- **客户列表页面**  
  ![前台客户关系管理界面](./images/front_system/精益管理一体化平台-05-15-2025_08_13_PM.png)  
  ![后台客户列表页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_38_PM.png)
  - **主要功能**:
    - 客户列表显示与分页
    - 按名称、行业、状态等条件筛选客户
    - 添加新客户
    - 批量导入/导出客户信息
    - 客户分类与标签管理
  - **调用API（推测）**:
    - `GET /api/admin/clients` (获取客户列表)
    - `POST /api/admin/clients` (创建新客户)
    - `GET /api/admin/clients/{clientId}` (获取客户详情)
    - `PUT /api/admin/clients/{clientId}` (更新客户信息)
    - `DELETE /api/admin/clients/{clientId}` (删除客户)
    - `POST /api/admin/clients/import` (批量导入客户)
    - `GET /api/admin/clients/export` (导出客户数据)
  - **时序图 (添加客户)**:
    ```plantuml
    @startuml
    actor User
    participant "Frontend" as FE
    participant "Backend API" as BE
    database "Database" as DB

    User -> FE: 点击"添加客户"按钮
    FE -> User: 显示客户信息表单
    User -> FE: 填写客户信息并提交
    FE -> BE: POST /api/admin/clients (客户数据)
    BE -> BE: 校验数据
    alt 数据有效
      BE -> DB: 插入新客户记录
      DB --> BE: 插入成功
      BE --> FE: {status: "success", clientId: "newId"}
      FE -> User: 显示成功消息，并可能跳转到客户详情或刷新列表
    else 数据无效
      BE --> FE: {status: "error", errors: [...]} 
      FE -> User: 显示错误信息
    end
    @enduml
    ```
  - **数据模型 (客户实体)**:
    ```plantuml
    @startuml
    object Client {
      clientId (PK)
      clientName
      contactPerson
      email
      phone
      address
      industry
      source
      status
      createdDate
      updatedDate
      tags
      customerLevel
      lastContactDate
      nextFollowUpDate
    }
    @enduml
    ```
  - **页面元素**:
    - 客户搜索框：按名称、联系人、行业等字段搜索
    - 高级筛选区：行业、地区、来源、客户等级筛选
    - 客户列表表格：显示客户名称、联系人、联系方式、所属行业、客户来源等
    - 操作按钮：新增、编辑、删除、查看详情
    - 批量操作：导入、导出、批量删除、批量分配

- **客户详情页面**  
  ![前台客户详情与联系记录](./images/front_system/精益管理一体化平台-05-15-2025_08_11_PM.png)
  - **主要功能**:
    - 查看客户基本信息
    - 管理联系人信息
    - 记录客户沟通历史
    - 维护客户关联项目
    - 客户文档管理
    - 客户评级与标签
  - **调用API（推测）**:
    - `GET /api/admin/clients/{clientId}` (获取客户详情)
    - `GET /api/admin/clients/{clientId}/contacts` (获取客户联系记录)
    - `POST /api/admin/clients/{clientId}/contacts` (添加联系记录)
    - `GET /api/admin/clients/{clientId}/projects` (获取客户关联项目)
    - `PUT /api/admin/clients/{clientId}/tags` (更新客户标签)
    - `PUT /api/admin/clients/{clientId}/level` (更新客户等级)
  - **页面元素**:
    - 客户信息卡片：基本信息、联系方式、行业分类等
    - 联系记录时间线：按时间顺序显示所有沟通记录
    - 联系人列表：客户的多位联系人信息及职位
    - 关联项目列表：与该客户相关的所有项目及状态
    - 客户标签管理：可视化标签编辑区
    - 客户评级选择器：设置客户重要程度

- **客户统计分析页面**  
  ![前台客户统计分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_38_PM.png)
  - **主要功能**:
    - 客户行业分布统计
    - 客户来源渠道分析
    - 客户增长趋势图表
    - 客户价值评估
    - 客户活跃度分析
  - **调用API（推测）**:
    - `GET /api/admin/clients/statistics/industry` (获取行业分布数据)
    - `GET /api/admin/clients/statistics/source` (获取来源渠道数据)
    - `GET /api/admin/clients/statistics/growth` (获取增长趋势数据)
    - `GET /api/admin/clients/statistics/value` (获取客户价值分析)
  - **页面元素**:
    - 概览数据卡片：总客户数、新增客户数、活跃客户数、流失客户数
    - 行业分布饼图：按行业显示客户占比
    - 来源渠道柱状图：不同渠道获客数量对比
    - 客户增长趋势线图：按月/季度/年显示客户增长情况
    - 客户等级分布：不同等级客户的数量与占比
    - 数据筛选器：时间范围、客户类型、地区等筛选条件

#### 3.10.2 商机管理

商机管理子模块支持对潜在业务机会的跟踪和转化，包括商机录入、跟进、评估和转化为项目等功能。

- **商机列表页面**  
  ![前台商机跟踪管理界面](./images/front_system/精益管理一体化平台-05-15-2025_08_02_PM.png)
  - **主要功能**:
    - 商机列表展示与筛选
    - 新增商机
    - 商机状态管理
    - 商机优先级设置
    - 商机转化跟踪
  - **调用API（推测）**:
    - `GET /api/admin/opportunities` (获取商机列表)
    - `POST /api/admin/opportunities` (创建新商机)
    - `PUT /api/admin/opportunities/{opportunityId}` (更新商机信息)
    - `PUT /api/admin/opportunities/{opportunityId}/status` (更新商机状态)
    - `PUT /api/admin/opportunities/{opportunityId}/priority` (更新商机优先级)
    - `POST /api/admin/opportunities/{opportunityId}/convert` (将商机转化为项目)
  - **数据模型 (商机实体)**:
    ```plantuml
    @startuml
    object Opportunity {
      opportunityId (PK)
      title
      description
      expectedValue
      probability
      status
      priority
      source
      clientId (FK)
      contactId (FK)
      ownerId (FK)
      createdDate
      updatedDate
      expectedClosingDate
      followUpDate
      stageId (FK)
    }
    @enduml
    ```
  - **页面元素**:
    - 商机搜索筛选区：按状态、优先级、客户、负责人筛选
    - 商机列表表格：商机名称、客户、预期价值、状态、负责人等
    - 商机看板视图切换：可在表格和看板视图间切换
    - 商机添加按钮：快速创建新商机
    - 操作按钮：编辑、删除、状态更新、转为项目

- **商机详情页面**  
  ![前台商机详细信息界面](./images/front_system/精益管理一体化平台-05-15-2025_08_01_PM.png)
  - **主要功能**:
    - 查看商机基本信息
    - 追踪商机跟进记录
    - 商机阶段管理
    - 商机关联文档
    - 商机相关活动
  - **调用API（推测）**:
    - `GET /api/admin/opportunities/{opportunityId}` (获取商机详情)
    - `GET /api/admin/opportunities/{opportunityId}/activities` (获取跟进活动)
    - `POST /api/admin/opportunities/{opportunityId}/activities` (添加跟进记录)
    - `PUT /api/admin/opportunities/{opportunityId}/stage` (更新商机阶段)
    - `POST /api/admin/opportunities/{opportunityId}/documents` (上传商机文档)
  - **页面元素**:
    - 商机信息卡片：基本信息、预期价值、成功概率等
    - 商机阶段进度条：直观显示当前阶段及流程
    - 跟进记录时间线：所有跟进活动的历史记录
    - 相关文档列表：报价单、方案文档等相关材料
    - 关联客户信息：客户基本情况及联系方式

- **销售漏斗页面**  
  ![前台销售漏斗分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_57_PM.png)
  - **主要功能**:
    - 销售漏斗可视化
    - 各阶段商机数量和金额统计
    - 转化率分析
    - 销售周期分析
    - 赢单/输单原因分析
  - **调用API（推测）**:
    - `GET /api/admin/opportunities/funnel` (获取销售漏斗数据)
    - `GET /api/admin/opportunities/conversion-rates` (获取转化率数据)
    - `GET /api/admin/opportunities/cycle-time` (获取销售周期数据)
    - `GET /api/admin/opportunities/win-loss` (获取赢单/输单分析)
  - **页面元素**:
    - 漏斗图：各阶段商机数量和金额的漏斗可视化
    - 统计卡片：总商机数、平均销售周期、平均成交金额、整体转化率
    - 阶段转化率图表：各阶段间的转化百分比
    - 时间分析图表：销售周期长度分布
    - 赢单/输单原因饼图：归类的原因占比分析

#### 3.10.3 市场分析

市场分析子模块提供市场数据可视化和分析功能，帮助决策者了解市场趋势和机会。

- **市场分析仪表盘**  
  ![前台市场分析数据仪表盘](./images/front_system/精益管理一体化平台-05-15-2025_07_38_PM.png)
  - **主要功能**:
    - 市场趋势总览
    - 客户行业分布分析
    - 区域市场分析
    - 销售漏斗分析
    - 业务增长趋势
  - **调用API（推测）**:
    - `GET /api/admin/market/dashboard` (获取市场分析仪表盘数据)
    - `GET /api/admin/market/trends` (获取市场趋势数据)
    - `GET /api/admin/market/regions` (获取区域市场数据)
    - `GET /api/admin/market/conversion-rates` (获取转化率数据)
    - `GET /api/admin/market/growth` (获取业务增长数据)
  - **页面元素**:
    - 业务概览卡片：各项关键指标的当前值和环比变化
    - 行业分布热力图：客户行业分布可视化
    - 销售漏斗图：各阶段商机数量和转化率
    - 区域市场地图：不同地区业务量的地图展示
    - 增长趋势线图：按时间展示的业务增长曲线
    - 筛选器：时间范围、产品线、销售团队等筛选维度

- **市场预测分析页面**  
  ![前台市场预测与规划界面](./images/front_system/精益管理一体化平台-05-15-2025_07_42_PM.png)
  - **主要功能**:
    - 销售预测分析
    - 市场潜力评估
    - 季度/年度目标设定
    - 市场发展趋势预测
    - 业绩目标跟踪
  - **调用API（推测）**:
    - `GET /api/admin/market/forecasts` (获取销售预测数据)
    - `GET /api/admin/market/potential` (获取市场潜力评估)
    - `POST /api/admin/market/targets` (设置销售目标)
    - `GET /api/admin/market/targets/progress` (获取目标完成进度)
  - **页面元素**:
    - 预测分析图表：基于历史数据的销售预测曲线
    - 目标设定面板：输入和调整销售目标的界面
    - 目标完成度进度条：各目标的实时完成情况
    - 市场机会热图：潜在市场机会的可视化展示
    - 趋势线与预测带：含置信区间的业务发展预测

#### 3.10.4 竞争对手分析

竞争对手分析子模块帮助企业收集和分析竞争情报，为制定竞争策略提供依据。

- **竞争对手列表页面**  
  ![前台竞争对手管理界面](./images/front_system/精益管理一体化平台-05-15-2025_07_47_PM.png)
  - **主要功能**:
    - 竞争对手基本信息管理
    - 竞争对手产品/服务记录
    - 竞争对手动态跟踪
    - 竞争情报收集
  - **调用API（推测）**:
    - `GET /api/admin/competitors` (获取竞争对手列表)
    - `POST /api/admin/competitors` (创建新竞争对手)
    - `PUT /api/admin/competitors/{competitorId}` (更新竞争对手信息)
    - `GET /api/admin/competitors/{competitorId}/products` (获取竞争对手产品)
    - `POST /api/admin/competitors/intelligence` (记录竞争情报)
  - **数据模型 (竞争对手实体)**:
    ```plantuml
    @startuml
    object Competitor {
      competitorId (PK)
      name
      description
      website
      foundedYear
      headquartersLocation
      employeeCount
      marketShare
      strengthPoints
      weaknessPoints
      threatsToUs
      opportunitiesForUs
      createdDate
      updatedDate
    }
    @enduml
    ```
  - **页面元素**:
    - 竞争对手列表表格：名称、市场份额、优劣势等
    - 竞争对手添加按钮：创建新竞争对手记录
    - 情报收集入口：快速记录最新竞争情报
    - 筛选选项：按行业、地区、规模等筛选竞争对手
    - 操作按钮：编辑、删除、查看详情

- **竞争对手详情分析页面**  
  ![前台竞争对手详情分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_46_PM.png)
  - **主要功能**:
    - 竞争对手详细信息查看
    - 产品/服务对比分析
    - 价格策略分析
    - 市场动态记录
    - SWOT分析
  - **调用API（推测）**:
    - `GET /api/admin/competitors/{competitorId}` (获取竞争对手详情)
    - `GET /api/admin/competitors/{competitorId}/products` (获取产品对比)
    - `GET /api/admin/competitors/{competitorId}/pricing` (获取价格策略)
    - `GET /api/admin/competitors/{competitorId}/news` (获取市场动态)
    - `GET /api/admin/competitors/{competitorId}/swot` (获取SWOT分析)
  - **页面元素**:
    - 竞争对手信息卡：基本信息、市场地位、发展历程
    - 产品对比表格：我方与竞争对手产品功能对比
    - 价格策略分析图：价格定位和策略变化趋势
    - 市场动态时间线：重要事件和新闻
    - SWOT分析矩阵：直观展示优势、劣势、机会和威胁

- **市场份额分析页面**  
  ![前台市场份额分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_41_PM.png)
  - **主要功能**:
    - 市场份额可视化
    - 市场格局分析
    - 竞争态势演变
    - 行业集中度分析
    - 细分市场分析
  - **调用API（推测）**:
    - `GET /api/admin/market-share` (获取市场份额数据)
    - `GET /api/admin/market-share/trends` (获取市场份额趋势)
    - `GET /api/admin/market-share/segments` (获取细分市场数据)
    - `GET /api/admin/market/concentration` (获取行业集中度数据)
  - **页面元素**:
    - 市场份额饼图：各竞争者市场份额占比
    - 趋势变化线图：市场份额随时间变化情况
    - 细分市场柱状图：各细分市场的份额分布
    - 竞争格局雷达图：从多维度比较主要竞争者
    - 筛选器：产品线、地区、时间范围等筛选维度

### 3.10.5 营销活动管理

营销活动管理子模块支持企业策划、执行和评估各类营销活动，实现营销过程的全程管理。

- **活动列表页面**  
  ![前台营销活动管理界面](./images/front_system/精益管理一体化平台-05-15-2025_07_39_PM.png)
  - **主要功能**:
    - 营销活动计划管理
    - 活动执行追踪
    - 活动日历视图
    - 活动资源分配
    - 活动状态监控
  - **调用API（推测）**:
    - `GET /api/admin/campaigns` (获取营销活动列表)
    - `POST /api/admin/campaigns` (创建新活动)
    - `PUT /api/admin/campaigns/{campaignId}` (更新活动信息)
    - `GET /api/admin/campaigns/calendar` (获取活动日历数据)
    - `PUT /api/admin/campaigns/{campaignId}/status` (更新活动状态)
  - **数据模型 (营销活动实体)**:
    ```plantuml
    @startuml
    object Campaign {
      campaignId (PK)
      name
      description
      type
      targetAudience
      startDate
      endDate
      budget
      actualCost
      status
      expectedROI
      actualROI
      channels
      goals
      createdBy (FK)
      createdDate
      updatedDate
    }
    @enduml
    ```
  - **页面元素**:
    - 活动列表表格：名称、类型、时间、预算、状态等
    - 日历/甘特图视图：可视化活动时间安排
    - 活动添加按钮：创建新营销活动
    - 状态筛选器：按计划中、进行中、已完成等状态筛选
    - 资源分配图表：展示各活动的资源分配情况

- **活动详情与效果分析页面**  
  ![前台活动效果分析界面](./images/front_system/精益管理一体化平台-05-15-2025_07_37_PM.png)
  - **主要功能**:
    - 活动详情查看
    - KPI指标追踪
    - 转化漏斗分析
    - 投资回报率计算
    - 活动效果评估
  - **调用API（推测）**:
    - `GET /api/admin/campaigns/{campaignId}` (获取活动详情)
    - `GET /api/admin/campaigns/{campaignId}/kpis` (获取KPI指标)
    - `GET /api/admin/campaigns/{campaignId}/funnel` (获取转化漏斗数据)
    - `GET /api/admin/campaigns/{campaignId}/roi` (获取ROI分析)
    - `POST /api/admin/campaigns/{campaignId}/evaluation` (提交效果评估)
  - **页面元素**:
    - 活动基本信息卡：目标、预算、时间、负责人等
    - KPI仪表盘：关键指标的目标值与实际值对比
    - 转化漏斗图：从曝光到转化的各环节数据
    - ROI计算器与图表：投入产出比分析
    - 活动效果评分面板：多维度评估活动效果

### 3.11 财务管理模块
#### 3.11.1 账务管理
- 账务总览页面  
  ![前台财务数据仪表盘](./images/front_system/精益管理一体化平台-05-15-2025_08_10_PM.png)  
  ![后台账务总览页面原型图](./images/后台系统/数字化管理平台-05-15-2025_08_36_PM.png)
  - **主要功能**: 展示关键财务指标、应收/应付账款摘要、现金流概览。
  - **调用API（推测）**:
    - `GET /api/admin/finance/overview` (获取财务概览)
    - `GET /api/admin/finance/receivables` (获取应收账款)
    - `GET /api/admin/finance/payables` (获取应付账款)
    - `GET /api/admin/finance/cashflow` (获取现金流数据)
  - **时序图 (获取财务概览)**:
    ```plantuml
    @startuml
    actor User
    participant "Frontend" as FE
    participant "Backend API" as BE
    database "Database" as DB

    User -> FE: 访问账务总览页面
    FE -> BE: GET /api/admin/finance/overview
    BE -> DB: 查询并聚合财务数据 (例如：总收入、总支出、利润等)
    DB --> BE: 返回聚合后的财务概览数据
    BE --> FE: [财务概览数据]
    FE -> User: 在仪表盘上显示关键指标和图表
    @enduml
    ```
  - **数据模型 (核心财务实体)**:
    ```plantuml
    @startuml
    entity Account {
      + accountId (PK)
      --
      accountName
      accountType (e.g., Asset, Liability, Equity, Revenue, Expense)
      balance
    }

    entity Transaction {
      + transactionId (PK)
      --
      date
      description
      amount
      type (Debit/Credit)
      accountId (FK)
    }

    entity FinancialReport {
        + reportId (PK)
        --
        reportType (e.g., Balance Sheet, Income Statement)
        periodStartDate
        periodEndDate
        generatedDate
        data (JSON/Text)
    }
    Transaction -- Account : belongs to
    @enduml
    ```

#### 3.11.2 收入管理
- 收入计划页面  
  ![前台收入计划管理界面](./images/front_system/精益管理一体化平台-05-15-2025_07_59_PM.png)
  - **主要功能**: 制定收入计划、跟踪实际收入、差异分析、调整预测。
  - **调用API（推测）**:
    - `GET /api/admin/finance/income-plans` (获取收入计划列表)
    - `POST /api/admin/finance/income-plans` (创建收入计划)
    - `PUT /api/admin/finance/income-plans/{planId}` (更新收入计划)
    - `GET /api/admin/finance/income-plans/{planId}/analysis` (获取计划与实际差异分析)

- 应收账款管理页面  
  ![前台应收账款跟踪界面](./images/front_system/精益管理一体化平台-05-15-2025_07_56_PM.png)
  - **主要功能**: 查看应收账款、账龄分析、催收管理、收款记录。
  - **调用API（推测）**:
    - `GET /api/admin/finance/receivables` (获取应收账款列表)
    - `GET /api/admin/finance/receivables/aging` (获取账龄分析)
    - `POST /api/admin/finance/receivables/{receivableId}/collection` (记录催收活动)
    - `POST /api/admin/finance/receivables/{receivableId}/payment` (记录收款)

#### 3.11.3 成本管理
- 成本分析页面  
  ![前台成本分析与控制界面](./images/front_system/精益管理一体化平台-05-15-2025_07_55_PM.png)
  - **主要功能**: 查看成本构成、成本趋势分析、成本控制点识别、成本优化建议。
  - **调用API（推测）**:
    - `GET /api/admin/finance/costs` (获取成本数据)
    - `GET /api/admin/finance/costs/analysis` (获取成本分析)
    - `GET /api/admin/finance/costs/trends` (获取成本趋势)
    - `GET /api/admin/finance/costs/optimization` (获取成本优化建议)

- 预算管理页面  
  ![前台预算编制与审批界面](./images/front_system/精益管理一体化平台-05-15-2025_07_54_PM.png)
  - **主要功能**: 创建预算、预算审批流程、预算执行跟踪、预算调整。
  - **调用API（推测）**:
    - `GET /api/admin/finance/budgets` (获取预算列表)
    - `POST /api/admin/finance/budgets` (创建预算)
    - `PUT /api/admin/finance/budgets/{budgetId}` (更新预算)
    - `GET /api/admin/finance/budgets/{budgetId}/tracking` (获取预算执行情况)

## 4. 业务流程

### 4.1 项目立项流程
1. 创建项目申请
2. 项目信息填写
3. 项目审批
4. 项目立项
5. 项目初始化

### 4.2 项目计划管理流程
1. 编制项目计划
2. 计划审批
3. 计划发布
4. 计划执行
5. 执行监控
6. 计划调整

### 4.3 资源调配流程
1. 资源需求提出
2. 资源审批
3. 资源分配
4. 资源使用
5. 资源回收

### 4.4 合同管理流程
1. 合同需求
2. 合同起草
3. 合同审批
4. 合同签订
5. 合同执行
6. 合同变更
7. 合同归档

### 4.5 成本管理流程
1. 预算编制
2. 预算审批
3. 成本控制
4. 成本分析
5. 成本报告

## 5. 技术架构

### 5.1 前端架构
- 前端框架：Vue3
- UI组件库：Ant Design Vue
- 微前端框架：Qiankun
- 构建工具：Webpack
- 样式：Less
- 状态管理：Pinia
- 路由管理：Vue Router

### 5.2 后端架构
- 开发框架：Spring Boot, Spring Cloud
- 持久层：MyBatis
- 服务注册与发现：Nacos
- 消息队列：RabbitMQ
- 缓存：Redis
- 数据库：MySQL
- 安全框架：Spring Security

### 5.3 微服务模块划分
- pms-app：应用服务与系统入口
- pms-project：项目管理服务
- pms-cost：成本管理服务
- pms-project-contract：合同管理服务
- pms-api：API接口服务
- pms-interface：接口定义服务
- pms-project-management：项目管理服务
- pms-production：生产管理服务
- pms-finance：财务管理服务
- pms-common：公共服务

## 6. 非功能性需求

### 6.1 性能需求
- 系统响应时间：页面加载<3秒
- 数据处理能力：支持100+并发用户访问
- 数据存储容量：支持大规模项目数据存储

### 6.2 安全性需求
- 用户认证与授权
- 数据加密与保护
- 安全审计与日志
- 权限控制与数据隔离

### 6.3 可用性需求
- 系统可用率>99.9%
- 系统维护与升级不影响正常业务
- 多浏览器兼容支持

### 6.4 扩展性需求
- 支持业务规模扩展
- 支持功能模块扩展
- 支持与第三方系统集成

## 7. 数据模型

系统主要包含以下核心数据实体：
- 用户与角色
- 项目与项目计划
- 资源与资源分配
- 合同与合同条款
- 成本与预算
- 工作报告与日志

## 8. 界面设计原则

- 界面简洁清晰，符合企业应用风格
- 操作流程符合用户习惯，减少学习成本
- 重要信息突出显示，提高用户感知效率
- 表单设计合理，减少用户输入负担
- 数据可视化展示，提升信息获取效率

## 9. 接口设计

系统主要对内提供REST API接口，遵循RESTful设计规范，支持JSON格式的数据交换。

## 10. 部署与发布

### 10.1 部署环境
- 操作系统：Linux
- Web服务器：Nginx
- 应用服务器：Spring Boot内置Tomcat
- 数据库服务器：MySQL
- 中间件：Redis, RabbitMQ, Nacos

### 10.2 发布策略
- 开发环境：持续集成与测试
- 测试环境：功能与性能测试
- 生产环境：灰度发布与全量发布

## 11. API接口列表

系统提供标准的RESTful API接口，主要分为以下几类：

### 11.1 用户与权限管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 用户登录 | POST | /api/auth/login | 用户登录认证 |
| 获取用户信息 | GET | /api/admin/users/{userId} | 获取用户详细信息 |
| 用户列表 | GET | /api/admin/users | 获取用户列表 |
| 新增用户 | POST | /api/admin/users | 创建新用户 |
| 更新用户 | PUT | /api/admin/users/{userId} | 更新用户信息 |
| 删除用户 | DELETE | /api/admin/users/{userId} | 删除用户 |
| 角色列表 | GET | /api/admin/roles | 获取角色列表 |
| 权限列表 | GET | /api/admin/permissions | 获取权限列表 |

### 11.2 项目管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 项目列表 | POST | /api/u/projects/list | 获取项目列表 |
| 项目分页列表 | POST | /api/u/projects/page | 获取分页项目列表 |
| 项目详情 | GET | /api/u/projects/{projectId} | 获取项目详细信息 |
| 创建项目 | POST | /api/u/projects | 创建新项目 |
| 更新项目 | PUT | /api/u/projects/{projectId} | 更新项目信息 |
| 删除项目 | DELETE | /api/u/projects/{projectId} | 删除项目 |
| 项目仪表盘 | GET | /api/u/projects/dashboard | 获取项目仪表盘数据 |
| 项目计划列表 | GET | /api/u/projects/{projectId}/plans | 获取项目计划列表 |
| 项目里程碑列表 | GET | /api/u/projects/{projectId}/milestones | 获取项目里程碑列表 |

### 11.3 合同管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 合同列表 | POST | /api/admin/contracts/page | 获取合同分页列表 |
| 合同详情 | GET | /api/admin/contracts/{contractId} | 获取合同详细信息 |
| 创建合同 | POST | /api/admin/contracts | 创建新合同 |
| 更新合同 | PUT | /api/admin/contracts/{contractId} | 更新合同信息 |
| 删除合同 | DELETE | /api/admin/contracts/{contractId} | 删除合同 |
| 合同里程碑列表 | GET | /api/admin/contracts/{contractId}/milestones | 获取合同里程碑列表 |
| 合同金额统计 | GET | /api/admin/contracts/statistics | 获取合同金额统计 |

### 11.4 客户管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 客户列表 | GET | /api/admin/clients | 获取客户列表 |
| 客户详情 | GET | /api/admin/clients/{clientId} | 获取客户详细信息 |
| 创建客户 | POST | /api/admin/clients | 创建新客户 |
| 更新客户 | PUT | /api/admin/clients/{clientId} | 更新客户信息 |
| 删除客户 | DELETE | /api/admin/clients/{clientId} | 删除客户 |
| 客户联系记录 | GET | /api/admin/clients/{clientId}/contacts | 获取客户联系记录 |
| 客户关联项目 | GET | /api/admin/clients/{clientId}/projects | 获取客户关联项目 |

### 11.5 采购管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 供应商列表 | GET | /api/admin/suppliers | 获取供应商列表 |
| 供应商详情 | GET | /api/admin/suppliers/{supplierId} | 获取供应商详细信息 |
| 创建供应商 | POST | /api/admin/suppliers | 创建新供应商 |
| 更新供应商 | PUT | /api/admin/suppliers/{supplierId} | 更新供应商信息 |
| 删除供应商 | DELETE | /api/admin/suppliers/{supplierId} | 删除供应商 |
| 采购申请列表 | GET | /api/admin/procurement/requests | 获取采购申请列表 |
| 采购订单列表 | GET | /api/admin/procurement/orders | 获取采购订单列表 |
| 采购看板数据 | GET | /api/admin/procurement/dashboard | 获取采购看板数据 |

### 11.6 财务管理接口

| 接口名称 | 请求方式 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 财务概览 | GET | /api/admin/finance/overview | 获取财务概览 |
| 应收账款列表 | GET | /api/admin/finance/receivables | 获取应收账款列表 |
| 应付账款列表 | GET | /api/admin/finance/payables | 获取应付账款列表 |
| 收入计划列表 | GET | /api/admin/finance/income-plans | 获取收入计划列表 |
| 预算列表 | GET | /api/admin/finance/budgets | 获取预算列表 |
| 成本数据 | GET | /api/admin/finance/costs | 获取成本数据 |
| 现金流数据 | GET | /api/admin/finance/cashflow | 获取现金流数据 |

## 12. 前后台对接时序图

### 12.1 用户登录认证时序图

```plantuml
@startuml
actor 用户
participant "前端应用" as Frontend
participant "后端认证服务" as AuthService
database "用户数据库" as DB

用户 -> Frontend: 1.输入用户名和密码
Frontend -> AuthService: 2.发送登录请求 POST /api/auth/login
AuthService -> DB: 3.验证用户凭证
DB --> AuthService: 4.返回用户数据
AuthService -> AuthService: 5.生成JWT令牌
AuthService --> Frontend: 6.返回令牌和用户信息
Frontend -> Frontend: 7.保存令牌到本地存储
Frontend --> 用户: 8.登录成功，重定向到主页
@enduml
```

### 12.2 项目创建流程时序图

```plantuml
@startuml
actor 用户
participant "前端应用" as Frontend
participant "项目服务" as ProjectService
participant "用户服务" as UserService
database "项目数据库" as ProjectDB
database "用户数据库" as UserDB

用户 -> Frontend: 1.打开项目创建表单
Frontend -> UserService: 2.获取用户列表 GET /api/admin/users
UserService -> UserDB: 3.查询用户数据
UserDB --> UserService: 4.返回用户数据
UserService --> Frontend: 5.返回用户列表
Frontend --> 用户: 6.显示项目创建表单和可选择的用户

用户 -> Frontend: 7.填写项目信息并提交
Frontend -> ProjectService: 8.发送创建请求 POST /api/u/projects
ProjectService -> ProjectService: 9.数据验证
ProjectService -> ProjectDB: 10.保存项目数据
ProjectDB --> ProjectService: 11.返回创建结果
ProjectService --> Frontend: 12.返回创建结果
Frontend --> 用户: 13.显示项目创建成功提示
@enduml
```

### 12.3 数据校验时序图

```plantuml
@startuml
actor 用户
participant "Dkm平台\nAI对话框" as DkmAI
participant "Maxkb\n工作流引擎" as MaxkbWorkflow
participant "微服务平台\n数据校验服务" as MicroService
database "MySQL数据库" as MySQL

用户 -> DkmAI: 1.输入查询问题数据的请求
activate DkmAI

DkmAI -> MaxkbWorkflow: 2.调用工作流处理用户请求
activate MaxkbWorkflow

MaxkbWorkflow -> MicroService: 3.发起数据校验API请求
activate MicroService

MicroService -> MySQL: 4.查询相关表数据
activate MySQL
MySQL --> MicroService: 5.返回查询结果
deactivate MySQL

MicroService -> MicroService: 6.执行数据校验处理\n(检查重名/空值等)
MicroService --> MaxkbWorkflow: 7.返回校验结果
deactivate MicroService

MaxkbWorkflow -> MaxkbWorkflow: 8.格式化结果数据
MaxkbWorkflow --> DkmAI: 9.返回处理结果
deactivate MaxkbWorkflow

DkmAI --> 用户: 10.以对话形式展示问题数据
deactivate DkmAI
@enduml
```

## 13. 数据库表物理模型图

### 13.1 合同管理模块数据模型

```plantuml
@startuml

!define TABLE(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define PK(x) <b><color:#b8861b>x</color></b>
!define FK(x) <color:#aaaaaa>x</color>

TABLE(pms_market_contract, "pms_market_contract\n市场合同表") {
  PK(id) : varchar(64) '主键
  number : varchar(64) '合同编号
  name : varchar(64) '合同名称
  contract_type : varchar(64) '合同类型
  quote_id : varchar(64) '定价id
  quote_number : varchar(64) '报价编号
  tech_rsp_user : varchar(64) '技术负责人
  tech_rsp_dept : varchar(64) '承担部门
  contract_amt : decimal(20, 6) '合同金额
  currency : varchar(64) '币种
  tak_effect_date : datetime '生效日期
  end_date : datetime '终止日期
  content : varchar(1024) '主要内容
  quality_end_date : datetime '质保到期日
  quality_amt : decimal(20, 6) '质保金额
  sign_time : datetime '签订时间
  begin_time : datetime '开始时间
  end_time : datetime '结束时间
}

TABLE(pms_contract_milestone, "pms_contract_milestone\n合同里程碑表") {
  PK(id) : varchar(64) '主键
  milestone_name : varchar(64) '里程碑名称
  milestone_type : varchar(64) '里程碑类型1-里程碑 0-子里程碑
  parent_id : varchar(64) '关联里程碑Id
  tech_rsp_user : varchar(64) '技术负责人
  bus_rsp_user : varchar(64) '商务负责人
  plan_accept_date : datetime '合同约定验收日期
  milestone_amt : decimal(20, 2) '合同约定验收金额
  number : varchar(64) '里程碑编号
  actual_accept_date : datetime '实际验收日期
  actual_milestone_amt : decimal(10, 0) '实际验收金额
  FK(contract_id) : varchar(64) '合同id
  contract_number : varchar(64) '合同编号
  tax_rate : decimal(20, 2) '税率
}

TABLE(pmsx_quotation_management, "pmsx_quotation_management\n报价管理表") {
  PK(id) : varchar(64) '主键
  quotation_id : varchar(64) '报价单编码
  requirement_id : varchar(64) '需求ID
  quote_content : text '报价内容
  quote_plan_detail : text '报价方案详情
  quote_amt : decimal(20, 6) '报价金额
  currency : varchar(64) '报出币种
  floor_price : decimal(20, 6) '底线价格
  issue_time : datetime '报价发出时间
  issuer : varchar(64) '发出报价用户
  result : varchar(64) '报价结果
  quotation_status : varchar(64) '报价状态
}

TABLE(pms_requirement_mangement, "pms_requirement_mangement\n需求管理表") {
  PK(id) : varchar(64) '主键
  requirement_number : varchar(255) '需求编号
  requirement_name : varchar(64) '需求标题
  res_source : varchar(64) '需求来源
  bid_opening_tm : datetime '开标时间
  cust_person : varchar(64) '客户
  cust_person_name : varchar(64) '客户名称
  cust_scope : varchar(64) '客户范围
  cust_con_person : varchar(64) '客户主要联系人
  business_person : varchar(64) '商务接口人
  tech_res : varchar(64) '技术接口人(技术负责人)
  project_status : varchar(64) '需求状态
  response_status : varchar(64) '响应状态
}

TABLE(pms_client, "pms_client\n客户信息表") {
  PK(clientId) : varchar(64) '客户ID
  clientName : varchar(255) '客户名称
  contactPerson : varchar(64) '联系人
  email : varchar(64) '邮箱
  phone : varchar(64) '电话
  address : varchar(255) '地址
  industry : varchar(64) '所属行业
  source : varchar(64) '客户来源
  status : int '客户状态
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_supplier, "pms_supplier\n供应商信息表") {
  PK(supplierId) : varchar(64) '供应商ID
  supplierName : varchar(255) '供应商名称
  contactName : varchar(64) '联系人
  email : varchar(64) '邮箱
  phone : varchar(64) '电话
  address : varchar(255) '地址
  status : int '状态
  registrationDate : datetime '注册日期
  lastUpdated : datetime '最后更新日期
}

pms_market_contract "1" -- "n" pms_contract_milestone : 包含
pmsx_quotation_management "1" -- "1" pms_market_contract : 关联
pms_requirement_mangement "1" -- "n" pmsx_quotation_management : 包含
pms_client "1" -- "n" pms_market_contract : 签订
pms_client "1" -- "n" pms_requirement_mangement : 提出

@enduml
```

### 13.2 财务管理模块数据模型

```plantuml
@startuml

!define TABLE(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define PK(x) <b><color:#b8861b>x</color></b>
!define FK(x) <color:#aaaaaa>x</color>

TABLE(pms_account, "pms_account\n账户表") {
  PK(accountId) : varchar(64) '账户ID
  accountName : varchar(64) '账户名称
  accountType : varchar(32) '账户类型
  balance : decimal(20,6) '余额
  currency : varchar(16) '币种
  status : int '状态
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_transaction, "pms_transaction\n交易表") {
  PK(transactionId) : varchar(64) '交易ID
  date : datetime '交易日期
  description : varchar(255) '交易描述
  amount : decimal(20,6) '交易金额
  type : varchar(16) '交易类型
  FK(accountId) : varchar(64) '关联账户ID
  status : int '状态
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_budget, "pms_budget\n预算表") {
  PK(budgetId) : varchar(64) '预算ID
  name : varchar(64) '预算名称
  period : varchar(32) '预算周期
  startDate : datetime '开始日期
  endDate : datetime '结束日期
  amount : decimal(20,6) '预算金额
  actualAmount : decimal(20,6) '实际支出
  status : int '状态
  FK(departmentId) : varchar(64) '部门ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_income_plan, "pms_income_plan\n收入计划表") {
  PK(planId) : varchar(64) '计划ID
  name : varchar(64) '计划名称
  planDate : datetime '计划日期
  planAmount : decimal(20,6) '计划金额
  actualAmount : decimal(20,6) '实际金额
  status : int '状态
  FK(projectId) : varchar(64) '项目ID
  FK(contractId) : varchar(64) '合同ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_cost, "pms_cost\n成本表") {
  PK(costId) : varchar(64) '成本ID
  name : varchar(64) '成本名称
  amount : decimal(20,6) '成本金额
  costDate : datetime '成本发生日期
  costType : varchar(32) '成本类型
  status : int '状态
  FK(projectId) : varchar(64) '项目ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_receivable, "pms_receivable\n应收账款表") {
  PK(receivableId) : varchar(64) '应收ID
  amount : decimal(20,6) '应收金额
  dueDate : datetime '到期日期
  status : int '状态
  FK(clientId) : varchar(64) '客户ID
  FK(contractId) : varchar(64) '合同ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

TABLE(pms_payable, "pms_payable\n应付账款表") {
  PK(payableId) : varchar(64) '应付ID
  amount : decimal(20,6) '应付金额
  dueDate : datetime '到期日期
  status : int '状态
  FK(supplierId) : varchar(64) '供应商ID
  FK(procurementId) : varchar(64) '采购ID
  createdDate : datetime '创建日期
  updatedDate : datetime '更新日期
}

pms_account "1" -- "n" pms_transaction : 包含
pms_income_plan "1" -- "1" pms_receivable : 对应
pms_budget "1" -- "n" pms_cost : 控制
pms_cost "n" -- "1" pms_payable : 产生

@enduml
```

## 14. 前台系统与第三方系统对接时序图

### 14.1 项目管理系统与CRM系统对接时序图

```plantuml
@startuml
participant "项目管理前台系统" as PMS
participant "项目管理API网关" as Gateway
participant "CRM系统API" as CRM
database "CRM数据库" as CRMDB

PMS -> Gateway: 1.请求客户信息 GET /api/external/crm/clients
Gateway -> CRM: 2.转发请求至CRM API
CRM -> CRMDB: 3.查询客户数据
CRMDB --> CRM: 4.返回客户数据
CRM --> Gateway: 5.返回客户数据结果
Gateway --> PMS: 6.格式化并返回客户数据
PMS -> PMS: 7.展示客户信息

PMS -> Gateway: 8.同步新增客户 POST /api/external/crm/clients
Gateway -> CRM: 9.转发客户创建请求
CRM -> CRMDB: 10.保存客户数据
CRMDB --> CRM: 11.返回保存结果
CRM --> Gateway: 12.返回创建结果
Gateway --> PMS: 13.返回同步结果
@enduml
```

### 14.2 项目管理系统与财务系统对接时序图

```plantuml
@startuml
participant "项目管理前台系统" as PMS
participant "项目管理API网关" as Gateway
participant "财务系统API" as Finance
database "财务系统数据库" as FinanceDB

PMS -> Gateway: 1.请求合同收款信息 GET /api/external/finance/contracts/{contractId}/payments
Gateway -> Finance: 2.转发请求至财务系统API
Finance -> FinanceDB: 3.查询收款记录
FinanceDB --> Finance: 4.返回收款数据
Finance --> Gateway: 5.返回收款记录
Gateway --> PMS: 6.格式化并返回收款信息
PMS -> PMS: 7.展示合同收款情况

PMS -> Gateway: 8.同步应收账款信息 POST /api/external/finance/receivables
Gateway -> Finance: 9.转发应收账款创建请求
Finance -> FinanceDB: 10.保存应收账款数据
FinanceDB --> Finance: 11.返回保存结果
Finance --> Gateway: 12.返回创建结果
Gateway --> PMS: 13.返回同步结果
@enduml
```

### 14.3 项目管理系统与人力资源系统对接时序图

```plantuml
@startuml
participant "项目管理前台系统" as PMS
participant "项目管理API网关" as Gateway
participant "HR系统API" as HR
database "HR系统数据库" as HRDB

PMS -> Gateway: 1.请求员工信息 GET /api/external/hr/employees
Gateway -> HR: 2.转发请求至HR系统API
HR -> HRDB: 3.查询员工数据
HRDB --> HR: 4.返回员工数据
HR --> Gateway: 5.返回员工信息
Gateway --> PMS: 6.格式化并返回员工信息
PMS -> PMS: 7.展示可选员工列表

PMS -> Gateway: 8.同步项目人员工时数据 POST /api/external/hr/timesheet
Gateway -> HR: 9.转发工时数据
HR -> HRDB: 10.保存工时记录
HRDB --> HR: 11.返回保存结果
HR --> Gateway: 12.返回同步结果
Gateway --> PMS: 13.显示同步完成状态
@enduml
```

## 15. 系统模块与页面设计

### 15.1 用户前台系统模块结构

```
用户前台系统
├── 登录页面
├── 主控制台
│   ├── 个人工作台
│   │   ├── 待办事项
│   │   ├── 日常工作
│   │   └── 个人报告
│   └── 数据概览
├── 项目管理
│   ├── 项目仪表盘
│   ├── 项目列表
│   ├── 项目详情
│   │   ├── 项目信息
│   │   ├── 项目计划
│   │   ├── 项目成员
│   │   ├── 项目文档
│   │   └── 项目风险
│   └── 工作报告
├── 资源管理
│   ├── 人力资源
│   ├── 工作量管理
│   └── 资源调配
├── 成本控制
│   ├── 预算管理
│   ├── 成本管理
│   └── 收入计划
├── 合同管理
│   ├── 合同列表
│   ├── 合同详情
│   └── 合同跟踪
├── 采购管理
│   ├── 供应商管理
│   ├── 采购申请
│   ├── 采购订单
│   └── 采购看板
├── 市场经营
│   ├── 客户管理
│   └── 市场分析
├── 财务管理
│   ├── 账务总览
│   ├── 收入管理
│   └── 成本管理
└── 绩效管理
    ├── 绩效模板
    ├── 绩效库
    └── 绩效报告
```

### 15.2 管理员后台系统模块结构

```
管理员后台系统
├── 登录页面
├── 系统管理
│   ├── 用户管理
│   ├── 角色管理
│   ├── 菜单管理
│   ├── 权限管理
│   └── 系统配置
├── 项目库管理
│   ├── 项目分类
│   └── 项目状态管理
├── 资源池管理
│   ├── 人力资源池
│   └── 岗位管理
├── 财务管理
│   ├── 财务报表
│   ├── 账务管理
│   └── 财务配置
├── 合同模板管理
│   ├── 合同模板库
│   └── 合同条款库
├── 文档管理
│   ├── 文档模板库
│   ├── 证书标准库
│   └── 文档分类
└── 系统监控
    ├── 操作日志
    ├── 登录日志
    └── 系统性能
>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
```