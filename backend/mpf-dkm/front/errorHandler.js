export default {
  install(app) {
    // 重写 console.log
    const originalConsoleLog = console.log;
    console.log = function(...args) {
      originalConsoleLog.apply(console, args);
      const logInfo = {
        timestamp: new Date().toISOString(),
        type: 'log',
        message: args.join(' '),
        page: localStorage.getItem('currentPage') || 'Unknown'
      };

      const logs = JSON.parse(localStorage.getItem('logs')) || [];
      logs.push(logInfo);
      localStorage.setItem('logs', JSON.stringify(logs));
    };

    // 重写 console.error
    const originalConsoleError = console.error;
    console.error = function(...args) {
      originalConsoleError.apply(console, args);
      const errorInfo = {
        timestamp: new Date().toISOString(),
        type: 'error',
        message: args.join(' '),
        page: localStorage.getItem('currentPage') || 'Unknown'
      };

      const errors = JSON.parse(localStorage.getItem('errors')) || [];
      errors.push(errorInfo);
      localStorage.setItem('errors', JSON.stringify(errors));
    };

    app.config.errorHandler = (err, vm, info) => {
      // 记录错误信息到 localStorage
      const errorInfo = {
        timestamp: new Date().toISOString(),
        message: err.message,
        stack: err.stack,
        component: vm ? vm.$options.name : 'Unknown',
        info: info,
        page: localStorage.getItem('currentPage') || 'Unknown'
      };

      const errors = JSON.parse(localStorage.getItem('errors')) || [];
      errors.push(errorInfo);
      localStorage.setItem('errors', JSON.stringify(errors));
    };

    window.addEventListener('unhandledrejection', event => {
      const errorInfo = {
        timestamp: new Date().toISOString(),
        message: event.reason.message,
        stack: event.reason.stack,
        component: 'Unhandled Rejection',
        info: 'Promise Rejection',
        page: localStorage.getItem('currentPage') || 'Unknown'
      };

      const errors = JSON.parse(localStorage.getItem('errors')) || [];
      errors.push(errorInfo);
      localStorage.setItem('errors', JSON.stringify(errors));
    });
  }
};