#!/bin/bash
mvn clean install -Pdev,SpringCloud

mkdir -p /mnt/c/code
cp ./target/*.jar /mnt/c/code/

# 检查文件是否存在
if [ ! -f ./agent/skywalking-agent.jar ]; then
    echo "skywalking-agent.jar 不存在，正在复制..."
    chmod 777 /data/agent
    mkdir -p ./agent/
    cp -rf /data/agent/ ./
    echo "复制完成。"
else
    echo "skywalking-agent.jar 已经存在，跳过复制。"
fi
# 在这里添加其他的 shell 语句
chmod 644 ./

# 创建网络（如果不存在）
docker network inspect dtc_net >/dev/null 2>&1 || docker network create --driver bridge dtc_net

# 构建镜像
echo "\033[44;37m build docker \033[0m"
docker build --no-cache -t jeecg-adapter-dkm ./

# 停止并删除已存在的容器（如果存在）
echo "\033[44;37m remove container \033[0m"
if [ "$(docker ps -aq -f name=jeecg-adapter-dkm)" ]; then
    docker stop jeecg-adapter-dkm
    docker rm jeecg-adapter-dkm
fi

# 运行新容器
echo "\033[44;37m run docker \033[0m"
docker run -d \
  --name jeecg-adapter-dkm \
  --hostname dkm \
  --restart on-failure \
  --network dtc_net \
  -p 7008:7008 \
  -e TZ=Asia/Shanghai \
  -e XMS=256m -e XMX=512m -e XSS=256k -e SKYWALKING_OAP=************* \
  -e NACOS_SERVER_ADDR=*************:8848 \
  --add-host skywalking-oap:************* \
  --add-host jeecg-boot-mysql:*********** \
  --add-host jeecg-boot-redis:*********** \
  --add-host jeecg-boot-nacos:************* \
  -v /data/agent:/data/agent \
  --memory=512m \
  jeecg-adapter-dkm

#chmod +x run_gateway.sh
#./run_gateway.sh