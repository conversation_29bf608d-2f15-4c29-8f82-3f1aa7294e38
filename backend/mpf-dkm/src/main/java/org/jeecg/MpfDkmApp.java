package org.jeecg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

@Slf4j
@SpringBootApplication
@EnableFeignClients
@EnableDiscoveryClient
@EnableSwagger2WebMvc
public class MpfDkmApp {

    public static void main(String[] args) {
        SpringApplication.run(MpfDkmApp.class, args);
        log.info("应用已成功启动！");
    }
}
