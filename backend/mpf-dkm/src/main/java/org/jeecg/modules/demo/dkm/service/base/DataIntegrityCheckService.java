package org.jeecg.modules.demo.dkm.service.base;

import org.jeecg.modules.demo.dkm.config.DuplicateCheckConfig;
import org.jeecg.modules.demo.dkm.config.FieldCheckConfig;
import org.jeecg.modules.demo.dkm.util.MarkdownTableUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 数据准确性检查基类
 * 提供通用的数据准确性检查方法，可被不同表的数据准确性检查复用
 *
 * <AUTHOR>
 * @date 2025-4-16
 */
@Slf4j
public abstract class DataIntegrityCheckService<T> {

    /**
     * 执行数据准确性检查，生成Markdown格式的检查报告
     *
     * @param dataSource        数据源名称
     * @param allIssuesSupplier 数据问题记录提供器
     * @param fieldConfigs      字段检查配置列表
     * @return Markdown格式的检查报告
     */
    public String executeIntegrityCheck(String dataSource, Supplier<List<T>> allIssuesSupplier,
                                        List<FieldCheckConfig<T>> fieldConfigs) {
        return executeIntegrityCheckWithDuplicates(dataSource, allIssuesSupplier, fieldConfigs, Collections.emptyList());
    }

    /**
     * 执行数据准确性检查，包含重复项检查，生成Markdown格式的检查报告
     *
     * @param dataSource        数据源名称
     * @param allIssuesSupplier 数据问题记录提供器
     * @param fieldConfigs      字段检查配置列表
     * @param duplicateConfigs  重复数据检查配置列表
     * @return Markdown格式的检查报告
     */
    public <E> String executeIntegrityCheckWithDuplicates(String dataSource, Supplier<List<T>> allIssuesSupplier,
                                                     List<FieldCheckConfig<T>> fieldConfigs, 
                                                     List<DuplicateCheckConfig<E>> duplicateConfigs) {
        StringBuilder markdownResult = new StringBuilder();
        markdownResult.append("数据来源：").append(dataSource).append("  \n");

        // 一次性查询所有数据准确性问题记录
        List<T> allIssues = allIssuesSupplier.get();

        // 检查所有问题记录和重复项是否都为空
        boolean allEmpty = allIssues.isEmpty() && duplicateConfigs.stream().allMatch(config -> config.getItems().isEmpty());
        
        if (allEmpty) {
            List<String> headers = Arrays.asList("检查项", "结果");
            List<List<String>> data = new ArrayList<>();
            data.add(Arrays.asList("数据准确性", "未发现数据缺失或重复问题"));

            markdownResult.append(MarkdownTableUtil.generateMarkdownTable(headers, data));
            return markdownResult.toString();
        }

        // 收集各类型的缺失数据
        Map<String, Integer> checkItemCounts = new HashMap<>();
        Map<String, List<T>> missingDataMap = new HashMap<>();
        int totalIssues = 0;

        // 处理字段检查配置
        for (FieldCheckConfig<T> config : fieldConfigs) {
            List<T> missing = allIssues.stream()
                    .filter(config.getMissingCondition())
                    .collect(Collectors.toList());
            missingDataMap.put(config.getFieldName(), missing);
            checkItemCounts.put(config.getFieldName(), missing.size());
            totalIssues += missing.size();
        }

        // 处理重复数据检查配置
        for (DuplicateCheckConfig<E> config : duplicateConfigs) {
            checkItemCounts.put(config.getName(), config.getItems().size());
            totalIssues += config.getItems().size();
        }

        // 输出汇总表格
        markdownResult.append("异常汇总\n\n");
        List<String> summaryHeaders = Arrays.asList("检查项", "异常数量");
        List<List<String>> summaryData = new ArrayList<>();

        // 添加字段检查项汇总数据
        for (FieldCheckConfig<T> config : fieldConfigs) {
            summaryData.add(Arrays.asList(
                    config.getFieldName(),
                    String.valueOf(checkItemCounts.get(config.getFieldName()))
            ));
        }
        
        // 添加重复数据检查项汇总数据
        for (DuplicateCheckConfig<E> config : duplicateConfigs) {
            summaryData.add(Arrays.asList(
                    config.getName(),
                    String.valueOf(checkItemCounts.get(config.getName()))
            ));
        }
        
        summaryData.add(Arrays.asList("合计", String.valueOf(totalIssues)));
        markdownResult.append(MarkdownTableUtil.generateMarkdownTable(summaryHeaders, summaryData));

        // 输出各类型缺失数据的详细信息
        for (FieldCheckConfig<T> config : fieldConfigs) {
            List<T> missingData = missingDataMap.get(config.getFieldName());
            if (!missingData.isEmpty()) {
                generateMissingDataTable(markdownResult, config.getFieldName(), missingData,
                        config.getHeaders(), config.getRowGenerator());
            }
        }

        // 输出重复数据检查项的详细信息
        for (DuplicateCheckConfig<E> config : duplicateConfigs) {
            if (!config.getItems().isEmpty()) {
                generateDuplicateDataTable(markdownResult, config.getName(), config.getItems(),
                        config.getHeaders(), config.getRowGenerator());
            }
        }

        log.info("数据准确性检查完成，发现问题数量：{}", totalIssues);
        return markdownResult.toString();
    }

    /**
     * 生成缺失数据的表格
     *
     * @param markdownResult 汇总结果
     * @param fieldName      字段名称
     * @param missingData    缺失数据列表
     * @param headers        表头
     * @param rowGenerator   行数据生成器
     */
    private <E> void generateMissingDataTable(StringBuilder markdownResult, String fieldName,
                                              List<E> missingData, List<String> headers,
                                              Function<E, List<String>> rowGenerator) {
        markdownResult.append("\n").append(fieldName).append("为空\n\n");
        generateDataTable(markdownResult, missingData, headers, rowGenerator);
    }
    
    /**
     * 生成重复数据的表格
     *
     * @param markdownResult 汇总结果
     * @param title          表格标题
     * @param items          重复数据列表
     * @param headers        表头
     * @param rowGenerator   行数据生成器
     */
    private <E> void generateDuplicateDataTable(StringBuilder markdownResult, String title,
                                              List<E> items, List<String> headers,
                                              Function<E, List<String>> rowGenerator) {
        markdownResult.append("\n").append(title).append("重名\n\n");
        generateDataTable(markdownResult, items, headers, rowGenerator);
    }
    
    /**
     * 生成数据表格
     *
     * @param markdownResult 汇总结果
     * @param items          数据列表
     * @param headers        表头
     * @param rowGenerator   行数据生成器
     */
    private <E> void generateDataTable(StringBuilder markdownResult, List<E> items,
                                      List<String> headers, Function<E, List<String>> rowGenerator) {
        List<List<String>> data = new ArrayList<>();
        for (E item : items) {
            data.add(rowGenerator.apply(item));
        }
        markdownResult.append(MarkdownTableUtil.generateMarkdownTable(headers, data));
    }
} 