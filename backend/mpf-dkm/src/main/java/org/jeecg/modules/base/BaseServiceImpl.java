package org.jeecg.modules.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public class BaseServiceImpl<M extends BaseMapper<T>,T> extends ServiceImpl<M,T> implements IBaseService<T> {

    @Override
    public <R> PageResult<R> customPageList(IPage<R> pageResult) {
        // return Optional.ofNullable(pageResult).map(p -> new PageResult<>(pageResult.getTotal(),pageResult.getRecords())).orElse(PageResult.emptyPageResult());
        return Optional.ofNullable(pageResult).map(p -> new PageResult<>(pageResult.getTotal(),pageResult.getRecords(),pageResult.getCurrent(),pageResult.getPages(),pageResult.getSize())).orElse(PageResult.emptyPageResult());
    }

    @Override
    public PageResult<T> pageList(PageParam pageParam, Wrapper<T> wrapper) {
        IPage<T> result = page(new Page<>(pageParam.getPageNo(), pageParam.getPageSize(), true), wrapper);
        // return Optional.ofNullable(result).map(p -> new PageResult<T>(result.getTotal(),result.getRecords())).orElse(PageResult.emptyPageResult());
        return Optional.ofNullable(result).map(p -> new PageResult<T>(result.getTotal(),result.getRecords(),result.getCurrent(),result.getPages(),result.getSize())).orElse(PageResult.emptyPageResult());
    }

    @Override
    public List<T> listByIds(Collection<? extends Serializable> idList) {
        return getBaseMapper().selectBatchIds(idList);
    }
}
