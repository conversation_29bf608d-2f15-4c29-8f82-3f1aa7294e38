<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.dkm.mapper.ContractMilestoneMapper">

    <!-- 查询数据完整性问题的记录 -->
    <select id="findMilestoneDataIntegrityIssues" resultType="org.jeecg.modules.demo.dkm.entity.ContractMilestone">
        SELECT id, milestone_name as milestoneName, number
        FROM pms_contract_milestone
        WHERE number IS NULL OR number = ''
    </select>

    <!-- 查找里程碑名称重复的数据 -->
    <select id="findDuplicateMilestoneNames" resultType="org.jeecg.modules.demo.dkm.entity.ContractMilestone">
        SELECT a.id, a.milestone_name as milestoneName, a.number, c.name as contractName
        FROM pms_contract_milestone a
        LEFT JOIN pms_market_contract c ON a.contract_id = c.id
        INNER JOIN (
            SELECT milestone_name, contract_id
            FROM pms_contract_milestone
            GROUP BY milestone_name, contract_id
            HAVING COUNT(*) > 1
        ) dup ON a.milestone_name = dup.milestone_name AND a.contract_id = dup.contract_id
    </select>
    
</mapper> 