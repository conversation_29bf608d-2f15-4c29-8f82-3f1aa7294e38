package org.jeecg.modules.demo.dkm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DtcPersonnelsDTO {
    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "员工号")
    private String staffNo;

    @ApiModelProperty(value = "姓名")
    private String staffName;

    @ApiModelProperty(value = "身份证号")
    private String staffIdCard;

    @ApiModelProperty(value = "部门编码")
    private String deptNo;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "第一负责人编号")
    private String deptLeaderId;

    @ApiModelProperty(value = "第二负责人编号")
    private String deptHeader2Id;

}
