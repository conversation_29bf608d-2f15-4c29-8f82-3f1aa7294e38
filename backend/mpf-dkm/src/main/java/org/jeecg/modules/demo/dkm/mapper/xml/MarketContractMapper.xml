<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.dkm.mapper.MarketContractMapper">

    <!-- 查询数据完整性问题的记录 -->
    <select id="findContractDataIntegrityIssues" resultType="org.jeecg.modules.demo.dkm.entity.MarketContract">
        SELECT m.id, m.name, m.number, m.commerce_rsp_user as commerceRspUser, m.tech_rsp_user as techRspUser,
               m.tech_rsp_dept as techRspDept, d.name as techRspDeptName
        FROM pms_market_contract m
        LEFT JOIN pmi_dept d ON d.id = m.tech_rsp_dept
        WHERE m.number IS NULL OR m.number = ''
        OR m.commerce_rsp_user IS NULL OR m.commerce_rsp_user = ''
        OR m.tech_rsp_user IS NULL OR m.tech_rsp_user = ''
    </select>

    <!-- 查找合同名称重复的数据 -->
    <select id="findDuplicateContractNames" resultType="org.jeecg.modules.demo.dkm.entity.MarketContract">
        SELECT a.id, a.name, a.number, a.commerce_rsp_user as commerceRspUser, a.tech_rsp_user as techRspUser, 
               r.requirement_name as requirementName, a.tech_rsp_dept as techRspDept, d.name as techRspDeptName
        FROM pms_market_contract a
        LEFT JOIN pms_requirement_mangement r ON a.requirement_id = r.id
        LEFT JOIN pmi_dept d ON d.id = a.tech_rsp_dept
        INNER JOIN (
            SELECT name, requirement_id
            FROM pms_market_contract
            GROUP BY name, requirement_id
            HAVING COUNT(*) > 1
            
            UNION
            
            SELECT name, NULL as requirement_id
            FROM pms_market_contract
            WHERE requirement_id IS NULL
            GROUP BY name
            HAVING COUNT(*) > 1
        ) dup ON a.name = dup.name 
                AND (a.requirement_id = dup.requirement_id 
                    OR (a.requirement_id IS NULL AND dup.requirement_id IS NULL))
    </select>
    
</mapper> 