package org.jeecg.modules.demo.dkm.service.base;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.demo.dkm.config.DuplicateCheckConfig;
import org.jeecg.modules.demo.dkm.config.FieldCheckConfig;
import org.jeecg.modules.demo.dkm.util.FilePathUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 数据准确性检查Excel导出服务
 * 提供直接生成Excel格式的数据准确性检查报告
 *
 * <AUTHOR>
 * @date 2025-5-19
 */
@Slf4j
public abstract class DataIntegrityExcelService<T> {

    /**
     * 执行数据准确性检查，生成Excel格式的检查报告
     *
     * @param dataSource        数据源名称
     * @param allIssuesSupplier 数据问题记录提供器
     * @param fieldConfigs      字段检查配置列表
     * @return Excel文件路径
     */
    public String executeIntegrityCheckToExcel(String dataSource, Supplier<List<T>> allIssuesSupplier,
                                        List<FieldCheckConfig<T>> fieldConfigs) {
        return executeIntegrityCheckWithDuplicatesToExcel(dataSource, allIssuesSupplier, fieldConfigs, Collections.emptyList());
    }

    /**
     * 执行数据准确性检查，包含重复项检查，生成Excel格式的检查报告
     *
     * @param dataSource        数据源名称
     * @param allIssuesSupplier 数据问题记录提供器
     * @param fieldConfigs      字段检查配置列表
     * @param duplicateConfigs  重复数据检查配置列表
     * @return Excel文件路径
     */
    public <E> String executeIntegrityCheckWithDuplicatesToExcel(String dataSource, Supplier<List<T>> allIssuesSupplier,
                                                     List<FieldCheckConfig<T>> fieldConfigs, 
                                                     List<DuplicateCheckConfig<E>> duplicateConfigs) {
        // 一次性查询所有数据准确性问题记录
        List<T> allIssues = allIssuesSupplier.get();

        // 检查所有问题记录和重复项是否都为空
        boolean allEmpty = allIssues.isEmpty() && duplicateConfigs.stream().allMatch(config -> config.getItems().isEmpty());
        
        // 收集各类型的缺失数据
        Map<String, Integer> checkItemCounts = new HashMap<>();
        Map<String, List<T>> missingDataMap = new HashMap<>();
        int totalIssues = 0;

        // 处理字段检查配置
        for (FieldCheckConfig<T> config : fieldConfigs) {
            List<T> missing = allIssues.stream()
                    .filter(config.getMissingCondition())
                    .collect(Collectors.toList());
            missingDataMap.put(config.getFieldName(), missing);
            checkItemCounts.put(config.getFieldName(), missing.size());
            totalIssues += missing.size();
        }

        // 处理重复数据检查配置
        for (DuplicateCheckConfig<E> config : duplicateConfigs) {
            checkItemCounts.put(config.getName(), config.getItems().size());
            totalIssues += config.getItems().size();
        }
        
        // 准备要写入Excel的数据
        List<Map<String, Object>> sheetDataList = new ArrayList<>();
        
        // 准备汇总表格数据
        Map<String, Object> summarySheet = new HashMap<>();
        summarySheet.put("sheetName", "异常汇总");
        List<String> summaryHeaders = Arrays.asList("数据来源", "检查项", "异常数量");
        summarySheet.put("headers", summaryHeaders);
        
        List<List<Object>> summaryDataRows = new ArrayList<>();
        
        // 添加字段检查项汇总数据
        for (FieldCheckConfig<T> config : fieldConfigs) {
            List<Object> row = new ArrayList<>();
            row.add(dataSource);
            row.add(config.getFieldName());
            row.add(checkItemCounts.get(config.getFieldName()));
            summaryDataRows.add(row);
        }
        
        // 添加重复数据检查项汇总数据
        for (DuplicateCheckConfig<E> config : duplicateConfigs) {
            List<Object> row = new ArrayList<>();
            row.add(dataSource);
            row.add(config.getName());
            row.add(checkItemCounts.get(config.getName()));
            summaryDataRows.add(row);
        }
        
        // 添加合计行
        List<Object> totalRow = new ArrayList<>();
        totalRow.add(dataSource);
        totalRow.add("合计");
        totalRow.add(totalIssues);
        summaryDataRows.add(totalRow);
        
        summarySheet.put("dataRows", summaryDataRows);
        sheetDataList.add(summarySheet);
        
        // 如果没有问题，只生成汇总表
        if (allEmpty) {
            List<Object> noIssueRow = new ArrayList<>();
            noIssueRow.add(dataSource);
            noIssueRow.add("数据准确性");
            noIssueRow.add("未发现数据缺失或重复问题");
            summaryDataRows.clear();
            summaryDataRows.add(noIssueRow);
            summarySheet.put("dataRows", summaryDataRows);
        } else {
            // 准备各类型缺失数据的详细信息
            for (FieldCheckConfig<T> config : fieldConfigs) {
                List<T> missingData = missingDataMap.get(config.getFieldName());
                if (!missingData.isEmpty()) {
                    Map<String, Object> detailSheet = prepareMissingDataSheet(config.getFieldName(), missingData,
                            config.getHeaders(), config.getRowGenerator());
                    sheetDataList.add(detailSheet);
                }
            }
            
            // 准备重复数据检查项的详细信息
            for (DuplicateCheckConfig<E> config : duplicateConfigs) {
                if (!config.getItems().isEmpty()) {
                    Map<String, Object> detailSheet = prepareDuplicateDataSheet(config.getName(), config.getItems(),
                            config.getHeaders(), config.getRowGenerator());
                    sheetDataList.add(detailSheet);
                }
            }
        }
        
        // 生成Excel文件
        String excelFilePath = generateExcelFile(dataSource, sheetDataList);
        
        log.info("数据准确性检查完成，生成Excel报告，发现问题数量：{}", totalIssues);
        return excelFilePath;
    }
    
    /**
     * 准备缺失数据的表格数据
     *
     * @param fieldName      字段名称
     * @param missingData    缺失数据列表
     * @param headers        表头
     * @param rowGenerator   行数据生成器
     * @return 表格数据
     */
    private <E> Map<String, Object> prepareMissingDataSheet(String fieldName,
                                              List<E> missingData, List<String> headers,
                                              Function<E, List<String>> rowGenerator) {
        Map<String, Object> sheetData = new HashMap<>();
        sheetData.put("sheetName", fieldName);
        sheetData.put("headers", headers);
        
        List<List<Object>> dataRows = new ArrayList<>();
        for (E item : missingData) {
            List<String> rowData = rowGenerator.apply(item);
            dataRows.add(new ArrayList<>(rowData));
        }
        
        sheetData.put("dataRows", dataRows);
        return sheetData;
    }
    
    /**
     * 准备重复数据的表格数据
     *
     * @param title          表格标题
     * @param items          重复数据列表
     * @param headers        表头
     * @param rowGenerator   行数据生成器
     * @return 表格数据
     */
    private <E> Map<String, Object> prepareDuplicateDataSheet(String title,
                                              List<E> items, List<String> headers,
                                              Function<E, List<String>> rowGenerator) {
        Map<String, Object> sheetData = new HashMap<>();
        sheetData.put("sheetName", title);
        sheetData.put("headers", headers);
        
        List<List<Object>> dataRows = new ArrayList<>();
        for (E item : items) {
            List<String> rowData = rowGenerator.apply(item);
            dataRows.add(new ArrayList<>(rowData));
        }
        
        sheetData.put("dataRows", dataRows);
        return sheetData;
    }
    
    /**
     * 生成Excel文件
     * 
     * @param dataSource 数据源名称，用于生成文件名
     * @param sheetDataList 工作表数据列表
     * @return Excel文件下载URL
     */
    private String generateExcelFile(String dataSource, List<Map<String, Object>> sheetDataList) {
        // 获取服务器存储路径
        String storagePath = FilePathUtil.getServerStoragePath();
        
        // 生成文件名
        String fileName = dataSource + "数据完整性检查_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String fileFullPath = storagePath + File.separator + fileName;
        
        try (ExcelWriter excelWriter = EasyExcel.write(fileFullPath)
                // 仅添加一个列宽策略，设置较大的列宽
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25))
                .build()) {
            
            // 遍历每个工作表数据
            for (int i = 0; i < sheetDataList.size(); i++) {
                Map<String, Object> sheetData = sheetDataList.get(i);
                String sheetName = (String) sheetData.get("sheetName");
                List<String> headers = (List<String>) sheetData.get("headers");
                List<List<Object>> dataRows = (List<List<Object>>) sheetData.get("dataRows");
                
                // 创建表头
                List<List<String>> headList = new ArrayList<>();
                for (String header : headers) {
                    List<String> head = new ArrayList<>();
                    head.add(header);
                    headList.add(head);
                }
                
                // 创建工作表并写入表头和数据
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetName).head(headList).build();
                excelWriter.write(dataRows, writeSheet);
            }
        } catch (Exception e) {
            log.error("生成Excel报告失败", e);
            return null;
        }
        
        // 返回文件下载URL
        String downloadUrl = FilePathUtil.getDownloadUrl();
        if (downloadUrl.endsWith("/")) {
            downloadUrl = downloadUrl.substring(0, downloadUrl.length() - 1);
        }
        
        return downloadUrl + "/" + fileName;
    }
} 