package org.jeecg.modules.demo.dkm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 市场合同实体类
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Data
@TableName("pms_market_contract")
public class MarketContract {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 合同名称
     */
    private String name;
    
    /**
     * 合同编号
     */
    private String number;
    
    /**
     * 商务负责人
     */
    private String commerceRspUser;
    
    /**
     * 技术负责人
     */
    private String techRspUser;
    
    /**
     * 需求ID(外键)
     */
    private String requirementId;
    
    /**
     * 需求名称(非数据库字段，用于显示)
     */
    @TableField(exist = false)
    private String requirementName;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    @TableField(value = "tech_rsp_dept")
    private String techRspDept;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    @TableField(exist = false)
    private String techRspDeptName;
} 