package org.jeecg.modules.demo.dkm.service;

import org.jeecg.modules.base.IBaseService;
import org.jeecg.modules.demo.dkm.entity.DtcPersonnels;

/**
 * @Description: DtcPersonnels Service接口
 * @Author: tancheng
 * @Date: 2025-4-16
 */
public interface DtcPersonnelsCheckService extends IBaseService<DtcPersonnels> {
    
    /**
     * 检查数据完整性
     * 检查员工号、姓名、身份证号、部门编码和部门名称是否缺失
     * @return 直接返回标准Markdown表格格式的字符串，多个表格之间用空行分隔
     */
    String checkPersonnelsDataIntegrity();
}