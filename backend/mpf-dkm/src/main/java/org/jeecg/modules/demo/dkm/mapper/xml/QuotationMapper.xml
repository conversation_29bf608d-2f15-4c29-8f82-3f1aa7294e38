<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.dkm.mapper.QuotationMapper">

    <!-- 查询数据完整性问题的记录 -->
    <select id="findQuotationDataIntegrityIssues" resultType="org.jeecg.modules.demo.dkm.entity.Quotation">
        SELECT id, quotation_name as quotationName, quotation_id as quotationId
        FROM pmsx_quotation_management
        WHERE quotation_id IS NULL OR quotation_id = ''
    </select>

    <!-- 查找报价名称重复的数据 -->
    <select id="findDuplicateQuotationNames" resultType="org.jeecg.modules.demo.dkm.entity.Quotation">
        SELECT a.id, a.quotation_name as quotationName, a.quotation_id as quotationId, r.requirement_name as requirementName
        FROM pmsx_quotation_management a
        LEFT JOIN pms_requirement_mangement r ON a.requirement_id = r.id
        INNER JOIN (
            SELECT quotation_name, requirement_id
            FROM pmsx_quotation_management
            GROUP BY quotation_name, requirement_id
            HAVING COUNT(*) > 1
        ) dup ON a.quotation_name = dup.quotation_name AND a.requirement_id = dup.requirement_id
    </select>
    
</mapper> 