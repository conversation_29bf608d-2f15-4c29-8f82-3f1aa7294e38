package org.jeecg.modules.demo.dkm.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * @Description: 需求管理实体类
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Data
@TableName("pms_requirement_mangement")
public class Requirement {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 需求名称
     */
    private String requirementName;
    
    /**
     * 需求编号
     */
    private String requirementNumber;
} 