<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.dkm.mapper.RequirementMapper">

    <!-- 查询数据完整性问题的记录 -->
    <select id="findRequirementDataIntegrityIssues" resultType="org.jeecg.modules.demo.dkm.entity.Requirement">
        SELECT id, requirement_name as requirementName, requirement_number as requirementNumber
        FROM pms_requirement_mangement
        WHERE requirement_number IS NULL OR requirement_number = ''
    </select>

    <!-- 查找需求名称重复的数据 -->
    <select id="findDuplicateRequirementNames" resultType="org.jeecg.modules.demo.dkm.entity.Requirement">
        SELECT a.id, a.requirement_name as requirementName, a.requirement_number as requirementNumber
        FROM pms_requirement_mangement a
        INNER JOIN (
            SELECT requirement_name
            FROM pms_requirement_mangement
            GROUP BY requirement_name
            HAVING COUNT(*) > 1
        ) dup ON a.requirement_name = dup.requirement_name
    </select>
    
</mapper> 