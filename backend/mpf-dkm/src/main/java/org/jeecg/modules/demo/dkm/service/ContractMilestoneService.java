package org.jeecg.modules.demo.dkm.service;

/**
 * @Description: 合同里程碑数据校验服务
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
public interface ContractMilestoneService {
    
    /**
     * 检查合同里程碑表数据准确性
     * 检查里程碑名称重名、里程碑编号为空
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     */
    String checkContractMilestoneDataIntegrity();
    
    /**
     * 检查合同里程碑表数据准确性并导出Excel
     * 检查里程碑名称重名、里程碑编号为空
     * @return Excel文件路径
     */
    String checkContractMilestoneDataIntegrityExcel();
} 