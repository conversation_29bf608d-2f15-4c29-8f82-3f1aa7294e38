package org.jeecg.modules.demo.dkm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.base.BaseServiceImpl;
import org.jeecg.modules.demo.dkm.config.DuplicateCheckConfig;
import org.jeecg.modules.demo.dkm.config.FieldCheckConfig;
import org.jeecg.modules.demo.dkm.entity.MarketContract;
import org.jeecg.modules.demo.dkm.mapper.MarketContractMapper;
import org.jeecg.modules.demo.dkm.service.MarketContractService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityCheckService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityExcelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 市场合同数据校验服务实现
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class MarketContractServiceImpl extends BaseServiceImpl<MarketContractMapper, MarketContract> 
    implements MarketContractService {

    @Autowired
    private MarketContractMapper marketContractMapper;
    
    // 使用组合方式引入数据准确性检查服务
    private final DataIntegrityCheckService<MarketContract> dataIntegrityCheckService;
    
    // 使用组合方式引入数据准确性Excel导出服务
    private final DataIntegrityExcelService<MarketContract> dataIntegrityExcelService;
    
    public MarketContractServiceImpl() {
        // 创建匿名内部类实例
        this.dataIntegrityCheckService = new DataIntegrityCheckService<MarketContract>() {};
        this.dataIntegrityExcelService = new DataIntegrityExcelService<MarketContract>() {};
    }

    @Override
    public String checkMarketContractDataIntegrity() {
        log.info("开始检查pms_market_contract表数据准确性");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<MarketContract>> fieldConfigs = Arrays.asList(
                // 合同编号缺失配置
                FieldCheckConfig.create(
                        "合同编号",
                        MarketContract::getNumber,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName
                ),
                // 商务负责人缺失配置
                FieldCheckConfig.create(
                        "商务负责人",
                        MarketContract::getCommerceRspUser,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName
                ),
                // 技术负责人缺失配置
                FieldCheckConfig.create(
                        "技术负责人",
                        MarketContract::getTechRspUser,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName
                )
        );
        
        // 查询合同名称重复的数据
        List<MarketContract> duplicateNameContracts = marketContractMapper.findDuplicateContractNames();
        
        // 定义合同名称重复的检查项
        List<DuplicateCheckConfig<MarketContract>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "合同名称",
                        duplicateNameContracts,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门", "所属需求"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName,
                        MarketContract::getRequirementName
                )
        );

        // 使用数据准确性检查服务执行检查
        String result = dataIntegrityCheckService.executeIntegrityCheckWithDuplicates(
                "市场合同表(pms_market_contract)", 
                () -> marketContractMapper.findContractDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pms_market_contract表数据准确性检查");
        return result;
    }

    @Override
    public String checkMarketContractDataIntegrityExcel() {
        log.info("开始生成pms_market_contract表数据准确性Excel报告");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<MarketContract>> fieldConfigs = Arrays.asList(
                // 合同编号缺失配置
                FieldCheckConfig.create(
                        "合同编号为空",
                        MarketContract::getNumber,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName
                ),
                // 商务负责人缺失配置
                FieldCheckConfig.create(
                        "商务负责人为空",
                        MarketContract::getCommerceRspUser,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName
                ),
                // 技术负责人缺失配置
                FieldCheckConfig.create(
                        "技术负责人为空",
                        MarketContract::getTechRspUser,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName
                )
        );
        
        // 查询合同名称重复的数据
        List<MarketContract> duplicateNameContracts = marketContractMapper.findDuplicateContractNames();
        
        // 定义合同名称重复的检查项
        List<DuplicateCheckConfig<MarketContract>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "合同名称重名",
                        duplicateNameContracts,
                        Arrays.asList("合同ID", "合同名称", "合同编号", "承担部门", "所属需求"),
                        MarketContract::getId,
                        MarketContract::getName,
                        MarketContract::getNumber,
                        MarketContract::getTechRspDeptName,
                        MarketContract::getRequirementName
                )
        );

        // 直接使用数据准确性Excel导出服务生成Excel报告
        String excelFilePath = dataIntegrityExcelService.executeIntegrityCheckWithDuplicatesToExcel(
                "市场合同表", 
                () -> marketContractMapper.findContractDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pms_market_contract表数据准确性Excel报告生成，文件路径：{}", excelFilePath);
        return excelFilePath;
    }
} 