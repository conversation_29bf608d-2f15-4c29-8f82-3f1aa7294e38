package org.jeecg.modules.demo.dkm.service;

/**
 * @Description: 需求管理数据校验服务
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
public interface RequirementService {
    
    /**
     * 检查需求管理表数据准确性
     * 检查需求名称重名、需求编号为空
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     */
    String checkRequirementDataIntegrity();
    
    /**
     * 检查需求管理表数据准确性并导出Excel
     * 检查需求名称重名、需求编号为空
     * @return Excel文件路径
     */
    String checkRequirementDataIntegrityExcel();
} 