package org.jeecg.modules.demo.dkm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 人员信息表
 * @Author: jeecg-boot
 * @Date: 2024-08-22
 * @Version: V1.0
 */
@Data
@TableName("dtc_personnels")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "dtc_personnels对象", description = "人员信息表")
public class DtcPersonnels implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 人员编号
     */
    @Excel(name = "人员编号", width = 15)
    @ApiModelProperty(value = "人员编号")
    private String staffNo;
    /**
     * 是否开启智慧GH 1，开启，0关闭
     */
    @Excel(name = "是否开启智慧GH 1，开启，0关闭", width = 15)
    @ApiModelProperty(value = "是否开启智慧GH 1，开启，0关闭")
    private String isOpenddId;
    /**
     * 是否开启智慧GH
     */
    @Excel(name = "是否开启智慧GH", width = 15)
    @ApiModelProperty(value = "是否开启智慧GH")
    private String isOpendd;
    /**
     * SAP手机
     */
    @Excel(name = "SAP手机", width = 15)
    @ApiModelProperty(value = "SAP手机")
    private String mobilePhone1Sap;
    /**
     * 姓名
     */
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String staffName;
    /**
     * 姓名拼音
     */
    @Excel(name = "姓名拼音", width = 15)
    @ApiModelProperty(value = "姓名拼音")
    private String staffNamePy;
    /**
     * 有效证件类别
     */
    @Excel(name = "有效证件类别", width = 15)
    @ApiModelProperty(value = "有效证件类别")
    private String staffIdType;
    /**
     * 有效证件号码
     */
    @Excel(name = "有效证件号码", width = 15)
    @ApiModelProperty(value = "有效证件号码")
    private String staffIdCard;
    /**
     * 性别ID
     */
    @Excel(name = "性别ID", width = 15)
    @ApiModelProperty(value = "性别ID")
    private String staffSexId;
    /**
     * 性别
     */
    @Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private String staffSex;
    /**
     * 籍贯
     */
    @Excel(name = "籍贯", width = 15)
    @ApiModelProperty(value = "籍贯")
    private String staffNative;
    /**
     * 人员类型
     */
    @Excel(name = "人员类型", width = 15)
    @ApiModelProperty(value = "人员类型")
    private String staffClass;
    /**
     * 人员状态id(0:离职，1：在职)
     */
    @Excel(name = "人员状态id(0:离职，1：在职)", width = 15)
    @ApiModelProperty(value = "人员状态id(0:离职，1：在职)")
    private String staffStatusId;
    /**
     * 人员状态
     */
    @Excel(name = "人员状态", width = 15)
    @ApiModelProperty(value = "人员状态")
    private String staffStatus;
    /**
     * 出生日期
     */
    @Excel(name = "出生日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "出生日期")
    private Date staffBirthday;
    /**
     * 婚姻状况
     */
    @Excel(name = "婚姻状况", width = 15)
    @ApiModelProperty(value = "婚姻状况")
    private String staffMaritalstatus;
    /**
     * 结婚时间
     */
    @Excel(name = "结婚时间", width = 15)
    @ApiModelProperty(value = "结婚时间")
    private String staffMaritaltime;
    /**
     * 健康状况
     */
    @Excel(name = "健康状况", width = 15)
    @ApiModelProperty(value = "健康状况")
    private String staffHealth;
    /**
     * 政治面貌ID
     */
    @Excel(name = "政治面貌ID", width = 15)
    @ApiModelProperty(value = "政治面貌ID")
    private String staffPcodeId;
    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌", width = 15)
    @ApiModelProperty(value = "政治面貌")
    private String staffPcode;
    /**
     * 职务名称
     */
    @Excel(name = "职务名称", width = 15)
    @ApiModelProperty(value = "职务名称")
    private String staffZhrzwmc;
    /**
     * 参加工作时间
     */
    @Excel(name = "参加工作时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "参加工作时间")
    private Date staffWorktime;
    /**
     * 来源公司ID
     */
    @Excel(name = "来源公司ID", width = 15)
    @ApiModelProperty(value = "来源公司ID")
    private String staffFromUnitId;
    /**
     * 来源公司
     */
    @Excel(name = "来源公司", width = 15)
    @ApiModelProperty(value = "来源公司")
    private String staffFromUnit;
    /**
     * 来GH时间
     */
    @Excel(name = "来GH时间", width = 15)
    @ApiModelProperty(value = "来GH时间")
    private String staffGhtime;
    /**
     * 入职时间
     */
    @Excel(name = "入职时间", width = 15)
    @ApiModelProperty(value = "入职时间")
    private String staffJoinGnp;
    /**
     * 离职时间
     */
    @Excel(name = "离职时间", width = 15)
    @ApiModelProperty(value = "离职时间")
    private String staffLeftDate;
    /**
     * 聘用方式ID
     */
    @Excel(name = "聘用方式ID", width = 15)
    @ApiModelProperty(value = "聘用方式ID")
    private String staffHireTypeId;
    /**
     * 聘用方式
     */
    @Excel(name = "聘用方式", width = 15)
    @ApiModelProperty(value = "聘用方式")
    private String staffHireType;
    /**
     * 人事范围id
     */
    @Excel(name = "人事范围id", width = 15)
    @ApiModelProperty(value = "人事范围id")
    private String staffWorkunitId;
    /**
     * 人事范围
     */
    @Excel(name = "人事范围", width = 15)
    @ApiModelProperty(value = "人事范围")
    private String staffWorkunit;
    /**
     * 人事子范围ID
     */
    @Excel(name = "人事子范围ID", width = 15)
    @ApiModelProperty(value = "人事子范围ID")
    private String staffsubrangeId;
    /**
     * 人事子范围
     */
    @Excel(name = "人事子范围", width = 15)
    @ApiModelProperty(value = "人事子范围")
    private String staffsubrange;
    /**
     * 员工组的名称
     */
    @Excel(name = "员工组的名称", width = 15)
    @ApiModelProperty(value = "员工组的名称")
    private String staffPersg;
    /**
     * 员工子组
     */
    @Excel(name = "员工子组", width = 15)
    @ApiModelProperty(value = "员工子组")
    private String staffPersk;
    /**
     * 成本中心编码
     */
    @Excel(name = "成本中心编码", width = 15)
    @ApiModelProperty(value = "成本中心编码")
    private String staffKostl;
    /**
     * 成本中心名称
     */
    @Excel(name = "成本中心名称", width = 15)
    @ApiModelProperty(value = "成本中心名称")
    private String staffKostlLtext;
    /**
     * 利润中心编码
     */
    @Excel(name = "利润中心编码", width = 15)
    @ApiModelProperty(value = "利润中心编码")
    private String staffPrctr;
    /**
     * 事件时间
     */
    @Excel(name = "事件时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "事件时间")
    private Date staffActionDate;
    /**
     * 职位
     */
    @Excel(name = "职位", width = 15)
    @ApiModelProperty(value = "职位")
    private String hrPersonnelPosi;
    /**
     * 职称ID
     */
    @Excel(name = "职称ID", width = 15)
    @ApiModelProperty(value = "职称ID")
    private String staffTitleId;
    /**
     * 职称
     */
    @Excel(name = "职称", width = 15)
    @ApiModelProperty(value = "职称")
    private String staffTitle;
    /**
     * HR职称ID
     */
    @Excel(name = "HR职称ID", width = 15)
    @ApiModelProperty(value = "HR职称ID")
    private String hrStaffTitleId;
    /**
     * HR职称
     */
    @Excel(name = "HR职称", width = 15)
    @ApiModelProperty(value = "HR职称")
    private String hrStaffTitle;
    /**
     * 编制部门编码
     */
    @Excel(name = "编制部门编码", width = 15)
    @ApiModelProperty(value = "编制部门编码")
    private String staffDeptId;
    /**
     * 编制部门
     */
    @Excel(name = "编制部门", width = 15)
    @ApiModelProperty(value = "编制部门")
    private String staffDept;
    /**
     * 姓名拼音缩写
     */
    @Excel(name = "姓名拼音缩写", width = 15)
    @ApiModelProperty(value = "姓名拼音缩写")
    private String staffShortPy;
    /**
     * 外语姓名
     */
    @Excel(name = "外语姓名", width = 15)
    @ApiModelProperty(value = "外语姓名")
    private String hrPersonnelLangu;
    /**
     * 办公手机
     */
    @Excel(name = "办公手机", width = 15)
    @ApiModelProperty(value = "办公手机")
    private String mobilePhone1;
    /**
     * 办公电话
     */
    @Excel(name = "办公电话", width = 15)
    @ApiModelProperty(value = "办公电话")
    private String telephoneNo1;
    /**
     * 其他移动电话
     */
    @Excel(name = "其他移动电话", width = 15)
    @ApiModelProperty(value = "其他移动电话")
    private String mobilePhone2;
    /**
     * 其他办公电话
     */
    @Excel(name = "其他办公电话", width = 15)
    @ApiModelProperty(value = "其他办公电话")
    private String telephoneNo2;
    /**
     * SAP移动电话
     */
    @Excel(name = "SAP移动电话", width = 15)
    @ApiModelProperty(value = "SAP移动电话")
    private String mobilePhoneSap;
    /**
     * 办公地址
     */
    @Excel(name = "办公地址", width = 15)
    @ApiModelProperty(value = "办公地址")
    private String addressOffice;
    /**
     * 出生地
     */
    @Excel(name = "出生地", width = 15)
    @ApiModelProperty(value = "出生地")
    private String staffBirthplace;
    /**
     * 民族
     */
    @Excel(name = "民族", width = 15)
    @ApiModelProperty(value = "民族")
    private String staffEthnic;
    /**
     * 信息来源
     */
    @Excel(name = "信息来源", width = 15)
    @ApiModelProperty(value = "信息来源")
    private String infotype;
    /**
     * 集团职级
     */
    @Excel(name = "集团职级", width = 15)
    @ApiModelProperty(value = "集团职级")
    private String jtRank;
    /**
     * 承包商人员类别ID
     */
    @Excel(name = "承包商人员类别ID", width = 15)
    @ApiModelProperty(value = "承包商人员类别ID")
    private String ctrStaffTypeId;
    /**
     * 承包商人员类别
     */
    @Excel(name = "承包商人员类别", width = 15)
    @ApiModelProperty(value = "承包商人员类别")
    private String ctrStaffType;
    /**
     * 所属公司ID
     */
    @Excel(name = "所属公司ID", width = 15)
    @ApiModelProperty(value = "所属公司ID")
    private String companyId;
    /**
     * 所属公司
     */
    @Excel(name = "所属公司", width = 15)
    @ApiModelProperty(value = "所属公司")
    private String company;
    /**
     * 国际码
     */
    @Excel(name = "国际码", width = 15)
    @ApiModelProperty(value = "国际码")
    private String interCode;
    /**
     * 4G短号码
     */
    @Excel(name = "4G短号码", width = 15)
    @ApiModelProperty(value = "4G短号码")
    private String shortTelephone;
    /**
     * 宿舍电话
     */
    @Excel(name = "宿舍电话", width = 15)
    @ApiModelProperty(value = "宿舍电话")
    private String dormitoryTelephone;
    /**
     * 邮箱
     */
    @Excel(name = "邮箱", width = 15)
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     * 修改时间
     */
    @Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date modifyDate;
    /**
     * 组织单元编号
     */
    @Excel(name = "组织单元编号", width = 15)
    @ApiModelProperty(value = "组织单元编号")
    private String deptNo;
    /**
     * 组织单元名称
     */
    @Excel(name = "组织单元名称", width = 15)
    @ApiModelProperty(value = "组织单元名称")
    private String deptName;
    /**
     * 人员职位ID
     */
    @Excel(name = "人员职位ID", width = 15)
    @ApiModelProperty(value = "人员职位ID")
    private String staffPosiId;
    /**
     * 人员职位
     */
    @Excel(name = "人员职位", width = 15)
    @ApiModelProperty(value = "人员职位")
    private String staffPosi;
    /**
     * 4G电话
     */
    @Excel(name = "4G电话", width = 15)
    @ApiModelProperty(value = "4G电话")
    @TableField("phone_4g")
    private String phone4g;
    /**
     * DORM电话
     */
    @Excel(name = "DORM电话", width = 15)
    @ApiModelProperty(value = "DORM电话")
    private String dormPhone;
    /**
     * ADDRESS_DORM
     */
    @Excel(name = "ADDRESS_DORM", width = 15)
    @ApiModelProperty(value = "ADDRESS_DORM")
    private String addressDorm;
    /**
     * 员工邮箱
     */
    @Excel(name = "员工邮箱", width = 15)
    @ApiModelProperty(value = "员工邮箱")
    private String staffMail;
    /**
     * 国家区号
     */
    @Excel(name = "国家区号", width = 15)
    @ApiModelProperty(value = "国家区号")
    private String countrycode;
    /**
     * 最高学历
     */
    @Excel(name = "最高学历", width = 15)
    @ApiModelProperty(value = "最高学历")
    private String education;
    /**
     * 所学专业
     */
    @Excel(name = "所学专业", width = 15)
    @ApiModelProperty(value = "所学专业")
    private String major;
    /**
     * 所在研究所/专业室部门编码
     */
    @Excel(name = "所在研究所/专业室部门编码", width = 15)
    @ApiModelProperty(value = "所在研究所/专业室部门编码")
    private String workDeptNo;
    /**
     * 工作部门
     */
    @Excel(name = "工作部门", width = 15)
    @ApiModelProperty(value = "工作部门")
    private String workDept;
    /**
     * 分管项目经理
     */
    @Excel(name = "分管项目经理", width = 15)
    @ApiModelProperty(value = "分管项目经理")
    private String projectManager;
    /**
     * 是否项目制人员
     */
    @Excel(name = "是否项目制人员", width = 15)
    @ApiModelProperty(value = "是否项目制人员")
    private String izProjectStaff;
    /**
     * 合同编号
     */
    @Excel(name = "合同编号", width = 15)
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;
    /**
     * 合同级别
     */
    @Excel(name = "合同级别", width = 15)
    @ApiModelProperty(value = "合同级别")
    private String contractLevel;
    /**
     * 合同级别编号
     */
    @Excel(name = "合同级别编号", width = 15)
    @ApiModelProperty(value = "合同级别编号")
    private String contractLevelId;
    /**
     * 合同名称
     */
    @Excel(name = "合同名称", width = 15)
    @ApiModelProperty(value = "合同名称")
    private String contractName;
    /**
     * 工作内容
     */
    @Excel(name = "工作内容", width = 15)
    @ApiModelProperty(value = "工作内容")
    private String workCon;
    /**
     * 常驻服务地点
     */
    @Excel(name = "常驻服务地点", width = 15)
    @ApiModelProperty(value = "常驻服务地点")
    private String residentServiceLocation;
    /**
     * 常驻服务地点编号
     */
    @Excel(name = "常驻服务地点编号", width = 15)
    @ApiModelProperty(value = "常驻服务地点编号")
    private String residentServiceLocationId;
    /**
     * 是否从事放射性工作
     */
    @Excel(name = "是否从事放射性工作", width = 15)
    @ApiModelProperty(value = "是否从事放射性工作")
    private String existRadiologicalWork;
    /**
     * 是否已取消授权
     */
    @Excel(name = "是否已取消授权", width = 15)
    @ApiModelProperty(value = "是否已取消授权")
    private String izRevokedAuth;
    /**
     * 是否有亲属在集团内
     */
    @Excel(name = "是否有亲属在集团内", width = 15)
    @ApiModelProperty(value = "是否有亲属在集团内")
    private String haveRelativesInGroup;
    /**
     * 亲属姓名
     */
    @Excel(name = "亲属姓名", width = 15)
    @ApiModelProperty(value = "亲属姓名")
    private String relativesName;
    /**
     * 亲属职务
     */
    @Excel(name = "亲属职务", width = 15)
    @ApiModelProperty(value = "亲属职务")
    private String relativesPosition;
    /**
     * 亲属公司
     */
    @Excel(name = "亲属公司", width = 15)
    @ApiModelProperty(value = "亲属公司")
    private String relativesCompany;
    /**
     * 是否违反相关安全规范
     */
    @Excel(name = "是否违反相关安全规范", width = 15)
    @ApiModelProperty(value = "是否违反相关安全规范")
    private String izViolationOfRelevantSafetyRegulations;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 操作人
     */
    @Excel(name = "操作人", width = 15)
    @ApiModelProperty(value = "操作人")
    private String operator;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 是否已完成体检
     */
    @Excel(name = "是否已完成体检", width = 15)
    @ApiModelProperty(value = "是否已完成体检")
    private String izExamCompleted;
    /**
     * 办卡或授权选择
     */
    @Excel(name = "办卡或授权选择", width = 15)
    @ApiModelProperty(value = "办卡或授权选择")
    private String applyOrAuth;
    /**
     * 专业技术证书
     */
    @Excel(name = "专业技术证书", width = 15)
    @ApiModelProperty(value = "专业技术证书")
    private String certificate;
    /**
     * 工作中心编号
     */
    @Excel(name = "工作中心编号", width = 15)
    @ApiModelProperty(value = "工作中心编号")
    private String centerdeptNo;
    /**
     * 工作中心（专门用于信息收集）
     */
    @Excel(name = "工作中心（专门用于信息收集）", width = 15)
    @ApiModelProperty(value = "工作中心（专门用于信息收集）")
    private String centerdept;
    /**
     * MDM工作部门（专门用于信息收集）
     */
    @Excel(name = "MDM工作部门（专门用于信息收集）", width = 15)
    @ApiModelProperty(value = "MDM工作部门（专门用于信息收集）")
    private String staffDeptMdm;
    /**
     * MDM工作部门id（专门用于信息收集）
     */
    @Excel(name = "MDM工作部门id（专门用于信息收集）", width = 15)
    @ApiModelProperty(value = "MDM工作部门id（专门用于信息收集）")
    private String staffWorkDeptId;
    /**
     * 工作部门（专门用于信息收集）
     */
    @Excel(name = "工作部门（专门用于信息收集）", width = 15)
    @ApiModelProperty(value = "工作部门（专门用于信息收集）")
    private String staffWorkDept;
    /**
     * 是否技术配置
     */
    @Excel(name = "是否技术配置", width = 15)
    @ApiModelProperty(value = "是否技术配置")
    private String izTecnologyConfiged;
    /**
     * 预计离岗时间
     */
    @Excel(name = "预计离岗时间", width = 15)
    @ApiModelProperty(value = "预计离岗时间")
    private String estimatedTimeOfAbsence;
    /**
     * 入场时间
     */
    @Excel(name = "入场时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;
    /**
     * 离场时间
     */
    @Excel(name = "离场时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "离场时间")
    private Date departureTime;
}
