package org.jeecg.modules.demo.dkm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.base.BaseServiceImpl;
import org.jeecg.modules.demo.dkm.config.DuplicateCheckConfig;
import org.jeecg.modules.demo.dkm.config.FieldCheckConfig;
import org.jeecg.modules.demo.dkm.entity.Requirement;
import org.jeecg.modules.demo.dkm.mapper.RequirementMapper;
import org.jeecg.modules.demo.dkm.service.RequirementService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityCheckService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityExcelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 需求管理数据校验服务实现
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class RequirementServiceImpl extends BaseServiceImpl<RequirementMapper, Requirement> 
    implements RequirementService {

    @Autowired
    private RequirementMapper requirementMapper;
    
    // 使用组合方式引入数据准确性检查服务
    private final DataIntegrityCheckService<Requirement> dataIntegrityCheckService;
    
    // 使用组合方式引入数据准确性Excel导出服务
    private final DataIntegrityExcelService<Requirement> dataIntegrityExcelService;
    
    public RequirementServiceImpl() {
        // 创建匿名内部类实例
        this.dataIntegrityCheckService = new DataIntegrityCheckService<Requirement>() {};
        this.dataIntegrityExcelService = new DataIntegrityExcelService<Requirement>() {};
    }

    @Override
    public String checkRequirementDataIntegrity() {
        log.info("开始检查pms_requirement_mangement表数据准确性");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<Requirement>> fieldConfigs = Arrays.asList(
                // 需求编号缺失配置
                FieldCheckConfig.create(
                        "需求编号",
                        Requirement::getRequirementNumber,
                        Arrays.asList("需求ID", "需求名称"),
                        Requirement::getId,
                        Requirement::getRequirementName
                )
        );
        
        // 查询需求名称重复的数据
        List<Requirement> duplicateNameRequirements = requirementMapper.findDuplicateRequirementNames();
        
        // 定义需求名称重复的检查项
        List<DuplicateCheckConfig<Requirement>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "需求名称",
                        duplicateNameRequirements,
                        Arrays.asList("需求ID", "需求名称"),
                        Requirement::getId,
                        Requirement::getRequirementName
                )
        );

        // 使用数据准确性检查服务执行检查
        String result = dataIntegrityCheckService.executeIntegrityCheckWithDuplicates(
                "需求管理表(pms_requirement_mangement)", 
                () -> requirementMapper.findRequirementDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pms_requirement_mangement表数据准确性检查");
        return result;
    }
    
    @Override
    public String checkRequirementDataIntegrityExcel() {
        log.info("开始生成pms_requirement_mangement表数据准确性Excel报告");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<Requirement>> fieldConfigs = Arrays.asList(
                // 需求编号缺失配置
                FieldCheckConfig.create(
                        "需求编号为空",
                        Requirement::getRequirementNumber,
                        Arrays.asList("需求ID", "需求名称"),
                        Requirement::getId,
                        Requirement::getRequirementName
                )
        );
        
        // 查询需求名称重复的数据
        List<Requirement> duplicateNameRequirements = requirementMapper.findDuplicateRequirementNames();
        
        // 定义需求名称重复的检查项
        List<DuplicateCheckConfig<Requirement>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "需求名称重名",
                        duplicateNameRequirements,
                        Arrays.asList("需求ID", "需求名称"),
                        Requirement::getId,
                        Requirement::getRequirementName
                )
        );

        // 直接使用数据准确性Excel导出服务生成Excel报告
        String excelFilePath = dataIntegrityExcelService.executeIntegrityCheckWithDuplicatesToExcel(
                "需求管理表", 
                () -> requirementMapper.findRequirementDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pms_requirement_mangement表数据准确性Excel报告生成，文件路径：{}", excelFilePath);
        return excelFilePath;
    }
} 