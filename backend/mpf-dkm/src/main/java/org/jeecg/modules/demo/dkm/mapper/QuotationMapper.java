package org.jeecg.modules.demo.dkm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.demo.dkm.entity.Quotation;

import java.util.List;

/**
 * @Description: 报价管理数据校验Mapper
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Mapper
public interface QuotationMapper extends BaseMapper<Quotation> {
    
    /**
     * 查询数据准确性问题的记录
     * 一次性查询所有字段缺失的记录
     * @return 存在数据准确性问题的记录列表
     */
    List<Quotation> findQuotationDataIntegrityIssues();
    
    /**
     * 查询报价名称重复的数据
     * @return 存在报价名称重复的记录列表
     */
    List<Quotation> findDuplicateQuotationNames();
} 