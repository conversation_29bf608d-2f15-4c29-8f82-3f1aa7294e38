package org.jeecg.modules.demo.dkm.config;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.function.Function;

/**
 * 重复数据检查配置类
 * 用于配置数据重复性检查的条件、表头和行数据生成逻辑
 *
 * @param <T> 数据对象类型
 * <AUTHOR>
 * @date 2025-4-16
 */
@Getter
@AllArgsConstructor
public class DuplicateCheckConfig<T> {
    /**
     * 检查项名称
     */
    private String name;
    /**
     * 重复数据列表
     */
    private List<T> items;
    /**
     * 表头信息
     */
    private List<String> headers;
    /**
     * 行数据生成器
     */
    private Function<T, List<String>> rowGenerator;

    /**
     * 创建重复数据检查配置
     *
     * @param name         检查项名称
     * @param items        重复数据列表
     * @param headers      表头信息
     * @param fieldGetters 字段获取器数组
     * @param <E>          数据对象类型
     * @return 重复数据检查配置
     */
    @SafeVarargs
    public static <E> DuplicateCheckConfig<E> create(
            String name,
            List<E> items,
            List<String> headers,
            Function<E, String>... fieldGetters) {
        return new DuplicateCheckConfig<>(
                name,
                items,
                headers,
                FieldCheckConfig.createRowGenerator(fieldGetters)
        );
    }
} 