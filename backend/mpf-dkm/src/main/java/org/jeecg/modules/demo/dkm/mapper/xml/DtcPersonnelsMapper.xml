<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.dkm.mapper.DtcPersonnelsMapper">

    <!-- 查询数据完整性问题的记录 -->
    <select id="findDataIntegrityIssues" resultType="org.jeecg.modules.demo.dkm.dto.DtcPersonnelsDTO">
        SELECT operator,
               staff_no as staffNo,
               staff_name as staffName,
               staff_id_card as staffIdCard,
               dept_no as deptNo,
               dept_name as deptName,
               (select dept_leader_id from dtc_organizations where dept_no = p.dept_no) deptLeaderId,
               (select dept_header2_id from dtc_organizations where dept_no = p.dept_no) deptHeader2Id
        FROM dtc_personnels p
        WHERE staff_status_id = 1 and  (staff_no IS NULL OR TRIM(staff_no) = ''
        OR staff_name IS NULL OR TRIM(staff_name) = ''
        OR staff_id_card IS NULL OR TRIM(staff_id_card) = ''
        OR dept_no IS NULL OR TRIM(dept_no) = ''
        OR dept_name IS NULL OR TRIM(dept_name) = '')
        AND EXISTS (
            select dept_no
            from dtc_organizations
            where dept_type = 1
              AND dtc_organizations.dept_no = p.dept_no
        )
    </select>
    
</mapper> 