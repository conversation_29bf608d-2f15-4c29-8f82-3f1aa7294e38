package org.jeecg.modules.demo.dkm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.base.BaseServiceImpl;
import org.jeecg.modules.demo.dkm.config.DuplicateCheckConfig;
import org.jeecg.modules.demo.dkm.config.FieldCheckConfig;
import org.jeecg.modules.demo.dkm.entity.ContractMilestone;
import org.jeecg.modules.demo.dkm.mapper.ContractMilestoneMapper;
import org.jeecg.modules.demo.dkm.service.ContractMilestoneService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityCheckService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityExcelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 合同里程碑数据校验服务实现
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class ContractMilestoneServiceImpl extends BaseServiceImpl<ContractMilestoneMapper, ContractMilestone> 
    implements ContractMilestoneService {

    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;
    
    // 使用组合方式引入数据准确性检查服务
    private final DataIntegrityCheckService<ContractMilestone> dataIntegrityCheckService;
    
    // 使用组合方式引入数据准确性Excel导出服务
    private final DataIntegrityExcelService<ContractMilestone> dataIntegrityExcelService;
    
    public ContractMilestoneServiceImpl() {
        // 创建匿名内部类实例
        this.dataIntegrityCheckService = new DataIntegrityCheckService<ContractMilestone>() {};
        this.dataIntegrityExcelService = new DataIntegrityExcelService<ContractMilestone>() {};
    }

    @Override
    public String checkContractMilestoneDataIntegrity() {
        log.info("开始检查pms_contract_milestone表数据准确性");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<ContractMilestone>> fieldConfigs = Arrays.asList(
                // 里程碑编号缺失配置
                FieldCheckConfig.create(
                        "里程碑编号",
                        ContractMilestone::getNumber,
                        Arrays.asList("里程碑ID", "里程碑名称"),
                        ContractMilestone::getId,
                        ContractMilestone::getMilestoneName
                )
        );
        
        // 查询里程碑名称重复的数据
        List<ContractMilestone> duplicateNameMilestones = contractMilestoneMapper.findDuplicateMilestoneNames();
        
        // 定义里程碑名称重复的检查项
        List<DuplicateCheckConfig<ContractMilestone>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "里程碑名称",
                        duplicateNameMilestones,
                        Arrays.asList("里程碑ID", "里程碑名称", "所属合同"),
                        ContractMilestone::getId,
                        ContractMilestone::getMilestoneName,
                        ContractMilestone::getContractName
                )
        );

        // 使用数据准确性检查服务执行检查
        String result = dataIntegrityCheckService.executeIntegrityCheckWithDuplicates(
                "合同里程碑表(pms_contract_milestone)", 
                () -> contractMilestoneMapper.findMilestoneDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pms_contract_milestone表数据准确性检查");
        return result;
    }
    
    @Override
    public String checkContractMilestoneDataIntegrityExcel() {
        log.info("开始生成pms_contract_milestone表数据准确性Excel报告");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<ContractMilestone>> fieldConfigs = Arrays.asList(
                // 里程碑编号缺失配置
                FieldCheckConfig.create(
                        "里程碑编号为空",
                        ContractMilestone::getNumber,
                        Arrays.asList("里程碑ID", "里程碑名称"),
                        ContractMilestone::getId,
                        ContractMilestone::getMilestoneName
                )
        );
        
        // 查询里程碑名称重复的数据
        List<ContractMilestone> duplicateNameMilestones = contractMilestoneMapper.findDuplicateMilestoneNames();
        
        // 定义里程碑名称重复的检查项
        List<DuplicateCheckConfig<ContractMilestone>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "里程碑名称重名",
                        duplicateNameMilestones,
                        Arrays.asList("里程碑ID", "里程碑名称", "所属合同"),
                        ContractMilestone::getId,
                        ContractMilestone::getMilestoneName,
                        ContractMilestone::getContractName
                )
        );

        // 直接使用数据准确性Excel导出服务生成Excel报告
        String excelFilePath = dataIntegrityExcelService.executeIntegrityCheckWithDuplicatesToExcel(
                "合同里程碑表", 
                () -> contractMilestoneMapper.findMilestoneDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pms_contract_milestone表数据准确性Excel报告生成，文件路径：{}", excelFilePath);
        return excelFilePath;
    }
} 