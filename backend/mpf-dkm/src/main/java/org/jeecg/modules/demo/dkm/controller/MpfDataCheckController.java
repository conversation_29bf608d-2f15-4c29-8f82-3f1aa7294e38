package org.jeecg.modules.demo.dkm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.config.shiro.IgnoreAuth;
import org.jeecg.modules.demo.dkm.service.ContractMilestoneService;
import org.jeecg.modules.demo.dkm.service.MarketContractService;
import org.jeecg.modules.demo.dkm.service.QuotationService;
import org.jeecg.modules.demo.dkm.service.RequirementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 数据校验控制器
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Api(tags = "数据校验接口")
@RestController
@RequestMapping("/mpf")
@Slf4j
public class MpfDataCheckController {

    @Autowired
    private MarketContractService marketContractService;
    
    @Autowired
    private QuotationService quotationService;
    
    @Autowired
    private ContractMilestoneService contractMilestoneService;
    
    @Autowired
    private RequirementService requirementService;

    /**
     * 检查市场合同表数据准确性
     * 检查合同名称重名、合同编号为空、商务负责人为空、技术负责人为空
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     */
    @ApiOperation(value = "检查市场合同数据准确性", notes = "检查合同名称重名、合同编号为空、商务负责人为空、技术负责人为空")
    @PostMapping("/check-market-contract")
    @IgnoreAuth
    public Result<String> checkMarketContractDataIntegrity() {
        log.info("开始检查pms_market_contract表数据准确性");
        String markdownResult = marketContractService.checkMarketContractDataIntegrity();
        log.info("完成pms_market_contract表数据准确性检查");
        return Result.OK(markdownResult);
    }

    /**
     * 检查市场合同表数据准确性并导出Excel
     * 检查合同名称重名、合同编号为空、商务负责人为空、技术负责人为空
     * @return Excel文件路径
     */
    @ApiOperation(value = "检查市场合同数据准确性并导出Excel", notes = "检查合同名称重名、合同编号为空、商务负责人为空、技术负责人为空")
    @PostMapping("/check-market-contract-excel")
    @IgnoreAuth
    public Result<String> checkMarketContractDataIntegrityExcel() {
        log.info("开始检查pms_market_contract表数据准确性Excel导出");
        String excelUrl = marketContractService.checkMarketContractDataIntegrityExcel();
        log.info("完成pms_market_contract表数据准确性Excel导出");
        return Result.OK(excelUrl);
    }
    
    /**
     * 检查报价管理表数据准确性
     * 检查报价名称重名、报价编号为空
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     */
    @ApiOperation(value = "检查报价管理数据准确性", notes = "检查报价名称重名、报价编号为空")
    @PostMapping("/check-quotation")
    @IgnoreAuth
    public Result<String> checkQuotationDataIntegrity() {
        log.info("开始检查pmsx_quotation_management表数据准确性");
        String markdownResult = quotationService.checkQuotationDataIntegrity();
        log.info("完成pmsx_quotation_management表数据准确性检查");
        return Result.OK(markdownResult);
    }
    
    /**
     * 检查报价管理表数据准确性并导出Excel
     * 检查报价名称重名、报价编号为空
     * @return Excel文件路径
     */
    @ApiOperation(value = "检查报价管理数据准确性并导出Excel", notes = "检查报价名称重名、报价编号为空")
    @PostMapping("/check-quotation-excel")
    @IgnoreAuth
    public Result<String> checkQuotationDataIntegrityExcel() {
        log.info("开始检查pmsx_quotation_management表数据准确性Excel导出");
        String excelUrl = quotationService.checkQuotationDataIntegrityExcel();
        log.info("完成pmsx_quotation_management表数据准确性Excel导出");
        return Result.OK(excelUrl);
    }
    
    /**
     * 检查合同里程碑表数据准确性
     * 检查里程碑名称重名、里程碑编号为空
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     */
    @ApiOperation(value = "检查合同里程碑数据准确性", notes = "检查里程碑名称重名、里程碑编号为空")
    @PostMapping("/check-contract-milestone")
    @IgnoreAuth
    public Result<String> checkContractMilestoneDataIntegrity() {
        log.info("开始检查pms_contract_milestone表数据准确性");
        String markdownResult = contractMilestoneService.checkContractMilestoneDataIntegrity();
        log.info("完成pms_contract_milestone表数据准确性检查");
        return Result.OK(markdownResult);
    }
    
    /**
     * 检查合同里程碑表数据准确性并导出Excel
     * 检查里程碑名称重名、里程碑编号为空
     * @return Excel文件路径
     */
    @ApiOperation(value = "检查合同里程碑数据准确性并导出Excel", notes = "检查里程碑名称重名、里程碑编号为空")
    @PostMapping("/check-contract-milestone-excel")
    @IgnoreAuth
    public Result<String> checkContractMilestoneDataIntegrityExcel() {
        log.info("开始检查pms_contract_milestone表数据准确性Excel导出");
        String excelUrl = contractMilestoneService.checkContractMilestoneDataIntegrityExcel();
        log.info("完成pms_contract_milestone表数据准确性Excel导出");
        return Result.OK(excelUrl);
    }
    
    /**
     * 检查需求管理表数据准确性
     * 检查需求名称重名、需求编号为空
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     */
    @ApiOperation(value = "检查需求管理数据准确性", notes = "检查需求名称重名、需求编号为空")
    @PostMapping("/check-requirement")
    @IgnoreAuth
    public Result<String> checkRequirementDataIntegrity() {
        log.info("开始检查pms_requirement_mangement表数据准确性");
        String markdownResult = requirementService.checkRequirementDataIntegrity();
        log.info("完成pms_requirement_mangement表数据准确性检查");
        return Result.OK(markdownResult);
    }
    
    /**
     * 检查需求管理表数据准确性并导出Excel
     * 检查需求名称重名、需求编号为空
     * @return Excel文件路径
     */
    @ApiOperation(value = "检查需求管理数据准确性并导出Excel", notes = "检查需求名称重名、需求编号为空")
    @PostMapping("/check-requirement-excel")
    @IgnoreAuth
    public Result<String> checkRequirementDataIntegrityExcel() {
        log.info("开始检查pms_requirement_mangement表数据准确性Excel导出");
        String excelUrl = requirementService.checkRequirementDataIntegrityExcel();
        log.info("完成pms_requirement_mangement表数据准确性Excel导出");
        return Result.OK(excelUrl);
    }
} 