server:
  port: 7011

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: mpf-dkm
  # 数据库连接配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************
    username: product_test
    password: 'OrionPtest!@#$2024'
  # 开发环境不使用nacos配置
  cloud:
    nacos:
      discovery:
        register-enabled: false  # 禁用服务注册到Nacos
  redis:
    database: 15
    host: ************
    password: 'pms123!@#%$orion'
    port: 6379

mybatis-plus:
  mapper-locations:
    - classpath*:**/mapper/xml/*.xml

redis:
  database: 15
  host: ************
  password: 'pms123!@#%$orion'
  port: 6379

# 新增下载服务器IP配置
mpf:
  download-server-ip: ************
  download-server-port: 7012
  download-server-path: /home/<USER>