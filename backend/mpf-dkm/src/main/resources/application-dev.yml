server:
  port: 7011

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: mpf-dkm
  # 数据库连接配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://***************:44306/orion_prd_test?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: root
    password: pms$&orion@!!
  # 开发环境不使用nacos配置
  cloud:
    nacos:
      discovery:
        register-enabled: false  # 禁用服务注册到Nacos
  redis:
    database: 0
    host: localhost
    port: 6379
    password:
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
    timeout: 5000

mybatis-plus:
  mapper-locations:
    - classpath*:**/mapper/xml/*.xml

redis:
  database: 0
  host: localhost
  port: 6379
  password:
  lettuce:
    pool:
      max-active: 8
      max-idle: 8
      min-idle: 0
      max-wait: -1ms
  timeout: 5000

# 新增下载服务器IP配置
mpf:
  download-server-ip: ************
  download-server-port: 7012
  download-server-path: D:\home\download