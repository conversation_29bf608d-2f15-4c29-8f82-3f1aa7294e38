<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
	<!-- 定义日志文件的存储地址 -->
	<property name="LOG_HOME" value="../logs" />

	<!-- 控制台输出 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!-- 格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] [%thread] %highlight(%-5level) %cyan(%logger{50}:%L) - %msg%n</pattern>
		</encoder>
	</appender>

	<!-- 按照每天生成日志文件 -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<FileNamePattern>${LOG_HOME}/log-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<MaxHistory>30</MaxHistory>
			<maxFileSize>100MB</maxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
				<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] [%thread] %-5level %tid %logger{50} - %msg%n</pattern>
			</layout>
		</encoder>
	</appender>

	<!-- 生成 error html格式日志开始 -->
	<appender name="HTML" class="ch.qos.logback.core.FileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<!-- 设置日志级别,过滤掉info日志,只输入error日志 -->
			<level>ERROR</level>
		</filter>
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="ch.qos.logback.classic.html.HTMLLayout">
				<pattern>%p%d%msg%M%F{32}%L</pattern>
			</layout>
		</encoder>
		<file>${LOG_HOME}/error-log.html</file>
	</appender>
	<!-- 生成 error html格式日志结束 -->

	<!-- 每天生成一个html格式的日志开始 -->
	<appender name="FILE_HTML" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 日志文件输出的文件名 -->
			<FileNamePattern>${LOG_HOME}/stable-%d{yyyy-MM-dd}.%i.html</FileNamePattern>
			<!-- 日志文件保留天数 -->
			<MaxHistory>30</MaxHistory>
			<MaxFileSize>10MB</MaxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="ch.qos.logback.classic.html.HTMLLayout">
				<pattern>%p%d%msg%M%F{32}%L</pattern>
			</layout>
		</encoder>
	</appender>
	<!-- 每天生成一个html格式的日志结束 -->

	<!-- Skywalking 采集日志 -->
	<appender name="grpc-log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
				<Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] [%thread] %-5level %logger{36}  -%msg%n</Pattern>
			</layout>
		</encoder>
	</appender>

	<!-- 异步控制台输出 -->
	<appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="STDOUT" />
		<queueSize>512</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<neverBlock>true</neverBlock>
	</appender>

	<!-- 异步文件输出 -->
	<appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="FILE" />
		<queueSize>512</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<neverBlock>true</neverBlock>
	</appender>

	<!-- 异步 HTML 输出 -->
	<appender name="ASYNC_HTML" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="HTML" />
		<queueSize>512</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<neverBlock>true</neverBlock>
	</appender>

	<!-- 异步 FILE_HTML 输出 -->
	<appender name="ASYNC_FILE_HTML" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="FILE_HTML" />
		<queueSize>512</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<neverBlock>true</neverBlock>
	</appender>

	<!-- 异步 Skywalking 输出 -->
	<appender name="ASYNC_GRPC_LOG" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="grpc-log" />
		<queueSize>512</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<neverBlock>true</neverBlock>
	</appender>

	<logger name="org.springframework" level="WARN"/>
	<logger name="org.jeecg" level="INFO"/>

	<!-- MyBatis log configure -->
	<logger name="com.apache.ibatis" level="TRACE" />
	<logger name="java.sql.Connection" level="DEBUG" />
	<logger name="java.sql.Statement" level="DEBUG" />
	<logger name="java.sql.PreparedStatement" level="DEBUG" />

	<root level="INFO">
		<appender-ref ref="ASYNC_GRPC_LOG"/>
		<appender-ref ref="ASYNC_STDOUT" />
		<appender-ref ref="ASYNC_FILE" />
		<appender-ref ref="ASYNC_HTML" />
		<appender-ref ref="ASYNC_FILE_HTML" />
	</root>

</configuration>