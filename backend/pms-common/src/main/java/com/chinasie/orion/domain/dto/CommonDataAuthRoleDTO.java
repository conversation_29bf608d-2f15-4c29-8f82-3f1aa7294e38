package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * CommonDataAuthRole DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-23 15:09:06
 */
@ApiModel(value = "CommonDataAuthRoleDTO对象", description = "通用数据权限")
@Data
@ExcelIgnoreUnannotated
public class CommonDataAuthRoleDTO extends  ObjectDTO   implements Serializable{

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @ExcelProperty(value = "数据类型 ", index = 1)
    private String dataType;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    @ExcelProperty(value = "数据ID ", index = 2)
    private String dataId;

    /**
     * 授权对象：Role:User
     */
    @ApiModelProperty(value = "授权对象：Role:User")
    @ExcelProperty(value = "授权对象：Role:User ", index = 3)
    private String authObject;

    /**
     * 权限code：read,edit
     */
    @ApiModelProperty(value = "权限code：read,write")
    @ExcelProperty(value = "权限code：read,write ", index = 4)
    private String permissionCode;

    /**
     * 对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID
     */
    @ApiModelProperty(value = "对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID")
    @ExcelProperty(value = "对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID ", index = 5)
    private String objectValue;




}
