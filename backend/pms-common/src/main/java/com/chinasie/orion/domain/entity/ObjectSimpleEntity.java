package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(
        value = "ObjectSimpleEntity",
        description = "ObjectSimpleEntity"
)
public class ObjectSimpleEntity extends LyraSimpleEntity {
    @ApiModelProperty("业务组织Id")
    @TableField(
            fill = FieldFill.INSERT,
            value = "org_id"
    )
    private String orgId;

    public ObjectSimpleEntity() {
    }

    public String getOrgId() {
        return this.orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}
