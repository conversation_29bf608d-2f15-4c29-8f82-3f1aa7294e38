package com.chinasie.orion.bo;

import cn.hutool.core.collection.CollUtil;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.helper.StatusRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class DataStatusNBO {

    @Autowired
    private StatusRedisHelper statusRedisHelper;



    public Map<Integer, DataStatusVO> getDataStatusMapByClassName(String className) throws Exception {
        List<DataStatusVO> statusVOList = statusRedisHelper.getStatusInfoListByClassName(className);
        if (CollUtil.isEmpty(statusVOList)) return new HashMap<>();
        return statusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));
    }

    public List<DataStatusVO> getDataStatusListByClassName(String className) throws Exception {
        return statusRedisHelper.getStatusInfoListByClassName(className);
    }



    public Map<Integer, DataStatusVO> getDataStatusMapById(String id) throws Exception {
        List<DataStatusVO> statusVOList = statusRedisHelper.getStatusList(id);
        if (CollUtil.isEmpty(statusVOList)) return new HashMap<>();
        return statusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));
    }

    public List<DataStatusVO> getDataStatusListById(String id) throws Exception {
         return statusRedisHelper.getStatusList(id);
    }

}
