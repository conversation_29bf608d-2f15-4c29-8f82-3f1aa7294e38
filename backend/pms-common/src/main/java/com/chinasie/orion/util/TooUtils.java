package com.chinasie.orion.util;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class TooUtils {


    public   static  final  String  yyyyMMdd="yyyyMMdd";

    /**
     *  判断两个日期是否是同一天
     * @param date
     * @return
     */
    public static boolean isSameDay(Date date) {
        if (Objects.isNull(date)){
            return false;
        }
        LocalDate givenDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate currentDate = LocalDate.now();

        return givenDate.equals(currentDate);
    }


    /**
     * 判断日期A是否早于日期B（仅比较日期部分）
     * @param a 日期A
     * @param b 日期B
     * @return true=A早于B false=A晚于或等于B
     */
    public static boolean isBeforeDay(Date a, Date b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }
        LocalDate dateA = a.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate dateB = b.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return dateA.isBefore(dateB);
    }

    /**
     * 计算两个日期之间的天数
     * @param inDate
     * @return
     */
    public static long getInDate(Date inDate) {
        if(!Objects.isNull(inDate)){
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(inDate));
            endCalendar.setTime(DateUtil.beginOfDay(new Date()));
            if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                return ((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000));

            }else{
                return ( startCalendar.getTimeInMillis() -endCalendar.getTimeInMillis() ) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }

    public static long getWorkDurationNew(Date beginDate,Date actDate) {
        try {
            if(Objects.nonNull(beginDate)){
                Calendar startCalendar = Calendar.getInstance();
                Calendar endCalendar = Calendar.getInstance();

                startCalendar.setTime(DateUtil.beginOfDay(beginDate));
                if(Objects.nonNull(actDate)){
                    endCalendar.setTime(DateUtil.beginOfDay(actDate));
                }else{
                    endCalendar.setTime(DateUtil.beginOfDay(new Date()));
                }
                if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                    return ((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000));

                }else{
                    return ( startCalendar.getTimeInMillis() -endCalendar.getTimeInMillis() ) / (24 * 60 * 60 * 1000);
                }
            }
            return 0;
        }catch (Exception e){
            log.error("计算工作时长失败:{},{}", beginDate,actDate);
            e.printStackTrace();
        }
        return 0;
    }


    public static long getWorkDuration(Date beginDate,Date endDate) {
        if(!Objects.isNull(beginDate) && !Objects.isNull(endDate)){
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(beginDate));
            endCalendar.setTime(DateUtil.beginOfDay(endDate));
            if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                return (( endCalendar.getTimeInMillis()-startCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000));

            }else{
                return (endCalendar.getTimeInMillis()- startCalendar.getTimeInMillis() ) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }

    public static Date getEndDate(Date beginDate, Integer workDuration) {
        try {
            if (!Objects.isNull(beginDate) && !Objects.isNull(workDuration)) {
                log.debug("开始时间: {}", beginDate);
                log.debug("工作时长: {}", workDuration);
                if (beginDate.after(new Date())) {
                    log.warn("开始时间在当前时间之后");
                    return null;
                }
                Calendar startCalendar = Calendar.getInstance();
                startCalendar.setTime(DateUtil.beginOfDay(beginDate));
                startCalendar.add(Calendar.DAY_OF_MONTH, workDuration);
                Date endDate = startCalendar.getTime();
                log.debug("结束时间: {}", endDate);
                return endDate;
            }
            log.warn("开始时间或工作时长为空");
        } catch (Exception e) {
            log.error("计算结束时间失败", e);
//            throw new RuntimeException("计算结束时间失败", e);
        }

        return null;
    }


    public static String  getCurrentDate(String pattern) {
        // 创建一个SimpleDateFormat对象，指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        // 获取当前日期
        Date currentDate = new Date();
        // 格式化日期
        return sdf.format(currentDate);
    }

    public static String  getCurrentDate(String pattern,Date date) {
        // 创建一个SimpleDateFormat对象，指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        // 获取当前日期
        // 格式化日期
        return sdf.format(date);
    }


    public static double divideAndCeil(BigDecimal dividend, BigDecimal divisor) {
        if (divisor.compareTo(BigDecimal.ZERO) == 0) {
            throw new ArithmeticException("Divisor cannot be zero");
        }
        BigDecimal result = dividend.divide(divisor, MathContext.DECIMAL128);
        result = result.setScale(result.scale(), RoundingMode.CEILING);
        return result.doubleValue();
    }
}
