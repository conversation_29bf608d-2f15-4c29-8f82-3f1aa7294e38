package com.chinasie.orion.bo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.helper.StatusRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 状态公共类获取
 **/
@Slf4j
@Component
public class StatusConvertBO {

    @Resource
    private StatusRedisHelper statusRedisHelperTemp;
    private StatusRedisHelper statusRedisHelper;

    @PostConstruct
    public void init() {
        statusRedisHelper = statusRedisHelperTemp;
    }

    public Map<Integer, DataStatusVO> getStatusMapByClassName(String className) {
        if (StrUtil.isNotBlank(className)) {
            // 拼接 SQL 语句
            String sql = "SELECT policy FROM dme_class " + " WHERE class_name = " + className;
            // 执行查询
            Map<String, Object> result = SqlRunner.db().selectOne(sql);
            if (ObjectUtil.isNotNull(result)) {
                String policy = (String) result.get("policy");
                if (StrUtil.isNotBlank(policy)) {
                    List<DataStatusVO> statusList = statusRedisHelper.getStatusList(policy);
                    if (!CollectionUtils.isEmpty(statusList)) {
                        return statusList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity(), (K1, K2) -> K2));
                    }
                }
            }
        }
        return null;
    }
}
