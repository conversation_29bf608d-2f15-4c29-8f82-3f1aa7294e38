package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CommonDataAuthRoleBatchDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.domain.entity.CommonDataAuthRole;
import com.chinasie.orion.domain.dto.CommonDataAuthRoleDTO;
import com.chinasie.orion.domain.vo.CommonDataAuthRoleVO;
import com.chinasie.orion.service.CommonDataAuthRoleService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * CommonDataAuthRole 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-23 15:09:06
 */
@RestController
@RequestMapping("/commonDataAuthRole")
@Api(tags = "通用数据权限")
public class  CommonDataAuthRoleController  {

    @Autowired
    private CommonDataAuthRoleService commonDataAuthRoleService;

    /**
     * 新增
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "权限设置")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】设置权限", type = "CommonDataAuthRole", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> createBath(@RequestBody CommonDataAuthRoleBatchDTO dataAuthRoleBatchDTO) throws Exception {
        return new ResponseDTO<>(commonDataAuthRoleService.createBath(dataAuthRoleBatchDTO));
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "CommonDataAuthRole", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = commonDataAuthRoleService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "当前人拥有的权限对于数据列表")
    @RequestMapping(value = "/current/user/roles", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取权限列表", type = "CommonDataAuthRole", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Map<String, Set<String>>> currentUserRoles(@RequestBody List<String> idList) throws Exception {
        return new ResponseDTO<>(commonDataAuthRoleService.currentUserRoles(idList));
    }

    @ApiOperation(value = "获取某数据所设置的权限列表")
    @RequestMapping(value = "/data/auth/roles/dataId", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取权限列表【{{#}}】", type = "CommonDataAuthRole", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Map<String,List<CommonDataAuthRoleVO>>> dataAuthRoleByDataId(@RequestParam("dataId") String dataId) throws Exception {
        return new ResponseDTO<>(commonDataAuthRoleService.dataAuthRoleByDataId(dataId));
    }
}
