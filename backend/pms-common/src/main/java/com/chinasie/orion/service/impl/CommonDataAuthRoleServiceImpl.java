package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.chinasie.orion.domain.dto.CommonDataAuthRoleBatchDTO;
import com.chinasie.orion.domain.dto.CommonDataAuthRoleDTO;
import com.chinasie.orion.domain.entity.CommonDataAuthRole;
import com.chinasie.orion.domain.vo.CommonDataAuthRoleVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.CommonDataAuthRoleMapper;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.RoleRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.CommonDataAuthRoleService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * CommonDataAuthRole 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-23 15:09:06
 */
@Service
@Slf4j
public class CommonDataAuthRoleServiceImpl extends  OrionBaseServiceImpl<CommonDataAuthRoleMapper, CommonDataAuthRole>   implements CommonDataAuthRoleService {

    @Autowired
    private LockTemplate lockTemplate;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private RoleRedisHelper roleRedisHelper;
    private String LOCKED_KEY ="pmsx::common-init-data::locked::dataId";

    /**
     *  新增
     *
     * * @param commonDataAuthRoleDTO
     */
    @Override
    public  String create(CommonDataAuthRoleDTO commonDataAuthRoleDTO) throws Exception {
        CommonDataAuthRole commonDataAuthRole =BeanCopyUtils.convertTo(commonDataAuthRoleDTO,CommonDataAuthRole::new);
        this.save(commonDataAuthRole);
        return commonDataAuthRole.getId();
    }

    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }

    @Override
    public Boolean createBath(CommonDataAuthRoleBatchDTO dataAuthRoleBatchDTO) {
        List<CommonDataAuthRoleBatchDTO.AuthRoleDto>  roleDtoList=  dataAuthRoleBatchDTO.getRoleDtoList();
        String dataId=  dataAuthRoleBatchDTO.getDataId();
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY+"::"+dataId, 30000L, 5000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        try {
            this.remove(new LambdaQueryWrapperX<>(CommonDataAuthRole.class).eq(CommonDataAuthRole::getDataId,dataId));
            String authObject = dataAuthRoleBatchDTO.getAuthObject();
            String dataType= dataAuthRoleBatchDTO.getDataType();
            List<CommonDataAuthRole> commonDataAuthRoles = new ArrayList<>();
            for (CommonDataAuthRoleBatchDTO.AuthRoleDto authRoleDto : roleDtoList) {
                CommonDataAuthRole commonDataAuthRole = new CommonDataAuthRole();
                commonDataAuthRole.setDataType(dataType);
                commonDataAuthRole.setDataId(dataId);
                commonDataAuthRole.setBusinessType(dataAuthRoleBatchDTO.getBusinessType().getCode());
                commonDataAuthRole.setAuthObject(authObject);
                commonDataAuthRole.setPermissionCode(authRoleDto.getPermissionCode());
                commonDataAuthRole.setObjectValue(authRoleDto.getObjectValue());
                commonDataAuthRoles.add(commonDataAuthRole);
            }
            return this.saveBatch(commonDataAuthRoles);
            // 设置数据锁
        }catch (Exception e){
            log.error("批量新增权限失败",e);
            return Boolean.FALSE;
        }finally {
            lockTemplate.releaseLock(lockInfo);
        }
    }

    @Override
    public Map<String, Set<String>> currentUserRoles(List<String> idList) {
        if(CollectionUtils.isBlank(idList)){
            return new HashMap<>();
        }
        LambdaQueryWrapperX<CommonDataAuthRole> wrapperX = new LambdaQueryWrapperX<>(CommonDataAuthRole.class);
        wrapperX.in(CommonDataAuthRole::getDataId,idList);
        wrapperX.eq(CommonDataAuthRole::getAuthObject,"User");
        wrapperX.eq(CommonDataAuthRole::getObjectValue, CurrentUserHelper.getCurrentUserId());
        wrapperX.select(CommonDataAuthRole::getDataId,CommonDataAuthRole::getPermissionCode);
        List<CommonDataAuthRole> list = this.list(wrapperX);
        if(CollectionUtils.isBlank(list)){
            return  new HashMap<>();
        }
        Map<String,Set<String>> map = new HashMap<>();
        list.forEach(item-> {
            String permissionCode = item.getPermissionCode();
            if (StringUtils.hasText(permissionCode)) {
                Set<String> set = map.get(item.getDataId());
                if (null == set) {
                    set = new HashSet<>();
                }
                set.add(permissionCode.toUpperCase());
                map.put(item.getDataId(), set);
            }
        });
        return map;
    }
    @Override
    public Map<String, List<CommonDataAuthRoleVO>> dataAuthRoleByDataId(String dataId) {
        LambdaQueryWrapperX<CommonDataAuthRole> wrapperX = new LambdaQueryWrapperX<>(CommonDataAuthRole.class);
        wrapperX.eq(CommonDataAuthRole::getDataId,dataId);
        wrapperX.select(CommonDataAuthRole::getDataId,CommonDataAuthRole::getPermissionCode
                ,CommonDataAuthRole::getObjectValue,CommonDataAuthRole::getAuthObject,CommonDataAuthRole::getDataType);
        List<CommonDataAuthRole> list = this.list(wrapperX);
        if(CollectionUtils.isBlank(list)){
            return  new HashMap<>();
        }
        Map<String, List<CommonDataAuthRoleVO>> map = new HashMap<>();
        List<String> roleList = new ArrayList<>();
        List<String> userIdList = new ArrayList<>();
        List<CommonDataAuthRole> roleTypeDataList = new ArrayList<>();
        List<CommonDataAuthRole> userTypeDataList = new ArrayList<>();
        for (CommonDataAuthRole commonDataAuthRole : list) {
            if("User".equals(commonDataAuthRole.getAuthObject())){
                userIdList.add(commonDataAuthRole.getObjectValue());
                userTypeDataList.add(commonDataAuthRole);
            }
            if("Role".equals(commonDataAuthRole.getAuthObject())){
                roleTypeDataList.add(commonDataAuthRole);
            }
        }

        if(!CollectionUtils.isBlank(userIdList)){
            List<CommonDataAuthRoleVO> vos = new ArrayList<>();
            List<UserBaseCacheVO> users =  userRedisHelper.getUserBaseCacheByIds(userIdList);
            Map<String,String>  userMap =users.stream().collect(
                    Collectors.toMap(UserBaseCacheVO::getId,userBaseCacheVO -> String.format("%s_%s",userBaseCacheVO.getName(),userBaseCacheVO.getCode())));
            for (CommonDataAuthRole commonDataAuthRole : userTypeDataList) {
                CommonDataAuthRoleVO vo = BeanCopyUtils.convertTo(commonDataAuthRole,CommonDataAuthRoleVO::new);
                String objectValueName = userMap.get(commonDataAuthRole.getObjectValue());
                String[] objectValueNames = objectValueName.split("_");
                vo.setObjectName(objectValueNames[0]);
                vo.setObjectCode(objectValueNames[1]);
                vos.add(vo);
            }
            map.put("User",vos);
        }
        if(!CollectionUtils.isBlank(roleList)){
            List<CommonDataAuthRoleVO> vos = new ArrayList<>();
            String orgId= CurrentUserHelper.getOrgId();
            for (CommonDataAuthRole commonDataAuthRole : roleTypeDataList) {
                RoleVO roleVO = roleRedisHelper.getRole(commonDataAuthRole.getAuthObject(),orgId);
                CommonDataAuthRoleVO vo = BeanCopyUtils.convertTo(commonDataAuthRole,CommonDataAuthRoleVO::new);
                vo.setObjectName(Objects.nonNull(roleVO)?roleVO.getName():"未知");
                vos.add(vo);
            }
            map.put("Role",vos);
        }
        return map;
    }

    /**
     *  根据数据类型获取当前用户相应权限的id集合
     * @param dataType 数据类型
     * @param type read-只读;write-只写;null-可读写
     */
    @Override
    public List<String> getIdListByTypeForDataType(String dataType, String type) {
        return getIdListByCondition("dataType", dataType, type);
    }

    /**
     *  根据业务类型获取当前用户相应权限的id集合
     * @param businessType 业务类型
     * @param type read-只读;write-只写;null-可读写
     */
    @Override
    public List<String> getIdListByTypeForBusinessType(String businessType, String type) {
        return getIdListByCondition("businessType", businessType, type);
    }

    /**
     *  根据业务类型获取当前用户相应权限数据
     * @param businessType 业务类型
     * @param type read-只读;write-只写;null-可读写
     */
    @Override
    public List<CommonDataAuthRole> getDataAuthByTypeForBusinessType(String businessType, String type) {
        LambdaQueryWrapperX<CommonDataAuthRole> sql = new LambdaQueryWrapperX<>(CommonDataAuthRole.class);
        sql.distinct();
        sql.select(CommonDataAuthRole::getDataId, CommonDataAuthRole::getDataType);
        sql.eq(CommonDataAuthRole::getAuthObject, "User");
        sql.eq(CommonDataAuthRole::getObjectValue, CurrentUserHelper.getCurrentUserId());
        sql.eq(CommonDataAuthRole::getBusinessType, businessType);
        // 如果权限类型不为空，则添加过滤条件
        if (StrUtil.isNotBlank(type)) {
            sql.eq(CommonDataAuthRole::getPermissionCode, type);
        }
        List<CommonDataAuthRole> list = this.getBaseMapper().selectList(sql);
        return CollUtil.isNotEmpty(list) ? list : Collections.emptyList();
    }

    /**
     * 根据条件获取当前用户相应权限的id集合（通用方法）
     *
     * @param field 查询条件字段名（dataType 或 businessType）
     * @param fieldValue 查询条件值
     * @param type 权限类型 (read, write, null)
     * @return 符合条件的id列表
     */
    private List<String> getIdListByCondition(String field, String fieldValue, String type) {
        LambdaQueryWrapperX<CommonDataAuthRole> sql = new LambdaQueryWrapperX<>(CommonDataAuthRole.class);
        sql.distinct();
        sql.select(CommonDataAuthRole::getDataId);
        sql.eq(CommonDataAuthRole::getAuthObject, "User");
        sql.eq(CommonDataAuthRole::getObjectValue, CurrentUserHelper.getCurrentUserId());

        // 动态设置条件字段
        if ("dataType".equals(field)) {
            sql.eq(CommonDataAuthRole::getDataType, fieldValue);
        } else if ("businessType".equals(field)) {
            sql.eq(CommonDataAuthRole::getBusinessType, fieldValue);
        }

        // 如果权限类型不为空，则添加过滤条件
        if (StrUtil.isNotBlank(type)) {
            sql.eq(CommonDataAuthRole::getPermissionCode, type);
        }

        List<String> list = this.getBaseMapper().selectObjs(sql);
        return CollUtil.isNotEmpty(list) ? list : Collections.emptyList();
    }

    public Map<String, Set<String>> getRoleByOrgIdCurrentUser() {
        return Map.of();
    }

}
