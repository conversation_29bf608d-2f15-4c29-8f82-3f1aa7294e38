package com.chinasie.orion;

import com.chinasie.orion.domain.dto.ProjectBudgetDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.production.JobManage;
import com.chinasie.orion.domain.vo.AnalysisVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.*;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest(classes = PMSApplication.class)
@Slf4j
class DeliverableServiceImplTest {

    @Resource
    private DeliverableService deliverableService;


    @Resource
    private ProjectAchievementService projectAchievementService;

    @Autowired
    protected WebApplicationContext context;

    @Resource
    private ProjectBudgetService projectBudgetService;

    protected MockMvc mvc;

    @Resource
    private CostCenterService costCenterService;

    @Resource
    private ExpenseSubjectService expenseSubjectService;


    @Resource
    private ProjectSchemeMilestoneNodeService projectSchemeMilestoneNodeService;

    @Resource
    private MaterialManageService materialManageService;

    @BeforeEach
    public void setUp() {
        mvc = MockMvcBuilders.webAppContextSetup(context).build();
    }



    @Test
    public void contextLoads() throws Exception {
        List<AnalysisVO> result = deliverableService.analysis("pbdz200eaf3478b3417b855a73d8b6bd0fbb");

        System.out.println(11);
    }
    @Test
    public void contextLoads2() throws Exception {
        MPJLambdaWrapper<MaterialManage> materialManageMPJLambdaWrapper = new LambdaQueryWrapperX<>(MaterialManage.class)
                .rightJoin(com.chinasie.orion.domain.entity.production.JobManage.class, on -> on.eq(MaterialManage::getNumber, com.chinasie.orion.domain.entity.production.JobManage::getNumber))
                .rightJoin(ProjectScheme.class, on -> on.eq(ProjectScheme::getId, JobManage::getPlanSchemeId).eq(ProjectScheme::getProjectId,"projectId"));

        long count = materialManageService.count(materialManageMPJLambdaWrapper);
        System.out.println(count);
    }


    @Test
    void contextLoads3() throws Exception {


        LambdaQueryWrapperX<ProjectSchemeMilestoneNode> wrapper = new LambdaQueryWrapperX<>();
        wrapper.selectAll(ProjectSchemeMilestoneNode.class);
        wrapper.eq(ProjectSchemeMilestoneNode::getTemplateId, "1719531676265680896");
        wrapper.orderByAsc(ProjectSchemeMilestoneNode::getSort);
        List<ProjectSchemeMilestoneNode> projectSchemeMilestoneNodes = projectSchemeMilestoneNodeService.list(wrapper);
        System.out.println(00);

    }


    @Test
    public void uploadFile() throws Exception {
        ClassPathResource classPathResource = new ClassPathResource("项目管理_任务导入.xlsx");
        MockMultipartFile file = new MockMultipartFile(
                "excel",
                "项目管理_任务导入.xlsx",
                MediaType.TEXT_PLAIN_VALUE,
                classPathResource.getInputStream()
        );
        MvcResult mvcResult = mvc.perform(
                MockMvcRequestBuilders
                        .multipart("/plan/import/excel/ci4176632cf38cd74e1bb824ccfbcfa21bd6").file(file)
                        .header("Authorization", "Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
                        .header("orgId", "rxlm04256c6e0d9d429a89084be972fdfa7f")
                        .header("pId", "ykovb40e9fb1061b46fb96c4d0d3333dcc13"))
                .andDo(result -> result.getResponse().setCharacterEncoding("UTF-8"))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();
        log.info(mvcResult.getResponse().getContentAsString());
    }


    @Test
    public void demo() throws Exception {
        MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.get("/order/add"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

        log.info(mvcResult.getResponse().getContentAsString());
    }

    @Test
    public void demoCreate() throws Exception {
        for (int i = 0; i < 50; i++) {
            ProjectBudgetDTO projectBudgetDTO = new ProjectBudgetDTO();
            projectBudgetDTO.setIsOut(0);
            projectBudgetDTO.setOwnerId("as4fadee5558057b4bc599b68be2a97b2584");
            projectBudgetDTO.setCreateTime(new Date());
            projectBudgetDTO.setLogicStatus(1);
            projectBudgetDTO.setModifyId("as4fadee5558057b4bc599b68be2a97b2584");
            projectBudgetDTO.setCreatorId("as4fadee5558057b4bc599b68be2a97b2584");
            projectBudgetDTO.setClassName("ProjectBudget");
            projectBudgetDTO.setStatus(1);

            projectBudgetDTO.setNumber("FY-CL-00" + i);
            projectBudgetDTO.setName("测试" + i);
            //3r811714472616796995584
            projectBudgetDTO.setCostCenterId("3r811714472554985537536");
            projectBudgetDTO.setCostCenterName("成本中心测试部门" + i);
            // 6jhn1714575633219076096
            projectBudgetDTO.setExpenseAccountId("6jhn1714575338887987200");
            projectBudgetDTO.setExpenseAccountName("费用科目测试" + i);
            projectBudgetDTO.setYear("200" + i);
            projectBudgetDTO.setYearExpense(new BigDecimal("********" + i));
            projectBudgetDTO.setTotalCost(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setPriceDifference(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setProjectId("kcam05e3d9ebfd1a44a282e5e1c4af35fc75");
            projectBudgetDTO.setJanuaryMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setFebruaryMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setMarchMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setAprilMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setMayMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setJuneMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setJulyMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setAugustMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setSeptemberMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setOctoberMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setNovemberMoney(new BigDecimal(i + "000" + i));
            projectBudgetDTO.setDecemberMoney(new BigDecimal(i + "000" + i));
            projectBudgetService.create(projectBudgetDTO);
        }

    }

    @Test
    public void demoMap() throws Exception {
        List<String> costCenters = new ArrayList<>();
        List<String> expenseSubjects = new ArrayList<>();
        costCenters.add("3r811714472554985537536");
        costCenters.add("3r811714472616796995584");
        Map<String, CostCenter> cosCenterMap = costCenterService.getCosCenterMap(costCenters);
        expenseSubjects.add("6jhn1714578533576097792");
        expenseSubjects.add("6jhn1714576544704249856");
        expenseSubjects.add("6jhn1714576254391304192");
        expenseSubjects.add("6jhn1714575633219076096");
        Map<String, ExpenseSubject> expenseSubjectMap = expenseSubjectService.getExpenseSubjectMap(expenseSubjects);
        log.info("cosCenterMap:" + cosCenterMap);
        log.info("expenseSubjectMap:" + expenseSubjectMap);

    }


    @Test
    public void test_2() throws Exception {
//        ProjectAchievementVO y6yf1722184995277307904 = projectAchievementService.detail("y6yf1722184995277307904");
//        System.out.println(1);

        ClassPathResource classPathResource = new ClassPathResource("projectlifecycle.json");
        //读取json
        List<ProjectLifeCycleNode> lifeCycleNodes = readJsonFileToList(classPathResource, ProjectLifeCycleNode.class);
        System.out.println(lifeCycleNodes);
    }
    public static <T> List<T> readJsonFileToList(ClassPathResource classPathResource, Class<T> clazz) {
        Gson gson = new Gson();
        try (InputStreamReader reader = new InputStreamReader(classPathResource.getInputStream(), StandardCharsets.UTF_8)) {
            Type listType = TypeToken.getParameterized(List.class, clazz).getType();
            return gson.fromJson(reader, listType);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
