package com.chinasie.orion;

import com.chinasie.orion.schedule.ProjectJobHeightRisk;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PMSApplication.class)
public class HeightRiskTest {

    @Autowired
    private ProjectJobHeightRisk projectJobHeightRisk;

    @Test
    public void riskTest() throws JsonProcessingException {
        projectJobHeightRisk.projectJobHeightRiskInsertData("");
    }
}
