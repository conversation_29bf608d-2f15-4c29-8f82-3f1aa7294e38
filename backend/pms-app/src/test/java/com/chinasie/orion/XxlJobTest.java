package com.chinasie.orion;

import com.chinasie.orion.management.schedule.MarketContractMilestoneXxlJob;
import com.chinasie.orion.management.schedule.QuotationDeadlineXxlJob;
import com.chinasie.orion.xxljob.RepairTrainingXxlJob;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PMSApplication.class)
public class XxlJobTest {

    @Autowired
    QuotationDeadlineXxlJob quotationDeadlineXxlJob;

    @Autowired
    MarketContractMilestoneXxlJob marketContractMilestoneXxlJob;

    @Autowired
    RepairTrainingXxlJob repairTrainingXxlJob;

    @Test
    public void quotationDeadlineXxl() {
        quotationDeadlineXxlJob.quotationDeadlineXxl();
    }

    @Test
    public void milestoneDeadline(){
        marketContractMilestoneXxlJob.milestoneDeadlineXxlJob();
    }


    @Test
    public void RepairTrainingXxlJob() throws Exception {
        repairTrainingXxlJob.repairTrainingNoticeJob();
    }
}
