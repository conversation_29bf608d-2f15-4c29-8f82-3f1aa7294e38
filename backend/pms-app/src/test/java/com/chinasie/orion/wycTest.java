package com.chinasie.orion;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.constant.MarketContractMilestoneEnum;
import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.management.schedule.MarketContractMilestoneXxlJob;
import com.chinasie.orion.management.schedule.QuotationDeadlineXxlJob;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.xxljob.RepairTrainingXxlJob;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest(classes = PMSApplication.class)
public class wycTest {

    @Autowired
    QuotationDeadlineXxlJob quotationDeadlineXxlJob;

    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @Autowired
    MarketContractMilestoneXxlJob marketContractMilestoneXxlJob;

    @Autowired
    RepairTrainingXxlJob repairTrainingXxlJob;

    @Autowired
    MarketContractService marketContractService;

    @Test
    public void quotationDeadlineXxl() {
        quotationDeadlineXxlJob.quotationDeadlineXxl();
    }

    @Test
    public void milestoneDeadline() {
        marketContractMilestoneXxlJob.milestoneDeadlineXxlJob();
    }


    @Test
    public void wycTest() throws Exception {
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, "1k4v1864997134145490944");
        List<ContractMilestone> contractMilestones = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX);
        if (ObjectUtil.isNotEmpty(contractMilestones)) {
            //获取所有的父里程碑
            List<ContractMilestone> parent = contractMilestones.stream().filter(obj -> Objects.isNull(obj.getParentId())).collect(Collectors.toList());
            List<ContractMilestone> child = contractMilestones.stream().filter(obj -> !Objects.isNull(obj.getParentId())).collect(Collectors.toList());
            Map<String, List<ContractMilestone>> childMap = child.stream().collect(Collectors.groupingBy(ContractMilestone::getParentId));
            //遍历所有的里程碑
            Integer sequenceNumber = findMax(parent);//获取当前合同所有的父里程碑序号的最大值
            parent.sort(Comparator.comparing(ContractMilestone::getCreateTime));//把父里程碑按照创建时间排序
            ContractMilestone contractMilestone1 = parent.get(0);
            String contractId = contractMilestone1.getContractId();
            MarketContract marketContract = marketContractService.getById(contractId);
            String number = marketContract.getNumber();
            for (ContractMilestone contractMilestone : contractMilestones) {
                Integer milestoneStatus = contractMilestone.getStatus();
                if (milestoneStatus.equals(MarketContractMilestoneStatusEnum.CREATED.getStatus())) {
                    contractMilestone.setStatus(MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
                    if (ObjectUtil.isEmpty(sequenceNumber)) {
                        contractMilestone1.setSequenceNumber(1);
                    }
                }
            }

            //设置父里程碑序号
            for (ContractMilestone contractMilestone : parent) {
                if (!contractMilestone.getId().equals(contractMilestone1.getId())) {
                    sequenceNumber = findMax(parent);
                    contractMilestone.setSequenceNumber(sequenceNumber + 1);
                }
            }
            //设置里程碑的编码
            for (ContractMilestone contractMilestone : parent) {
                StringBuffer milestoneNumber = new StringBuffer();
                if (ObjectUtil.isNotEmpty(number)){
                    String substring = number.substring(9);
                    milestoneNumber.append(substring).append("-").append(contractMilestone.getSequenceNumber());
                    String milestoneNumberStr = milestoneNumber.toString();
                    contractMilestone.setNumber(milestoneNumberStr);
                }
                List<ContractMilestone> milestones = childMap.get(contractMilestone.getId());
                if (ObjectUtil.isNotEmpty(milestones)){
                    milestones.sort(Comparator.comparing(ContractMilestone::getCreateTime));//把子里程碑按照创建时间排序
                    for (ContractMilestone milestone : milestones) {
                        sequenceNumber = findMax(milestones);
                        if (ObjectUtil.isEmpty(sequenceNumber)){
                            ContractMilestone contractMilestoneSon1 = milestones.get(0);
                            contractMilestoneSon1.setSequenceNumber(1);
                        } else {
                            sequenceNumber = findMax(milestones);
                            milestone.setSequenceNumber(sequenceNumber + 1);
                        }
                    }

                    for (ContractMilestone milestone : milestones) {
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append(contractMilestone.getNumber()).append("-").append(milestone.getSequenceNumber());
                        String string = stringBuffer.toString();
                        milestone.setNumber(string);
                    }
                }

            }


            contractMilestoneService.updateBatchById(contractMilestones);
        }
    }

    private Integer findMax(List<ContractMilestone> contractMilestones) {
        Optional<Integer> maxOpt = contractMilestones.stream().map(ContractMilestone::getSequenceNumber).filter(Objects::nonNull).max(Integer::compareTo);
        return maxOpt.orElse(null);
    }
}
