[{"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "BUDGET_MANAGEMENT", "name": "预算管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareBudget\",\"label\":\"编制项目预算\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_DECLARE", "name": "项目申报", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startDeclare\",\"label\":\"发起申报\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeclare\",\"label\":\"查看申报\"}]", "nodeType": "NORMAL_NODE", "ordinate": 290, "abscissa": 55}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "isAttachment": false, "nodeKey": "BUDGET_MANAGEMENT", "name": "预算管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareBudget\",\"label\":\"编制项目预算\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "RISK_MANAGEMENT", "name": "风险管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceRisk\",\"label\":\"维护项目风险\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CHANGE_MANAGEMENT", "name": "变更管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startChange\",\"label\":\"发起项目变更\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CLOSE", "name": "已关闭", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 370, "abscissa": 1050}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PURCHASING_MANAGEMENT", "name": "采购管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addPurchasingOrder\",\"label\":\"添加采购订单\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CREATE", "name": "已创建", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 180, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CONTRACT_SIGNING", "name": "合同签订", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startContractSigning\",\"label\":\"发起合同签订\"}]", "nodeType": "NORMAL_NODE", "ordinate": 460, "abscissa": 55}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MAN_HOUR_MANAGEMENT", "name": "工时管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"workEstimate\",\"label\":\"项目工时预估\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"workFill\",\"label\":\"工时填报\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_APPROVAL", "name": "项目立项", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startApproval\",\"label\":\"发起立项\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewApproval\",\"label\":\"查看立项\"}]", "nodeType": "NORMAL_NODE", "ordinate": 510, "abscissa": 55}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CONTRACT_MANAGEMENT", "name": "采购合同", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addContract\",\"label\":\"添加项目合同\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "START", "name": "开始", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "START_END_NODE", "ordinate": 50, "abscissa": 100}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PENDING", "name": "待立项", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 360, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_ACCEPTANCE", "name": "项目验收", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startAcceptance\",\"label\":\"发起项目验收\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewAcceptance\",\"label\":\"查看验收\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 1030}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "RISK_MANAGEMENT", "name": "风险管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceRisk\",\"label\":\"维护项目风险\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "DELIVER_MANAGEMENT", "name": "交付物管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeliver\",\"label\":\"查看项目交付物\"}]", "nodeType": "NORMAL_NODE", "ordinate": 430, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_EVALUATION", "name": "项目评价", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startEvaluation\",\"label\":\"发起项目评价\"}]", "nodeType": "NORMAL_NODE", "ordinate": 270, "abscissa": 1030}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CHANGE_MANAGEMENT", "name": "变更管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startChange\",\"label\":\"发起项目变更\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "APPROVED", "name": "已立项", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 620, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MEMBER_MANAGEMENT", "name": "成员管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceMember\",\"label\":\"维护项目成员\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "ACCEPTED", "name": "已验收", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 170, "abscissa": 1050}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_DOCUMENT", "name": "项目文档", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageDocument\",\"label\":\"管理项目文档\"}]", "nodeType": "NORMAL_NODE", "ordinate": 520, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PLAN_MANAGEMENT", "name": "计划管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行121212", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startScheme\",\"label\":\"项目计划编制\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MAN_HOUR_MANAGEMENT", "name": "工时管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"workEstimate\",\"label\":\"项目工时预估\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"workFill\",\"label\":\"工时填报\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "isAttachment": true, "nodeKey": "CONTRACT_MANAGEMENT", "name": "采购合同", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addContract\",\"label\":\"添加项目合同\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CREATE", "name": "已创建", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 160, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PENDING", "name": "待立项", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 400, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "REVENUE_MANAGEMENT", "name": "营收管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageRevenue\",\"label\":\"管理项目营收\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "ACCEPTED", "name": "已验收", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 170, "abscissa": 1050}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CHANGE_MANAGEMENT", "name": "变更管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startChange\",\"label\":\"发起项目变更\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PLAN_MANAGEMENT", "name": "计划管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行121212", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startScheme\",\"label\":\"项目计划编制\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PURCHASING_MANAGEMENT", "name": "采购管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addPurchasingOrder\",\"label\":\"添加采购订单\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "COST_MANAGEMENT", "name": "成本管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageCost\",\"label\":\"管理项目成本\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MATERIAL_MANAGEMENT", "name": "物资管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareMaterialPlan\",\"label\":\"编制物资计划\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"materialStorage\",\"label\":\"添加物资入库\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "ACCEPTED", "name": "已验收", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 170, "abscissa": 1050}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "END", "name": "结束", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "START_END_NODE", "ordinate": 470, "abscissa": 1075}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "DELIVER_MANAGEMENT", "name": "交付物管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeliver\",\"label\":\"查看项目交付物\"}]", "nodeType": "NORMAL_NODE", "ordinate": 430, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "BUDGET_MANAGEMENT", "name": "预算管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareBudget\",\"label\":\"编制项目预算\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CLOSE", "name": "已关闭", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 370, "abscissa": 1050}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "isAttachment": true, "nodeKey": "PROJECT_ACCEPTANCE", "name": "项目验收", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[{\"id\":\"fopk1731551961775546368\",\"name\":\"thread_41101479_20210609000624_s_17392_o_w_720_h_480_73854.jpg\",\"dataId\":\"95q967e8b88e9ade4621bfc203cec81698d3\",\"fileSize\":3705,\"filePath\":\"2023-12-04/1512f290b71645079dadef1aea6d2312.jpg\",\"filePostfix\":\"jpg\"},{\"id\":\"fopk1731551961901375488\",\"name\":\"副本新建 XLS 工作表.xls\",\"dataId\":\"95q967e8b88e9ade4621bfc203cec81698d3\",\"fileSize\":20992,\"filePath\":\"2023-12-04/c2a3c6b07d8148778deaa4ac61e6db91.xls\",\"filePostfix\":\"xls\"}]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startAcceptance\",\"label\":\"发起项目验收\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewAcceptance\",\"label\":\"查看验收\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 1030}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MAN_HOUR_MANAGEMENT", "name": "工时管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"workEstimate\",\"label\":\"项目工时预估\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"workFill\",\"label\":\"工时填报\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "START", "name": "开始", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "START_END_NODE", "ordinate": 50, "abscissa": 100}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "APPROVED", "name": "已立项", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 620, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_APPROVAL", "name": "项目立项", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startApproval\",\"label\":\"发起立项\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewApproval\",\"label\":\"查看立项\"}]", "nodeType": "NORMAL_NODE", "ordinate": 510, "abscissa": 55}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_EVALUATION", "name": "项目评价", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startEvaluation\",\"label\":\"发起项目评价\"}]", "nodeType": "NORMAL_NODE", "ordinate": 270, "abscissa": 1030}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MEMBER_MANAGEMENT", "name": "成员管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceMember\",\"label\":\"维护项目成员\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "REVENUE_MANAGEMENT", "name": "营收管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageRevenue\",\"label\":\"管理项目营收\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_DOCUMENT", "name": "项目文档", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageDocument\",\"label\":\"管理项目文档\"}]", "nodeType": "NORMAL_NODE", "ordinate": 520, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "isAttachment": true, "nodeKey": "CONTRACT_SIGNING", "name": "合同签订", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行多少度s", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startContractSigning\",\"label\":\"发起合同签订\"}]", "nodeType": "NORMAL_NODE", "ordinate": 290, "abscissa": 55}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROBLEM_MANAGEMENT", "name": "问题管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceProblem\",\"label\":\"维护项目问题\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PENDING", "name": "待立项", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 400, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "RISK_MANAGEMENT", "name": "风险管理", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceRisk\",\"label\":\"维护项目风险\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CONTRACT_MANAGEMENT", "name": "采购合同", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addContract\",\"label\":\"添加项目合同\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 790}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CREATE", "name": "已创建", "projectType": "sell", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 180, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "isAttachment": false, "nodeKey": "COST_MANAGEMENT", "name": "成本管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[{\"id\":\"fopk1746725703606493184\",\"name\":\"科研类项目10002项目计划\",\"dataId\":\"9b61d4ddd1e04a829d5bc42937ea0ac4\",\"fileSize\":4174,\"filePath\":\"2024-01-15/b61755a5b8ea4a24a790be7d27f9e87a.xlsx\",\"filePostfix\":\"xlsx\"}]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageCost\",\"label\":\"管理项目成本\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "CLOSE", "name": "已关闭", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 370, "abscissa": 1050}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MATERIAL_MANAGEMENT", "name": "物资管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareMaterialPlan\",\"label\":\"编制物资计划\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"materialStorage\",\"label\":\"添加物资入库\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_DOCUMENT", "name": "项目文档", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageDocument\",\"label\":\"管理项目文档\"}]", "nodeType": "NORMAL_NODE", "ordinate": 520, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_APPROVAL", "name": "项目立项", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startApproval\",\"label\":\"发起立项\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewApproval\",\"label\":\"查看立项\"}]", "nodeType": "NORMAL_NODE", "ordinate": 560, "abscissa": 55}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "APPROVED", "name": "已立项", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "STATUS_NODE", "ordinate": 660, "abscissa": 75}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "END", "name": "结束", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "START_END_NODE", "ordinate": 470, "abscissa": 1075}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "DELIVER_MANAGEMENT", "name": "交付物管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeliver\",\"label\":\"查看项目交付物\"}]", "nodeType": "NORMAL_NODE", "ordinate": 430, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PLAN_MANAGEMENT", "name": "计划管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行121212", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startScheme\",\"label\":\"项目计划编制\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "END", "name": "结束", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "START_END_NODE", "ordinate": 470, "abscissa": 1075}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROBLEM_MANAGEMENT", "name": "问题管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceProblem\",\"label\":\"维护项目问题\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "START", "name": "开始", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[]", "nodeType": "START_END_NODE", "ordinate": 50, "abscissa": 100}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROBLEM_MANAGEMENT", "name": "问题管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceProblem\",\"label\":\"维护项目问题\"}]", "nodeType": "NORMAL_NODE", "ordinate": 340, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MEMBER_MANAGEMENT", "name": "成员管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceMember\",\"label\":\"维护项目成员\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "MATERIAL_MANAGEMENT", "name": "物资管理", "projectType": "invest_server", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareMaterialPlan\",\"label\":\"编制物资计划\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"materialStorage\",\"label\":\"添加物资入库\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 310}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "COST_MANAGEMENT", "name": "成本管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageCost\",\"label\":\"管理项目成本\"}]", "nodeType": "NORMAL_NODE", "ordinate": 160, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_EVALUATION", "name": "项目评价", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startEvaluation\",\"label\":\"发起项目评价\"}]", "nodeType": "NORMAL_NODE", "ordinate": 270, "abscissa": 1030}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "isAttachment": false, "nodeKey": "PURCHASING_MANAGEMENT", "name": "采购管理", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addPurchasingOrder\",\"label\":\"添加采购订单\"}]", "nodeType": "NORMAL_NODE", "ordinate": 250, "abscissa": 550}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "nodeKey": "PROJECT_DECLARE", "name": "项目申报", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startDeclare\",\"label\":\"发起申报\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeclare\",\"label\":\"查看申报\"}]", "nodeType": "NORMAL_NODE", "ordinate": 260, "abscissa": 55}, {"className": "ProjectLifeCycleNode", "status": 1, "logicStatus": 1, "isAttachment": true, "nodeKey": "PROJECT_ACCEPTANCE", "name": "项目验收", "projectType": "scientific_research", "content": "当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行", "attachments": "[]", "actions": "[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startAcceptance\",\"label\":\"发起项目验收\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewAcceptance\",\"label\":\"查看验收\"}]", "nodeType": "NORMAL_NODE", "ordinate": 70, "abscissa": 1030}]