ALTER TABLE pmsx_project_approval_milestone
    ADD `level` int COMMENT '层级';
ALTER TABLE pmsx_project_approval_milestone
    ADD `scheme_type` VARCHAR(64) COMMENT '计划类型';
ALTER TABLE pmsx_project_approval_milestone
    ADD `scheme_activity` varchar(1024) COMMENT '计划活动项';
ALTER TABLE pmsx_project_approval_milestone
    ADD `duration_days` int COMMENT '工期';
ALTER TABLE pmsx_project_approval_milestone
    ADD `rsp_section_id` varchar(64) COMMENT '责任科室';
ALTER TABLE pmsx_project_approval_milestone
    ADD `participant_users` varchar(10240) COMMENT '参与人';
ALTER TABLE pmsx_project_approval_milestone
    ADD `scheme_desc` varchar(1024) COMMENT '计划描述';
ALTER TABLE pmsx_project_approval_milestone
    ADD `template_id` varchar(64) COMMENT '计划模板id';



CREATE TABLE `pms_project_approval_risk_plan`
(
    `id`               varchar(64) NOT NULL COMMENT '主键',
    `class_name`       varchar(64) COMMENT '创建人',
    `creator_id`       varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`      datetime    NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64) COMMENT '拥有者',
    `create_time`      datetime    NOT NULL COMMENT '创建时间',
    `modify_id`        varchar(64) NOT NULL COMMENT '修改人',
    `remark`           varchar(1024) COMMENT '备注',
    `platform_id`      varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`           varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`           int         NOT NULL COMMENT '状态',
    `logic_status`     int         NOT NULL COMMENT '逻辑删除字段',
    `project_id`       varchar(64) COMMENT '项目ID',
    `approve_id`       varchar(64) COMMENT '立项ID',
    `risk_id`          varchar(64) COMMENT '风险ID',
    `risk_probability` varchar(64) COMMENT '风险发生概率',
    `risk_influence`   varchar(64) COMMENT '影响程度',
    `risk_type`        varchar(64) COMMENT '风险类型',
    `solutions`        text(0)   COMMENT '应对措施',
    `document_id`      varchar(64) COMMENT '文档ID',
    `name`             varchar(512) COMMENT '名称',
    `number`           varchar(64) COMMENT '编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='项目立项风险策划';

CREATE TABLE `pms_risk_to_review_from`
(
    `id`           varchar(64)  NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)  NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`       int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `to_id`        varchar(64)  NOT NULL COMMENT '副Id',
    `from_id`      varchar(64)  NOT NULL COMMENT '主id',
    `to_class`     varchar(128) NOT NULL COMMENT '副类名',
    `from_class`   varchar(128) NOT NULL COMMENT '主类名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险与评审单关系';


CREATE TABLE `pms_plan_to_quality_item`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64) NOT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64) NOT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `to_id`        varchar(64) NOT NULL COMMENT '副Id',
    `from_id`      varchar(64) NOT NULL COMMENT '主id',
    `to_class`     varchar(64) NOT NULL COMMENT '副类名',
    `from_class`   varchar(64) NOT NULL COMMENT '主类名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划与质量管控关系';



CREATE TABLE `pms_project_contribute`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`        datetime    NOT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64) NOT NULL COMMENT '修改人',
    `remark`             varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`             int(11) NOT NULL COMMENT '状态',
    `logic_status`       int(11) NOT NULL COMMENT '逻辑删除字段',
    `type`               varchar(20) NOT NULL COMMENT '贡献类型',
    `award_type`         varchar(128)  DEFAULT NULL COMMENT '奖项类型',
    `name`               varchar(128)  DEFAULT NULL COMMENT '名称',
    `major_user`         varchar(128)  DEFAULT NULL COMMENT '主要人员',
    `authorization_time` datetime      DEFAULT NULL COMMENT '授权时间',
    `project_id`         varchar(64) NOT NULL COMMENT '项目id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目贡献情况';

CREATE TABLE `pms_project_major_deed`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime    NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64) NOT NULL COMMENT '修改人',
    `remark`         varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int(11) NOT NULL COMMENT '状态',
    `logic_status`   int(11) NOT NULL COMMENT '逻辑删除字段',
    `begin_time`     datetime      DEFAULT NULL COMMENT '开始时间',
    `end_time`       datetime      DEFAULT NULL COMMENT '结束时间',
    `major_deed`     varchar(1024) DEFAULT NULL COMMENT '主要事迹',
    `project_id`     varchar(64) NOT NULL COMMENT '项目id',
    `begin_end_time` varchar(64)   DEFAULT NULL COMMENT '起止时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目主要事迹';

CREATE TABLE `pms_project_reward_punishment`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `type`         varchar(20) NOT NULL COMMENT '奖惩类型',
    `situation`    varchar(1024) DEFAULT NULL COMMENT '情况',
    `project_id`   varchar(64) NOT NULL COMMENT '项目id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目奖惩情况';


CREATE TABLE `pms_contract_milestone`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime    NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`               int(11) NOT NULL COMMENT '状态',
    `logic_status`         int(11) NOT NULL COMMENT '逻辑删除字段',
    `milestone_name`       varchar(64)    DEFAULT NULL COMMENT '里程碑名称',
    `milestone_type`       varchar(64)    DEFAULT NULL COMMENT '里程碑类型',
    `parent_id`            varchar(64)    DEFAULT NULL COMMENT '关联里程碑Id',
    `tech_rsp_user`        varchar(64)    DEFAULT NULL COMMENT '技术负责人',
    `bus_rsp_user`         varchar(64)    DEFAULT NULL COMMENT '商务负责人',
    `plan_accept_date`     datetime       DEFAULT NULL COMMENT '计划验收日期',
    `cost_bus_type`        varchar(64)    DEFAULT NULL COMMENT '成本业务分类',
    `milestone_amt`        decimal(10, 0) DEFAULT NULL COMMENT '里程碑金额',
    `undert_dept`          varchar(64)    DEFAULT NULL COMMENT '承接部门',
    `number`               varchar(64)    DEFAULT NULL COMMENT '合同编号',
    `rsp_user`             varchar(64)    DEFAULT NULL COMMENT '负责人',
    `actual_accept_date`   datetime       DEFAULT NULL COMMENT '实际验收日期',
    `actual_milestone_amt` decimal(10, 0) DEFAULT NULL COMMENT '实际验收金额',
    `total_accept_rate`    decimal(10, 2) DEFAULT NULL COMMENT '累计验收比例',
    `contract_id`          varchar(64) NOT NULL COMMENT '合同id',
    `contract_number`      varchar(64)    DEFAULT NULL COMMENT '合同编号',
    `tax_rate`             decimal(20, 2) unsigned zerofill DEFAULT NULL COMMENT '税率',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同里程碑';



CREATE TABLE `pms_contract_our_signed_subject`
(
    `id`                     varchar(64) NOT NULL COMMENT '主键',
    `class_name`             varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`            datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime    NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                 int(11) NOT NULL COMMENT '状态',
    `logic_status`           int(11) NOT NULL COMMENT '逻辑删除字段',
    `contract_number`        varchar(64) NOT NULL COMMENT '合同编号',
    `signed_main_name`       varchar(64) NOT NULL COMMENT '签约主体名称',
    `company_duty_paragraph` varchar(64)   DEFAULT NULL COMMENT '公司税号',
    `main_contact_person`    varchar(64)   DEFAULT NULL COMMENT '主要联系人',
    `main_contact_phone`     varchar(20)   DEFAULT NULL COMMENT '主要联系人电话',
    `tech_contact_person`    varchar(64)   DEFAULT NULL COMMENT '技术联系人',
    `tech_contact_phone`     varchar(20)   DEFAULT NULL COMMENT '技术联系人电话',
    `contract_email`         varchar(64)   DEFAULT NULL COMMENT '联系邮箱',
    `contact_address`        varchar(100)  DEFAULT NULL COMMENT '联系地址',
    `sort`                   int(11) DEFAULT NULL COMMENT '排序值',
    `tech_contact_dept`      varchar(64)   DEFAULT NULL COMMENT '技术联系部门',
    `contract_id`            varbinary(64) DEFAULT NULL COMMENT '合同id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='甲方签约主体';


CREATE TABLE `pms_contract_supplier_signed_subject`
(
    `id`                     varchar(64) NOT NULL COMMENT '主键',
    `class_name`             varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`            datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime    NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                 int(11) NOT NULL COMMENT '状态',
    `logic_status`           int(11) NOT NULL COMMENT '逻辑删除字段',
    `contract_number`        varchar(64) NOT NULL COMMENT '合同编号',
    `signed_main_name`       varchar(64) NOT NULL COMMENT '签约主体名称',
    `company_duty_paragraph` varchar(64)   DEFAULT NULL COMMENT '公司税号',
    `main_contact_person`    varchar(64)   DEFAULT NULL COMMENT '主要联系人',
    `main_contact_phone`     varchar(20)   DEFAULT NULL COMMENT '主要联系人电话',
    `contract_email`         varchar(100)  DEFAULT NULL COMMENT '邮箱',
    `contact_address`        varchar(100)  DEFAULT NULL COMMENT '联系地址',
    `tech_contact_person`    varchar(64)   DEFAULT NULL COMMENT '技术联系人',
    `tech_contact_phone`     varchar(64)   DEFAULT NULL COMMENT '技术联系人电话',
    `tech_contact_dept`      varchar(64)   DEFAULT NULL COMMENT '技术联系部门',
    `contract_id`            varbinary(64) DEFAULT NULL COMMENT '合同id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='乙方签约主体';


CREATE TABLE `pms_market_contract`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`        datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`             int(11) NOT NULL COMMENT '状态',
    `logic_status`       int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`             varchar(64)    DEFAULT NULL COMMENT '合同编号',
    `name`               varchar(64) NOT NULL COMMENT '合同名称',
    `contract_type`      varchar(64) NOT NULL COMMENT '合同类型',
    `quote_id`           varchar(64)    DEFAULT NULL COMMENT '定价id',
    `quote_number`       varchar(64)    DEFAULT NULL COMMENT '报价编号',
    `fream_contract`     varchar(64)    DEFAULT NULL COMMENT '框架合同编号',
    `tech_rsp_user`      varchar(64)    DEFAULT NULL COMMENT '技术负责人',
    `tech_rsp_dept`      varchar(64)    DEFAULT NULL COMMENT '承担部门',
    `contract_amt`       decimal(10, 0) DEFAULT NULL COMMENT '合同金额',
    `currency`           varchar(64)    DEFAULT NULL COMMENT '币种',
    `tak_effect_date`    datetime       DEFAULT NULL COMMENT '生效日期',
    `end_date`           datetime       DEFAULT NULL COMMENT '终止日期',
    `rel_trans_appr`     bit(1)         DEFAULT NULL COMMENT '关联交易审批',
    `trans_appr_id`      varchar(64)    DEFAULT NULL COMMENT '交易审批id',
    `trans_appr_number`  varchar(64)    DEFAULT NULL COMMENT '交易审批单号',
    `content`            varchar(1024)  DEFAULT NULL COMMENT '主要内容',
    `is_quality_period`  bit(1)         DEFAULT NULL COMMENT '是否有质保期',
    `quality_end_date`   datetime       DEFAULT NULL COMMENT '质保到期日',
    `is_quality_amt`     bit(1)         DEFAULT NULL COMMENT '是否有质保金',
    `quality_amt`        decimal(10, 0) DEFAULT NULL COMMENT '质保金额',
    `quality_level`      varchar(64)    DEFAULT NULL COMMENT '质保等级',
    `sign_time`          datetime       DEFAULT NULL COMMENT '签订时间',
    `begin_time`         datetime       DEFAULT NULL COMMENT '开始时间',
    `end_time`           datetime       DEFAULT NULL COMMENT '结束时间',
    `end_type`           varchar(64)    DEFAULT NULL COMMENT '完结类型',
    `sign_remark`        varchar(1024)  DEFAULT NULL COMMENT '签署备注',
    `commerce_rsp_user`  varchar(64)    DEFAULT NULL COMMENT '合同商务接口人',
    `close_date`         datetime       DEFAULT NULL COMMENT '关闭日期',
    `close_user_id`      varchar(64)    DEFAULT NULL COMMENT '关闭用户id',
    `is_purchase`        bit(1)         DEFAULT NULL COMMENT '是否需要采购',
    `frame_contract_id`  varchar(64)    DEFAULT NULL COMMENT '关联框架合同id',
    `frame_contract_amt` decimal(20, 2) DEFAULT NULL COMMENT '框架合同金额',
    `close_type`         varchar(10)    DEFAULT NULL COMMENT '关闭类型(0:正常关闭;1:合同未签署)',
    `requirement_id`     varchar(64)    DEFAULT NULL COMMENT '需求Id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='市场合同';

CREATE TABLE `pms_market_contract_milestone_acceptance`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`        datetime    NOT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64) NOT NULL COMMENT '修改人',
    `remark`             varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`             int(11) NOT NULL COMMENT '状态',
    `logic_status`       int(11) NOT NULL COMMENT '逻辑删除字段',
    `milestone_id`       varchar(64) NOT NULL COMMENT '里程碑id',
    `acceptance_user_id` varchar(64)    DEFAULT NULL COMMENT '验收人',
    `actual_accept_date` datetime       DEFAULT NULL COMMENT '实际验收日期',
    `acceptance_ratio`   decimal(10, 0) DEFAULT NULL COMMENT '本次验收比例',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='市场合同里程碑验收信息';

CREATE TABLE `pms_market_contract_milestone_exception`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime    NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64) NOT NULL COMMENT '修改人',
    `remark`         varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int(11) NOT NULL COMMENT '状态',
    `logic_status`   int(11) NOT NULL COMMENT '逻辑删除字段',
    `milestone_id`   varchar(64) NOT NULL COMMENT '里程碑id',
    `exception_desc` varchar(1024) DEFAULT NULL COMMENT '异常说明',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='市场合同里程碑异常信息';

CREATE TABLE `pms_market_contract_milestone_reschedule`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`          datetime    NOT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime    NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64) NOT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`               int(11) NOT NULL COMMENT '状态',
    `logic_status`         int(11) NOT NULL COMMENT '逻辑删除字段',
    `change_reason`        varchar(1024) DEFAULT NULL COMMENT '变更原因',
    `milestone_id`         varchar(64)   DEFAULT NULL COMMENT '里程碑id',
    `old_plan_accept_date` datetime      DEFAULT NULL COMMENT '原预计验收日期',
    `new_plan_accept_date` datetime      DEFAULT NULL COMMENT '新预计验收日期',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='市场合同里程碑改期信息';


-- ----------------------------
-- Table structure for pms_market_contract_sign
-- ----------------------------
DROP TABLE IF EXISTS `pms_market_contract_sign`;
CREATE TABLE `pms_market_contract_sign`
(
    `id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `class_name`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
    `modify_time`      datetime                                               NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`      datetime                                               NOT NULL COMMENT '创建时间',
    `modify_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
    `remark`           varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
    `org_id`           varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
    `status`           int(11) NOT NULL COMMENT '状态',
    `logic_status`     int(11) NOT NULL COMMENT '逻辑删除字段',
    `contract_id`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同id',
    `sign_result`      bit(1)                                                 NOT NULL COMMENT '签署结果',
    `sign_date`        datetime NULL DEFAULT NULL COMMENT '合同签署日期',
    `effect_date`      datetime NULL DEFAULT NULL COMMENT '合同生效日期',
    `complete_date`    datetime NULL DEFAULT NULL COMMENT '合同完结日期',
    `complete_type`    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同完结类型',
    `end_sign_reason`  varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '终止签署原因',
    `cust_contract_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '市场合同签署信息' ROW_FORMAT = Dynamic;



CREATE TABLE `pms_related_transaction_form`
(
    `id`           varchar(64)   NOT NULL COMMENT '主键',
    `class_name`   varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)   NOT NULL COMMENT '创建人',
    `modify_time`  datetime      NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime      NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) NOT NULL COMMENT '备注',
    `platform_id`  varchar(64)   NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64)   NOT NULL COMMENT '业务组织Id',
    `status`       int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `work_title`   varchar(64) DEFAULT NULL COMMENT '工作主题',
    `start_user`   varchar(64)   NOT NULL COMMENT '发起人',
    `start_date`   datetime      NOT NULL COMMENT '发起时间',
    `end_date`     datetime      NOT NULL COMMENT '结束日期',
    `form_status`  varchar(10)   NOT NULL COMMENT '表单状态',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关联交易表单';



DROP TABLE IF EXISTS `pms_req_detail`;
CREATE TABLE `pms_req_detail`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `req_id`       varchar(64)   DEFAULT NULL COMMENT '需求ID',
    `context`      text COMMENT '富文本内容',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='富文本详情';

DROP TABLE IF EXISTS `pms_requirement_mangement`;
CREATE TABLE `pms_requirement_mangement`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`          datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`               int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`         int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `requirement_number`   varchar(255)  DEFAULT NULL COMMENT '需求编号',
    `requirement_name`     varchar(64)   DEFAULT NULL COMMENT '需求标题',
    `res_source`           varchar(64)   DEFAULT NULL COMMENT '需求来源',
    `bid_opening_tm`       datetime      DEFAULT NULL COMMENT '开标时间',
    `sign_start_time`      datetime      DEFAULT NULL COMMENT '报名开始日期',
    `sign_end_time`        datetime      DEFAULT NULL COMMENT '报名结束日期',
    `sign_deadln_time`     datetime      DEFAULT NULL COMMENT '报价截止时间',
    `cust_person`          varchar(64)   DEFAULT NULL COMMENT '客户',
    `cust_scope`           varchar(64)   DEFAULT NULL COMMENT '客户范围',
    `cust_con_person`      varchar(64)   DEFAULT NULL COMMENT '客户主要联系人',
    `cust_contact_ph`      varchar(64)   DEFAULT NULL COMMENT '客户主要联系人电话',
    `cust_bs_person`       varchar(255)  DEFAULT NULL COMMENT '客户商务接口人',
    `cust_tec_person`      varchar(255)  DEFAULT NULL COMMENT '客户技术接口人',
    `business_person`      varchar(64)   DEFAULT NULL COMMENT '商务接口人',
    `business_person_name` varchar(64)   DEFAULT NULL,
    `tech_res_name`        varchar(64)   DEFAULT NULL,
    `tech_res`             varchar(64)   DEFAULT NULL COMMENT '技术接口人(技术负责人)',
    `req_ownership`        varchar(64)   DEFAULT NULL COMMENT '需求归属中心',
    `cooperate_person`     varchar(255)  DEFAULT NULL COMMENT '配合部门接口人',
    `cooperate_dpt`        varchar(255)  DEFAULT NULL COMMENT '配合部门',
    `project_status`       varchar(64)   DEFAULT NULL COMMENT '需求状态',
    `response_status`      varchar(64)   DEFAULT NULL COMMENT '响应状态',
    `confirm_remark`       varchar(512)  DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='需求管理';


DROP TABLE IF EXISTS `pms_customer_contact`;
CREATE TABLE `pms_customer_contact`
(
    `p_remark`      varchar(256)  DEFAULT NULL COMMENT '备注',
    `mail`          varchar(256)  DEFAULT NULL COMMENT '邮箱',
    `phone_number`  varchar(256)  DEFAULT NULL COMMENT '手机',
    `job_name`      varchar(256)  DEFAULT NULL COMMENT '职位',
    `is_main`       varchar(256)  DEFAULT NULL COMMENT '主要联系人',
    `sex`           varchar(256)  DEFAULT NULL COMMENT '性别',
    `role_name`     varchar(256)  DEFAULT NULL COMMENT '角色',
    `name`          varchar(256)  DEFAULT NULL COMMENT '姓名',
    `department`    varchar(256)  DEFAULT NULL COMMENT '部门',
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`   datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`        int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`  int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`        varchar(64)   DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64)   DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='客户管理详情';



DROP TABLE IF EXISTS `pms_customer_info`;
CREATE TABLE `pms_customer_info`
(
    `comnumber`             varchar(256)  DEFAULT NULL COMMENT '冗余字段',
    `bus_register_code`     varchar(256)  DEFAULT NULL COMMENT '工商注册号',
    `tax_id_code`           varchar(256)  DEFAULT NULL COMMENT '纳税人识别号',
    `cus_full_name`         varchar(256)  DEFAULT NULL COMMENT '企业全称',
    `cus_level`             varchar(256)  DEFAULT NULL COMMENT '客户级别',
    `country`               varchar(256)  DEFAULT NULL COMMENT '国家',
    `organizatioin_code`    varchar(256)  DEFAULT NULL COMMENT '组织机构代码',
    `biz_period`            varchar(256)  DEFAULT NULL COMMENT '营业期限',
    `registered_capital`    varchar(256)  DEFAULT NULL COMMENT '注册资本',
    `uniform_credit_code`   varchar(256)  DEFAULT NULL COMMENT '统一社会信用代码',
    `cus_category`          varchar(256)  DEFAULT NULL COMMENT '企业类型',
    `registration_time`     varchar(256)  DEFAULT NULL COMMENT '成立日期',
    `business_scope`        varchar(256)  DEFAULT NULL COMMENT '经营范围',
    `comtaxnumber`          varchar(256)  DEFAULT NULL COMMENT '税务登记证号',
    `registered_address`    varchar(256)  DEFAULT NULL COMMENT '注册地址',
    `legal_repr`            varchar(256)  DEFAULT NULL COMMENT '法定代表人',
    `cus_ceate_time`        varchar(256)  DEFAULT NULL COMMENT '创建日期',
    `public_account_info`   varchar(256)  DEFAULT NULL COMMENT '公众号信息',
    `cus_num_count`         varchar(256)  DEFAULT NULL COMMENT '员工人数',
    `cus_address`           varchar(256)  DEFAULT NULL COMMENT '客户联系地址',
    `ywsrlx`                varchar(256)  DEFAULT NULL COMMENT '业务收入类型',
    `industry`              varchar(256)  DEFAULT NULL COMMENT '所属行业',
    `bus_scope`             varchar(256)  DEFAULT NULL COMMENT '客户范围',
    `group_in_out`          varchar(256)  DEFAULT NULL COMMENT '客户关系(集团内外)',
    `cus_remark`            tinytext COMMENT '备注',
    `cus_status`            varchar(256)  DEFAULT NULL COMMENT '客户状态',
    `customerdepartmentent` varchar(256)  DEFAULT NULL COMMENT '客户部门',
    `group_info`            varchar(256)  DEFAULT NULL COMMENT '所属集团',
    `cus_number`            varchar(256)  DEFAULT NULL COMMENT '客户编码',
    `cus_name`              varchar(256)  DEFAULT NULL COMMENT '客户名称',
    `id`                    varchar(64) NOT NULL COMMENT '主键',
    `class_name`            varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`            varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`           datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`           datetime    NOT NULL COMMENT '创建时间',
    `modify_id`             varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`                int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`          int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`                varchar(64)   DEFAULT NULL COMMENT '编码',
    `zzxx`                  varchar(256)  DEFAULT NULL COMMENT '资质信息',
    `comtpye`               varchar(256)  DEFAULT NULL COMMENT '公司类型',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='客户管理';



DROP TABLE IF EXISTS `pmsx_quotation_management`;
CREATE TABLE `pmsx_quotation_management`
(
    `id`                    varchar(64) NOT NULL COMMENT '主键',
    `class_name`            varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`            varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`           datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`           datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`             varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`                varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`                varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`                int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`          int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `requirement_number`    varchar(64)    DEFAULT NULL COMMENT '项目编号-需求编号',
    `quotation_id`          varchar(64)    DEFAULT NULL COMMENT '报价单ID',
    `busi_goal`             varchar(128)   DEFAULT NULL COMMENT '业务目标',
    `busi_goal_cont`        varchar(512)   DEFAULT NULL COMMENT '业务目标内容',
    `busi_info`             varchar(512)   DEFAULT NULL COMMENT '业务信息',
    `busi_info_cont`        varchar(512)   DEFAULT NULL COMMENT '业务信息内容',
    `cost_est_res`          varchar(128)   DEFAULT NULL COMMENT '成本估算（资源）',
    `cost_est_res_cont`     varchar(255)   DEFAULT NULL COMMENT '成本估算（资源）内容',
    `cost_est_rr_res`       text COMMENT '成本估算（人力、资源占用）',
    `cost_est_hr_res_cont`  varchar(255)   DEFAULT NULL COMMENT '成本估算（人力、资源占用）内容',
    `rev_anal`              varchar(128)   DEFAULT NULL COMMENT '收益分析',
    `rev_anal_cont`         varchar(255)   DEFAULT NULL COMMENT '收益分析内容',
    `other_info`            varchar(128)   DEFAULT NULL COMMENT '其他信息',
    `other_info_cont`       varchar(256)   DEFAULT NULL COMMENT '其他信息内容',
    `quote_content`         text COMMENT '报价内容',
    `quote_plan_detail`     text COMMENT '报价方案详情',
    `quote_amt`             decimal(10, 0) DEFAULT NULL COMMENT '报价金额',
    `currency`              varchar(64)    DEFAULT NULL COMMENT '报出币种',
    `floor_price`           decimal(10, 0) DEFAULT NULL COMMENT '底线价格',
    `issue_time`            datetime       DEFAULT NULL COMMENT '报价发出时间',
    `issuer`                varchar(64)    DEFAULT NULL COMMENT '发出报价用户',
    `result`                varchar(64)    DEFAULT NULL COMMENT '报价结果',
    `result_note`           varchar(512)   DEFAULT NULL COMMENT '报价结果备注',
    `quotation_status`      varchar(64)    DEFAULT NULL COMMENT '报价状态',
    `requirement_id`        varchar(64)    DEFAULT NULL COMMENT '需求ID',
    `fieldwork`             varchar(8)     DEFAULT NULL COMMENT '是否涉及现场工作',
    `incl_financing_trade`  varchar(8)     DEFAULT NULL COMMENT '是否涉及融资贸易业务',
    `quote_accept_pen`      varchar(64)    DEFAULT NULL COMMENT '报价接收人',
    `quote_accept_com`      varchar(64)    DEFAULT NULL COMMENT '报价接收方',
    `issue_way`             varchar(64)    DEFAULT NULL COMMENT '报价发出途径',
    `quotation_name`        varchar(64)    DEFAULT NULL,
    `quote_remark`          varchar(512)   DEFAULT NULL COMMENT '报价备注',
    `obsolete_reason`       varchar(512)   DEFAULT NULL,
    `re_quote_reason`       varchar(512)   DEFAULT NULL,
    `quote_execu_condition` varchar(64)    DEFAULT NULL,
    `fin_trade_bus`         varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报价管理';

-- ----------------------------
-- Table structure for pmsx_project_initiation
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_project_initiation`;
CREATE TABLE `pmsx_project_initiation`
(
    `id`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `modify_time`           datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`           datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modify_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台ID',
    `org_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务组织Id',
    `status`                int(11) NULL DEFAULT NULL COMMENT '状态',
    `logic_status`          int(11) NULL DEFAULT 1 COMMENT '逻辑删除字段',
    `project_number`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项编号',
    `project_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项名称',
    `project_label`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项标签',
    `project_type`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目类型',
    `project_init_date`     datetime NULL DEFAULT NULL COMMENT '项目发起日期',
    `project_start_date`    datetime NULL DEFAULT NULL COMMENT '项目开始日期',
    `project_end_date`      datetime NULL DEFAULT NULL COMMENT '项目结束日期',
    `project_person`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目责任人',
    `project_assume_center` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '承担中心',
    `project_reson`         varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项理由',
    `contract_numbers`      text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '合同编号拼接',
    `clue_numbers`          text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '线索编号拼接',
    `project_status`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项状态',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目立项' ROW_FORMAT = Dynamic;


CREATE TABLE `pmsx_project_order`
(
    `id`                    varchar(64) NOT NULL COMMENT '主键',
    `class_name`            varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`            varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`           datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`           datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`             varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`                varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`                varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`                int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`          int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `order_number`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `order_person`          varchar(32)    DEFAULT NULL COMMENT '下单人',
    `order_business`        varchar(255)   DEFAULT NULL COMMENT '下单企业',
    `order_tel`             varchar(11)    DEFAULT NULL COMMENT '下单人电话',
    `contract_number`       varchar(64)    DEFAULT NULL COMMENT '框架合同编号',
    `contract_name`         varchar(255)   DEFAULT NULL COMMENT '框架合同名称',
    `order_surcharge`       decimal(10, 2) DEFAULT NULL COMMENT '附加费',
    `contract_numbers`      varchar(255)   DEFAULT NULL COMMENT '合同编号拼接',
    `business_person_id`    varchar(64)    DEFAULT NULL COMMENT '商务接口人id',
    `business_person_name`  varchar(64)    DEFAULT NULL COMMENT '商务接口人名称',
    `technical_person_id`   varchar(64)    DEFAULT NULL COMMENT '技术接口人id',
    `technical_person_name` varchar(64)    DEFAULT NULL COMMENT '技术接口人名称',
    `bear_org_id`           varchar(64)    DEFAULT NULL COMMENT '承接部门id',
    `bear_org_name`         varchar(64)    DEFAULT NULL COMMENT '承接部门名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商城订单';

CREATE TABLE `pmsx_project_receive`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64) COMMENT '创建人',
    `creator_id`      varchar(64) COMMENT '创建人',
    `modify_time`     datetime COMMENT '修改时间',
    `owner_id`        varchar(64) COMMENT '拥有者',
    `create_time`     datetime COMMENT '创建时间',
    `modify_id`       varchar(64) COMMENT '修改人',
    `remark`          varchar(1024) COMMENT '备注',
    `platform_id`     varchar(64) COMMENT '平台ID',
    `org_id`          varchar(64) COMMENT '业务组织Id',
    `status`          int COMMENT '状态',
    `logic_status`    int COMMENT '逻辑删除字段',
    `receive_person`  varchar(255) COMMENT '收货人',
    `receive_tel`     varchar(11) COMMENT '收货人电话',
    `receive_address` varchar(255) COMMENT '收货人地址',
    `order_number`    varchar(64) COMMENT '订单编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='收货信息';

CREATE TABLE `pmsx_project_invoice`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64) COMMENT '创建人',
    `creator_id`           varchar(64) COMMENT '创建人',
    `modify_time`          datetime COMMENT '修改时间',
    `owner_id`             varchar(64) COMMENT '拥有者',
    `create_time`          datetime COMMENT '创建时间',
    `modify_id`            varchar(64) COMMENT '修改人',
    `remark`               varchar(1024) COMMENT '备注',
    `platform_id`          varchar(64) COMMENT '平台ID',
    `org_id`               varchar(64) COMMENT '业务组织Id',
    `status`               int COMMENT '状态',
    `logic_status`         int COMMENT '逻辑删除字段',
    `invoice_type`         varchar(255) COMMENT '发票类型',
    `invoice_address`      varchar(255) COMMENT '地址',
    `invoice_tel`          varchar(11) COMMENT '电话',
    `invoice_head`         varchar(255) COMMENT '发票抬头',
    `invoice_bank`         varchar(255) COMMENT '开户行',
    `invoice_account`      varchar(64) COMMENT '账号',
    `invoice_A_identifier` varchar(64) COMMENT '纳税人识别号',
    `order_number`         varchar(64) COMMENT '订单编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='发票信息';

CREATE TABLE `pmsx_project_flow`
(
    `id`                  varchar(64) NOT NULL COMMENT '主键',
    `class_name`          varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`         datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`              varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`              varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`              int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`        int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `flow_pay_person`     varchar(64)   DEFAULT NULL COMMENT '支付申请人',
    `flow_receive_person` varchar(64)   DEFAULT NULL COMMENT '收货申请人',
    `order_number`        varchar(64)   DEFAULT NULL COMMENT '订单编号',
    `business_person`     varchar(64)   DEFAULT NULL COMMENT '商务接口人',
    `technical_person`    varchar(64)   DEFAULT NULL COMMENT '技术接口人',
    `bear_org`            varchar(255)  DEFAULT NULL COMMENT '承担部门',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流程信息';


CREATE TABLE `pmsx_project_order_other`
(
    `id`               varchar(64) NOT NULL COMMENT '主键',
    `class_name`       varchar(64) COMMENT '创建人',
    `creator_id`       varchar(64) COMMENT '创建人',
    `modify_time`      datetime COMMENT '修改时间',
    `owner_id`         varchar(64) COMMENT '拥有者',
    `create_time`      datetime COMMENT '创建时间',
    `modify_id`        varchar(64) COMMENT '修改人',
    `remark`           varchar(1024) COMMENT '备注',
    `platform_id`      varchar(64) COMMENT '平台ID',
    `org_id`           varchar(64) COMMENT '业务组织Id',
    `status`           int COMMENT '状态',
    `logic_status`     int COMMENT '逻辑删除字段',
    `order_number`     varchar(64) COMMENT '订单编号',
    `receive_date`     datetime COMMENT '要求到货时间',
    `receive_demand`   varchar(255) COMMENT '特殊送货要求',
    `buy_org_code`     varchar(64) COMMENT '采购组织编码',
    `buy_org_name`     varchar(255) COMMENT '采购组织名称',
    `shop`             varchar(255) COMMENT '工厂',
    `confirm_control`  varchar(255) COMMENT '确认控制',
    `settlementmethod` varchar(64) COMMENT '结算方式',
    `leave_word`       varchar(255) COMMENT '买方留言',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='其他信息';

CREATE TABLE `pmsx_project_inventory`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) COMMENT '创建人',
    `modify_time`    datetime COMMENT '修改时间',
    `owner_id`       varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime COMMENT '创建时间',
    `modify_id`      varchar(64) COMMENT '修改人',
    `remark`         varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`    varchar(64) COMMENT '平台ID',
    `org_id`         varchar(64) COMMENT '业务组织Id',
    `status`         int(11)  COMMENT '状态',
    `logic_status`   int(11)  COMMENT '逻辑删除字段',
    `order_number`   varchar(64) COMMENT '订单编号',
    `inventory_img`  varchar(32) COMMENT '商品图',
    `inventory_name` varchar(32) COMMENT '商品名',
    `item_name`      varchar(64) COMMENT '单品名称',
    `univalence`     decimal(10, 0) COMMENT '单价',
    `quantity`       decimal(10, 0) COMMENT '数量',
    `amount`         decimal(10, 2) COMMENT '金额',
    `notes`          varchar(255)  DEFAULT NULL COMMENT '采购备注',
    `prMessage`      varchar(255)  DEFAULT NULL COMMENT 'PR信息',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品清单';

