INSERT INTO `sys_code_rules`(`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11797808917833764864', 'LXWDMBWDFJ', NULL, '立项文档模板文档分解编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:53:42', '314j1000000000000000000', '2024-06-04 09:54:04', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, '886b534321994631b2d2b41ee024a7dc', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_rules`(`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11797807250786668544', 'LXWDMB', NULL, '立项文档模板编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:47:05', '314j1000000000000000000', '2024-06-04 09:47:20', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, 'f74d513d4f0443198569e7756981a1d5', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_rules`(`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11798545095486803968', 'CPCH', NULL, '项目立项产品策划编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-06-06 10:39:01', '314j1000000000000000000', '2024-06-06 10:39:14', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, 'd8f7c6b1b9d8468d924a7de4d347f5a1', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');


INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797807555347664896', 'WDMB', '0', '9hi11797807250786668544', '', 'fixedValue', '1', 'WDMB', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:48:18', '314j1000000000000000000', '2024-06-04 09:48:18', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797807681709461504', '固定符一', '0', '9hi11797807250786668544', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:48:48', '314j1000000000000000000', '2024-06-04 09:48:48', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797807859375984640', '年份', '0', '9hi11797807250786668544', '', 'DATE_YYYY', '1', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:49:30', '314j1000000000000000000', '2024-06-04 09:49:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797807955710758912', '固定符二', '0', '9hi11797807250786668544', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:49:53', '314j1000000000000000000', '2024-06-04 09:49:53', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797808179007115264', '流水号', '0', '9hi11797807250786668544', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 5, '', '0', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:50:46', '314j1000000000000000000', '2024-06-04 09:50:46', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797809224353501184', 'WDMK', '0', '9hi11797808917833764864', '', 'fixedValue', '1', 'WDMK', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:54:55', '314j1000000000000000000', '2024-06-04 09:54:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797809342888726528', '固定符一', '0', '9hi11797808917833764864', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:55:24', '314j1000000000000000000', '2024-06-04 09:55:24', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797809472807292928', '年份', '0', '9hi11797808917833764864', '', 'DATE_YYYY', '1', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:55:55', '314j1000000000000000000', '2024-06-04 09:55:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797809594148507648', '固定符二', '0', '9hi11797808917833764864', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:56:24', '314j1000000000000000000', '2024-06-04 09:56:24', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1797809683554291712', '流水号', '0', '9hi11797808917833764864', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 5, '', '0', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:56:45', '314j1000000000000000000', '2024-06-04 09:56:45', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1798545490686709760', 'ZB', '0', '9hi11798545095486803968', '', 'fixedValue', '1', 'ZB', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-06 10:40:35', '314j1000000000000000000', '2024-06-06 10:40:35', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1798545596957790208', '固定符一', '0', '9hi11798545095486803968', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-06 10:41:00', '314j1000000000000000000', '2024-06-06 10:41:00', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1798545760019746816', '年份', '0', '9hi11798545095486803968', '', 'DATE_YYYY', '1', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-06 10:41:39', '314j1000000000000000000', '2024-06-06 10:41:39', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1798545842618175488', '固定符二', '0', '9hi11798545095486803968', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-06 10:41:59', '314j1000000000000000000', '2024-06-06 10:41:59', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1798545979847413760', '流水号', '0', '9hi11798545095486803968', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '1', '', '5', 5, '', '0', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-06 10:42:32', '314j1000000000000000000', '2024-06-06 10:42:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);

INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1797810625037127680', 'number', '9hi11797808917833764864', 'DocumentDecomposition', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 10:00:29', '314j1000000000000000000', '2024-06-04 10:00:29', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1797808502836744192', 'number', '9hi11797807250786668544', 'DocumentModelLibrary', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 09:52:03', '314j1000000000000000000', '2024-06-04 09:52:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1798553896973701120', 'number', '9hi11798545095486803968', 'ProductPlan', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-06 11:13:59', '314j1000000000000000000', '2024-06-06 11:13:59', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');






INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11800122581580443648', 'WDFJ', NULL, '文档分解编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:07:23', '314j1000000000000000000', '2024-06-10 19:24:17', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, '408d731a9a5b4ac1824de7362d5238aa', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11800132245126836224', 'XTBZ', NULL, '协同编制编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:45:47', '314j1000000000000000000', '2024-06-10 19:48:22', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, '1289fc2150ee4610866f80a5ea16e122', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');



INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800126318969548800', '文档模块', '0', '9hi11800122581580443648', '', 'fixedValue', '', 'WDMK', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:22:14', '314j1000000000000000000', '2024-06-10 19:22:14', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800126395758866432', '间隔', '0', '9hi11800122581580443648', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:22:32', '314j1000000000000000000', '2024-06-10 19:22:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800126506857590784', '年度', '0', '9hi11800122581580443648', '', 'DATE_YYYY', '', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:22:59', '314j1000000000000000000', '2024-06-10 19:22:59', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800126604261912576', '间隔2', '0', '9hi11800122581580443648', '', 'fixedValue', '', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:23:22', '314j1000000000000000000', '2024-06-10 19:23:22', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800126777818017792', '流水器', '0', '9hi11800122581580443648', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 5, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:24:03', '314j1000000000000000000', '2024-06-10 19:24:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800132554939101184', '协同编制编码', '0', '9hi11800132245126836224', '', 'fixedValue', '', 'XTBZ', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:47:01', '314j1000000000000000000', '2024-06-10 19:47:01', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800132615546793984', '间隔', '0', '9hi11800132245126836224', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:47:15', '314j1000000000000000000', '2024-06-10 19:47:15', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800132724623863808', '时间', '0', '9hi11800132245126836224', '', 'DATE_YYYY', '', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:47:41', '314j1000000000000000000', '2024-06-10 19:47:41', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800132784325586944', '间隔2', '0', '9hi11800132245126836224', '', 'fixedValue', '', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 19:47:55', '314j1000000000000000000', '2024-06-10 19:47:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1800722328037003264', '流水器', '0', '9hi11800132245126836224', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 5, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-12 10:50:33', '314j1000000000000000000', '2024-06-12 10:50:33', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);


INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1800708039486103552', 'number', '9hi11800122581580443648', 'CollaborativeCompilationDocument', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-12 09:53:47', '314j1000000000000000000', '2024-06-12 09:53:47', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1800708149137793024', 'number', '9hi11800132245126836224', 'CollaborativeCompilationTask', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-06-12 09:54:13', '314j1000000000000000000', '2024-06-12 09:54:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');

