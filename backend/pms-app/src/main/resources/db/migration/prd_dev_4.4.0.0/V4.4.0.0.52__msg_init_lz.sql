INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1844551326864429056', '固定资产鉴定提醒', 'NODE_FIXED_NOTIFY', 'vub01844550259330174976', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1770010828494475264', 1, NULL, '314j1000000000000000000', '2024-10-11 09:31:21', '314j1000000000000000000', '2024-10-11 10:03:21', NULL, NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01844549878072135680', '固定资产鉴定日期提醒', 'PMS_ASSETS_NOTIYF', '产品编码为【$number$】的资产检定日期已不足两个月，请及时安排时间送检。', '1', 1, '', '314j1000000000000000000', '2024-10-11 09:25:36', '314j1000000000000000000', '2024-10-11 10:11:43', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01844550259330174976', '固定资产鉴定日期提醒标题', 'PMS_ASSETS_NOTIFY_TITLE', '固定资产鉴定提醒', '1', 1, '', '314j1000000000000000000', '2024-10-11 09:27:07', '314j1000000000000000000', '2024-10-11 09:27:14', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');


INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (127, 5, '固定资产鉴定提醒', '2024-10-10 17:30:10', '2024-10-11 10:19:54', 'orion', '', 'CRON', '0 30 8 * * ?', 'DO_NOTHING', 'FIRST', 'fixedAssetsNotifyJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-10-10 17:30:10', '', 0, 0, 0);