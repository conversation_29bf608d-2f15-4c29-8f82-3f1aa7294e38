INSERT INTO `msc_business_node_classification` (`id`, `name`, `parent_id`, `sort`, `status`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('u0rn1813482156561539072', '商城集采订单', NULL, 0, 1, '314j1000000000000000000', '2024-07-17 15:53:34', '314j1000000000000000000', '2024-07-17 15:53:34', NULL, NULL, '', 1);
INSERT INTO `msc_business_node_classification` (`id`, `name`, `parent_id`, `sort`, `status`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('u0rn1813515965948502016', '采购合同', NULL, 10, 1, '314j1000000000000000000', '2024-07-17 18:07:54', '314j1000000000000000000', '2024-07-17 18:07:54', NULL, NULL, '', 1);
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1827180852534718464', '集采订单收货消息提醒', 'NODE_RECEVING_NOTIFY', 'vub01826895983564959744', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1813482156561539072', 1, NULL, '314j1000000000000000000', '2024-08-24 11:07:17', '314j1000000000000000000', '2024-08-24 11:07:20', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1828315205063028736', '合同执行验收情况评价', 'NODE_CONTRACT_ACCEPTANCE', 'vub01828313550586261504', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1813515965948502016', 1, NULL, '314j1000000000000000000', '2024-08-27 14:14:48', '314j1000000000000000000', '2024-08-27 14:29:55', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1831239019040223232', '采购合同到期提醒', 'NODE_CONTRACT_DEADLINE', 'vub01831238287234838528', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1813515965948502016', 1, NULL, '314j1000000000000000000', '2024-09-04 15:53:00', '314j1000000000000000000', '2024-09-04 15:53:02', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01831238287234838528', '框架合同到期提醒通知-标题', 'PMS_CONTRACT_DEADLINE_TITLE', '框架合同到期提醒通知', '1', 1, '', '314j1000000000000000000', '2024-09-04 15:50:05', '314j1000000000000000000', '2024-09-04 15:51:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01828313550586261504', '合同执行验收情况评价-标题', 'PMS_CONTRACT_ACCEPTANCE_TITLE', '合同执行验收情况评价', '1', 1, '', '314j1000000000000000000', '2024-08-27 14:08:14', '314j1000000000000000000', '2024-08-27 14:08:25', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01826895983564959744', '商城订单收货提醒-标题', 'PMS_PURCHASE_GET_WOODS_TITLE', '商城订单收货提醒', '1', 1, '', '314j1000000000000000000', '2024-08-23 16:15:19', '314j1000000000000000000', '2024-08-23 16:15:25', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
