ALTER TABLE `pmsx_person_role_maintenance_log` add column `role_id` DEFAULT NULL VARCHAR(64) COMMENT '人员角色维护表id';
ALTER TABLE `pmsx_person_role_maintenance_log` add column `change_person_name` DEFAULT NULL VARCHAR(255) COMMENT '变更人姓名';
ALTER TABLE `pmsx_person_role_maintenance_log` add column `change_reason` DEFAULT NULL VARCHAR(255) COMMENT '变更原因';
ALTER TABLE `pmsx_person_role_maintenance_log` add column `expertise_center_title` DEFAULT NULL VARCHAR(255) COMMENT '专业中心名称';
ALTER TABLE `pmsx_person_role_maintenance_log` add column `expertise_station_title` DEFAULT NULL VARCHAR(255) COMMENT '专业所名称';
