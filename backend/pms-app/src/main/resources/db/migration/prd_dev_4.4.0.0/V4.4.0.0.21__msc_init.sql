INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1831239019040223232', '采购合同到期提醒', 'NODE_CONTRACT_DEADLINE', 'vub01831238287234838528', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1813515965948502016', 1, NULL, '314j1000000000000000000', '2024-09-04 15:53:00', '314j1000000000000000000', '2024-09-04 15:53:02', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1828315205063028736', '合同执行验收情况评价', 'NODE_CONTRACT_ACCEPTANCE', 'vub01828313550586261504', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1813515965948502016', 1, NULL, '314j1000000000000000000', '2024-08-27 14:14:48', '314j1000000000000000000', '2024-08-27 14:29:55', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1827180852534718464', '集采订单收货消息提醒', 'NODE_RECEVING_NOTIFY', 'vub01826895983564959744', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1813482156561539072', 1, NULL, '314j1000000000000000000', '2024-08-24 11:07:17', '314j1000000000000000000', '2024-08-24 11:07:20', NULL, NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01831238569079484416', '框架合同到期提醒通知', 'PMS_CONTRACT_DEADLINE', '您好！\n合同名称将于【$endTime$}】到期，如还有采购需求，请尽快启动新一轮框架合同采购。\n如无需采购/已发起采购，请邮件回复【$rspUser$】关闭邮件提醒！', '1', 1, '', '314j1000000000000000000', '2024-09-04 15:51:12', '314j1000000000000000000', '2024-09-04 15:51:14', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01831238287234838528', '框架合同到期提醒通知-标题', 'PMS_CONTRACT_DEADLINE_TITLE', '框架合同到期提醒通知', '1', 1, '', '314j1000000000000000000', '2024-09-04 15:50:05', '314j1000000000000000000', '2024-09-04 15:51:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01828314599439085568', '合同执行验收情况评价', 'PMS_CONTRACT_ACCEPTANCE', '您好！\n您负责的采购合同【$contractName$】+【$contractNumber$】已到合同约定的验收节点，请点击下方链接登录一体化平台，到【合同管理】模块搜索合同编号，填写其”是否一次验收合格“、“是否一次交付”！\n$link$', '1', 1, '', '314j1000000000000000000', '2024-08-27 14:12:24', '314j1000000000000000000', '2024-08-27 14:12:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01828313550586261504', '合同执行验收情况评价-标题', 'PMS_CONTRACT_ACCEPTANCE_TITLE', '合同执行验收情况评价', '1', 1, '', '314j1000000000000000000', '2024-08-27 14:08:14', '314j1000000000000000000', '2024-08-27 14:08:25', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01826896169536204800', '商城订单收货提醒-内容', 'PMS_PURCHASE_GET_WOODS', '您好！\n您采购的商城订单【$number$】已发货，为避免逾期支付产生的风险，请在收到本邮件的3个工作日内进行收货确认！', '1', 1, '', '314j1000000000000000000', '2024-08-23 16:16:04', '314j1000000000000000000', '2024-08-24 10:52:17', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01826895983564959744', '商城订单收货提醒-标题', 'PMS_PURCHASE_GET_WOODS_TITLE', '商城订单收货提醒', '1', 1, '', '314j1000000000000000000', '2024-08-23 16:15:19', '314j1000000000000000000', '2024-08-23 16:15:25', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
