ALTER TABLE `pmsx_fixed_assets`
    ADD COLUMN `product_code` varchar(256) DEFAULT NULL COMMENT '产品编码',
    ADD COLUMN `tool_status` varchar(32) DEFAULT NULL COMMENT '工具状态',
    ADD COLUMN `maintenance_cycle` int DEFAULT NULL COMMENT '检定维护周期';

ALTER TABLE `pmsx_material_manage`
    ADD COLUMN `product_code` varchar(256) DEFAULT NULL COMMENT '产品编码',
    ADD COLUMN `tool_status` varchar(32) DEFAULT NULL COMMENT '工具状态',
    ADD COLUMN `maintenance_cycle` int DEFAULT NULL COMMENT '检定维护周期';

ALTER TABLE `pmsx_scheme_to_material`
    ADD COLUMN `product_code` varchar(256) DEFAULT NULL COMMENT '产品编码',
    ADD COLUMN `tool_status` varchar(32) DEFAULT NULL COMMENT '工具状态',
    ADD COLUMN `maintenance_cycle` int DEFAULT NULL COMMENT '检定维护周期';

ALTER TABLE `pmsx_material_out_manage`
    ADD COLUMN `product_code` varchar(256) DEFAULT NULL COMMENT '产品编码',
    ADD COLUMN `tool_status` varchar(32) DEFAULT NULL COMMENT '工具状态',
    ADD COLUMN `maintenance_cycle` int DEFAULT NULL COMMENT '检定维护周期';


ALTER TABLE `pmsx_job_material`
    ADD COLUMN `product_code` varchar(256) DEFAULT NULL COMMENT '产品编码',
    ADD COLUMN `tool_status` varchar(32) DEFAULT NULL COMMENT '工具状态',
    ADD COLUMN `maintenance_cycle` int DEFAULT NULL COMMENT '检定维护周期';