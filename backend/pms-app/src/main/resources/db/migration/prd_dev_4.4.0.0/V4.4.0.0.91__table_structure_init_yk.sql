CREATE TABLE `pmsx_user_satisfaction_evaluation` (
                                                     `id` varchar(64) NOT NULL COMMENT '主键',
                                                     `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                     `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                     `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                     `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                                     `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                     `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                     `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                     `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                     `status` int(11) NOT NULL COMMENT '状态',
                                                     `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                     `org_code` varchar(64) NOT NULL COMMENT '中心编号',
                                                     `org_name` varchar(64) DEFAULT NULL COMMENT '中心名称',
                                                     `dept_code` varchar(64) NOT NULL COMMENT '部门编号',
                                                     `dept_name` varchar(64) DEFAULT NULL COMMENT '部门名称',
                                                     `user_code` varchar(64) NOT NULL COMMENT '工号',
                                                     `user_name` varchar(64) NOT NULL COMMENT '用户名称',
                                                     `supplier_no` varchar(64) DEFAULT NULL COMMENT '供应商编号',
                                                     `supplier_name` varchar(64) DEFAULT NULL COMMENT '供应商名称',
                                                     `contract_no` varchar(64) NOT NULL COMMENT '合同编号',
                                                     `contract_name` varchar(64) DEFAULT NULL COMMENT '合同名称',
                                                     `job_grade` varchar(64) DEFAULT NULL COMMENT '岗级',
                                                     `one_quarter` varchar(10) NOT NULL COMMENT '1季度评价',
                                                     `two_quarter` varchar(10) DEFAULT NULL COMMENT '2季度评分',
                                                     `three_quarter` varchar(10) DEFAULT NULL COMMENT '3季度评分',
                                                     `four_quarter` varchar(10) DEFAULT NULL COMMENT '年度评价',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员满意度评价';