
DROP TABLE IF EXISTS `pmsx_requirements_management_logs`;
CREATE TABLE `pmsx_requirements_management_logs` (
                                                     `id` varchar(64) NOT NULL COMMENT '主键',
                                                     `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                     `modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                     `class_name` varchar(64) DEFAULT NULL COMMENT '类名称',
                                                     `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                     `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
                                                     `remark` text DEFAULT NULL COMMENT '备注',
                                                     `platform_id` varchar(64) DEFAULT NULL COMMENT '平台ID',
                                                     `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织Id',
                                                     `status` int  DEFAULT 0 COMMENT '状态',
                                                     `logic_status` int DEFAULT 0 COMMENT '逻辑删除字段',
                                                     `reqiurements_id` varchar(64) DEFAULT NULL COMMENT '需求表单id',
                                                     `cust_tec_person` varchar(64) DEFAULT NULL COMMENT '操作人id',
                                                     `cust_tec_person_name` varchar(64) DEFAULT NULL COMMENT '操作人名称',
                                                     `feedback_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '反馈时间',
                                                     `req_ownership` varchar(64) DEFAULT NULL COMMENT '需求中心',
                                                     `cust_bs_person` varchar(64) DEFAULT NULL COMMENT '商务接口人id',
                                                     `cust_bs_person_name` varchar(64) DEFAULT NULL COMMENT '商务接口人名称',
                                                     `remark_type` int DEFAULT NULL DEFAULT 0 COMMENT '备注类型，0是操作记录,1是反馈',
                                                     `op_user` varchar(64) DEFAULT NULL COMMENT '操作人Id',
                                                     `op_user_name` varchar(64) DEFAULT NULL COMMENT '操作人名称',
                                                     `req_ownership_name` varchar(64) DEFAULT NULL COMMENT '需求中心名称',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='需求表单操作日志记录';
