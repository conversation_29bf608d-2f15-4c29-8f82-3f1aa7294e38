ALTER TABLE `pms_contract_milestone` add column `income_type` VARCHAR(255) COMMENT '收入确认类型';
ALTER TABLE `pms_contract_milestone` add column `is_provisional_estimate` INT COMMENT '是否暂估0：否 1：是';
ALTER TABLE `pms_contract_milestone` add column `except_acceptance_amt` DECIMAL(10,2) COMMENT '预计验收金额';
ALTER TABLE `pms_contract_milestone` add column `milestone_provisional_estimate_amt` DECIMAL(10,2) COMMENT '里程碑已暂估金额';
ALTER TABLE `pms_contract_milestone` add column `milestone_advance_amt` DECIMAL(10,2) COMMENT '里程碑已预收款开票金额（价税合计）';
ALTER TABLE `pms_contract_milestone` add column `contract_supplier_signed_main` VARCHAR(255) COMMENT '开票主体名称';
ALTER TABLE `pms_contract_milestone` add column `except_invoice_date` DATETIME COMMENT '预计开票日期';
ALTER TABLE `pms_contract_milestone` add column `currency` VARCHAR(255) COMMENT '币种';
ALTER TABLE `pms_contract_milestone` add column `amt_tax` VARCHAR(255) COMMENT '含税金额';
ALTER TABLE `pms_contract_milestone` add column `amt_no_tax` VARCHAR(255) COMMENT '不含税金额';
