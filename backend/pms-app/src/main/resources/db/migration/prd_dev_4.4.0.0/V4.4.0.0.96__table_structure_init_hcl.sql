create or replace
algorithm = UNDEFINED view `quotation_requirement_view` as
select
    `pqm`.`id` as `id`,
    `pqm`.`ywsrlx` as `ywsrlx`,
    `pqm`.`class_name` as `class_name`,
    `pqm`.`creator_id` as `creator_id`,
    `pqm`.`modify_time` as `modify_time`,
    `pqm`.`owner_id` as `owner_id`,
    `pqm`.`create_time` as `create_time`,
    `pqm`.`modify_id` as `modify_id`,
    `pqm`.`remark` as `remark`,
    `pqm`.`platform_id` as `platform_id`,
    `pqm`.`org_id` as `org_id`,
    `pqm`.`status` as `status`,
    `pqm`.`logic_status` as `logic_status`,
    `pqm`.`requirement_number` as `requirement_number`,
    `pqm`.`quotation_id` as `quotation_id`,
    `pqm`.`busi_goal` as `busi_goal`,
    `pqm`.`busi_goal_cont` as `busi_goal_cont`,
    `pqm`.`busi_info` as `busi_info`,
    `pqm`.`busi_info_cont` as `busi_info_cont`,
    `pqm`.`cost_est_res` as `cost_est_res`,
    `pqm`.`cost_est_res_cont` as `cost_est_res_cont`,
    `pqm`.`cost_est_rr_res` as `cost_est_rr_res`,
    `pqm`.`cost_est_hr_res_cont` as `cost_est_hr_res_cont`,
    `pqm`.`rev_anal` as `rev_anal`,
    `pqm`.`rev_anal_cont` as `rev_anal_cont`,
    `pqm`.`other_info` as `other_info`,
    `pqm`.`other_info_cont` as `other_info_cont`,
    `pqm`.`quote_content` as `quote_content`,
    `pqm`.`quote_plan_detail` as `quote_plan_detail`,
    `pqm`.`quote_amt` as `quote_amt`,
    `pqm`.`currency` as `currency`,
    `pqm`.`floor_price` as `floor_price`,
    `pqm`.`issue_time` as `issue_time`,
    `pqm`.`issuer` as `issuer`,
    `pqm`.`result` as `result`,
    `pqm`.`result_note` as `result_note`,
    `pqm`.`quotation_status` as `quotation_status`,
    `pqm`.`requirement_id` as `requirement_id`,
    `pqm`.`fieldwork` as `fieldwork`,
    `pqm`.`incl_financing_trade` as `incl_financing_trade`,
    `pqm`.`quote_accept_pen` as `quote_accept_pen`,
    `pqm`.`quote_accept_com` as `quote_accept_com`,
    `pqm`.`issue_way` as `issue_way`,
    `pqm`.`quotation_name` as `quotation_name`,
    `pqm`.`quote_remark` as `quote_remark`,
    `pqm`.`obsolete_reason` as `obsolete_reason`,
    `pqm`.`re_quote_reason` as `re_quote_reason`,
    `pqm`.`quote_execu_condition` as `quote_execu_condition`,
    `pqm`.`fin_trade_bus` as `fin_trade_bus`,
    `pqm`.`business_type` as `business_type`,
    `prm`.`req_ownership` as `req_ownership`,
    `prm`.`tech_res` as `tech_res`,
    `prm`.`business_person` as `business_person`,
    `pqm`.`re_quotation_id` as `re_quotation_id`,
    `pqm`.`office_leader` as `office_leader`,
    `pqm`.`send_out_user` as `send_out_user`,
    `pqm`.`send_out_time` as `send_out_time`,
    `pqm`.`priority` as `priority`

from
    (`pms_requirement_mangement` `prm`
left join `pmsx_quotation_management` `pqm` on
    ((`prm`.`id` = convert(`pqm`.`requirement_id`
        using utf8mb4))));

