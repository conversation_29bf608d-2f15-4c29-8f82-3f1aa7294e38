CREATE TABLE `pmsx_quotation_manage_cust_contact`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64) COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime    NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64) COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) NOT NULL COMMENT '修改人',
    `remark`          text(1024) COMMENT '备注',
    `platform_id`     varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`          int         NOT NULL COMMENT '状态',
    `logic_status`    int         NOT NULL COMMENT '逻辑删除字段',
    `quotation_id`    varchar(64) NOT NULL COMMENT '报价单id，pmsx_quotation_management id',
    `cust_contact_id` varchar(64) NOT NULL COMMENT '客户联系人id，pms_customer_contact id',
    `contact_name`    varchar(64) COMMENT '联系人名称',
    `contact_phone`   varchar(30) COMMENT '联系人手机号',
    `contact_type`    varchar(30) NOT NULL DEFAULT 'business' COMMENT '联系人类型；business.商务联系人；technology.技术负责人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='报价管理-客户-联系人';



INSERT INTO `dme_class` (`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`,
                         `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`,
                         `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`,
                         `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`)
VALUES ('gtjl1831970082330943488', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_quotation_manage_cust_contact',
        'QuotationManageCustContact', 'mv5g', NULL, 'pmsx', 'quotationmanagecustcontact', 'common', NULL, NULL, NULL,
        NULL, NULL, NULL, 1, '报价管理-客户-联系人', '314j1000000000000000000', '2024-09-06 16:17:59',
        '314j1000000000000000000', '2024-09-06 16:23:53', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, '314j1000000000000000000', 0);



INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052352', 'gtjl1831970082330943488', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL,
        1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:17:59', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052353', 'gtjl1831970082330943488', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:17:59', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052354', 'gtjl1831970082330943488', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:17:59', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052355', 'gtjl1831970082330943488', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59',
        '314j1000000000000000000', '2024-09-06 16:17:59', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052356', 'gtjl1831970082330943488', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:17:59', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052357', 'gtjl1831970082330943488', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59',
        '314j1000000000000000000', '2024-09-06 16:17:59', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052358', 'gtjl1831970082330943488', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:17:59', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052359', 'gtjl1831970082330943488', 'remark', 'Text', 1024, NULL, 'remark', NULL, NULL, NULL,
        NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:19:09', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052360', 'gtjl1831970082330943488', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59',
        '314j1000000000000000000', '2024-09-06 16:17:59', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052361', 'gtjl1831970082330943488', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:17:59', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052362', 'gtjl1831970082330943488', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59', '314j1000000000000000000',
        '2024-09-06 16:17:59', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970082398052363', 'gtjl1831970082330943488', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:17:59',
        '314j1000000000000000000', '2024-09-06 16:17:59', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831970599497015296', 'gtjl1831970082330943488', 'quotationId', 'Varchar', 64, NULL, 'quotationid', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:20:02',
        '314j1000000000000000000', '2024-09-06 16:20:02', '报价单id，pmsx_quotation_management id',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831971046500769792', 'gtjl1831970082330943488', 'custContactId', 'Varchar', 64, NULL, 'custcontactid',
        NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:21:49',
        '314j1000000000000000000', '2024-09-06 16:21:49', '客户联系人id，pms_customer_contact id',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831971114591100928', 'gtjl1831970082330943488', 'contactName', 'Varchar', 64, NULL, 'contactname', NULL,
        NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:22:05',
        '314j1000000000000000000', '2024-09-06 16:22:05', '联系人名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831971192143781888', 'gtjl1831970082330943488', 'contactPhone', 'Varchar', 30, NULL, 'contactphone', NULL,
        NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:22:23',
        '314j1000000000000000000', '2024-09-06 16:22:23', '联系人手机号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831971297454366720', 'gtjl1831970082330943488', 'contactType', 'Varchar', 30, NULL, 'contacttype', NULL,
        'business', NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:22:48',
        '314j1000000000000000000', '2024-09-06 16:22:48', '联系人类型；business.商务联系人；technology.技术负责人',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');



CREATE TABLE `pmsx_market_contract_cust_contact`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64) COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime    NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64) COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) NOT NULL COMMENT '修改人',
    `remark`          text(1024) COMMENT '备注',
    `platform_id`     varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`          int         NOT NULL COMMENT '状态',
    `logic_status`    int         NOT NULL COMMENT '逻辑删除字段',
    `contract_id`     varchar(64) NOT NULL COMMENT '市场合同id，pms_market_contract id',
    `cust_contact_id` varchar(64) NOT NULL COMMENT '客户联系人id，pms_customer_contact id',
    `contact_name`    varchar(64) COMMENT '联系人名称',
    `contact_phone`   varchar(30) COMMENT '联系人手机号',
    `contact_type`    varchar(30) NOT NULL DEFAULT 'business' COMMENT '联系人类型；business.商务联系人；technology.技术负责人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='市场合同-客户-联系人';



INSERT INTO `dme_class` (`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`,
                         `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`,
                         `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`,
                         `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`)
VALUES ('gtjl1831977076970397696', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_market_contract_cust_contact',
        'MarketContractCustContact', 'fizv', NULL, 'pmsx', 'marketcontractcustcontact', 'common', NULL, NULL, NULL,
        NULL, NULL, NULL, 1, '市场合同-客户-联系人', '314j1000000000000000000', '2024-09-06 16:45:46',
        '314j1000000000000000000', '2024-09-06 16:48:05', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, '314j1000000000000000000', 0);



INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226816', 'gtjl1831977076970397696', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL,
        1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:46', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226817', 'gtjl1831977076970397696', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:46', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226818', 'gtjl1831977076970397696', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:46', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226819', 'gtjl1831977076970397696', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46',
        '314j1000000000000000000', '2024-09-06 16:45:46', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226820', 'gtjl1831977076970397696', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:46', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226821', 'gtjl1831977076970397696', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46',
        '314j1000000000000000000', '2024-09-06 16:45:46', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226822', 'gtjl1831977076970397696', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:46', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226823', 'gtjl1831977076970397696', 'remark', 'Text', 1024, NULL, 'remark', NULL, NULL, NULL,
        NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:56', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226824', 'gtjl1831977076970397696', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46',
        '314j1000000000000000000', '2024-09-06 16:45:46', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226825', 'gtjl1831977076970397696', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:46', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226826', 'gtjl1831977076970397696', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46', '314j1000000000000000000',
        '2024-09-06 16:45:46', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977077096226827', 'gtjl1831977076970397696', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:45:46',
        '314j1000000000000000000', '2024-09-06 16:45:46', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977309460668416', 'gtjl1831977076970397696', 'contractId', 'Varchar', 64, NULL, 'contractid', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:46:42',
        '314j1000000000000000000', '2024-09-06 16:46:42', '市场合同id，pms_market_contract id',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977383070703616', 'gtjl1831977076970397696', 'custContactId', 'Varchar', 64, NULL, 'custcontactid',
        NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:46:59',
        '314j1000000000000000000', '2024-09-06 16:46:59', '客户联系人id，pms_customer_contact id',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977446060761088', 'gtjl1831977076970397696', 'contactName', 'Varchar', 64, NULL, 'contactname', NULL,
        NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:47:14',
        '314j1000000000000000000', '2024-09-06 16:47:14', '联系人名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977510334275584', 'gtjl1831977076970397696', 'contactPhone', 'Varchar', 30, NULL, 'contactphone', NULL,
        NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:47:30',
        '314j1000000000000000000', '2024-09-06 16:47:59', '联系人手机号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831977607482744832', 'gtjl1831977076970397696', 'contactType', 'Varchar', 30, NULL, 'contacttype', NULL,
        'business', NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 16:47:53',
        '314j1000000000000000000', '2024-09-06 16:47:53', '联系人类型；business.商务联系人；technology.技术负责人',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
