INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11846119817949036544', 'SRJH', NULL, '收入计划编号', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 17:23:59', 'user00000000000000000100000000000000', '2024-10-15 17:23:59', 101, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, '7b020ffa934d406a9f4d540707f2c7d4', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'0');

INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1846120357718212608', '开头编码', '0', '9hi11846119817949036544', '', 'fixedValue', '', 'hh', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 17:26:07', 'user00000000000000000100000000000000', '2024-10-15 19:22:48', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, -1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1846120460390580224', '年度', '0', '9hi11846119817949036544', '', 'DATE_YYYY', '', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 17:26:32', 'user00000000000000000100000000000000', '2024-10-15 17:26:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1846120554183606272', '月度', '0', '9hi11846119817949036544', '', 'DATE_M', '', '', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 17:26:54', 'user00000000000000000100000000000000', '2024-10-15 17:26:54', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1846120725730639872', '专业中心编码', '0', '9hi11846119817949036544', '', 'fixedValue', '', '1233', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 17:27:35', 'user00000000000000000100000000000000', '2024-10-15 19:40:31', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, -1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1846120857830244352', '流水器', '0', '9hi11846119817949036544', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '', '', '4', 5, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 17:28:06', 'user00000000000000000100000000000000', '2024-10-15 17:28:06', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1846149944103747584', '计划编码', '0', '9hi11846119817949036544', '', 'PARAMETER', '', 'incomeCode', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 19:23:41', 'user00000000000000000100000000000000', '2024-10-15 19:38:35', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1846155083795480576', '专业中心编码', '0', '9hi11846119817949036544', '', 'PARAMETER', '', 'expertiseCenter', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-15 19:44:07', 'user00000000000000000100000000000000', '2024-10-15 19:44:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);

INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1847555535430828032', 'number', '9hi11846119817949036544', 'IncomePlanData', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-10-19 16:29:00', 'user00000000000000000100000000000000', '2024-10-19 16:29:00', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
