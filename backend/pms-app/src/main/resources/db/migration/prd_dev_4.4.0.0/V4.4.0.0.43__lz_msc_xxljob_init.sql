INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822911340184477696', '大修管理-重大项目选择-标题', 'PMS_REPAIR_IMPORTANT_TITLE', '重大项目作业认定通知', '1', 1, '', '314j1000000000000000000', '2024-08-12 16:21:46', '314j1000000000000000000', '2024-08-12 16:22:08', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822911705252503552', '大修管理-重大项目选择', 'PMS_REPAIR_PROJECT', '您好\n您所负责的【$workName$】【$workTitle$】由大修指挥部认定为重大项目。请进入【精益一体化系统】查看。', '1', 1, '', '314j1000000000000000000', '2024-08-12 16:23:13', '314j1000000000000000000', '2024-08-12 16:23:15', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');

INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1839493606805233664', '重大项目每日进展反馈', 'NODE_PROJECT_PROGRESS', 'vub01839491956816371712', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1770010828494475264', 1, NULL, '314j1000000000000000000', '2024-09-27 10:33:47', '314j1000000000000000000', '2024-09-27 10:34:35', NULL, NULL, NULL, 1, b'1', b'1');

INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (126, 5, '重大项目进展每日提醒', '2024-09-26 20:11:52', '2024-09-26 20:12:12', 'orion', '', 'CRON', '0 50 9 * * ?', 'DO_NOTHING', 'FIRST', 'importantProjectProgressXxlJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-09-26 20:11:52', '', 0, 0, 0);
