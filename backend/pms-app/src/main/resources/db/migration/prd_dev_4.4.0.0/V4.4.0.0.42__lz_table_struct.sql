CREATE TABLE `pmsx_important_project` (
                                          `id` varchar(64) NOT NULL  COMMENT '主键',
                                          `class_name` varchar(64)   COMMENT '创建人',
                                          `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                          `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                          `owner_id` varchar(64)   COMMENT '拥有者',
                                          `create_time` datetime NOT NULL  COMMENT '创建时间',
                                          `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                          `remark` varchar(1024)   COMMENT '备注',
                                          `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                          `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                          `status` int NOT NULL  COMMENT '状态',
                                          `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                          `project_name` varchar(64) NOT NULL  COMMENT '重大项目名称',
                                          `rsp_user_id` varchar(64) NOT NULL  COMMENT '负责人id',
                                          `rsp_user_code` varchar(64) NOT NULL  COMMENT '负责人工号',
                                          `rsp_user_name` varchar(64)   COMMENT '负责人姓名',
                                          `dept_id` varchar(64)   COMMENT '负责人所在部门',
                                          `plan_start` datetime   COMMENT '计划开始时间',
                                          `plan_end` datetime   COMMENT '计划完成时间',
                                          `acture_start` datetime   COMMENT '实际开始时间',
                                          `acture_end` datetime   COMMENT '实际完成时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='重大项目';

ALTER TABLE `pmsx_important_project`
    ADD COLUMN `repair_round` varchar(64) NULL COMMENT '大修轮次' AFTER `rsp_user_code`;

CREATE TABLE `pmsx_project_job`
(
    `id`         int(11)  AUTO_INCREMENT NOT NULL COMMENT 'id',
    `project_id` varchar(64) DEFAULT NULL COMMENT '项目id',
    `job_id`     varchar(64) DEFAULT NULL COMMENT '作业id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目作业关系表';


CREATE TABLE `pmsx_project_progress`
(
    `id`         varchar(64) NOT NULL COMMENT 'id',
    `project_id` varchar(64) DEFAULT NULL COMMENT '项目id',
    `progress_id`     varchar(64) DEFAULT NULL COMMENT '进展id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目进展关系表';