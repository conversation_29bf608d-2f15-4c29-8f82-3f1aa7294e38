CREATE TABLE `pmsx_income_plan` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `work_topics` varchar(50) NOT NULL COMMENT '工作主题',
  `work_topics_name` varchar(100) DEFAULT NULL COMMENT '工作主题名称',
  `issue_person` varchar(64) NOT NULL COMMENT '下发人员',
  `issue_time` datetime NOT NULL COMMENT '下发时间',
  `lock_status` varchar(50) NOT NULL COMMENT '锁定状态',
  `income_plan_count` int(11) DEFAULT NULL COMMENT '本月收入计划笔数',
  `income_plan_amt` decimal(20,2) DEFAULT NULL COMMENT '本月收入计划金额',
  `complete_count` int(11) DEFAULT NULL COMMENT '已完成计划数量',
  `complete_amt` decimal(20,2) DEFAULT NULL COMMENT '已完成计划金额',
  `execution_count` int(11) DEFAULT NULL COMMENT '执行中笔数',
  `execution_amt` decimal(20,2) DEFAULT NULL COMMENT '执行中金额',
  `no_start_count` int(11) DEFAULT NULL COMMENT '未开始笔数',
  `no_start_amt` decimal(20,2) DEFAULT NULL COMMENT '未开始金额',
  `no_hook_milestone_count` int(11) DEFAULT NULL COMMENT '未挂接里程碑数量',
  `no_hook_milestone_amt` decimal(20,2) DEFAULT NULL COMMENT '未挂接里程碑金额',
  `income_plan_type` varchar(10) DEFAULT NULL COMMENT '编制调整状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收入计划填报';

CREATE TABLE `pmsx_income_plan_data` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `contract_id` varchar(64) DEFAULT NULL COMMENT '合同ID',
  `contract_number` varchar(100) DEFAULT NULL COMMENT '合同编码',
  `milestone_id` varchar(64) DEFAULT NULL COMMENT '合同里程碑id',
  `milestone_number` varchar(100) DEFAULT NULL COMMENT '合同里程碑编码',
  `party_A_dept_id` varchar(64) DEFAULT NULL COMMENT '甲方单位id',
  `number` varchar(100) DEFAULT NULL COMMENT '收入计划编码',
  `lock_status` varchar(50) DEFAULT NULL COMMENT '锁定状态',
  `income_confirm_type` varchar(100) DEFAULT NULL COMMENT '收入确认类型',
  `estimate_invoice_date` datetime DEFAULT NULL COMMENT '预计开票/暂估日期',
  `tax_rate` varchar(100) DEFAULT NULL COMMENT '税率',
  `estimate_amt` decimal(20,2) DEFAULT NULL COMMENT '本次暂估金额（价税合计）',
  `inv_amt` decimal(20,2) DEFAULT NULL COMMENT '开票金额（价税合计）',
  `cancel_inv_amt` decimal(20,2) DEFAULT NULL COMMENT '作废发票合计（价税合计）',
  `mileston_estimate_amt` decimal(20,2) DEFAULT NULL COMMENT '里程碑已暂估金额（价税合计）',
  `write_off_amt` decimal(20,2) DEFAULT NULL COMMENT '本次冲销暂估金额（价税合计）',
  `milestone_pre_paid_inv_amt` decimal(20,2) DEFAULT NULL COMMENT '里程碑已预收款开票金额（价税合计）',
  `advance_pay_income_amt` decimal(20,2) DEFAULT NULL COMMENT '预收款转收入金额（价税合计）',
  `income_plan_amt` decimal(20,2) DEFAULT NULL COMMENT '本次收入计划金额（价税合计）',
  `expertise_center` varchar(64) DEFAULT NULL COMMENT '专业中心',
  `expertise_station` varchar(64) DEFAULT NULL COMMENT '专业所',
  `project_number` varchar(64) DEFAULT NULL COMMENT '项目编码',
  `project_id` varchar(64) DEFAULT NULL COMMENT '项目id',
  `project_rsp_user_id` varchar(64) DEFAULT NULL COMMENT '项目负责人',
  `billing_company` varchar(64) DEFAULT NULL COMMENT '开票/收入确认公司',
  `internal_external` varchar(10) DEFAULT NULL COMMENT '集团内（基地）/外',
  `milestone_amt` decimal(20,2) DEFAULT NULL COMMENT '里程碑金额',
  `milestone_inv_amt` decimal(20,2) DEFAULT NULL COMMENT '里程碑已开票收入金额（价税合计）',
  `milestone_no_inv_amt` decimal(20,2) DEFAULT NULL COMMENT '里程碑未开票金额（价税合计）',
  `estimate_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '本次暂估金额（不含税）',
  `inv_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '本次开票金额（不含税）',
  `cancel_inv_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '本次作废发票金额（不含税）',
  `milestone_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '里程碑已暂估金额（不含税）',
  `write_off_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '本次冲销暂估金额（不含税）',
  `milestone_inv_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '里程碑已预收款开票金额（不含税）',
  `adv_pay_income_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '预收款转收入金额（不含税）',
  `is_revenue` varchar(2) DEFAULT NULL COMMENT '本月是否申报收入计划',
  `no_revenue_plan_reason` varchar(500) DEFAULT NULL COMMENT '不申报收入计划原因',
  `other_notes` varchar(500) DEFAULT NULL COMMENT '其他说明',
  `data_version` varchar(100) DEFAULT NULL COMMENT '数据版本',
  `data_source` varchar(100) DEFAULT NULL COMMENT '数据来源',
  `income_plan_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报id',
  `risk_link` varchar(100) DEFAULT NULL COMMENT '风险环节',
  `risk_type` varchar(100) DEFAULT NULL COMMENT '风险状态',
  `change_reason` varchar(500) DEFAULT NULL COMMENT '修改收入计划原因',
  `is_adjustment` varchar(2) DEFAULT NULL COMMENT '是否调整',
  `compile_id` varchar(64) DEFAULT NULL COMMENT '编制ID',
  `contract_name` varchar(255) DEFAULT NULL COMMENT '合同名称',
  `milestone_name` varchar(255) DEFAULT NULL COMMENT '合同里程碑名称',
  `is_multiple_projects` varchar(2) DEFAULT NULL COMMENT '是否多项目',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收入计划填报数据';

CREATE TABLE `pmsx_income_plan_data_control` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `lock_status` varchar(10) DEFAULT NULL COMMENT '锁定状态',
  `expertise_center` varchar(64) NOT NULL COMMENT '专业中心',
  `expertise_center_money` decimal(20,2) DEFAULT NULL COMMENT '专业中心收入计划金额',
  `income_plan_id` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收入计划数据管控';


CREATE TABLE `pmsx_income_plan_data_lock` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `expertise_center` varchar(64) DEFAULT NULL COMMENT '专业中心',
  `expertise_station` varchar(64) DEFAULT NULL COMMENT '专业所',
  `lock_type` varchar(10) DEFAULT NULL COMMENT '锁类型 1：专业中心锁  2：专业所锁',
  `income_plan_id` varchar(64) DEFAULT NULL COMMENT '收入计划Id',
  `lock_status` varchar(64) DEFAULT NULL COMMENT '锁状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收入计划数据锁定表';

CREATE TABLE `pmsx_income_plan_data_log` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `change_id` varchar(64) DEFAULT NULL COMMENT '变更日志',
  `change_user_id` varchar(64) DEFAULT NULL COMMENT '变更人员',
  `change_time` datetime DEFAULT NULL COMMENT '变更时间',
  `change_content` tinytext NOT NULL COMMENT '变更内容',
  `income_plan_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收入计划填报内容日志';


CREATE TABLE `pmsx_income_provision_information` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `year` varchar(10) DEFAULT NULL COMMENT '年份',
  `voucher_number` varchar(100) DEFAULT NULL COMMENT '凭证号',
  `accrued_amt` decimal(20,2) DEFAULT NULL COMMENT '挂账金额',
  `amortized_amt` decimal(20,2) DEFAULT NULL COMMENT '已冲销金额',
  `remain_unamortized_amt` decimal(20,2) DEFAULT NULL COMMENT '剩余未冲销金额',
  `amortization_amount` decimal(20,2) DEFAULT NULL COMMENT '本次冲销金额',
  `amortization_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '本次冲销金额（不含税）',
  `no_amortization_reason` varchar(500) DEFAULT NULL COMMENT '不冲销原因',
  `income_plan_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报id',
  `income_plan_data_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报数据id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收入计提信息表';


CREATE TABLE `pmsx_person_role_maintenance` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `expertise_center` varchar(64) NOT NULL COMMENT '专用中心',
  `expertise_station` varchar(64) NOT NULL COMMENT '专业所',
  `change_reason` tinytext COMMENT '变更原因',
  `financial_staff` varchar(64) DEFAULT NULL COMMENT '财务人员',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人员角色维护表';


CREATE TABLE `pmsx_person_role_maintenance_detail` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `mian_table_id` varchar(64) NOT NULL COMMENT '主表Id',
  `person_type` varchar(64) NOT NULL COMMENT '人员类型',
  `person_id` varchar(64) NOT NULL COMMENT '人员Id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人员角色维护表人员明细';


CREATE TABLE `pmsx_person_role_maintenance_log` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `change_person` varchar(64) DEFAULT NULL COMMENT '变更人',
  `change_time` datetime NOT NULL COMMENT '变更时间',
  `change_content` varchar(500) DEFAULT NULL COMMENT '变更内容',
  `before_change` varchar(500) DEFAULT NULL COMMENT '变更前',
  `after_changfe` varchar(500) DEFAULT NULL COMMENT '变更后',
  `change_id` varchar(64) DEFAULT NULL COMMENT '变更日志关联人员角色',
  `role_id` varchar(64) DEFAULT NULL COMMENT '人员角色维护表id',
  `change_person_name` varchar(255) DEFAULT NULL COMMENT '变更人姓名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人员角色维护日志';


CREATE TABLE `pmsx_billing_account_information` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
  `project_number` varchar(100) DEFAULT NULL COMMENT '项目编码',
  `project_rsp_user_id` varchar(64) DEFAULT NULL COMMENT '项目责任人Id',
  `project_type` varchar(100) DEFAULT NULL COMMENT '项目类型',
  `actual_acceptance_amt` decimal(20,2) DEFAULT NULL COMMENT '实际验收金额',
  `tax_rate` decimal(20,2) DEFAULT NULL COMMENT '税率',
  `total_amt_tax` decimal(20,2) DEFAULT NULL COMMENT '价税合计金额',
  `vat_amt` decimal(20,2) DEFAULT NULL COMMENT '税额（增值税）',
  `amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '不含税金额',
  `income_plan_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报id',
  `income_plan_data_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报内容id',
  `income_wbs_number` varchar(100) DEFAULT NULL COMMENT '收入wbs编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='开票核算信息表';

CREATE TABLE `pmsx_advance_payment_information` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `year` varchar(10) DEFAULT NULL COMMENT '年份',
  `voucher_number` varchar(100) DEFAULT NULL COMMENT '凭证号',
  `accrued_amt` decimal(20,2) DEFAULT NULL COMMENT '挂账金额',
  `cleared_amt` decimal(20,2) DEFAULT NULL COMMENT '已清账金额',
  `un_adv_receivable_amt` decimal(20,2) DEFAULT NULL COMMENT '未结清预收款金额',
  `current_clear_amt` decimal(20,2) DEFAULT NULL COMMENT '本次清账金额',
  `current_clear_amt_ex_tax` decimal(20,2) DEFAULT NULL COMMENT '本次清账金额（不含税）',
  `no_clear_reason` varchar(500) DEFAULT NULL COMMENT '不清账原因',
  `income_plan_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报id',
  `income_plan_data_id` varchar(64) DEFAULT NULL COMMENT '收入计划填报数据id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预收款信息';