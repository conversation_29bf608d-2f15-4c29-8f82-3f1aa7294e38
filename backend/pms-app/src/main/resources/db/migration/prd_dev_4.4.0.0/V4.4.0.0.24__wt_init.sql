ALTER TABLE `pmsx_basic_user` ADD COLUMN `user_type` int(1) default 0 COMMENT '人员类型 0：正式成员 1：临时辅助人员';
ALTER TABLE `pmsx_basic_user` ADD COLUMN `add_work_time` datetime default NULL COMMENT '到岗时间';
DROP TABLE IF EXISTS `pmsx_basic_user_ledger`;
CREATE TABLE `pmsx_basic_user_ledger`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `basic_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '技术支持人员id',
  `user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `company_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `department_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用人部门',
  `institute_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '研究所',
  `add_unittime` datetime(0) NULL DEFAULT NULL COMMENT '加入本单位时间',
  `add_work_time` datetime(0) NULL DEFAULT NULL COMMENT '到岗时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '技术支持人员台账记录' ROW_FORMAT = Dynamic;
ALTER TABLE `pmsx_basic_user` ADD COLUMN `phone` varchar(255) default NULL COMMENT '联系电话';
ALTER TABLE `pmsx_basic_user` ADD COLUMN `user_id` varchar(64) default NULL COMMENT '基础用户id';
ALTER TABLE `pmsx_basic_user` ADD COLUMN `leave_work_time` datetime default NULL COMMENT '离岗时间';
INSERT INTO `dme_dict`(`id`, `keyword`, `label`, `parent_id`, `is_group`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `owner_id`, `remark`, `logic_status`, `type`) VALUES ('dict1834124600950812672', '政治面貌', 'zhengzhimianmao', 'dictdd48c34df497467384607e50d121fb38', 0, 4395, 'politicalAffiliation', 1, '政治面貌', '314j1000000000000000000', '2024-09-12 14:59:16', '314j1000000000000000000', '2024-09-12 14:59:16', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'Dict', '314j1000000000000000000', NULL, 1, 0);

INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834124833130704896', 'dict1834124600950812672', '', '中共党员', '', 1, '中共党员', 1, '中共党员', '314j1000000000000000000', '2024-09-12 15:00:11', '314j1000000000000000000', '2024-09-12 15:02:25', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125122432823296', 'dict1834124600950812672', '', '中共预备党员', '', 2, '中共预备党员', 1, '中共预备党员', '314j1000000000000000000', '2024-09-12 15:01:20', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125122441211904', 'dict1834124600950812672', '', '共青团员', '', 3, '共青团员', 1, '共青团员', '314j1000000000000000000', '2024-09-12 15:01:20', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125122441211905', 'dict1834124600950812672', '', '民革党员', '', 4, '民革党员', 1, '民革党员', '314j1000000000000000000', '2024-09-12 15:01:20', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125122441211906', 'dict1834124600950812672', '', '民盟盟员', '', 5, '民盟盟员', 1, '民盟盟员', '314j1000000000000000000', '2024-09-12 15:01:20', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125122445406208', 'dict1834124600950812672', '', '民建会员', '', 6, '民建会员', 1, '民建会员', '314j1000000000000000000', '2024-09-12 15:01:20', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125122449600512', 'dict1834124600950812672', '', '民进会员', '', 7, '民进会员', 1, '民进会员', '314j1000000000000000000', '2024-09-12 15:01:20', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125122453794816', 'dict1834124600950812672', '', '农工党党员', '', 8, '农工党党员', 1, '农工党党员', '314j1000000000000000000', '2024-09-12 15:01:20', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125398992646144', 'dict1834124600950812672', '', '致公党党员', '', 9, '致公党党员', 1, '致公党党员', '314j1000000000000000000', '2024-09-12 15:02:26', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125398996840448', 'dict1834124600950812672', '', '九三学社社员', '', 10, '九三学社社员', 1, '九三学社社员', '314j1000000000000000000', '2024-09-12 15:02:26', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125398996840449', 'dict1834124600950812672', '', '台盟盟员', '', 11, '台盟盟员', 1, '台盟盟员', '314j1000000000000000000', '2024-09-12 15:02:26', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125398996840450', 'dict1834124600950812672', '', '无党派人士', '', 12, '无党派人士', 1, '无党派人士', '314j1000000000000000000', '2024-09-12 15:02:26', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1834125398996840451', 'dict1834124600950812672', '', '群众', '', 13, '群众', 1, '群众', '314j1000000000000000000', '2024-09-12 15:02:26', '314j1000000000000000000', '2024-09-12 15:02:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);

ALTER TABLE `pmsx_basic_user_certificate` ADD COLUMN `initial_certification_date` datetime default NULL COMMENT '初次取证日期';
ALTER TABLE `pmsx_basic_user_certificate` ADD COLUMN `valid_to_date` datetime default NULL COMMENT '有效期至';
ALTER TABLE `pmsx_basic_user_certificate` ADD COLUMN `number` varchar(255) default NULL COMMENT '证书编码';
