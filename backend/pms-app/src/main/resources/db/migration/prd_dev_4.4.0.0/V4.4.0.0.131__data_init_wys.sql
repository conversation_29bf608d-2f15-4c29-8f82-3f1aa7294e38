-- 消息

INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01851600884353064960', '大修行动项督办提醒_标题', 'PMS_ACTION_ITEM_SUPERIVISE_TI', '【$repairRound$】大修，由您负责的大修行动项任务完成时限即将到期，请进入系统尽早反馈', '1', 1, '', 'user00000000000000000100000000000000', '2024-10-30 20:23:47', 'user00000000000000000100000000000000', '2024-11-05 18:25:50', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01851601439779577856', '大修行动项临期提醒_标题', 'PMS_ACTION_ITEM_REMIND_TITLE', '【$repairRound$】大修，由您负责的大修行动项任务完成时限不足15天，请进入系统尽早反馈。', '1', 1, '', 'user00000000000000000100000000000000', '2024-10-30 20:25:59', 'user00000000000000000100000000000000', '2024-10-30 20:43:05', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01851605428529127424', '大修行动项临期提醒_内容', 'PMS_ACTION_ITEM_REMIND', '<p>【$repairRound$】大修，由您负责的大修行动项任务完成时限不足15天，请进入系统尽早反馈。</p>', '2', 1, '', 'user00000000000000000100000000000000', '2024-10-30 20:41:50', 'user00000000000000000100000000000000', '2024-10-30 20:43:03', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01851605712273793024', '大修行动项督办提醒_内容', 'PMS_ACTION_ITEM_SUPERICISE', '<p>【$repairRound$】大修，由您负责的大修行动项任务完成时限即将到期，请进入系统尽早反馈</p>', '2', 1, '', 'user00000000000000000100000000000000', '2024-10-30 20:42:58', 'user00000000000000000100000000000000', '2024-10-30 20:43:01', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');


INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1852315821857636352', '大修行动项-督办', 'NODE_ACTION_ITEM_SUPERVISE', 'vub01851600884353064960', 'vub01851605712273793024', 1, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-11-01 19:44:41', 'user00000000000000000100000000000000', '2024-11-06 20:05:27', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1852315958625501184', '大修行动项-临期', 'NODE_ACTION_ITEM_REMIND', 'vub01851601439779577856', 'vub01851605428529127424', 1, 'SYS,EMAIL', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-11-01 19:45:14', 'user00000000000000000100000000000000', '2024-11-05 14:02:40', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');


INSERT INTO `msc_business_node_channel`(`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`, `business_node_id`, `type`, `template_flag`, `custom`) VALUES ('35rj1854132425859874816', 'BusinessNodeChannel', 'user00000000000000000100000000000000', '2024-11-06 20:03:13', 'user00000000000000000100000000000000', '2024-11-06 20:03:13', 'user00000000000000000100000000000000', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 1, -1, '0a0y1852315821857636352', 'SYS', b'0', NULL);
INSERT INTO `msc_business_node_channel`(`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`, `business_node_id`, `type`, `template_flag`, `custom`) VALUES ('35rj1854132977322770432', 'BusinessNodeChannel', 'user00000000000000000100000000000000', '2024-11-06 20:05:25', 'user00000000000000000100000000000000', '2024-11-06 20:05:25', 'user00000000000000000100000000000000', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 1, 1, '0a0y1852315821857636352', 'SYS', b'0', NULL);


-- 状态策略

INSERT INTO `pmi_data_policy`(`id`, `class_name`, `name`, `label`, `remark`, `description`, `creator_id`, `modify_id`, `owner_id`, `create_time`, `modify_time`, `status`, `platform_id`, `unique_key`, `logic_status`) VALUES ('txf71852167097957310464', 'DataPolicy', '大修行动项状态策略', NULL, NULL, NULL, '314j1000000000000000000', '314j1000000000000000000', '314j1000000000000000000', '2024-11-01 09:53:42', '2024-11-01 09:53:42', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1);
INSERT INTO `pmi_data_status`(`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1852167723265122304', 'DataStatus', 'txf71852167097957310464', NULL, '1', '待发布', 101, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-01 09:56:11', 'user00000000000000000100000000000000', '2024-11-01 09:56:11', 1, 4882, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 1);
INSERT INTO `pmi_data_status`(`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1852167856027426816', 'DataStatus', 'txf71852167097957310464', NULL, '5', '执行中', 110, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-01 09:57:04', 'user00000000000000000100000000000000', '2024-11-01 09:56:43', 1, 4894, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status`(`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1852168206394417152', 'DataStatus', 'txf71852167097957310464', NULL, '4', '待反馈', 111, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-01 09:58:07', 'user00000000000000000100000000000000', '2024-11-01 09:58:07', 1, 4906, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status`(`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1852168293703049216', 'DataStatus', 'txf71852167097957310464', NULL, '4', '待验证', 120, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-01 09:59:07', 'user00000000000000000100000000000000', '2024-11-01 09:58:27', 1, 4918, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status`(`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1852168564160159744', 'DataStatus', 'txf71852167097957310464', NULL, '3', '验证通过', 121, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-01 09:59:45', 'user00000000000000000100000000000000', '2024-11-01 09:59:32', 1, 4930, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status`(`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1852168703092285440', 'DataStatus', 'txf71852167097957310464', NULL, '3', '已关闭', 130, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-01 10:00:05', 'user00000000000000000100000000000000', '2024-11-01 10:00:05', 1, 4942, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
