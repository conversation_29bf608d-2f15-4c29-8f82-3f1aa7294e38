INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (125, 5, '大修日常培训检查提醒', '2024-09-19 18:50:11', '2024-09-19 18:50:11', 'orion', '', 'CRON', '0 0 10 * * ?', 'DO_NOTHING', 'FIRST', 'repairTrainingNoticeJobCheaked', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-09-19 18:50:11', '', 0, 0, 0);
