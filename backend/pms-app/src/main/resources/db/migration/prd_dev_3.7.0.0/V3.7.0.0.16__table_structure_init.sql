/*
 Navicat Premium Data Transfer

 Source Server         : orin_zgh
 Source Server Type    : MySQL
 Source Server Version : 50738
 Source Host           : ***********:15306
 Source Schema         : orion_prd_test

 Target Server Type    : MySQL
 Target Server Version : 50738
 File Encoding         : 65001

 Date: 20/06/2024 20:40:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ncf_form_contract_change
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_change`;
CREATE TABLE `ncf_form_contract_change`  (
                                             `change_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更编号',
                                             `change_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更标题',
                                             `change_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更类型',
                                             `change_request_date` datetime(0) NULL DEFAULT NULL COMMENT '变更申请日期',
                                             `this_change_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '本次变更金额',
                                             `cumulative_change_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '累计变更金额',
                                             `cumulative_change_rate` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '累计变更比率',
                                             `contact_amount_after_change` decimal(20, 2) NULL DEFAULT NULL COMMENT '变更后合同承诺总价（总目标值）',
                                             `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                             `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                             `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                             `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                             `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                             `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                             `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                             `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                             `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                             `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                             `status` int(11) NOT NULL COMMENT '状态',
                                             `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                             `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                             `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                             `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                             `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同变更信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_claim
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_claim`;
CREATE TABLE `ncf_form_contract_claim`  (
                                            `claim_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '索赔编号',
                                            `claim_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '索赔标题',
                                            `claim_status` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '索赔状态',
                                            `claim_direction` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '索赔方向',
                                            `claim_process_time` int(11) NULL DEFAULT NULL COMMENT '索赔处理时间',
                                            `claim_request_time` datetime(0) NULL DEFAULT NULL COMMENT '索赔申请时间',
                                            `cumulative_claim_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '累计索赔金额（含本次）',
                                            `total_claim_pct_of_orig_price` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '总累计索赔占原合同价%',
                                            `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                            `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                            `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                            `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                            `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                            `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                            `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                            `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                            `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                            `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                            `status` int(11) NOT NULL COMMENT '状态',
                                            `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                            `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                            `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                            `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                            `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同索赔信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_extend_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_extend_info`;
CREATE TABLE `ncf_form_contract_extend_info`  (
                                                  `procurement_org_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购组织',
                                                  `procurement_org_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购组织ID',
                                                  `procurement_group_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购组',
                                                  `procurement_group_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购组ID',
                                                  `business_rsp_user` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务负责人',
                                                  `business_rsp_user_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务负责人ID',
                                                  `technical_rsp_user` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '技术负责人',
                                                  `technical_rsp_user_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '技术负责人ID',
                                                  `recommendation_basis` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推荐依据',
                                                  `negotiate_save_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '节省总金额（RMB）',
                                                  `sum_save_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '渠道优化节省金额',
                                                  `compare_save_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '谈判节省金额',
                                                  `channel_save_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '与立项相比节省金额',
                                                  `optimize_save_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '优化采购节省金额',
                                                  `is_process_amount` bit(1) NULL DEFAULT NULL COMMENT '是否办理履约保证金',
                                                  `prcess_amount_pay_way` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保证金支付方式',
                                                  `prcess_amount` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保证金',
                                                  `account_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账户名称',
                                                  `bank_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行账号',
                                                  `bank_account` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行',
                                                  `bank_code` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行代码',
                                                  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                                  `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                                  `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                                  `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                                  `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                                  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                  `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                  `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                                  `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                                  `status` int(11) NOT NULL COMMENT '状态',
                                                  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                  `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                                  `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                                  `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                                  `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同拓展信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_info`;
CREATE TABLE `ncf_form_contract_info`  (
                                           `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                           `is_fream` bit(10) NULL DEFAULT NULL COMMENT '是否框架合同',
                                           `purchase_applicant` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购申请号',
                                           `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                           `status_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同执行状态',
                                           `fream_residue_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '框架合同剩余金额',
                                           `type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同类型',
                                           `procurement_order_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购订单号',
                                           `project_code` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购立项号',
                                           `procurement_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '采购立项金额',
                                           `supplier_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商',
                                           `factory_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工厂',
                                           `business_rsp_user` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务负责人',
                                           `change_money` decimal(20, 2) NULL DEFAULT NULL COMMENT '变更金额',
                                           `change_percent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更比例',
                                           `pay_money` decimal(20, 2) NULL DEFAULT NULL COMMENT '支付金额',
                                           `is_contract_terminate` bit(10) NULL DEFAULT NULL COMMENT '是否合同终止',
                                           `claim_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '索赔金额',
                                           `claim_percent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '索赔比例',
                                           `terminate_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '终止金额',
                                           `terminate_percent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '终止比例',
                                           `fream_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '框架开始时间',
                                           `fream_end_time` datetime(0) NULL DEFAULT NULL COMMENT '框架结束时间',
                                           `fream_used_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '框架合同已使用金额',
                                           `is_fream_period` bit(10) NULL DEFAULT NULL COMMENT '是否框架有效期内',
                                           `pay_percent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付比例',
                                           `procurement_way` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购方式',
                                           `procurement_cycle` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购周期',
                                           `amount_saved` decimal(20, 2) NULL DEFAULT NULL COMMENT '节约金额',
                                           `business_activity_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务活动类型',
                                           `end_procurement_way` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最终采购方式',
                                           `business_file_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务文件类型',
                                           `estimated_start_time` datetime(0) NULL DEFAULT NULL COMMENT '预计合同开始日期',
                                           `estimated_end_time` datetime(0) NULL DEFAULT NULL COMMENT '预计合同结束日期',
                                           `pay_way` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款方式',
                                           `execution_status_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同履约状态',
                                           `object_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标的类别',
                                           `type_percent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类别占比（%）',
                                           `approved_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '审批价格（RMB）',
                                           `final_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '最终价格（原币）',
                                           `actual_start_time` datetime(0) NULL DEFAULT NULL COMMENT '实际合同开始日期',
                                           `actual_end_time` datetime(0) NULL DEFAULT NULL COMMENT '实际合同结束日期',
                                           `estimated_delivery_time` datetime(0) NULL DEFAULT NULL COMMENT '预计合同交付日期',
                                           `acceptance_results` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '验收结果',
                                           `actual_acceptance_times` datetime(0) NULL DEFAULT NULL COMMENT '实际验收日期',
                                           `is_public_launch` bit(10) NULL DEFAULT NULL COMMENT '是否发布启动公示',
                                           `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                           `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                           `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                           `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                           `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                           `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                           `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                           `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                           `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                           `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                           `status` int(11) NOT NULL COMMENT '状态',
                                           `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                           `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                           `is_performance_bond` bit(10) NULL DEFAULT NULL COMMENT '是否办理履约保证金',
                                           `margin_payment_method` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保证金支付方式',
                                           `security_deposit` decimal(20, 2) NULL DEFAULT NULL COMMENT '保证金',
                                           `ia_calculation` bit(10) NULL DEFAULT NULL COMMENT '是否参与计算	',
                                           `project_end_time` datetime(0) NULL DEFAULT NULL COMMENT '采购立项审批完成时间',
                                           `recommend_end_time` datetime(0) NULL DEFAULT NULL COMMENT '合同推荐审批完成时间',
                                           `is_biz_recommend` bit(10) NULL DEFAULT NULL COMMENT '商务是否推荐供应商',
                                           `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                           `is_calculation` bit(10) NULL DEFAULT NULL COMMENT '是否参与计算',
                                           `price_model` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '价格模式',
                                           `subdivision` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部处',
                                           `is_fill_onetime_acceptance` bit(1) NULL DEFAULT NULL COMMENT '是否填写一次验收合格',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同主表信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_line_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_line_info`;
CREATE TABLE `ncf_form_contract_line_info`  (
                                                `line_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同行项目',
                                                `num_count` decimal(20, 2) NULL DEFAULT NULL COMMENT '数量',
                                                `unit_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '单价（含税）',
                                                `tax_rate` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税率',
                                                `list_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '原始价格',
                                                `update_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '修改价格',
                                                `planned_delivery_date` datetime(0) NULL DEFAULT NULL COMMENT '计划交货日期',
                                                `procurement_applicant_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购申请号',
                                                `procurement_applicant_line_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购申请行号',
                                                `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                                `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                                `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                                `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                                `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                                `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                                `status` int(11) NOT NULL COMMENT '状态',
                                                `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                                `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                                `final_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '最终价格',
                                                `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                                `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同行项目信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_pay_milestone
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_pay_milestone`;
CREATE TABLE `ncf_form_contract_pay_milestone`  (
                                                    `milestone_desc` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '里程碑业务描述',
                                                    `in_out_payment` bit(10) NULL DEFAULT NULL COMMENT '是否涉及境外付款',
                                                    `contract_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同类型',
                                                    `payment_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付类型',
                                                    `est_payment_date` datetime(0) NULL DEFAULT NULL COMMENT '预计付款时间',
                                                    `attachment_req` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件要求',
                                                    `payment_ratio` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付比例',
                                                    `contract_agreed_payment` decimal(20, 2) NULL DEFAULT NULL COMMENT '合同约定支付金额',
                                                    `price_total_fixed` bit(10) NULL DEFAULT NULL COMMENT '价格属性总价是否固定',
                                                    `invoice_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开票类型',
                                                    `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                                    `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                                    `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                                    `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                                    `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                    `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                    `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                    `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                                    `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                                    `status` int(11) NOT NULL COMMENT '状态',
                                                    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                    `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                                    `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                                    `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                                    `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同支付里程碑（计划）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_supplier_record
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_supplier_record`;
CREATE TABLE `ncf_form_contract_supplier_record`  (
                                                      `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同号',
                                                      `supplier_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商ID',
                                                      `supplier_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                                      `supplier_from` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商来源',
                                                      `is_inquiry_supplier` bit(10) NULL DEFAULT NULL COMMENT '是否询价供应商',
                                                      `is_winner_supplier` bit(10) NULL DEFAULT NULL COMMENT '是否中标供应商',
                                                      `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                                      `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                                      `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                                      `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                                      `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                                      `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                      `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                      `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                      `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                                      `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                                      `status` int(11) NOT NULL COMMENT '状态',
                                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                      `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                                      `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                                      `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同供应商记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_termination
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_termination`;
CREATE TABLE `ncf_form_contract_termination`  (
                                                  `is_pre_sign_termination` bit(10) NULL DEFAULT NULL COMMENT '是否签约前终止',
                                                  `termination_request_date` datetime(0) NULL DEFAULT NULL COMMENT '终止申请日期',
                                                  `contract_termination_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '合同终止金额',
                                                  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                                  `class_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
                                                  `creator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                                  `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                                  `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                                  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                  `modify_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                  `platform_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
                                                  `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
                                                  `status` int(11) NOT NULL COMMENT '状态',
                                                  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                  `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                                  `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                                  `contract_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
                                                  `contract_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同终止信息表' ROW_FORMAT = Dynamic;

alter table pms_project_approval_estimate_template_classify add COLUMN  `description` varchar(1024)   COMMENT '描述';


