CREATE TABLE `pms_project_material_plan`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `remark`            varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`            int(11)     NOT NULL COMMENT '状态',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除字段',
    `number`            varchar(64) NOT NULL COMMENT '物料编码',
    `project_id`        varchar(64) NOT NULL COMMENT '项目id',
    `material_id`       varchar(64) NOT NULL COMMENT '物料id',
    `plan_use_time`     datetime      DEFAULT NULL COMMENT '计划使用时间',
    `plan_num`          int(11)       DEFAULT NULL COMMENT '计划数量',
    `base_unit`         varchar(64)   DEFAULT NULL COMMENT '基本单位',
    `procurement_cycle` int(11)       DEFAULT NULL COMMENT '采购周期',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目物资计划';

CREATE TABLE `pmsx_project_scheme_bom`
(
    `id`                   varchar(64)  NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`          datetime     NOT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime     NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)  NOT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`               int(11)      NOT NULL COMMENT '状态',
    `logic_status`         int(11)      NOT NULL COMMENT '逻辑删除字段',
    `materiel_name`        varchar(100)  DEFAULT NULL COMMENT '物料名称',
    `materiel_code`        varchar(100)  DEFAULT NULL COMMENT '物料编码',
    `version`              varchar(100) NOT NULL COMMENT '版本',
    `materiel_type`        varchar(100)  DEFAULT NULL COMMENT '物料类型',
    `amount`               varchar(100)  DEFAULT NULL COMMENT '数量',
    `use_num`              varchar(100)  DEFAULT NULL COMMENT '编码',
    `materiel_status`      varchar(100)  DEFAULT NULL COMMENT '物料状态',
    `plm_creator`          varchar(100)  DEFAULT NULL COMMENT 'plm创建人id',
    `plm_create_time`      varchar(100)  DEFAULT NULL COMMENT '创建时间',
    `plm_modify`           varchar(100)  DEFAULT NULL COMMENT '修改人',
    `plm_last_modify_time` varchar(100)  DEFAULT NULL COMMENT '修改时间',
    `scheme_id`            varchar(64)   DEFAULT NULL COMMENT '计划id',
    `parent_code`          varchar(100)  DEFAULT NULL COMMENT '父级id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目计划bom信息表';



-- ----------------------------
-- Table structure for ncf_form_actual_pay_milestone
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_actual_pay_milestone`;
CREATE TABLE `ncf_form_actual_pay_milestone`
(
    `pay_number`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付编号',
    `pay_req_user`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付申请人',
    `pay_req_start_time`        datetime                                                       NULL DEFAULT NULL COMMENT '支付申请发起时间',
    `pay_type`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付类型',
    `estimated_pay_time`        datetime                                                       NULL DEFAULT NULL COMMENT '预计付款时间',
    `milestone_desc`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '里程碑业务描述',
    `supplier_name`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '供应商名称',
    `currency`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '币种',
    `current_pay_total_amount`  decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '本次支付汇总金额',
    `pay_ratio`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付比例',
    `paid_amount`               decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '已支付金额',
    `estimated_acceptance_time` datetime                                                       NULL DEFAULT NULL COMMENT '合同预计验收时间',
    `acceptance_time`           datetime                                                       NULL DEFAULT NULL COMMENT '合同验收时间',
    `is_have_quality_amount`    bit(10)                                                        NULL DEFAULT NULL COMMENT '是否有质保金',
    `id`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`               datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`               datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`                    varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`                    int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`              int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `contract_name`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    `actual_delivery_time`      datetime                                                       NULL DEFAULT NULL COMMENT '实际交付时间',
    `reason`                    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          NULL COMMENT '原因',
    `actual_acceptance_time`    datetime                                                       NULL DEFAULT NULL COMMENT '实际验收时间',
    `is_acceptance_qualified`   bit(1)                                                         NULL DEFAULT NULL COMMENT '是否一次验收合格',
    `reason_of_undeliver`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '未按时交付验收原因',
    `is_deliver_on_time`        bit(1)                                                         NULL DEFAULT NULL COMMENT '是否按时交付',
    `is_onetime_acceptance`     bit(1)                                                         NULL DEFAULT NULL COMMENT '是否一次验收合格',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '合同支付里程碑（实际）'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_extend_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_extend_info`;
CREATE TABLE `ncf_form_contract_extend_info`
(
    `procurement_org_name`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购组织',
    `procurement_org_id`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购组织ID',
    `procurement_group_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购组',
    `procurement_group_id`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购组ID',
    `business_rsp_user`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '商务负责人',
    `business_rsp_user_id`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '商务负责人ID',
    `technical_rsp_user`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '技术负责人',
    `technical_rsp_user_id`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '技术负责人ID',
    `recommendation_basis`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '推荐依据',
    `negotiate_save_amount`  decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '节省总金额（RMB）',
    `sum_save_amount`        decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '渠道优化节省金额',
    `compare_save_amount`    decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '谈判节省金额',
    `channel_save_amount`    decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '与立项相比节省金额',
    `optimize_save_amount`   decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '优化采购节省金额',
    `is_process_amount`      bit(1)                                                         NULL DEFAULT NULL COMMENT '是否办理履约保证金',
    `prcess_amount_pay_way`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '保证金支付方式',
    `prcess_amount`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '保证金',
    `account_name`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '账户名称',
    `bank_name`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '银行账号',
    `bank_account`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '开户银行',
    `bank_code`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '银行代码',
    `id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`            datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`            datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`                 int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`           int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `contract_name`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '合同拓展信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_pay_milestone
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_pay_milestone`;
CREATE TABLE `ncf_form_contract_pay_milestone`
(
    `milestone_desc`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '里程碑业务描述',
    `in_out_payment`          bit(10)                                                        NULL DEFAULT NULL COMMENT '是否涉及境外付款',
    `contract_type`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同类型',
    `payment_type`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付类型',
    `est_payment_date`        datetime                                                       NULL DEFAULT NULL COMMENT '预计付款时间',
    `attachment_req`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '附件要求',
    `payment_ratio`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付比例',
    `contract_agreed_payment` decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '合同约定支付金额',
    `price_total_fixed`       bit(10)                                                        NULL DEFAULT NULL COMMENT '价格属性总价是否固定',
    `invoice_type`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '开票类型',
    `id`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`             datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`             datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`                  varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`                  int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`            int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `contract_name`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '合同支付里程碑（计划）'
  ROW_FORMAT = Dynamic;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pmsx_base_place
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_base_place`;
CREATE TABLE `pmsx_base_place`  (
                                    `code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地编码',
                                    `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地名称',
                                    `city` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地所在城市',
                                    `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                    `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                    `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                    `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                    `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                    `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                    `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                    `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                    `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                    `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                    `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '基地库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_basic_user
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_basic_user`;
CREATE TABLE `pmsx_basic_user`  (
                                    `person_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人员类型',
                                    `institute_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '研究所编号',
                                    `dept_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门编号',
                                    `company_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司编号',
                                    `user_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工号',
                                    `full_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
                                    `sex` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
                                    `personnel_nature` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人员性质',
                                    `company_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司',
                                    `dept_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门',
                                    `institute_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '研究所',
                                    `nation` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '民族',
                                    `id_card` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证号',
                                    `political_affiliation` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '政治面貌',
                                    `home_town` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '籍贯',
                                    `job_level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '职级',
                                    `now_position` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '现任职务',
                                    `job_title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '职称',
                                    `join_wordtime` datetime(0) NULL DEFAULT NULL COMMENT '参加工作时间',
                                    `add_zghtime` datetime(0) NULL DEFAULT NULL COMMENT '加入ZGH时间',
                                    `add_unittime` datetime(0) NULL DEFAULT NULL COMMENT '加入本单位时间',
                                    `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                    `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                    `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                    `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                    `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                    `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                    `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                    `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                    `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                    `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                    `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                    `date_of_birth` datetime(0) NULL DEFAULT NULL COMMENT '出生日期',
                                    `birth_place` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出生地',
                                    `join_word_time` datetime(0) NULL DEFAULT NULL COMMENT '参加工作时间',
                                    `add_zgh_time` datetime(0) NULL DEFAULT NULL COMMENT '加入ZGH时间',
                                    `add_unit_time` datetime(0) NULL DEFAULT NULL COMMENT '加入本单位时间',
                                    `birthplace` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出生地',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '员工能力库人员信息基础表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_basic_user_certificate
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_basic_user_certificate`;
CREATE TABLE `pmsx_basic_user_certificate`  (
                                                `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                                `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                                `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                                `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                                `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                                `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                                `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                                `status` int(11) NOT NULL COMMENT '状态',
                                                `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                `certificate_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '证书ID',
                                                `uses_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户编码',
                                                `obtain_date` datetime(0) NOT NULL COMMENT '获取日期',
                                                `review_date` datetime(0) NOT NULL COMMENT '复审日期',
                                                `issuing_authority` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '发证机构',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '基础用户证书关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_basic_user_extend_info
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_basic_user_extend_info`;
CREATE TABLE `pmsx_basic_user_extend_info`  (
                                                `process_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程名称',
                                                `initiator` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发起人',
                                                `initiation_time` datetime(0) NULL DEFAULT NULL COMMENT '发起时间',
                                                `priority` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优先级',
                                                `application_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请类型',
                                                `applicant` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
                                                `has_relative_in_group` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否有亲属在集团内',
                                                `relative_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '亲属姓名',
                                                `relative_position` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '亲属职务',
                                                `relative_company` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '亲属公司',
                                                `highest_education` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最高学历',
                                                `major` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所学专业',
                                                `title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '职称',
                                                `professional_technical_certificate` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '专业技术证书',
                                                `needs_employee_number` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否需要工号',
                                                `supplier_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商编码',
                                                `affiliated_supplier` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属供应商',
                                                `works_with_radioactive_materials` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否从事放射性工作',
                                                `job_content` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作内容',
                                                `permanent_service_location` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '常驻服务地点',
                                                `contract_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属合同名称',
                                                `contract_level` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同级别',
                                                `contract_number` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同编号',
                                                `project_based_staff` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否项目制人员',
                                                `entry_time` datetime(0) NULL DEFAULT NULL COMMENT '入场时间',
                                                `contact_information` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系方式',
                                                `completed_physical_examination` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否已完成体检',
                                                `department_head_project_manager` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分管项目经理',
                                                `card_or_authorization_choice` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '办卡或授权选择',
                                                `project_interface_person` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目接口人',
                                                `technical_configuration` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否技术配置',
                                                `entry_remarks` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入场备注',
                                                `departure_time` datetime(0) NULL DEFAULT NULL COMMENT '离场时间',
                                                `expected_departure_date` datetime(0) NULL DEFAULT NULL COMMENT '预计离岗时间',
                                                `authorization_cancelled` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否已取消授权',
                                                `violated_safety_regulations` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否违反相关安全规范',
                                                `completed_departure_physical` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否完成离职体检',
                                                `departure` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '离职备注',
                                                `locked_status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '锁定状态',
                                                `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                                `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                                `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                                `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                                `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                                `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                                `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                                `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                                `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                                `main_table_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主表ID',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '人员拓展信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_certificate_info
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_certificate_info`;
CREATE TABLE `pmsx_certificate_info`  (
                                          `is_need_renewal` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否需要复审',
                                          `renewal_date` datetime(0) NULL DEFAULT NULL COMMENT '复审日期',
                                          `acquisition_date` datetime(0) NULL DEFAULT NULL COMMENT '获取日期',
                                          `issuing_authority` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发证机构',
                                          `level` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '等级',
                                          `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                          `certificate_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '证书类型',
                                          `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                          `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                          `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                          `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                          `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                          `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                          `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                          `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                          `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                          `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                          `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                          `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                          `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                          `renewal_year_num` int(3) NULL DEFAULT NULL COMMENT '复审年限',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '证书信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_fixed_assets
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_fixed_assets`;
CREATE TABLE `pmsx_fixed_assets`  (
                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                      `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                      `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                      `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                      `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                      `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                      `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                      `code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产代码',
                                      `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产名称',
                                      `num_count` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
                                      `cost_center` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成本中心名称',
                                      `sp_model` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
                                      `is_need_verification` bit(1) NULL DEFAULT NULL COMMENT '是否需要检定',
                                      `next_verification_time` datetime(0) NULL DEFAULT NULL COMMENT '下次检定日期',
                                      `rsp_user_number` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人工号',
                                      `rsp_user_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人姓名',
                                      `use_user_number` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人工号',
                                      `use_user_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人姓名',
                                      `storage_location` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产存放地',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '固定资产能力库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_job_manage
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_job_manage`;
CREATE TABLE `pmsx_job_manage`  (
                                    `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                    `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                    `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                    `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                    `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                    `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                    `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                    `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                    `status` int(11) NOT NULL COMMENT '状态',
                                    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                    `plan_scheme_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目计划id',
                                    `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业类型（大修作业。日常作业）',
                                    `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工单号',
                                    `person_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人员Id',
                                    `rsp_user_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目负责人编号（工号）',
                                    `hight_risk` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '高风险（字典）',
                                    `is_importment` bit(1) NULL DEFAULT NULL COMMENT '是否重要作业',
                                    `n_or_o` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'N/O',
                                    `work_center` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作中心(默认值：SNPI)',
                                    `is_carry_tool` bit(1) NULL DEFAULT NULL COMMENT '是否自带工器具（如果为是：需要去详情 新增物资）',
                                    `anti_forfeign_level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '防异物等级',
                                    `first_execute` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '首次执行',
                                    `new_participants` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新人参与',
                                    `rsp_dept` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任中心（部门）',
                                    `job_base` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业基地（编号）',
                                    `job_base_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业基地名称',
                                    `start_examine` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开工审查（字典）',
                                    `is_major_project` bit(1) NULL DEFAULT NULL COMMENT '是否重大项目',
                                    `study_examine_status` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '研读审查状态',
                                    `close_date` datetime(0) NULL DEFAULT NULL COMMENT '关闭日期',
                                    `work_package_status` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作包状态',
                                    `begin_time` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
                                    `end_time` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间',
                                    `actual_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
                                    `actual_end_time` datetime(0) NULL DEFAULT NULL COMMENT '实际结束时间',
                                    `work_duration` int(11) NULL DEFAULT NULL COMMENT '计划工期',
                                    `job_desc` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业描述',
                                    `repair_round` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修轮次',
                                    `project_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目序列号',
                                    `project_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目名称',
                                    `plan_scheme_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目计划名称（冗余）',
                                    `important_project` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '重要项目',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '作业管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_major_repair_plan
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_major_repair_plan`;
CREATE TABLE `pmsx_major_repair_plan`  (
                                           `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                           `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                           `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                           `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                           `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                           `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                           `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                           `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                           `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                           `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                           `status` int(11) NOT NULL COMMENT '状态',
                                           `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                           `repair_round` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修轮次（全局唯一）',
                                           `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修名称',
                                           `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修类别',
                                           `begin_time` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
                                           `end_time` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间',
                                           `actual_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
                                           `actual_end_time` datetime(0) NULL DEFAULT NULL COMMENT '实际结束时间',
                                           `work_duration` int(11) NULL DEFAULT NULL COMMENT '工期（天数）',
                                           `repair_manager` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修经理',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '大修计划' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_material_manage
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_material_manage`;
CREATE TABLE `pmsx_material_manage`  (
                                         `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                         `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                         `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                         `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                         `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                         `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                         `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                         `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                         `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台ID',
                                         `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务组织Id',
                                         `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                         `logic_status` int(11) NULL DEFAULT NULL COMMENT '逻辑删除字段',
                                         `asset_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产类型',
                                         `asset_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产代码',
                                         `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产编码',
                                         `asset_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产名称',
                                         `cost_center_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成本中心',
                                         `specification_model` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
                                         `stock_num` int(11) NULL DEFAULT NULL COMMENT '库存数量',
                                         `next_verification_date` datetime(0) NULL DEFAULT NULL COMMENT '下次检定日期',
                                         `is_verification` bit(1) NULL DEFAULT NULL COMMENT '是否需要检定',
                                         `base_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物质所在基地',
                                         `rsp_user_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人工号',
                                         `rsp_user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人名称',
                                         `use_user_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人工号',
                                         `use_user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人名称',
                                         `enter_date` datetime(0) NULL DEFAULT NULL COMMENT '资产入库日期',
                                         `is_metering` bit(1) NULL DEFAULT NULL COMMENT '是否计量器具',
                                         `is_report` bit(1) NULL DEFAULT NULL COMMENT '是否向电厂报备',
                                         `is_overdue` bit(1) NULL DEFAULT NULL COMMENT '检定是否超期',
                                         `job_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物质应用作业(工单号)',
                                         `job_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业名称',
                                         `cost_center` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成本中心',
                                         `base_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地code',
                                         `input_stock_num` int(11) NULL DEFAULT NULL COMMENT '入库数量',
                                         `rsp_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人id',
                                         `use_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '物资库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_material_out_manage
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_material_out_manage`;
CREATE TABLE `pmsx_material_out_manage`  (
                                             `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                             `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                             `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                             `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                             `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                             `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                             `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                             `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                             `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                             `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                             `status` int(11) NOT NULL COMMENT '状态',
                                             `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                             `asset_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产类型',
                                             `asset_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产代码',
                                             `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产编码',
                                             `asset_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产名称',
                                             `cost_center_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成本中心',
                                             `specification_model` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
                                             `material_destination` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物质去向',
                                             `out_reason` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出库原因',
                                             `out_date` datetime(0) NULL DEFAULT NULL COMMENT '出库时间',
                                             `out_num` int(11) NULL DEFAULT NULL COMMENT '出库数量',
                                             `stock_num` int(11) NULL DEFAULT NULL COMMENT '库存数量',
                                             `next_verification_date` datetime(0) NULL DEFAULT NULL COMMENT '下次检定日期',
                                             `is_verification` bit(1) NULL DEFAULT NULL COMMENT '是否需要检定',
                                             `base_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物质所在基地',
                                             `rsp_user_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人工号',
                                             `rsp_user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人名称',
                                             `use_user_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人工号',
                                             `use_user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人名称',
                                             `enter_date` datetime(0) NULL DEFAULT NULL COMMENT '资产入库日期',
                                             `is_metering` bit(1) NULL DEFAULT NULL COMMENT '是否计量器具',
                                             `is_report` bit(1) NULL DEFAULT NULL COMMENT '是否向电厂报备',
                                             `is_overdue` bit(1) NULL DEFAULT NULL COMMENT '检定是否超期',
                                             `job_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物质应用作业(工单号)',
                                             `job_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业名称',
                                             `cost_center` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成本中心',
                                             `base_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地code',
                                             `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '台账类型',
                                             `rsp_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任人id',
                                             `use_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用人id',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '物质出库管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_non_fixed_assets
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_non_fixed_assets`;
CREATE TABLE `pmsx_non_fixed_assets`  (
                                          `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                          `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                          `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                          `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                          `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                          `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                          `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                          `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                          `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                          `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                          `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                          `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                          `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                          `barcode` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产条码',
                                          `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资产名称',
                                          `sp_model` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
                                          `is_need_verification` bit(1) NULL DEFAULT NULL COMMENT '是否需要检定',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '非固定资产标准库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_person_manage_ledger
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_person_manage_ledger`;
CREATE TABLE `pmsx_person_manage_ledger`  (
                                              `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                              `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                              `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                              `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                              `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                              `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                              `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                              `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                              `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                              `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                              `status` int(11) NOT NULL COMMENT '状态',
                                              `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                              `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编号',
                                              `base_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地名称',
                                              `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人员名称',
                                              `sex` bit(1) NULL DEFAULT NULL COMMENT '性别',
                                              `id_card` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证号码',
                                              `political_affiliation` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '政治面貌',
                                              `person_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人员状(进入、离开)',
                                              `nature` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性质',
                                              `company_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司名称',
                                              `dept_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门名称',
                                              `nstitute_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '研究所',
                                              `now_position` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '现任职务',
                                              `contact_dept` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口部门',
                                              `contact_office` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口科室',
                                              `contact_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口人',
                                              `enter_mode` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '进入形式',
                                              `work_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修/日常',
                                              `actual_enter_date` datetime(0) NULL DEFAULT NULL COMMENT '实际到厂时间',
                                              `actual_leave_date` datetime(0) NULL DEFAULT NULL COMMENT '实际离厂时间',
                                              `leave_reason` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '离厂原因',
                                              `leave_remark` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '离厂备注',
                                              `plan_enter_date` datetime(0) NULL DEFAULT NULL COMMENT '计划到厂时间',
                                              `plan_leave_date` datetime(0) NULL DEFAULT NULL COMMENT '计划离厂时间',
                                              `base_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地编码',
                                              `base_place_project` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地内承担的主要项目',
                                              `repair_round` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '大修轮次',
                                              `height_str` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身高/米',
                                              `weight_str` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '体重',
                                              `job_taboos` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '职业禁忌症',
                                              `chemical_toxin_use_job` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '化学品/毒物使用或接触作业',
                                              `work_res_person` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作负责人',
                                              `preparation_engineer` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '准备工程师',
                                              `qc_str` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'qc',
                                              `qc_work_year` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'QC工作年限',
                                              `fu_ti_saf_off` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '专职安全员',
                                              `pa_ti_saf_off` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '兼职安全员',
                                              `spe_task_cert_sit` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '特种作业持证情况(含无损检测资质',
                                              `participate_or_not` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一年内参与过集团内大修、高剂量人员',
                                              `newcomer` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新人',
                                              `newcomer_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新人类型',
                                              `newcomer_match_person` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新人对口人',
                                              `authorization_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '授权状态',
                                              `is_base_permanent` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否基地常驻',
                                              `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '人员管理台账' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_person_mange
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_person_mange`;
CREATE TABLE `pmsx_person_mange`  (
                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                      `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                      `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工号',
                                      `base_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地名称',
                                      `contact_dept` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口部门',
                                      `contact_office` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口科室',
                                      `contact_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口人',
                                      `enter_mode` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '进入形式',
                                      `work_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修/日常',
                                      `actual_enter_date` datetime(0) NULL DEFAULT NULL COMMENT '实际到厂时间',
                                      `actual_leave_date` datetime(0) NULL DEFAULT NULL COMMENT '实际离厂时间',
                                      `leave_reason` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '离厂原因',
                                      `leave_remark` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '离厂备注',
                                      `plan_enter_date` datetime(0) NULL DEFAULT NULL COMMENT '计划到厂时间',
                                      `plan_leave_date` datetime(0) NULL DEFAULT NULL COMMENT '计划离厂时间',
                                      `base_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地编码',
                                      `base_place_project` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地内承担的主要项目',
                                      `repair_round` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修轮次',
                                      `height_str` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身高/米',
                                      `weight_str` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '体重',
                                      `job_taboos` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '职业禁忌症',
                                      `chemical_toxin_use_job` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '化学品/毒物使用或接触作业',
                                      `work_res_person` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作负责人',
                                      `preparation_engineer` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '准备工程师',
                                      `qc_str` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'qc',
                                      `qc_work_year` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'QC工作年限',
                                      `fu_ti_saf_off` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '专职安全员',
                                      `pa_ti_saf_off` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '兼职安全员',
                                      `spe_task_cert_sit` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '特种作业持证情况(含无损检测资质',
                                      `participate_or_not` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一年内参与过集团内大修、高剂量人员',
                                      `newcomer` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新人',
                                      `newcomer_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新人类型',
                                      `newcomer_match_person` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新人对口人',
                                      `authorization_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '授权状态',
                                      `is_base_permanent` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否基地常驻',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '人员管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_safety_quality_env
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_safety_quality_env`;
CREATE TABLE `pmsx_safety_quality_env`  (
                                            `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                            `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                            `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                            `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                            `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                            `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                            `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                            `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                            `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                            `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                            `status` int(11) NOT NULL COMMENT '状态',
                                            `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                            `event_topic` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件主题',
                                            `event_level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件等级',
                                            `event_location` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件地点',
                                            `event_position` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件位置',
                                            `classification_type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类类型',
                                            `hidden_danger_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '隐患类型',
                                            `occurrence_date` datetime(0) NULL DEFAULT NULL COMMENT '事发日期',
                                            `rsp_center` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任中心',
                                            `is_major_repair` bit(1) NULL DEFAULT NULL COMMENT '是否大修',
                                            `hidden_event` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '隐患/事件领域',
                                            `event_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件类型',
                                            `is_closed` bit(1) NULL DEFAULT NULL COMMENT '是否已关闭',
                                            `current_process` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '当前流程',
                                            `pyramid_category` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '金字塔类别',
                                            `major_repair_turn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大修轮次',
                                            `is_assessed` bit(1) NULL DEFAULT NULL COMMENT '是否考核',
                                            `assessment_level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '考核级别',
                                            `hidden_danger_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '隐患编号',
                                            `reviewer_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查人ID',
                                            `reviewer_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查人编号',
                                            `reviewer_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查人名称',
                                            `dept_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查人所在部门Id',
                                            `dept_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查人所在部门名称',
                                            `rsp_dept_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '直接责任部门Id',
                                            `rsp_dept_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '直接责任部门名称',
                                            `event_desc` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件描述',
                                            `event_location_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间地点编码',
                                            `rsp_center_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任中心id',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '安质环' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_train_center
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_train_center`;
CREATE TABLE `pmsx_train_center`  (
                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                      `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                      `train_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训Id',
                                      `train_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训编码',
                                      `attend_center` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参培中心编号',
                                      `attend_center_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参培中心名称',
                                      `train_address` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训地点',
                                      `train_lecturer` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训讲师',
                                      `end_date` datetime(0) NULL DEFAULT NULL COMMENT '办结时间',
                                      `contrac_person` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '中心培训联络人',
                                      `train_num` int(11) NULL DEFAULT NULL COMMENT '参培人数',
                                      `expire_time` datetime(0) NULL DEFAULT NULL COMMENT '到期时间：办结时间+有效期限（月））',
                                      `expiration_month` int(3) NULL DEFAULT NULL COMMENT '有效期限（月）',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '培训中心管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_train_equivalent
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_train_equivalent`;
CREATE TABLE `pmsx_train_equivalent`  (
                                          `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                          `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                          `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                          `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                          `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                          `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                          `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                          `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                          `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                          `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                          `status` int(11) NOT NULL COMMENT '状态',
                                          `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                          `equivalent_base_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '等效基地编号',
                                          `equivalent_base_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '等效基地名称',
                                          `equivalent_date` datetime(0) NULL DEFAULT NULL COMMENT '等效认定时间',
                                          `user_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户编号',
                                          `train_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训编号',
                                          `is_equivalent` bit(1) NULL DEFAULT NULL COMMENT '是否等效',
                                          `uk_key` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '唯一标识',
                                          `train_center_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训中心ID',
                                          `base_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训基地编码',
                                          `base_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训基地名称',
                                          `user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名称',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '培训等效' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_train_manage
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_train_manage`;
CREATE TABLE `pmsx_train_manage`  (
                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                      `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                      `train_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训编码',
                                      `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训类型',
                                      `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训名称',
                                      `is_check` bit(1) NULL DEFAULT NULL COMMENT '是否考核',
                                      `base_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训基地编码',
                                      `base_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训基地名称',
                                      `complete_date` datetime(0) NULL DEFAULT NULL COMMENT '拟完成时间',
                                      `lesson_hour` decimal(10, 0) NULL DEFAULT NULL COMMENT '培训课时',
                                      `end_date` datetime(0) NULL DEFAULT NULL COMMENT '办结时间',
                                      `content` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训内容',
                                      `expire_time` datetime(0) NULL DEFAULT NULL COMMENT '到期时间：办结时间+有效期限（月））',
                                      `expiration_month` int(3) NULL DEFAULT NULL COMMENT '有效期限（月）',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '培训管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_train_person
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_train_person`;
CREATE TABLE `pmsx_train_person`  (
                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                      `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                      `train_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '培训id',
                                      `train_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '培训编码',
                                      `train_center_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参培中心关联表Id',
                                      `user_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工号',
                                      `full_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
                                      `company_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司编号',
                                      `company_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司名称',
                                      `dept_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门编号',
                                      `dept_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门名称',
                                      `institute_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '研究所编号',
                                      `institute_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '研究所名称',
                                      `sex` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
                                      `end_date` datetime(0) NULL DEFAULT NULL COMMENT '到期日期',
                                      `is_equivalent` bit(1) NULL DEFAULT NULL COMMENT '是否等效',
                                      `now_position` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '现任职务',
                                      `score` decimal(5, 2) NULL DEFAULT NULL COMMENT '成绩/分数',
                                      `is_ok` bit(1) NULL DEFAULT NULL COMMENT '是否合格',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '参培人员' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;




DROP TABLE IF EXISTS `pmsx_job_study_review`;
CREATE TABLE `pmsx_job_study_review` (
                                         `id` varchar(64) NOT NULL  COMMENT '主键',
                                         `class_name` varchar(64)   COMMENT '创建人',
                                         `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                         `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                         `owner_id` varchar(64)   COMMENT '拥有者',
                                         `create_time` datetime NOT NULL  COMMENT '创建时间',
                                         `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                         `remark` varchar(1024)   COMMENT '备注',
                                         `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                         `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                         `status` int NOT NULL  COMMENT '状态',
                                         `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                         `job_id` varchar(64)   COMMENT '作业id',
                                         `review_conclusion` varchar(128)   COMMENT '研读审查结论',
                                         `review_date` datetime   COMMENT '审查时间',
                                         `review_problem` varchar(256)   COMMENT '审查存在问题',
                                         `corrective_action` varchar(256)   COMMENT '纠正行动',
                                         `complete_date` datetime   COMMENT '完成时间',
                                         `progrem_version` varchar(64)   COMMENT '程序版本',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业研读审查';

DROP TABLE IF EXISTS `pmsx_major_repair_plan_meter_reduce`;
CREATE TABLE `pmsx_major_repair_plan_meter_reduce` (
                                                       `id` varchar(64) NOT NULL  COMMENT '主键',
                                                       `class_name` varchar(64)   COMMENT '创建人',
                                                       `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                       `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                       `owner_id` varchar(64)   COMMENT '拥有者',
                                                       `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                       `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                       `remark` varchar(1024)   COMMENT '备注',
                                                       `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                       `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                       `status` int NOT NULL  COMMENT '状态',
                                                       `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                       `job_manage_number` varchar(64)   COMMENT '工单号',
                                                       `major_repair_turn` varchar(64)   COMMENT '大修轮次',
                                                       `job_manage_id` varchar(64)   COMMENT '作业ID',
                                                       `is_reduce` bit   COMMENT 'is_reduce',
                                                       `number` varchar(64)   COMMENT '编号',
                                                       `belong_field` varchar(64)   COMMENT '领域',
                                                       `application_occasion` varchar(64)   COMMENT '技术应用窗口',
                                                       `application_base` varchar(128)   COMMENT '落地电厂',
                                                       `application_unittype` varchar(64)   COMMENT '应用机组类型',
                                                       `environment_meter_rate` decimal   COMMENT '现场环境计量率',
                                                       `reduce_hour` decimal   COMMENT '减少工时',
                                                       `conserve_meter` decimal   COMMENT '节约集体剂量',
                                                       `create_excellence` text(1)   COMMENT '创优技术或工作',
                                                       `content` text(1)   COMMENT '内容介绍',
                                                       `is_continue_use` bit   COMMENT '是否可沿用',
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='大修计划集体剂量降低';

DROP TABLE IF EXISTS `pmsx_major_repair_plan_economize`;
CREATE TABLE `pmsx_major_repair_plan_economize` (
                                                    `id` varchar(64) NOT NULL  COMMENT '主键',
                                                    `class_name` varchar(64)   COMMENT '创建人',
                                                    `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                    `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                    `owner_id` varchar(64)   COMMENT '拥有者',
                                                    `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                    `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                    `remark` varchar(1024)   COMMENT '备注',
                                                    `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                    `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                    `status` int NOT NULL  COMMENT '状态',
                                                    `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                    `job_manage_id` varchar(64)   COMMENT '作业ID',
                                                    `is_reduce` bit   COMMENT '集体剂量是否降低',
                                                    `close_date` datetime   COMMENT '结项日期',
                                                    `is_economize` bit   COMMENT '关键路径是否节约',
                                                    `number` varchar(64)   COMMENT '编号',
                                                    `optimize_field` varchar(64)   COMMENT '优化领域',
                                                    `major_repair_type` varchar(64)   COMMENT '大修类型',
                                                    `application_crew` varchar(64)   COMMENT '应用机组类型',
                                                    `plan_duration` decimal   COMMENT '计划工期(H)',
                                                    `actual_exe_duration` decimal   COMMENT '实际执行用时(H)',
                                                    `economize_duration` decimal   COMMENT '节约(H)',
                                                    `content` text(1)   COMMENT '内容介绍',
                                                    `delay_reason` varchar(1024)   COMMENT '延误原因',
                                                    `job_manage_number` varchar(64)   COMMENT '工单号',
                                                    `major_repair_turn` varchar(64)   COMMENT '大修轮次',
                                                    `inn_tech_or_work` varchar(128)   COMMENT '创优技术或工作',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='大修计划关键路径节约';

DROP TABLE IF EXISTS `pmsx_job_progress`;
CREATE TABLE `pmsx_job_progress` (
                                     `id` varchar(64) NOT NULL  COMMENT '主键',
                                     `class_name` varchar(64)   COMMENT '创建人',
                                     `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                     `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                     `owner_id` varchar(64)   COMMENT '拥有者',
                                     `create_time` datetime NOT NULL  COMMENT '创建时间',
                                     `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                     `remark` varchar(1024)   COMMENT '备注',
                                     `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                     `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                     `status` int NOT NULL  COMMENT '状态',
                                     `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                     `job_id` varchar(64)   COMMENT '作业id',
                                     `work_date` datetime   COMMENT '工作日期',
                                     `progress_schedule` decimal   COMMENT '总体进展',
                                     `progress_detail` varchar(1024)   COMMENT '工作进展',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业工作进展';

DROP TABLE IF EXISTS `pmsx_job_package`;
CREATE TABLE `pmsx_job_package` (
                                    `id` varchar(64) NOT NULL  COMMENT '主键',
                                    `class_name` varchar(64)   COMMENT '创建人',
                                    `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                    `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                    `owner_id` varchar(64)   COMMENT '拥有者',
                                    `create_time` datetime NOT NULL  COMMENT '创建时间',
                                    `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                    `remark` varchar(1024)   COMMENT '备注',
                                    `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                    `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                    `status` int NOT NULL  COMMENT '状态',
                                    `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                    `job_id` varchar(64)   COMMENT '作业id',
                                    `functional_location` varchar(128)   COMMENT '功能位置',
                                    `equipment_system` varchar(128) NOT NULL  COMMENT '设备/系统',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业工作包信息';

DROP TABLE IF EXISTS `pmsx_job_risk`;
CREATE TABLE `pmsx_job_risk` (
                                 `id` varchar(64) NOT NULL  COMMENT '主键',
                                 `class_name` varchar(64)   COMMENT '创建人',
                                 `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                 `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                 `owner_id` varchar(64)   COMMENT '拥有者',
                                 `create_time` datetime NOT NULL  COMMENT '创建时间',
                                 `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                 `remark` varchar(1024)   COMMENT '备注',
                                 `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                 `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                 `status` int NOT NULL  COMMENT '状态',
                                 `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                 `job_id` varchar(64) NOT NULL  COMMENT '风险ID',
                                 `functional_location` varchar(128)   COMMENT '功能位置',
                                 `risk_number` varchar(64)   COMMENT '风险号',
                                 `risk_type` varchar(64)   COMMENT '风险类型',
                                 `risk_text` text(0)   COMMENT '风险长文本',
                                 `risk_desc` text(0)   COMMENT '风险描述',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业与风险关联';

DROP TABLE IF EXISTS `pmsx_job_security_measure`;
CREATE TABLE `pmsx_job_security_measure` (
                                             `id` varchar(64) NOT NULL  COMMENT '主键',
                                             `class_name` varchar(64)   COMMENT '创建人',
                                             `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                             `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                             `owner_id` varchar(64)   COMMENT '拥有者',
                                             `create_time` datetime NOT NULL  COMMENT '创建时间',
                                             `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                             `remark` varchar(1024)   COMMENT '备注',
                                             `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                             `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                             `status` int NOT NULL  COMMENT '状态',
                                             `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                             `job_id` varchar(64)   COMMENT '作业id',
                                             `measure_code` varchar(64)   COMMENT '安全措施编码',
                                             `measure_desc` varchar(1024)   COMMENT '安全措施描述',
                                             `measure_type` varchar(20)   COMMENT '措施类型',
                                             `measure_text` text(0)   COMMENT '措施长文本',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业安措信息';


DROP TABLE IF EXISTS `pmsx_job_material`;
CREATE TABLE `pmsx_job_material` (
                                     `id` varchar(64) NOT NULL  COMMENT '主键',
                                     `class_name` varchar(64)   COMMENT '创建人',
                                     `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                     `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                     `owner_id` varchar(64)   COMMENT '拥有者',
                                     `create_time` datetime NOT NULL  COMMENT '创建时间',
                                     `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                     `remark` varchar(1024)   COMMENT '备注',
                                     `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                     `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                     `status` int NOT NULL  COMMENT '状态',
                                     `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                     `job_id` varchar(64)   COMMENT '作业ID',
                                     `repair_round` varchar(64)   COMMENT '大修轮次',
                                     `storage_place_code` varchar(64)   COMMENT '存放地编码',
                                     `storage_place_name` varchar(128)   COMMENT '存放地名称',
                                     `number` varchar(64)   COMMENT '资产编码/条形码',
                                     `asset_type` varchar(64)   COMMENT '资产类型',
                                     `asset_code` varchar(64)   COMMENT '资产代码',
                                     `asset_name` varchar(128)   COMMENT '资产名称',
                                     `cost_center_name` varchar(128)   COMMENT '成本中心',
                                     `specification_model` varchar(64)   COMMENT '规格型号',
                                     `stock_num` int   COMMENT '库存数量',
                                     `demand_num` int   COMMENT '需求数量',
                                     `next_verification_date` datetime   COMMENT '下次检定日期',
                                     `is_verification` bit   COMMENT '是否需要检定',
                                     `base_code` varchar(64)   COMMENT '物质所在基地编码',
                                     `base_name` varchar(64)   COMMENT '物质所在基地名称',
                                     `enter_date` datetime   COMMENT '资产入库日期',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业相关的物资';

DROP TABLE IF EXISTS `pmsx_author_person_job_post_equ`;
CREATE TABLE `pmsx_author_person_job_post_equ` (
                                                   `id` varchar(64) NOT NULL  COMMENT '主键',
                                                   `class_name` varchar(64)   COMMENT '创建人',
                                                   `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                   `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                   `owner_id` varchar(64)   COMMENT '拥有者',
                                                   `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                   `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                   `remark` varchar(1024)   COMMENT '备注',
                                                   `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                   `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                   `status` int NOT NULL  COMMENT '状态',
                                                   `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                   `person_id` varchar(64)   COMMENT '人员ID',
                                                   `person_code` varchar(64)   COMMENT '人员编号/工号',
                                                   `history_author_id` varchar(64)   COMMENT '历史岗位授权ID',
                                                   `author_id` varchar(64)   COMMENT '等效的现有授权ID',
                                                   `author_manage_id` varchar(64)   COMMENT '授权管理ID',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业人员岗位授权等效';

DROP TABLE IF EXISTS `pmsx_authorize_job_post_manage`;
CREATE TABLE `pmsx_authorize_job_post_manage` (
                                                  `id` varchar(64) NOT NULL  COMMENT '主键',
                                                  `class_name` varchar(64)   COMMENT '创建人',
                                                  `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                  `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                  `owner_id` varchar(64)   COMMENT '拥有者',
                                                  `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                  `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                  `remark` varchar(1024)   COMMENT '备注',
                                                  `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                  `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                  `status` int NOT NULL  COMMENT '状态',
                                                  `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                  `is_apply_job_equ` bit   COMMENT '是否申请岗位等效',
                                                  `authorization_guide` varchar(1024)   COMMENT '岗位授权指引（冗余）',
                                                  `job_id` varchar(64)   COMMENT '作业ID',
                                                  `job_header` varchar(256)   COMMENT '工作抬头',
                                                  `job_post_code` varchar(64)   COMMENT '作业岗位编码（冗余）',
                                                  `authorize_start_date` datetime   COMMENT '授权起始日期',
                                                  `authorize_end_date` datetime   COMMENT '授权到期日期（必须小于等于 起始时间+授权时间）',
                                                  `perons_code` varchar(64)   COMMENT '人员编号',
                                                  `person_id` varchar(64)   COMMENT '人员ID',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业岗位授权管理';

DROP TABLE IF EXISTS `pmsx_authorize_job_post_requirement`;
CREATE TABLE `pmsx_authorize_job_post_requirement` (
                                                       `id` varchar(64) NOT NULL  COMMENT '主键',
                                                       `class_name` varchar(64)   COMMENT '创建人',
                                                       `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                       `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                       `owner_id` varchar(64)   COMMENT '拥有者',
                                                       `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                       `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                       `remark` varchar(1024)   COMMENT '备注',
                                                       `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                       `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                       `status` int NOT NULL  COMMENT '状态',
                                                       `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                       `authorize_manage_id` varchar(64)   COMMENT '岗位授权管理Id',
                                                       `job_post_id` varchar(64)   COMMENT '岗位id',
                                                       `type` varchar(64)   COMMENT '要求类型(取得证书、通过培训)（冗余）',
                                                       `name` varchar(128)   COMMENT '要求名称（冗余）',
                                                       `certificate_number` varchar(64)   COMMENT '应取得证书number（冗余）',
                                                       `train_number` varchar(64)   COMMENT '应通过培训number',
                                                       `is_satisfy` bit   COMMENT '是否满足要求',
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='岗位授权要求检验表';


DROP TABLE IF EXISTS `pmsx_job_post_authorize`;
CREATE TABLE `pmsx_job_post_authorize` (
                                           `id` varchar(64) NOT NULL  COMMENT '主键',
                                           `class_name` varchar(64)   COMMENT '创建人',
                                           `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                           `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                           `owner_id` varchar(64)   COMMENT '拥有者',
                                           `create_time` datetime NOT NULL  COMMENT '创建时间',
                                           `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                           `remark` varchar(1024)   COMMENT '备注',
                                           `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                           `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                           `status` int NOT NULL  COMMENT '状态',
                                           `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                           `person_id` varchar(64)   COMMENT '人员ID',
                                           `person_code` varchar(64)   COMMENT '人员编号/工号',
                                           `job_id` varchar(64)   COMMENT '所属作业ID',
                                           `repair_round` varchar(64)   COMMENT '所属大修轮次',
                                           `plan_scheme_id` varchar(64)   COMMENT '所属计划ID',
                                           `job_post_code` varchar(64)   COMMENT '作业岗位编码',
                                           `is_authorization` int   COMMENT '是否满足授权*（0-待确认，1-不满足，2-满足）',
                                           `end_date` datetime   COMMENT '授权到期日期',
                                           `base_place_code` varchar(64)   COMMENT '人员所在基地',
                                           `base_place_name` varchar(64)   COMMENT '人员所在基地名称',
                                           `enter_base_date` datetime   COMMENT '进入基地时间',
                                           `authorize_status` int   COMMENT '授权状态（100-未授权，111-已授权）',
                                           `is_apply_job_equ` bit   COMMENT '是否申请岗位等效',
                                           `authorization_guide` varchar(1024)   COMMENT '岗位授权指引（冗余）',
                                           `authorize_start_date` datetime   COMMENT '授权起始日期',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业授权信息';


DROP TABLE IF EXISTS `pmsx_job_authorizationinformations`;
CREATE TABLE `pmsx_job_authorizationinformations` (
                                                      `base_place_code` varchar(256) DEFAULT NULL COMMENT '所属基地',
                                                      `job_positions` varchar(256) DEFAULT NULL COMMENT '作业岗位',
                                                      `end_date` datetime DEFAULT NULL COMMENT '授权到期日期',
                                                      `authorize_status` varchar(256) DEFAULT NULL COMMENT '授权状态',
                                                      `equivalent_ornot` varchar(256) DEFAULT NULL COMMENT '是否等效',
                                                      `equivalent_certification_base` varchar(256) DEFAULT NULL COMMENT '等效认定基地',
                                                      `authorization_records` varchar(256) DEFAULT NULL COMMENT '授权记录',
                                                      `id` varchar(64) NOT NULL COMMENT '主键',
                                                      `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
                                                      `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                      `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
                                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                                      `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
                                                      `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                      `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                      `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
                                                      `status` int(11) NOT NULL COMMENT '状态',
                                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                      `number` varchar(64) DEFAULT NULL COMMENT '编码',
                                                      `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位授权信息';

-- ----------------------------
-- Table structure for ncf_form_contract_claim
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_claim`;
CREATE TABLE `ncf_form_contract_claim`
(
    `claim_id`                      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '索赔编号',
    `claim_title`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '索赔标题',
    `claim_status`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '索赔状态',
    `claim_direction`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '索赔方向',
    `claim_process_time`            int(11)                                                        NULL DEFAULT NULL COMMENT '索赔处理时间',
    `claim_request_time`            datetime                                                       NULL DEFAULT NULL COMMENT '索赔申请时间',
    `cumulative_claim_amount`       decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '累计索赔金额（含本次）',
    `total_claim_pct_of_orig_price` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '总累计索赔占原合同价%',
    `id`                            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`                   datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`                   datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`                        varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`                        int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`                  int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `contract_name`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '合同索赔信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_require_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_require_info`;
CREATE TABLE `ncf_form_require_info`
(
    `time`               datetime                                                       NULL DEFAULT NULL COMMENT '时间',
    `amount`             decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '金额',
    `resp_dept`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '责任部门',
    `tech_user`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '技术责任人',
    `total_amt`          decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '汇总金额',
    `id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`        datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`        datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`             int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`       int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `unused_amt`         decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '剩余未使用金额',
    `used_amt`           decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '已使用金额',
    `project_ID`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购申请行号',
    `purch_req_doc_code` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购申请号',
    `contract_name`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '需求单'
  ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ncf_form_contract_line_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_line_info`;
CREATE TABLE `ncf_form_contract_line_info`
(
    `line_number`                       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同行项目',
    `num_count`                         decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '数量',
    `unit_price`                        decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '单价（含税）',
    `tax_rate`                          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '税率',
    `list_price`                        decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '原始价格',
    `update_price`                      decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '修改价格',
    `planned_delivery_date`             datetime                                                       NULL DEFAULT NULL COMMENT '计划交货日期',
    `procurement_applicant_number`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购申请号',
    `procurement_applicant_line_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采购申请行号',
    `id`                                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`                       datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`                       datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`                         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`                            varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`                       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`                            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`                            int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`                      int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`                            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `final_price`                       decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '最终价格',
    `contract_name`                     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '合同行项目信息'
  ROW_FORMAT = Dynamic;
DROP TABLE IF EXISTS `pms_job_post_library`;
CREATE TABLE `pms_job_post_library`  (
                                         `authorization_guide` tinytext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '岗位授权指引',
                                         `base_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属基地编码',
                                         `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '岗位名称',
                                         `authorization_time` datetime(0) NULL DEFAULT NULL COMMENT '授权时间（月）',
                                         `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                         `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                         `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                         `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                         `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                         `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                         `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                         `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                         `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                         `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                         `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                         `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                         `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '作业岗位库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_termination
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_termination`;
CREATE TABLE `ncf_form_contract_termination`
(
    `is_pre_sign_termination`     bit(10)                                                        NULL DEFAULT NULL COMMENT '是否签约前终止',
    `termination_request_date`    datetime                                                       NULL DEFAULT NULL COMMENT '终止申请日期',
    `contract_termination_amount` decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '合同终止金额',
    `id`                          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`                 datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`                 datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`                      varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`                      int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`                int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `contract_name`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '合同终止信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_purchase_app_wbs_wbs
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_purchase_app_wbs_wbs`;
CREATE TABLE `ncf_form_purchase_app_wbs_wbs`
(
    `project_number_name`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '项目编号/名称',
    `general_ledger_subject` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '总账科目',
    `wbs_number`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT 'WBS编号',
    `req_quantity`           decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '需求数量',
    `unit`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '单位',
    `delivery_time`          datetime                                                       NULL DEFAULT NULL COMMENT '交货时间',
    `unit_price`             decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '单价',
    `total_price`            decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '总价',
    `local_currency_amt`     decimal(20, 2)                                                 NULL DEFAULT NULL COMMENT '本位币金额',
    `id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '类名',
    `creator_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`            datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`            datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织ID',
    `status`                 int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status`           int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '主表ID',
    `contract_name`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '采购立项WBS信息'
  ROW_FORMAT = Dynamic;
-- ----------------------------

-- ----------------------------

ALTER TABLE `pms_project_approval_estimate_material`
    DROP COLUMN `material_id`,
    ADD COLUMN `number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL NULL COMMENT '物料编码' ,
    ADD COLUMN `average_price` decimal(11, 2) NULL DEFAULT NULL COMMENT '移动平均价格' ,
    ADD COLUMN `price_unit` varchar(64) NULL DEFAULT NULL COMMENT '价格单位'  ;

ALTER TABLE `pms_project_material_plan`
    MODIFY COLUMN `procurement_cycle` decimal(11, 2) NULL DEFAULT NULL COMMENT '采购周期' ;
-- ----------------------------
-- Table structure for pms_job_post_requirement
-- ----------------------------
DROP TABLE IF EXISTS `pms_job_post_requirement`;
CREATE TABLE `pms_job_post_requirement`  (
                                             `authorize_manage_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '岗位授权管理Id',
                                             `type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '要求类型',
                                             `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '要求名称',
                                             `certificate_number` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应取得证书',
                                             `train_number` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应通过培训',
                                             `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                             `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
                                             `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                             `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                             `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
                                             `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                             `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                             `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                             `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                             `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织ID',
                                             `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
                                             `logic_status` int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
                                             `number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                                             `job_post_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '岗位ID',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '岗位要求' ROW_FORMAT = Dynamic;