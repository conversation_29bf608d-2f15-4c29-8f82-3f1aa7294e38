CREATE TABLE `pmsx_production_index` (
                                         `id` varchar(64) NOT NULL COMMENT '主键',
                                         `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                         `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                         `modify_time` datetime NOT NULL COMMENT '修改时间',
                                         `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                         `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                         `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                         `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                         `status` int(11) NOT NULL COMMENT '状态',
                                         `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                         `index_number` varchar(64) NOT NULL COMMENT '指标编码',
                                         `index_type` varchar(64) NOT NULL COMMENT '考核类型',
                                         `index_target` varchar(256) NOT NULL COMMENT '目标值',
                                         `index_actual` varchar(256) NOT NULL COMMENT '实际值',
                                         `index_status` varchar(64) NOT NULL COMMENT '状态灯',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='生产看板指标维护';