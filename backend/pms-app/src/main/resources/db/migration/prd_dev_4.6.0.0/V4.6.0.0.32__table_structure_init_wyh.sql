-- orion_prd_dev.pmsx_adjustment_voucher definition

CREATE TABLE `pmsx_adjustment_voucher` (
  `id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '主键',
  `class_name` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '创建人',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `owner_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '修改人',
  `remark` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `company_code` varchar(255) DEFAULT NULL COMMENT '公司代码',
  `subject` varchar(255) DEFAULT NULL COMMENT '科目',
  `allocation` varchar(255) DEFAULT NULL COMMENT '分配',
  `posting_period` varchar(20) DEFAULT NULL COMMENT '过账期间',
  `voucher_num` varchar(64) DEFAULT NULL COMMENT '凭证编号',
  `voucher_date` datetime DEFAULT NULL COMMENT '凭证日期',
  `posting_date` datetime DEFAULT NULL COMMENT '过账日期',
  `local_currency_amount` decimal(20,2) DEFAULT NULL COMMENT '本币金额',
  `profit_center` varchar(255) DEFAULT NULL COMMENT '利润中心',
  `cost_center` varchar(255) DEFAULT NULL COMMENT '成本中心',
  `con_text` varchar(1000) DEFAULT NULL COMMENT '文本',
  `wbs_element` varchar(255) DEFAULT NULL COMMENT 'wbs要素',
  `trprt` varchar(255) DEFAULT NULL COMMENT 'Tr.prt',
  `committed_project` varchar(255) DEFAULT NULL COMMENT '承诺项目',
  `funding_center` varchar(255) DEFAULT NULL COMMENT '基金中心',
  `pay_reference_date` datetime DEFAULT NULL COMMENT '付款基准日期',
  `contract_id` varchar(64) DEFAULT NULL COMMENT '关联合同id',
  `contract_num` varchar(64) DEFAULT NULL COMMENT '合同编号',
  `contract_name` varchar(200) DEFAULT NULL COMMENT '合同名称',
  `milestone_name` varchar(200) DEFAULT NULL COMMENT '合同里程碑名称',
  `milestone_id` varchar(200) DEFAULT NULL COMMENT '合同里程碑id',
  `hanging_connect_status` int(11) DEFAULT NULL,
  KEY `pmsx_adjustment_voucher_voucher_num_IDX` (`voucher_num`) USING BTREE,
  KEY `pmsx_adjustment_voucher_contract_num_IDX` (`contract_num`) USING BTREE,
  KEY `pmsx_adjustment_voucher_contract_id_IDX` (`contract_id`) USING BTREE,
  KEY `pmsx_adjustment_voucher_milestone_id_IDX` (`milestone_id`) USING BTREE,
  KEY `pmsx_adjustment_voucher_milestone_name_IDX` (`milestone_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调账凭证数据表';


-- orion_prd_dev.pmsx_income_account_confirm definition

CREATE TABLE `pmsx_income_account_confirm` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `voucher_type` varchar(200) DEFAULT NULL COMMENT '凭证类型',
  `adj_account_voucher` varchar(10) DEFAULT NULL COMMENT '是否调账凭证',
  `confirm_status` varchar(10) DEFAULT NULL COMMENT '确认状态',
  `company_code` varchar(100) DEFAULT NULL COMMENT '公司代码',
  `subject` varchar(100) DEFAULT NULL COMMENT '科目',
  `distributive_code` varchar(100) DEFAULT NULL COMMENT '分配编码',
  `posting_date` datetime DEFAULT NULL COMMENT '过账期间',
  `voucher_num` varchar(64) DEFAULT NULL COMMENT '凭证编号',
  `voucher_date` datetime DEFAULT NULL COMMENT '凭证日期',
  `posting_period` varchar(10) DEFAULT NULL COMMENT '过账期间',
  `local_currency_amt` decimal(10,0) DEFAULT NULL COMMENT '本币金额',
  `profit_center` varchar(100) DEFAULT NULL COMMENT '利润中心',
  `cost_center` varchar(100) DEFAULT NULL COMMENT '成本中心',
  `con_text` varchar(200) DEFAULT NULL COMMENT '文本',
  `income_plan_num` varchar(64) DEFAULT NULL COMMENT '收入计划编号',
  `is_update` varchar(10) DEFAULT NULL COMMENT '是否修改凭证类型',
  `is_red` varchar(10) DEFAULT NULL COMMENT '是否标红',
  PRIMARY KEY (`id`),
  KEY `pmsx_income_account_confirm_voucher_num_IDX` (`voucher_num`) USING BTREE,
  KEY `pmsx_income_account_confirm_voucher_type_IDX` (`voucher_type`) USING BTREE,
  KEY `pmsx_income_account_confirm_income_plan_num_IDX` (`income_plan_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收入记账明细确认表';


-- orion_prd_dev.pmsx_connected_milestones definition

CREATE TABLE `pmsx_connected_milestones` (
  `milestone_name` varchar(255) DEFAULT NULL COMMENT '合同里程碑',
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `contract_status` int(11) DEFAULT NULL COMMENT '合同状态',
  `certificate_serial_number` varchar(64) DEFAULT NULL COMMENT '凭证编号',
  `posting_date` datetime DEFAULT NULL COMMENT '过账日期',
  `confirm_revenue_amount` decimal(10,0) DEFAULT NULL COMMENT '确认收入金额',
  `reverse_amount` decimal(10,0) DEFAULT NULL COMMENT '冲销暂估金额',
  `contract_num_remark` varchar(64) DEFAULT NULL COMMENT '合同编码(备注)',
  `mile_stone_remark` varchar(255) DEFAULT NULL COMMENT '里程碑名称(备注)',
  `contract_id` varchar(64) DEFAULT NULL COMMENT '关联合同id',
  `contract_num` varchar(64) DEFAULT NULL COMMENT '合同编号',
  `contract_name` varchar(255) DEFAULT NULL COMMENT '合同名称',
  `frame_contract_number` varchar(64) DEFAULT NULL COMMENT '关联合同(子订单)编号',
  `hanging_connect_status` int(1) DEFAULT '0' COMMENT '挂接状态:0未挂接，1已挂接',
  `party_A_dept_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '甲方单位id',
  `number` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '收入计划编号',
  `expertise_center` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '专业中心',
  `expertise_station` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '专业所',
  `estimate_invoice_date` datetime DEFAULT NULL COMMENT '收入计划月份',
  `income_confirm_type` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '收入确认类型',
  `billing_company` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '开票/收入确认公司',
  `party_A_dept_id_name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '甲方单位名称',
  `expertise_center_name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '专业中心名称',
  `expertise_station_name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '专业所名称',
  `billing_company_name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '开票/收入确认公司名称',
  `income_plan_id` varchar(64) DEFAULT NULL COMMENT '收入计划id',
  `milestone_id` varchar(64) DEFAULT NULL COMMENT '里程碑Id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='挂接里程碑';