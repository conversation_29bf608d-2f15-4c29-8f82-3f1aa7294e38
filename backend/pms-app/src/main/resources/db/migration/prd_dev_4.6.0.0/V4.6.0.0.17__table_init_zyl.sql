-- 资产转固申请主表
DROP TABLE IF EXISTS pmsx_project_asset_apply;
CREATE TABLE `pmsx_project_asset_apply`
(
    `id`           varchar(64)  NOT NULL COMMENT '主键',
    `class_name`   varchar(64) COMMENT '创建人',
    `creator_id`   varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64) COMMENT '拥有者',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)  NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) COMMENT '备注',
    `platform_id`  varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`       int          NOT NULL COMMENT '状态',
    `logic_status` int          NOT NULL COMMENT '逻辑删除字段',
    `project_id`   varchar(255) NOT NULL COMMENT '项目id',
    `number`       varchar(255) NOT NULL COMMENT '申请编号',
    `name`         varchar(255) NOT NULL COMMENT '申请名称',
    `res_person`   varchar(255) NOT NULL COMMENT '申请人',
    `res_dept`     varchar(255) NOT NULL COMMENT '申请部门',
    `res_time`     datetime     NOT NULL COMMENT '申请时间',
    `res_describe` varchar(1000) COMMENT '申请说明',
    `finish_time`  datetime COMMENT '转固时间（生效时间）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资产转固申请主表';

-- 资产转固申请子表wbs
DROP TABLE IF EXISTS pmsx_project_asset_apply_detail_wbs;
CREATE TABLE `pmsx_project_asset_apply_detail_wbs`
(
    `id`             varchar(64)  NOT NULL COMMENT '主键',
    `class_name`     varchar(64) COMMENT '创建人',
    `creator_id`     varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`    datetime     NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64) COMMENT '拥有者',
    `create_time`    datetime     NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64)  NOT NULL COMMENT '修改人',
    `remark`         varchar(1024) COMMENT '备注',
    `platform_id`    varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`         int          NOT NULL COMMENT '状态',
    `logic_status`   int          NOT NULL COMMENT '逻辑删除字段',
    `wbs_id`         varchar(255) NOT NULL COMMENT 'WBS预算数据信息Id',
    `asset_apply_id` varchar(255) NOT NULL COMMENT '主表id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资产转固申请详情表-WBS';

DROP TABLE IF EXISTS pmsx_project_asset_apply_detail_assets;
CREATE TABLE `pmsx_project_asset_apply_detail_assets` (
   `id` varchar(64) NOT NULL  COMMENT '主键',
   `class_name` varchar(64)   COMMENT '创建人',
   `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
   `modify_time` datetime NOT NULL  COMMENT '修改时间',
   `owner_id` varchar(64)   COMMENT '拥有者',
   `create_time` datetime NOT NULL  COMMENT '创建时间',
   `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
   `remark` varchar(1024)   COMMENT '备注',
   `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
   `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
   `status` int NOT NULL  COMMENT '状态',
   `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
   `asset_apply_id` varchar(255) NOT NULL  COMMENT '资产转固申请主表id',
   `asset_sync_id` varchar(255) NOT NULL  COMMENT '资产同步表id',
   `procurement_applicant_line_number` varchar(255)   COMMENT '采购申请行号',
   `project_id_name` varchar(255)   COMMENT '项目编号/名称',
   `general_ledger_subject` varchar(255)   COMMENT '总账科目',
   `asset` varchar(255)   COMMENT '资产',
   `required_quantity` varchar(255)   COMMENT '需求数量',
   `unit` varchar(50)   COMMENT '单位',
   `delivery_time` datetime   COMMENT '交货时间',
   `unit_price` varchar(50)   COMMENT '单价',
   `total_price` varchar(255)   COMMENT '总价',
   `local_currency_amount` decimal   COMMENT '本位币金额',
   `cost_center` varchar(255)   COMMENT '成本中心',
   `item` varchar(255)   COMMENT '物料',
   `item_group` varchar(255)   COMMENT '物料组',
   `internal_order` varchar(255)   COMMENT '内部订单',
   `wbs_id` varchar(255)   COMMENT 'WBS编号',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资产转固申请详情表-Asset';

ALTER TABLE `ncf_form_purchase_request_detail`
    ADD COLUMN `project_number` varchar(255) COMMENT '项目编码' ;

DROP TABLE IF EXISTS pmsx_asset_sync;
CREATE TABLE `pmsx_asset_sync` (
   `id` varchar(64) NOT NULL  COMMENT '主键',
   `class_name` varchar(64)   COMMENT '创建人',
   `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
   `modify_time` datetime NOT NULL  COMMENT '修改时间',
   `owner_id` varchar(64)   COMMENT '拥有者',
   `create_time` datetime NOT NULL  COMMENT '创建时间',
   `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
   `remark` varchar(1024)   COMMENT '备注',
   `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
   `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
   `status` int NOT NULL  COMMENT '状态',
   `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
   `procurement_applicant_line_number` varchar(255)   COMMENT '采购申请行号',
   `project_id_name` varchar(255)   COMMENT '项目编号/名称',
   `project_number` varchar(255)   COMMENT '项目编号',
   `project_name` varchar(255)   COMMENT '项目名称',
   `general_ledger_subject` varchar(255)   COMMENT '总账科目',
   `asset` varchar(255)   COMMENT '资产',
   `required_quantity` varchar(255)   COMMENT '需求数量',
   `unit` varchar(50)   COMMENT '单位',
   `delivery_time` datetime   COMMENT '交货时间',
   `unit_price` varchar(50)   COMMENT '单价',
   `total_price` varchar(255)   COMMENT '总价',
   `local_currency_amount` decimal   COMMENT '本位币金额',
   `cost_center` varchar(255)   COMMENT '成本中心',
   `item` varchar(255)   COMMENT '物料',
   `item_group` varchar(255)   COMMENT '物料组',
   `internal_order` varchar(255)   COMMENT '内部订单',
   `wbs_id` varchar(255)   COMMENT 'WBS编号',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资产同步表';

DROP TABLE IF EXISTS pmsx_asset_sync;
CREATE TABLE `pmsx_asset_sync` (
   `id` varchar(64) NOT NULL  COMMENT '主键',
   `class_name` varchar(64)   COMMENT '创建人',
   `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
   `modify_time` datetime NOT NULL  COMMENT '修改时间',
   `owner_id` varchar(64)   COMMENT '拥有者',
   `create_time` datetime NOT NULL  COMMENT '创建时间',
   `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
   `remark` varchar(1024)   COMMENT '备注',
   `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
   `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
   `status` int NOT NULL  COMMENT '状态',
   `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
   `procurement_applicant_line_number` varchar(255)   COMMENT '采购申请行号',
   `project_id_name` varchar(255)   COMMENT '项目编号/名称',
   `project_number` varchar(255)   COMMENT '项目编号',
   `project_name` varchar(255)   COMMENT '项目名称',
   `general_ledger_subject` varchar(255)   COMMENT '总账科目',
   `asset` varchar(255)   COMMENT '资产',
   `required_quantity` varchar(255)   COMMENT '需求数量',
   `unit` varchar(50)   COMMENT '单位',
   `delivery_time` datetime   COMMENT '交货时间',
   `unit_price` varchar(50)   COMMENT '单价',
   `total_price` varchar(255)   COMMENT '总价',
   `local_currency_amount` decimal   COMMENT '本位币金额',
   `cost_center` varchar(255)   COMMENT '成本中心',
   `item` varchar(255)   COMMENT '物料',
   `item_group` varchar(255)   COMMENT '物料组',
   `internal_order` varchar(255)   COMMENT '内部订单',
   `wbs_id` varchar(255)   COMMENT 'WBS编号',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资产同步表';


DROP TABLE IF EXISTS pms_plan_to_fixed_asset;
CREATE TABLE `pms_plan_to_fixed_asset` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) NOT NULL COMMENT '创建人',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime NOT NULL COMMENT '修改时间',
    `owner_id` varchar(64) NOT NULL COMMENT '拥有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) NOT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `to_id` varchar(64) NOT NULL COMMENT '副Id',
    `from_id` varchar(64) NOT NULL COMMENT '主id',
    `to_class` varchar(64) NOT NULL COMMENT '副类名',
    `from_class` varchar(64) NOT NULL COMMENT '主类名',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划与固定资产能力库关系';


DROP TABLE IF EXISTS pms_plan_to_ncf_formpurchase;
CREATE TABLE `pms_plan_to_ncf_formpurchase` (
   `id` varchar(64) NOT NULL COMMENT '主键',
   `class_name` varchar(64) NOT NULL COMMENT '创建人',
   `creator_id` varchar(64) NOT NULL COMMENT '创建人',
   `modify_time` datetime NOT NULL COMMENT '修改时间',
   `owner_id` varchar(64) NOT NULL COMMENT '拥有者',
   `create_time` datetime NOT NULL COMMENT '创建时间',
   `modify_id` varchar(64) NOT NULL COMMENT '修改人',
   `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
   `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
   `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
   `status` int(11) DEFAULT NULL COMMENT '状态',
   `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
   `to_id` varchar(64) NOT NULL COMMENT '副Id',
   `from_id` varchar(64) NOT NULL COMMENT '主id',
   `to_class` varchar(64) NOT NULL COMMENT '副类名',
   `from_class` varchar(64) NOT NULL COMMENT '主类名',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划与采购行';