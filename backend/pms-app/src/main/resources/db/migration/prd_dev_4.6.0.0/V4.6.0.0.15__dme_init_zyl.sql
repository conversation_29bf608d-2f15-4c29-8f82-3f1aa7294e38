-- 类定义
insert into dme_class (`id`,`package_id`,`table_name`,`class_name`,`code`,`parent_id`,`prefix`,`label`,`type`,`icon`,`description`,`is_abstract`,`is_extend`,`class_route`,`table_route`,`status`,`remark`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`sort`,`policy`,`revision`,`platform_id`,`unique_key`,`logic_status`,`owner_id`,`tree_flag`)
values ('gtjl1863783555089698816','eh6of839dd0486dd4e28a76134550b57834f','pmsx_project_asset_apply','ProjectAssetApply','7u2q',null,'pmsx','projectassetapply','common',null,null,null,null,null,null,1,'资产转固申请主表','314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 13:54:53',10,'txf71863782613078380543',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'314j1000000000000000000',0);

-- 类属性
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650112','gtjl1863783555089698816','id','Varchar',64,null,'id',null,null,null,null,1,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','主键','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650113','gtjl1863783555089698816','className','Varchar',64,null,'classname',null,null,null,null,0,1,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','创建人','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650114','gtjl1863783555089698816','creatorId','Varchar',64,null,'creatorid',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','创建人','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650115','gtjl1863783555089698816','modifyTime','DateTime',6,null,'modifytime',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','修改时间','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650116','gtjl1863783555089698816','ownerId','Varchar',64,null,'ownerid',null,null,null,null,0,1,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','拥有者','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650117','gtjl1863783555089698816','createTime','DateTime',6,null,'createtime',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','创建时间','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650118','gtjl1863783555089698816','modifyId','Varchar',64,null,'modifyid',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','修改人','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650119','gtjl1863783555089698816','remark','Varchar',1024,null,'remark',null,null,null,null,0,1,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','备注','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650120','gtjl1863783555089698816','platformId','Varchar',64,null,'platformid',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','平台ID','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650121','gtjl1863783555089698816','orgId','Varchar',64,null,'orgid',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','业务组织Id','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650122','gtjl1863783555089698816','status','Integer',1,null,'status',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','状态','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783555714650123','gtjl1863783555089698816','logicStatus','Integer',1,null,'logicstatus',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:13:22','314j1000000000000000000','2024-12-03 11:13:22','逻辑删除字段','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'CeS','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863783821969068032','gtjl1863783555089698816','projectId','Varchar',255,null,'projectid',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:14:25','314j1000000000000000000','2024-12-03 11:14:25','项目id','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863784158360637440','gtjl1863783555089698816','number','Varchar',255,null,'number',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:15:45','314j1000000000000000000','2024-12-03 11:15:45','申请编号','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863784232545292288','gtjl1863783555089698816','name','Varchar',255,null,'name',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:16:03','314j1000000000000000000','2024-12-03 11:16:03','申请名称','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863784550247043072','gtjl1863783555089698816','resPerson','Varchar',255,null,'resperson',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:17:19','314j1000000000000000000','2024-12-03 11:17:19','申请人','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863784706975600640','gtjl1863783555089698816','resDept','Varchar',255,null,'resdept',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:17:56','314j1000000000000000000','2024-12-03 11:17:56','申请部门','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863784853155483648','gtjl1863783555089698816','resTime','DateTime',6,null,'restime',null,null,null,null,0,0,1,null,null,'314j1000000000000000000','2024-12-03 11:18:31','314j1000000000000000000','2024-12-03 11:18:31','申请时间','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863785395177000960','gtjl1863783555089698816','resDescribe','Varchar',1000,null,'resdescribe',null,null,null,null,0,1,1,null,null,'314j1000000000000000000','2024-12-03 11:20:40','314j1000000000000000000','2024-12-03 11:20:56','申请说明','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');
insert into dme_class_attribute (`id`,`class_id`,`name`,`data_type`,`data_length`,`precision`,`label`,`rule`,`default_value`,`relation_class`,`relation_id`,`is_primary`,`is_null`,`status`,`description`,`sort`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_name`,`owner_id`)
values ('qfoh1863786631389384704','gtjl1863783555089698816','finishTime','DateTime',6,null,'finishtime',null,null,null,null,0,1,1,null,null,'314j1000000000000000000','2024-12-03 11:25:35','314j1000000000000000000','2024-12-03 11:25:35','转固时间（生效时间）','ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'ClassAttribute','314j1000000000000000000');

