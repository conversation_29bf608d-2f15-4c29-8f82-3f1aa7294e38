CREATE TABLE `pmsx_budget_record`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`               int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`         int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `budget_change_type`   varchar(100)   DEFAULT NULL COMMENT '预算修改类型',
    `budget_id`            varchar(64)    DEFAULT NULL COMMENT '预算Id',
    `budget_change_id`     varchar(64)    DEFAULT NULL COMMENT '预算修改单Id',
    `budget_change_number` varchar(100)   DEFAULT NULL COMMENT '预算修改编码',
    `budget_change_name`   varchar(100)   DEFAULT NULL COMMENT '预算修改表单名称',
    `operation_time`       datetime       DEFAULT NULL COMMENT '操作时间',
    `operation_person`     varchar(64)    DEFAULT NULL COMMENT '操作人',
    `change_money`         decimal(10, 0) DEFAULT NULL COMMENT '改变金额',
    `after_change_money`   decimal(10, 0) DEFAULT NULL COMMENT '改变后金额',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算改变记录';



CREATE TABLE `pmsx_budget_management`
(
    `id`                     varchar(64) NOT NULL COMMENT '主键',
    `class_name`             varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`            datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`                 int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`           int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `cost_center_id`         varchar(64)    DEFAULT NULL COMMENT '成本中心Id',
    `expense_subject_number` varchar(100)   DEFAULT NULL COMMENT '科目编码',
    `expense_subject_name`   varchar(100)   DEFAULT NULL COMMENT '科目名称',
    `time_type`              varchar(20)    DEFAULT NULL COMMENT '期间类型',
    `budget_time`            varchar(10)    DEFAULT NULL COMMENT '预算期间',
    `budget_object_type`     varchar(20)    DEFAULT NULL COMMENT '预算对象类型',
    `budget_object_id`       varchar(64)    DEFAULT NULL COMMENT '预算对象Id',
    `currency`               varchar(10)    DEFAULT NULL COMMENT '币种',
    `budget_money`           decimal(20, 0) DEFAULT NULL COMMENT '预算申请总金额',
    `january_money`          decimal(20, 0) DEFAULT NULL COMMENT '1月预算',
    `february_money`         decimal(20, 0) DEFAULT NULL COMMENT '2月预算',
    `march_money`            decimal(20, 0) DEFAULT NULL COMMENT '3月预算',
    `april_money`            decimal(20, 0) DEFAULT NULL COMMENT '4月预算',
    `may_money`              decimal(20, 0) DEFAULT NULL COMMENT '5月预算',
    `june_money`             decimal(20, 0) DEFAULT NULL COMMENT '6月预算',
    `july_money`             decimal(20, 0) DEFAULT NULL COMMENT '7月预算',
    `august_money`           decimal(20, 0) DEFAULT NULL COMMENT '8月预算',
    `september_money`        decimal(20, 0) DEFAULT NULL COMMENT '9月预算',
    `october_money`          decimal(20, 0) DEFAULT NULL COMMENT '10月预算',
    `november_money`         decimal(20, 0) DEFAULT NULL COMMENT '11月预算',
    `december_money`         decimal(20, 0) DEFAULT NULL COMMENT '12月预算',
    `first_quarter_money`    decimal(20, 0) DEFAULT NULL COMMENT '第一季度预算',
    `second_quarter`         decimal(20, 0) DEFAULT NULL COMMENT '第二季度预算',
    `third_quarter`          decimal(20, 0) DEFAULT NULL COMMENT '第三季度预算',
    `fourth_quarter`         decimal(20, 0) DEFAULT NULL COMMENT '第四季度预算',
    `name`                   varchar(100)   DEFAULT NULL COMMENT '预算名称',
    `number`                 varchar(100)   DEFAULT NULL COMMENT '编码',
    `residue_money`          decimal(20, 0) DEFAULT NULL COMMENT '预算剩余金额',
    `project_id`             varchar(64)    DEFAULT NULL COMMENT '项目Id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算管理表';

CREATE TABLE `pmsx_budget_expend`
(
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`    varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`   datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`   datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`     varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`        varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`        int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`  int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `expend_money`  decimal(20, 0) DEFAULT NULL COMMENT '支出金额',
    `residue_money` decimal(20, 0) DEFAULT NULL COMMENT '剩余金额',
    `form_id`       varchar(64)    DEFAULT NULL COMMENT '支出单Id',
    `project_id`    varchar(64)    DEFAULT NULL COMMENT '项目id',
    `sort`          int(11) DEFAULT NULL COMMENT '排序',
    `budget_id`     varchar(64)    DEFAULT NULL COMMENT '预算',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目支出';

-- ----------------------------
-- Table structure for pmsx_budget_expend_form
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_budget_expend_form`;
CREATE TABLE `pmsx_budget_expend_form`
(
    `id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `modify_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modify_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务组织Id',
    `status`                 int(11) NULL DEFAULT NULL COMMENT '状态',
    `logic_status`           int(11) NULL DEFAULT NULL COMMENT '逻辑删除字段',
    `number`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '成本支出编码',
    `expense_account_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科目名称',
    `expense_account_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科目编码',
    `expense_account_id`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科目Id',
    `cost_center_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '成本中心id',
    `occurrence_time`        datetime NULL DEFAULT NULL COMMENT '发生时间',
    `occurrence_person`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发生人',
    `expend_money`           decimal(20, 0) NULL DEFAULT NULL COMMENT '支出金额',
    `project_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目Id',
    `occupation_receive`     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '占用领用',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预算支出表单' ROW_FORMAT = Dynamic;



CREATE TABLE `pmsx_budget_application_form`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `name`         varchar(255)   DEFAULT NULL COMMENT '申请标题',
    `number`       varchar(100)   DEFAULT NULL COMMENT '申请预算单编码',
    `budget_money` decimal(20, 0) DEFAULT NULL COMMENT '申请预算金额',
    `budget_count` int(11) DEFAULT NULL COMMENT '申请预算条目数',
    `approval_id`  varchar(64)    DEFAULT NULL COMMENT '立项id',
    `project_id`   varchar(64)    DEFAULT NULL COMMENT '项目id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算申请单';

CREATE TABLE `pmsx_budget_application`
(
    `id`                     varchar(64) NOT NULL COMMENT '主键',
    `class_name`             varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`            datetime    NOT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`                 int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`           int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `name`                   varchar(100)   DEFAULT NULL COMMENT '预算名称',
    `number`                 varchar(100)   DEFAULT NULL COMMENT '预算申请编码',
    `cost_center_id`         varchar(64)    DEFAULT NULL COMMENT '成本中心Id',
    `expense_subject_number` varchar(100)   DEFAULT NULL COMMENT '科目编码',
    `expense_subject_name`   varchar(100)   DEFAULT NULL COMMENT '科目名称',
    `time_type`              varchar(20)    DEFAULT NULL COMMENT '期间类型',
    `budget_time`            varchar(10)    DEFAULT NULL COMMENT '预算期间',
    `budget_object_type`     varchar(20)    DEFAULT NULL COMMENT '预算对象类型',
    `budget_object_id`       varchar(64)    DEFAULT NULL COMMENT '预算对象Id',
    `currency`               varchar(10)    DEFAULT NULL COMMENT '币种',
    `budget_money`           decimal(20, 2) DEFAULT NULL COMMENT '预算申请总金额',
    `january_money`          decimal(20, 2) DEFAULT NULL COMMENT '1月预算',
    `february_money`         decimal(20, 0) DEFAULT NULL COMMENT '2月预算',
    `march_money`            decimal(20, 0) DEFAULT NULL COMMENT '3月预算',
    `april_money`            decimal(20, 0) DEFAULT NULL COMMENT '4月预算',
    `may_money`              decimal(20, 0) DEFAULT NULL COMMENT '5月预算',
    `june_money`             decimal(20, 0) DEFAULT NULL COMMENT '6月预算',
    `july_money`             decimal(20, 0) DEFAULT NULL COMMENT '7月预算',
    `august_money`           decimal(20, 0) DEFAULT NULL COMMENT '8月预算',
    `september_money`        decimal(20, 0) DEFAULT NULL COMMENT '9月预算',
    `october_money`          decimal(20, 0) DEFAULT NULL COMMENT '10月预算',
    `november_money`         decimal(20, 0) DEFAULT NULL COMMENT '11月预算',
    `december_money`         decimal(20, 0) DEFAULT NULL COMMENT '12月预算',
    `first_quarter_money`    decimal(20, 0) DEFAULT NULL COMMENT '第一季度预算',
    `second_quarter`         decimal(20, 0) DEFAULT NULL COMMENT '第二季度预算',
    `third_quarter`          decimal(20, 0) DEFAULT NULL COMMENT '第三季度预算',
    `fourth_quarter`         decimal(20, 0) DEFAULT NULL COMMENT '第四季度预算',
    `budget_management_id`   varchar(64)    DEFAULT NULL COMMENT '预算id',
    `form_id`                varchar(64)    DEFAULT NULL COMMENT '申请单Id',
    `is_estimate`            varchar(10)    DEFAULT NULL COMMENT '是否为概算',
    `approve_budget_money`   decimal(20, 0) DEFAULT NULL COMMENT '审核通过预算金额',
    `project_id`             varchar(64)    DEFAULT NULL COMMENT '项目id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算申请表';

CREATE TABLE `pmsx_budget_adjustment_form`
(
    `id`               varchar(64) NOT NULL COMMENT '主键',
    `class_name`       varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`       varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`      datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`        varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`           varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`      varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`           varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`           int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`     int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `project_id`       varchar(64)    DEFAULT NULL COMMENT '项目Id',
    `name`             varchar(100)   DEFAULT NULL COMMENT '预算调整名称',
    `number`           varchar(100)   DEFAULT NULL COMMENT '预算调整编码',
    `adjustment_money` decimal(10, 0) DEFAULT NULL COMMENT '调整金额',
    `adjustment_num`   int(11) DEFAULT NULL COMMENT '调整预算条目',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算调整表';


CREATE TABLE `pmsx_budget_adjustment`
(
    `budget_money`         decimal(20, 0) DEFAULT NULL COMMENT '预算申请总金额',
    `february_money`       decimal(20, 0) DEFAULT NULL COMMENT '2月预算',
    `january_money`        decimal(20, 0) DEFAULT NULL COMMENT '1月预算',
    `april_money`          decimal(20, 0) DEFAULT NULL COMMENT '4月预算',
    `june_money`           decimal(20, 0) DEFAULT NULL COMMENT '6月预算',
    `march_money`          decimal(20, 0) DEFAULT NULL COMMENT '3月预算',
    `july_money`           decimal(20, 0) DEFAULT NULL COMMENT '7月预算',
    `august_money`         decimal(20, 0) DEFAULT NULL COMMENT '8月预算',
    `october_money`        decimal(20, 0) DEFAULT NULL COMMENT '10月预算',
    `november_money`       decimal(20, 0) DEFAULT NULL COMMENT '11月预算',
    `december_money`       decimal(20, 0) DEFAULT NULL COMMENT '12月预算',
    `first_quarter_money`  decimal(20, 0) DEFAULT NULL COMMENT '第一季度预算',
    `second_quarter`       decimal(20, 0) DEFAULT NULL COMMENT '第二季度预算',
    `third_quarter`        decimal(20, 0) DEFAULT NULL COMMENT '第三季度预算',
    `fourth_quarter`       decimal(20, 0) DEFAULT NULL COMMENT '第四季度预算',
    `september_money`      decimal(20, 0) DEFAULT NULL COMMENT '9月预算',
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`               int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`         int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `may_money`            decimal(20, 0) DEFAULT NULL COMMENT '5月预算',
    `form_id`              varchar(64)    DEFAULT NULL COMMENT '调整单Id',
    `budget_id`            varchar(64)    DEFAULT NULL COMMENT '预算Id',
    `approve_budget_money` decimal(20, 2) DEFAULT NULL COMMENT '审核通过预算金额',
    `project_id`           varchar(64)    DEFAULT NULL COMMENT '项目id',
    `is_change`            varchar(2)     DEFAULT NULL COMMENT '是否是调整数据改变数据',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算调整单';


CREATE TABLE `pms_project_approval_estimate`
(
    `id`                  varchar(64) NOT NULL COMMENT '主键',
    `class_name`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`         datetime    NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime    NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64) NOT NULL COMMENT '修改人',
    `remark`              varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`              int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`        int(11) NOT NULL COMMENT '逻辑删除字段',
    `project_approval_id` varchar(64) NOT NULL COMMENT '项目立项id',
    `material_fee`        decimal(11, 2) DEFAULT NULL COMMENT '材料费概算',
    `people_num`          int(11) DEFAULT NULL COMMENT '人天数',
    `labor_fee`           decimal(11, 2) DEFAULT NULL COMMENT '工资及劳务费',
    `inter_trial_fee`     decimal(11, 2) DEFAULT NULL COMMENT '内部试验费',
    `out_trial_fee`       decimal(11, 2) DEFAULT NULL COMMENT '外部试验费',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算';

CREATE TABLE `pms_project_approval_estimate_expense_subject`
(
    `id`                  varchar(64) NOT NULL COMMENT '主键',
    `class_name`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`         datetime    NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime    NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64) NOT NULL COMMENT '修改人',
    `remark`              varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`              int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`        int(11) NOT NULL COMMENT '逻辑删除字段',
    `project_approval_id` varchar(64) NOT NULL COMMENT '项目立项id',
    `amount`              decimal(11, 2) DEFAULT NULL COMMENT '概算金额',
    `parent_id`           varchar(64) NOT NULL COMMENT '父级',
    `number`              varchar(64) NOT NULL COMMENT '编号',
    `formula`             tinytext COMMENT '公式',
    `name`                varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算科目';


CREATE TABLE `pms_project_approval_estimate_inter_out_trial_fees`
(
    `id`                  varchar(64) NOT NULL COMMENT '主键',
    `class_name`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`         datetime    NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime    NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64) NOT NULL COMMENT '修改人',
    `remark`              varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`              int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`        int(11) NOT NULL COMMENT '逻辑删除字段',
    `type`                varchar(11) NOT NULL COMMENT '类型：内部、外部',
    `num`                 int(11) DEFAULT NULL COMMENT '台数',
    `batch`               int(11) DEFAULT NULL COMMENT '批次',
    `price`               decimal(11, 2) DEFAULT NULL COMMENT '单价',
    `device_param`        varchar(255)   DEFAULT NULL COMMENT '设备参数',
    `trial_num`           int(11) DEFAULT NULL COMMENT '试验量',
    `unit`                varchar(64)    DEFAULT NULL COMMENT '单位',
    `unit_name`           varchar(255)   DEFAULT NULL COMMENT '单位名称',
    `trial_basic_data_id` varchar(64) NOT NULL COMMENT '内外部试验项目基础数据id',
    `project_approval_id` varchar(64) NOT NULL COMMENT '项目立项id',
    `trial_fee`           decimal(11, 2) DEFAULT NULL COMMENT '试验费用',
    `trial_day`           decimal(11, 2) DEFAULT NULL COMMENT '试验周期天数',
    `name`                varchar(255)   DEFAULT NULL COMMENT '试验项目名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算内外部试验费';

CREATE TABLE `pms_project_approval_estimate_labor_fee`
(
    `id`                  varchar(64)  NOT NULL COMMENT '主键',
    `class_name`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`         datetime     NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime     NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)  NOT NULL COMMENT '修改人',
    `remark`              varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`              int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`        int(11) NOT NULL COMMENT '逻辑删除字段',
    `user_id`             varchar(64)  NOT NULL COMMENT '用户id',
    `job_position_name`   varchar(255)   DEFAULT NULL COMMENT '职级',
    `job_position_rate`   varchar(64)    DEFAULT NULL COMMENT '职级费率',
    `job_name`            text COMMENT '岗位名称',
    `job_position_id`     varchar(64)    DEFAULT NULL COMMENT '职级id',
    `job_id`              text COMMENT '岗位id',
    `required_num`        decimal(11, 2) DEFAULT NULL COMMENT '人员需求数量',
    `people_days`         decimal(11, 2) DEFAULT NULL COMMENT '人天',
    `labor_fee`           decimal(11, 2) DEFAULT NULL COMMENT '工资费用',
    `project_approval_id` varchar(64)  NOT NULL COMMENT '项目立项id',
    `name`                varchar(255) NOT NULL COMMENT '员工名称',
    `people_day_fee`      decimal(11, 2) DEFAULT NULL COMMENT '人天费用',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算工资及劳务费';


CREATE TABLE `pms_project_approval_estimate_material`
(
    `id`                  varchar(64)  NOT NULL COMMENT '主键',
    `class_name`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`         datetime     NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime     NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)  NOT NULL COMMENT '修改人',
    `remark`              varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`              int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`        int(11) NOT NULL COMMENT '逻辑删除字段',
    `material_amount`     int(11) DEFAULT NULL COMMENT '物料数量',
    `material_price`      decimal(11, 2) DEFAULT NULL COMMENT '物料价格',
    `parent_id`           varchar(64)  NOT NULL COMMENT '父级id',
    `required_num`        int(11) DEFAULT NULL COMMENT '需求数量',
    `amount`              decimal(11, 2) DEFAULT NULL COMMENT '材料概算',
    `material_id`         varchar(64)  NOT NULL COMMENT '物料id',
    `project_approval_id` varchar(64)  NOT NULL COMMENT '项目立项id',
    `name`                varchar(255) NOT NULL COMMENT '物料结构名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算物料';



CREATE TABLE `pas_project_people_day_basic_data`
(
    `id`           varchar(64)  NOT NULL COMMENT '主键',
    `class_name`   varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)  NOT NULL COMMENT '修改人',
    `remark`       varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`       int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `name`         varchar(255) NOT NULL COMMENT '项目名称',
    `number`       varchar(255)   DEFAULT NULL COMMENT '项目编码',
    `people_num`   decimal(10, 0) DEFAULT NULL COMMENT '人天数量',
    `labor_fee`    decimal(10, 0) DEFAULT NULL COMMENT '劳务费',
    `project_id`   varchar(64)    DEFAULT NULL COMMENT '项目id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目人天基础数据';



-- 表结构
-- ----------------------------
-- Table structure for pmsx_quality_item
-- ----------------------------
CREATE TABLE `pmsx_quality_item`
(
    `id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`        datetime                                                       NOT NULL COMMENT '修改时间',
    `owner_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '修改人',
    `remark`             varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织Id',
    `status`             int(11) NOT NULL COMMENT '状态',
    `logic_status`       int(11) NOT NULL COMMENT '逻辑删除字段',
    `point`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质控点',
    `number`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `scheme`             varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '方案',
    `type`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
    `stage`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '阶段',
    `process`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过程',
    `activity`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动',
    `project_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目id',
    `affirm`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '确认人',
    `res_person`         varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '责任人',
    `relevance_scheme`   int(11) NULL DEFAULT 0 COMMENT '是否关联计划',
    `execute`            int(11) NULL DEFAULT NULL COMMENT '执行情况',
    `delivery_file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交付文件名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量管控项' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_quality_item_message
-- ----------------------------
CREATE TABLE `pmsx_quality_item_message`
(
    `id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`     datetime                                                     NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改人',
    `remark`          varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织Id',
    `logic_status`    int(11) NOT NULL COMMENT '逻辑删除字段',
    `quality_item_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管控id',
    `message_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息id',
    `finish`          int(11) NULL DEFAULT 0 COMMENT '是否确定',
    `status`          int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量管控项和消息关联关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_quality_item_scheme
-- ----------------------------
CREATE TABLE `pmsx_quality_item_scheme`
(
    `id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`       datetime                                                     NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改人',
    `remark`            varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织Id',
    `logic_status`      int(11) NOT NULL COMMENT '逻辑删除字段',
    `quality_item_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管控id',
    `project_scheme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `status`            int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量管控项和消息关联关系' ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;


ALTER TABLE `pms_risk_management`
    ADD COLUMN `is_need_approval` bit(1) COMMENT '是否需要审批' AFTER `data_source_id`,
ADD COLUMN `is_need_reminder` bit(1)  COMMENT '是否需要提醒' AFTER `is_need_approval`,
ADD COLUMN `is_typical_risk` bit(1)   COMMENT '是否典型风险' AFTER `is_need_reminder`;



CREATE TABLE `pas_risk_library`
(
    `id`                  varchar(64)   NOT NULL COMMENT '主键',
    `class_name`          varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64)   NOT NULL COMMENT '创建人',
    `modify_time`         datetime      NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime      NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)   NOT NULL COMMENT '修改人',
    `remark`              varchar(1024) NOT NULL COMMENT '备注',
    `platform_id`         varchar(64)   NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64)   NOT NULL COMMENT '业务组织Id',
    `status`              int(11) NOT NULL COMMENT '状态',
    `logic_status`        int(11) NOT NULL COMMENT '逻辑删除字段',
    `project_id`          varchar(64)   DEFAULT NULL COMMENT '项目ID',
    `name`                varchar(128)  NOT NULL COMMENT '名称',
    `number`              varchar(64)   NOT NULL COMMENT '风险编号',
    `risk_type`           varchar(64)   NOT NULL COMMENT '风险类型',
    `risk_probability`    varchar(64)   DEFAULT NULL COMMENT '发生概率',
    `risk_influence`      varchar(64)   DEFAULT NULL COMMENT '影响程度',
    `predict_start_time`  varchar(64)   DEFAULT NULL COMMENT '预估发生时间',
    `coping_strategy`     varchar(64)   DEFAULT NULL COMMENT '应对策略',
    `principal_id`        varchar(64)   NOT NULL COMMENT '负责人',
    `principal_name`      varchar(64)   NOT NULL COMMENT '负责人名称',
    `discern_person`      varchar(64)   NOT NULL COMMENT '识别人',
    `discern_person_name` varchar(64)   DEFAULT NULL COMMENT '识别人名称',
    `is_need_approval`    bit(1)        NOT NULL COMMENT '是否需要审批',
    `is_need_reminder`    bit(1)        NOT NULL COMMENT '是否需要提醒',
    `risk_id`             varchar(64)   DEFAULT NULL COMMENT '风险ID',
    `solutions`           varchar(1024) DEFAULT NULL COMMENT '应对措施',
    `predict_end_time`    datetime      DEFAULT NULL COMMENT '预计完成时间',
    `is_typical_risk`     bit(1)        DEFAULT NULL COMMENT '是否典型风险',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险库';

CREATE TABLE `pas_risk_impact_analysis`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `ext_field`    json          DEFAULT NULL COMMENT '扩展字段',
    `project_id`   varchar(64) NOT NULL COMMENT '项目ID',
    `risk_id`      varchar(64) NOT NULL COMMENT '风险ID',
    `impact_item`  varchar(64) NOT NULL COMMENT '风险影响项',
    `impact_level` varchar(64) NOT NULL COMMENT '影响程度',
    `measures`     varchar(1024) DEFAULT NULL COMMENT '应对措施',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险影响分析';



CREATE TABLE `pms_project_approval_estimate_template`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`          datetime    NOT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime    NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64) NOT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`               int(11) NOT NULL COMMENT '状态',
    `logic_status`         int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`               varchar(64)   DEFAULT NULL COMMENT '编号',
    `name`                 varchar(128)  DEFAULT NULL COMMENT '名称',
    `subject_number`       int(11) DEFAULT NULL COMMENT '科目数量',
    `template_classify_id` varchar(64)   DEFAULT '' COMMENT '所属分类ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算模板';



CREATE TABLE `pms_project_approval_estimate_template_classify`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `name`         varchar(128)  DEFAULT NULL COMMENT '名称',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`       varchar(64)   DEFAULT NULL COMMENT '编号',
    `parent_id`    varchar(64)   DEFAULT NULL COMMENT '父级ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算模板分类';


CREATE TABLE `pms_project_approval_estimate_template_expense_subject`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`          datetime    NOT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime    NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64) NOT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`               int(11) NOT NULL COMMENT '状态',
    `logic_status`         int(11) NOT NULL COMMENT '逻辑删除字段',
    `estimate_template_id` varchar(64)   DEFAULT NULL COMMENT '概算模板id',
    `expense_subject_id`   varchar(64)   DEFAULT NULL COMMENT '科目id',
    `required`             bit(1)        DEFAULT NULL COMMENT '是否必填',
    `parent_id`            varchar(64) NOT NULL COMMENT '父级id',
    `number`               varchar(64) NOT NULL COMMENT '编号',
    `name`                 varchar(255)  DEFAULT NULL COMMENT '名称',
    `formula`              text COMMENT '公式',
    `formula_name`         text COMMENT '公式名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='概算模板科目';
