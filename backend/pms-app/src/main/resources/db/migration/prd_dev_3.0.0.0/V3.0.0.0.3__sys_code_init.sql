-- -------------------------------------
-- 编码
-- -------------------------------------
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m129552676bdb4906bd1d7a756756fdd9', 'number', '9hi14b8799ae20fd4a2c81c5cf1b6bff0b94', 'ProjectRole', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 11:02:03', 'user00000000000000000100000000000000', '2022-09-20 11:02:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m12b7c29644d5415e92663cc142663b09', 'number', '9hi193022fe4c9b3404fb5e25c5acdd79ca2', 'Plan', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:57:56', 'user00000000000000000100000000000000', '2022-09-20 10:57:56', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1714111472864505856', 'number', '9hi11714109669456392192', 'CostCenter', 'SysCodeMappingRelation', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:49:56', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:49:56', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1714112413537841152', 'number', '9hi11714111728729632768', 'ExpenseSubject', 'SysCodeMappingRelation', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:53:41', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:53:41', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1714534756147965952', 'number', '9hi11714534296469024768', 'ProjectDeclare', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 14:51:55', 'user00000000000000000100000000000000', '2023-10-18 14:51:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1714823648457777152', 'number', '9hi1946e96158bd44854be390b61b59183eb', 'ProjectBudget', 'SysCodeMappingRelation', '预算', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:59:52', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 10:10:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, -1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1714823724978659328', 'number', '9hi1946e96158bd44854be390b61b59183eb', 'ExponseDetail', 'SysCodeMappingRelation', '预算执行', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 10:00:11', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 10:09:45', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, -1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1714826236181139456', 'number', '9hi11714822013065093120', 'ExponseDetail', 'SysCodeMappingRelation', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 10:10:09', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 10:12:46', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1714826401868730368', 'number', '9hi11714821796525760512', 'ProjectBudget', 'SysCodeMappingRelation', '预算编码', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 10:10:49', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 10:12:16', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1716715726125129728', 'number', '9hi11716709758926577664', 'ProjectReceivable', 'SysCodeMappingRelation', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:18:19', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:18:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1716715909344911360', 'number', '9hi11716714262640189440', 'ProjectFundsReceived', 'SysCodeMappingRelation', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:19:02', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:19:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1716727761323941888', 'number', '9hi11716710257927118848', 'ProjectContractSale', 'SysCodeMappingRelation', '项目销售合同编码', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 16:06:08', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:18:25', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1716728056103821312', 'number', '9hi11716710045288488960', 'ProjectContractPurchase', 'SysCodeMappingRelation', '项目采购合同编码', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 16:07:18', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:18:37', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1717184053515640832', 'number', '9hi11717057937727545344', 'ProjectContractChangeApply', 'SysCodeMappingRelation', '项目合同变更申请单编码', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:19:17', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:19:17', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1717373862603382784', 'number', '9hi11717372669382615040', 'GoodsServicePlan', 'SysCodeMappingRelation', '物资/服务计划编码规则', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:53:31', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:53:31', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1717544997064269824', 'number', '9hi11717544381411745792', 'ContractPayNodeConfirm', 'SysCodeMappingRelation', '合同节点支撑性材料审核单编码', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:13:32', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:13:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1721776587579473920', 'number', '9hi11721775277757054976', 'ProjectPurchaseOrder', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 14:28:22', 'user00000000000000000100000000000000', '2023-11-07 14:28:22', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1721825910967123968', 'number', '9hi11721823489977106432', 'ProjectApproval', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 17:44:22', 'user00000000000000000100000000000000', '2023-11-07 17:44:22', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1724379026526715904', 'number', '9hi11724352551387807744', 'WorkHourEstimate', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 18:49:32', 'user00000000000000000100000000000000', '2023-11-14 18:49:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1725137147582889984', 'number', '9hi11724353480187068416', 'WorkHourFill', 'SysCodeMappingRelation', '工时填报', 'as4f0cab09530b914568ac05c185f2627d57', 'as4f0cab09530b914568ac05c185f2627d57', '2023-11-16 21:02:02', 'as4f0cab09530b914568ac05c185f2627d57', '2023-11-16 21:02:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1725137214221991936', 'number', '9hi11724354627069165568', 'WorkHourFillManage', 'SysCodeMappingRelation', '工时填报(项目经理)', 'as4f0cab09530b914568ac05c185f2627d57', 'as4f0cab09530b914568ac05c185f2627d57', '2023-11-16 21:02:18', 'as4f0cab09530b914568ac05c185f2627d57', '2023-11-16 21:02:18', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1752668712214896640', 'number', '9hi11752659786522034176', 'DeliverGoals', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 20:22:39', 'user00000000000000000100000000000000', '2024-01-31 20:22:39', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, false, false);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1752668849829801984', 'number', '9hi11752662097998811136', 'IedBaseLineInfo', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 20:23:11', 'user00000000000000000100000000000000', '2024-02-01 20:35:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1753025734294781952', 'number', '9hi11752241454186643456', 'InterfaceManagement', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-01 20:01:19', 'user00000000000000000100000000000000', '2024-02-01 20:01:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1753039617944322048', 'number', '9hi11753037975173636096', 'IdeaForm', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-01 20:56:29', 'user00000000000000000100000000000000', '2024-02-01 20:56:29', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1f9872df84104d52b278d56f09b5a45b', 'number', '9hi1b81b8e61b0eb4bb385ff43c726778244', 'DeliverGoals', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-07-16 14:02:34', 'user00000000000000000100000000000000', '2022-10-24 16:10:21', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, 'e96m1f9872df84104d52b278d56f09b5a45b', 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m2030be015a5041349b37e2352db87045', 'number', '9hi153c4b49b990f452cb2a7d4bfc024ed44', 'Project', 'SysCodeMappingRelation', '项目编码映射', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-07-11 11:27:10', 'user00000000000000000100000000000000', '2022-10-24 16:10:22', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, 'e96m2030be015a5041349b37e2352db87045', 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m234c40916189458094ec835ca258abc1', 'number', '9hi13461e3c81417465495af792fb0547a46', 'RiskManagement', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:59:34', 'user00000000000000000100000000000000', '2022-09-20 10:59:34', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m23d468326edc4feaac0cd1381ed879cb', 'number', '9hi1b0ac4feb23564b4bb014a244c49fc94b', 'DemandManagement', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:59:19', 'user00000000000000000100000000000000', '2022-09-20 10:59:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m2d713fe6145449a6bfff5932f9c0b31a', 'number', '9hi1bc4cb1828d1f4d289b5bbd4fbb3d0536', 'PlanBaseLine', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:59:05', 'user00000000000000000100000000000000', '2022-09-20 10:59:05', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m2f72c7a53d0b409881379b3e5609e783', 'number', '9hi13fb663b1180e4041a14b8d2b82382e48', 'ProjectTaskStatus', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 11:01:56', 'user00000000000000000100000000000000', '2022-09-20 11:01:56', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m370f63b8a56649e598b3bed1d0de3d29', 'number', '9hi1370bca7eaaf24ed7b434642e8c191bfc', 'TaskSubject', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 11:00:05', 'user00000000000000000100000000000000', '2022-09-20 11:00:05', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m46b0957ed3054551b8328565514b6d21', 'number', '9hi1875c3675d9d04089ac673a28b6973b0c', 'QuestionManagement', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:59:26', 'user00000000000000000100000000000000', '2022-09-20 10:59:26', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m5a75df3d84e34d66bc0ae8ab4262f052', 'number', '9hi17edd0fd554c841cd9933e9cd43c5a5cd', 'AcceptanceForm', 'SysCodeMappingRelation', '采购计划验收单编码规则', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-05-03 14:36:55', 'user00000000000000000100000000000000', '2023-05-03 14:36:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m6f5e8f75291444ffb73456ff72062771', 'number', '9hi1f405f271183c4ca9b8f89f00c32506b7', 'Deliverable', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 17:09:52', 'user00000000000000000100000000000000', '2022-09-20 17:09:52', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m957763b769f844e79c093128b57a60b0', 'number', '9hi1ed2a5d041fd74c7a951c1645ed9600f0', 'TaskSubject', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-07 09:42:15', 'user00000000000000000100000000000000', '2023-03-07 09:42:15', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96mb154b7047d7540d8bf39f3bb057a346b', 'number', '9hi14a6f42bb875841f6bf7e2390e733eaea', 'RiskPlan', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:59:42', 'user00000000000000000100000000000000', '2022-09-20 10:59:42', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96mcaf95a8fb93d4c77aaa306aed7a0d4c2', 'number', '9hi13651759a37854a498288d32c27cbca08', 'PostProject', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:59:54', 'user00000000000000000100000000000000', '2022-09-20 10:59:54', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96me14408c0be234257a3bc9e2ef23aea43', 'number', '9hi1e8c3403a5cdf4eae8b7fb61e0692b371', 'Stakeholder', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-06 18:02:04', 'user00000000000000000100000000000000', '2023-03-06 18:02:04', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, true, true);




INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11714109669456392192', 'CBZX', null, '成本中心编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:42:46', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:47:29', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'f189a1084c534c29926587f31d975bc8', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11714111728729632768', 'FYKM', null, '费用科目', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:50:57', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:52:46', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '681dafe198804f219b10e3d6ef22250e', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11714534296469024768', 'XMSHB', null, '项目申报申请单编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 14:50:05', 'user00000000000000000100000000000000', '2023-10-18 15:00:36', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'a9d3676b8a25480199531d351022f3ff', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11714821796525760512', 'YS', null, '预算编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', '预算编码', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:52:31', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-25 09:21:52', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '72e27c395c834b768a2db04bcf3727f2', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11714822013065093120', 'YSZX', null, '预算执行编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', '预算执行编码', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:53:22', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-25 09:21:54', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '51ec3f9283784e60a346ca97f0346437', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11716709758926577664', 'YSK', null, '应收编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 14:54:36', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:12:43', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '111d2e2dcfc9420abbf7a2bcc3718fad', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11716710045288488960', 'CGHT', null, '项目合同(采购合同)编号', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-24 14:55:44', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:14:01', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'a6a52cede89f4a4ab5c7fec54b53479e', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11716710257927118848', 'XSHT', null, '项目合同(销售合同)编号', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-24 14:56:35', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:14:02', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '40e769bb406748afa38550d3cb7c6ffb', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11716714262640189440', 'SSK', null, '实收编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:12:30', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:15:20', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'e2075fca7a9d40e98753c627b1d1fa33', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11717057937727545344', 'HTBG', null, '合同变更申请单编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 13:58:08', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:14:00', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '395b3d8d30b34fdbb442302ac4259905', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11717372669382615040', 'ZYJH', null, '物资/服务计划编码规则', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', '物资/服务计划编码规则', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:48:46', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:53:52', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'c38ded66798043ff9f2d5067fb4677a9', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11717544381411745792', 'ZC', null, '合同节点支撑性材料审核单编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:11:06', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:14:07', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'efb137f555c24c978e0836be22542f55', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11721775277757054976', 'PO', null, '采购订单编号编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 14:23:10', 'user00000000000000000100000000000000', '2023-11-07 14:23:10', 101, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'e3d7479f4c08404bbbe9afc337b91e1f', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11721823489977106432', 'QP', null, '项目立项编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 17:34:45', 'user00000000000000000100000000000000', '2023-11-07 17:40:42', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '42eb658300a24a18ac8dac2116ef41a2', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11724352551387807744', 'TM', null, '工时预估编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:04:20', 'user00000000000000000100000000000000', '2023-11-14 17:04:20', 101, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '034d9d42615e4d29892b899a288e001e', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11724353480187068416', 'TF', null, '工时填报编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:08:01', 'user00000000000000000100000000000000', '2023-11-14 17:08:01', 101, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'acad7e1bf10a4d688bb3865eec79f744', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11724354627069165568', 'TD', null, '工时填报(项目经理)编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:12:35', 'user00000000000000000100000000000000', '2023-11-14 17:12:35', 101, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '3f5c8554f0024c81be9e667804970892', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11752241454186643456', 'JKGL', null, '接口管理', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-30 16:04:52', 'user00000000000000000100000000000000', '2024-01-30 16:08:46', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, '65a671075cab40cbadbc1b29fb5c0f40', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11752659786522034176', 'IED', null, '交付目标', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 19:47:11', 'user00000000000000000100000000000000', '2024-01-31 19:55:07', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, '2be0b347689a4dd2bcf74477f5de0b6e', null, null, null, 'A', null, 1, false, false);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11752662097998811136', 'IEDL', null, 'IED基线', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 19:56:22', 'user00000000000000000100000000000000', '2024-01-31 20:15:09', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, 'a70be2c962be40aebe94bd4f319618e2', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11753037975173636096', 'JKYYD', null, '意见单编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-01 20:49:58', 'user00000000000000000100000000000000', '2024-02-01 20:50:20', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1, 'd37e76d9cb6643e19e333a35a4b61199', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi13461e3c81417465495af792fb0547a46', 'XM007', null, '风险编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:49:59', 'user00000000000000000100000000000000', '2023-03-30 13:57:22', 0, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi13461e3c81417465495af792fb0547a46', 1, 'da4fddcf5f8a4c5d9bf7184f4a8605b8', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi13651759a37854a498288d32c27cbca08', 'XM016', null, '结项编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:29:51', 'user00000000000000000100000000000000', '2023-03-30 13:58:20', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi13651759a37854a498288d32c27cbca08', 1, 'd9ffc80ec3964df0bd439733a672e50d', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1370bca7eaaf24ed7b434642e8c191bfc', 'XM012', null, '计划科目编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:04:26', 'user00000000000000000100000000000000', '2023-03-30 13:57:12', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi1370bca7eaaf24ed7b434642e8c191bfc', 1, '033345aa95144565973dd2b476c7ab7c', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi13fb663b1180e4041a14b8d2b82382e48', 'XM011', null, '项目状态编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:02:09', 'user00000000000000000100000000000000', '2023-03-30 14:04:21', 0, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi13fb663b1180e4041a14b8d2b82382e48', 1, '54711f3a251d43e6b31fdf59f366465b', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi14a6f42bb875841f6bf7e2390e733eaea', 'XM009', null, '预案编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:54:38', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi14a6f42bb875841f6bf7e2390e733eaea', 1, 'f246e1d56f354e82ad6fd4c6f57419ac', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi14b8799ae20fd4a2c81c5cf1b6bff0b94', 'XM010', null, '项目角色编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:57:30', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi14b8799ae20fd4a2c81c5cf1b6bff0b94', 1, 'a2b40f0313b74352aeb43449632b541d', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi153c4b49b990f452cb2a7d4bfc024ed44', 'BM_YW_018', null, '项目编码', 'zmz82689476dd5dd434da2c7ebc32ca16869', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-06-24 15:16:07', 'user00000000000000000100000000000000', '2022-10-24 16:10:12', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi153c4b49b990f452cb2a7d4bfc024ed44', 1, 'f26bfa8e77fb4f018fb6208765fd376a', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1875c3675d9d04089ac673a28b6973b0c', 'XM006', null, '问题编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:47:36', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi1875c3675d9d04089ac673a28b6973b0c', 1, 'c94307cf4c294cbe9bf90652a96d90b4', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi193022fe4c9b3404fb5e25c5acdd79ca2', 'XM014', null, '任务编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:24:06', 'user00000000000000000100000000000000', '2022-10-24 16:10:11', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi193022fe4c9b3404fb5e25c5acdd79ca2', 1, '441b1535b69049daa5b3b29ab38a81bc', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1946e96158bd44854be390b61b59183eb', 'PM001', null, '项目编码（新）', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-04-03 20:47:19', 'user00000000000000000100000000000000', '2023-04-03 20:55:03', 101, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, 'c47b28aa0e4c4abf86349680da6c21dd', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1b0ac4feb23564b4bb014a244c49fc94b', 'XM005', null, '需求编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:45:43', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi1b0ac4feb23564b4bb014a244c49fc94b', 1, '0bfb1dc32bab4f9cab3c146f93e13d23', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1b81b8e61b0eb4bb385ff43c726778244', 'BM_YW_007', null, '交付目标编码', 'zmz82689476dd5dd434da2c7ebc32ca16869', 'SysCodeRules', '用户已存在规则，可能需要更新索引', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-06-24 14:55:10', 'user00000000000000000100000000000000', '2022-10-24 16:10:12', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi1b81b8e61b0eb4bb385ff43c726778244', 1, '7f2dff19c3b84629967041c311eef97e', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1bc4cb1828d1f4d289b5bbd4fbb3d0536', 'XM004', null, '基线编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:43:46', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi1bc4cb1828d1f4d289b5bbd4fbb3d0536', 1, '7aacaa303c9a4b7ba9bb3386fe544d41', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1e8c3403a5cdf4eae8b7fb61e0692b371', 'XM017', null, '干系人编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-06 17:56:35', 'user00000000000000000100000000000000', '2023-03-30 13:57:18', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, 1, '5d4d7d699146425ea3ecff670a6e1b8d', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1ed2a5d041fd74c7a951c1645ed9600f0', 'XM018', null, '任务科目编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-07 09:40:06', 'user00000000000000000100000000000000', '2023-03-07 09:40:06', 101, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, null, -1, 'c4f047c9624947f08ac2942f3c3e9e6b', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi1f405f271183c4ca9b8f89f00c32506b7', 'XM015', null, '交付物编码', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:24:19', 'user00000000000000000100000000000000', '2022-10-24 16:10:11', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', null, '9hi1f405f271183c4ca9b8f89f00c32506b7', 1, '47ae6a3970574021bc832de76816df53', null, null, null, 'A', null, 1, true, true);


-- -------------------------------------
-- 编码值
-- -------------------------------------
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd0409f50faa0b4f1f948ad016ff700c7a', '结项', '1', '9hi13651759a37854a498288d32c27cbca08', '', 'fixedValue', '', 'SW', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:31:04', 'user00000000000000000100000000000000', '2022-09-20 10:31:04', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd065f4477ab3d4ea69ce2974a9e9b8f4d', '风险', '1', '9hi13461e3c81417465495af792fb0547a46', '', 'fixedValue', '', 'FX', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:50:14', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd065f4477ab3d4ea69ce2974a9e9b8f4d', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd07d69fa33ef84e48ae748b9842d939cd', '需求', '1', '9hi1b0ac4feb23564b4bb014a244c49fc94b', '', 'fixedValue', '', 'XQ', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:46:15', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd07d69fa33ef84e48ae748b9842d939cd', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd0d45eb653e694794b90b03235aa48144', '基线', '1', '9hi1bc4cb1828d1f4d289b5bbd4fbb3d0536', '', 'fixedValue', '', 'JX', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:44:25', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd0d45eb653e694794b90b03235aa48144', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1067758865ba4019851954244ee9d52a', '间隔', '0', '9hi13461e3c81417465495af792fb0547a46', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:20:21', 'user00000000000000000100000000000000', '2022-09-20 10:20:21', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714110191320084480', '成本中心', '0', '9hi11714109669456392192', '', 'fixedValue', '', 'CBZX', '0', 1, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:44:51', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:44:51', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714110478684434432', '间隔', '0', '9hi11714109669456392192', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:45:59', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:45:59', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714110759916711936', '流水器', '0', '9hi11714109669456392192', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '', '', '3', 3, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:47:06', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:47:06', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714111895675514880', '费用科目', '0', '9hi11714111728729632768', '', 'fixedValue', '', 'FYKM', '0', 1, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:51:37', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:51:37', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714112000491171840', '间隔', '0', '9hi11714111728729632768', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:52:02', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:52:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714112123837263872', '流水器', '0', '9hi11714111728729632768', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '', '', '3', 3, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:52:32', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-17 10:52:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714547445276594176', '申请年份', '0', '9hi11714534296469024768', '', 'DATE_YYYY', '', '', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 15:42:20', 'user00000000000000000100000000000000', '2023-10-18 15:45:14', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714548324792782848', '申请单前缀', '0', '9hi11714534296469024768', '', 'fixedValue', '', 'XMSHB', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 15:45:50', 'user00000000000000000100000000000000', '2023-10-18 15:45:50', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714548396280500224', '申请单分隔符', '0', '9hi11714534296469024768', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 15:46:07', 'user00000000000000000100000000000000', '2023-10-18 15:46:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714548469513048064', '申请单分隔符2', '0', '9hi11714534296469024768', '', 'fixedValue', '', '-', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 15:46:25', 'user00000000000000000100000000000000', '2023-10-18 15:46:25', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714549139649581056', '申请单流水码', '0', '9hi11714534296469024768', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '', '', '4', 5, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 15:49:04', 'user00000000000000000100000000000000', '2023-10-18 15:49:04', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714822324483776512', '预算编码前缀', '0', '9hi11714821796525760512', '', 'fixedValue', '1', 'YS', '0', 1, '', '', 'SysCodeSegment', '预算编码前缀', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:54:37', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:54:37', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714822463873081344', '预算编码分隔符1', '0', '9hi11714821796525760512', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '预算编码分隔符1', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:55:10', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:55:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714822604394848256', '预算编码日期编码', '0', '9hi11714821796525760512', '', 'DATE_YYYY', '0', '', '0', 3, '', '', 'SysCodeSegment', '预算编码日期编码', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:55:43', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:55:43', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714822685214892032', '预算编码分隔符2', '0', '9hi11714821796525760512', '', 'fixedValue', '0', '-', '0', 4, '', '', 'SysCodeSegment', '预算编码分隔符2', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:56:03', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:56:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714822905382297600', '预算编码流水编码', '0', '9hi11714821796525760512', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '0', '', '3', 5, '', '0', 'SysCodeSegment', '预算编码分隔符2', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:56:55', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:56:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714823239500554240', '预算执行编码前缀', '0', '9hi11714822013065093120', '', 'fixedValue', '0', 'YSZX', '0', 1, '', '', 'SysCodeSegment', '预算执行编码前缀', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:58:15', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:58:15', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714823312670187520', '预算执行编码分割符1', '0', '9hi11714822013065093120', '', 'fixedValue', '0', '-', '0', 2, '', '', 'SysCodeSegment', '预算执行编码分割符1', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:58:32', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:58:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1714823457474338816', '预算执行流水编码', '0', '9hi11714822013065093120', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '0', '', '3', 3, '', '0', 'SysCodeSegment', '预算执行流水编码', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:59:07', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-19 09:59:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716711380037656576', '应收编码', '0', '9hi11716709758926577664', '', 'fixedValue', '', 'YSK', '0', 1, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:01:03', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:01:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716712973462134784', '间隔1', '0', '9hi11716709758926577664', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:07:23', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:07:23', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716713425935261696', '日期', '0', '9hi11716709758926577664', '', 'DATE_YYYY', '', '', '0', 3, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:09:10', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:09:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716713509657763840', '日期2', '0', '9hi11716709758926577664', '', 'DATE_M', '', '', '0', 4, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:09:30', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:09:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716713592147140608', '间隔2', '0', '9hi11716709758926577664', '', 'fixedValue', '', '-', '0', 5, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:09:50', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:09:50', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716713807449153536', '流水器', '0', '9hi11716709758926577664', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '', '', '3', 6, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:10:41', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:10:41', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716714495424061440', '实收编码', '0', '9hi11716714262640189440', '', 'fixedValue', '', 'SSK', '0', 1, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:13:25', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:13:25', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716714549509611520', '间隔1', '0', '9hi11716714262640189440', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:13:38', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:13:38', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716714696343805952', '间隔2', '0', '9hi11716714262640189440', '', 'fixedValue', '', '-', '0', 5, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:14:13', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:14:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716714766610980864', '日期1', '0', '9hi11716714262640189440', '', 'DATE_YYYY', '', '', '0', 3, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:14:30', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:14:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716714805878054912', '日期2', '0', '9hi11716714262640189440', '', 'DATE_M', '', '', '0', 4, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:14:39', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:14:39', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716714922706198528', '流水器', '0', '9hi11716714262640189440', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '', '', '3', 6, '', '', 'SysCodeSegment', '', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:15:07', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-24 15:15:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716725504712892416', '合同编号前缀', '0', '9hi11716710045288488960', '', 'fixedValue', '1', 'CGHT', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 15:57:10', 'user00000000000000000100000000000000', '2023-10-24 15:57:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716725753330262016', '合同编号年份', '0', '9hi11716710045288488960', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 15:58:09', 'user00000000000000000100000000000000', '2023-10-24 15:58:09', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716725941973278720', '合同编号分隔符', '0', '9hi11716710045288488960', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 15:58:54', 'user00000000000000000100000000000000', '2023-10-24 15:58:54', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716726207434973184', '合同编号流水码', '0', '9hi11716710045288488960', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 15:59:58', 'user00000000000000000100000000000000', '2023-10-24 15:59:58', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716726776123875328', '合同编码前缀', '0', '9hi11716710257927118848', '', 'fixedValue', '1', 'XSHT', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 16:02:13', 'user00000000000000000100000000000000', '2023-10-24 16:02:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716726859791851520', '合同编码年份', '0', '9hi11716710257927118848', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 16:02:33', 'user00000000000000000100000000000000', '2023-10-24 16:02:33', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716726924337995776', '合同编码分隔符', '0', '9hi11716710257927118848', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 16:02:49', 'user00000000000000000100000000000000', '2023-10-24 16:02:49', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1716727054256562176', '合同编码流水号', '0', '9hi11716710257927118848', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-24 16:03:20', 'user00000000000000000100000000000000', '2023-10-24 16:03:20', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717183238512041984', '合同变更申请单编码前缀', '0', '9hi11717057937727545344', '', 'fixedValue', '1', 'HTBG', '0', 1, '', '', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:16:02', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:16:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717183324268781568', '合同变更申请单编码年份', '0', '9hi11717057937727545344', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:16:23', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:16:23', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717183395223822336', '合同变更申请单编码分隔符', '0', '9hi11717057937727545344', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:16:40', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:16:40', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717183489897652224', '合同变更申请单编码流水码', '0', '9hi11717057937727545344', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:17:02', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-25 22:17:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717372910899027968', '物资服务计划前缀', '0', '9hi11717372669382615040', '', 'fixedValue', '', 'ZYJH', '0', 1, '', '', 'SysCodeSegment', '物资服务计划前缀', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:49:44', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:49:44', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717373110304628736', '物资服务计划年', '0', '9hi11717372669382615040', '', 'DATE_YYYY', '', '', '0', 2, '', '', 'SysCodeSegment', '物资服务计划年', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:50:31', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:50:31', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717373181821706240', '物资服务计划月', '0', '9hi11717372669382615040', '', 'DATE_M', '', '', '0', 3, '', '', 'SysCodeSegment', '物资服务计划月', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:50:48', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:50:48', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717373273341419520', '物资服务计划间隔1', '0', '9hi11717372669382615040', '', 'fixedValue', '', '-', '0', 4, '', '', 'SysCodeSegment', '物资服务计划间隔1', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:51:10', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:51:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717373470750531584', '物资服务计划流水器', '0', '9hi11717372669382615040', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '', '', '4', 5, '', '0', 'SysCodeSegment', '物资服务计划流水器', 'as4fadee5558057b4bc599b68be2a97b2584', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:51:57', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-26 10:51:57', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717544566846119936', '审核单编码前缀', '0', '9hi11717544381411745792', '', 'fixedValue', '1', 'ZC', '0', 1, '', '', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:11:50', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:11:50', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717544636794527744', '审核单编码年份', '0', '9hi11717544381411745792', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:12:07', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:12:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717544694814334976', '审核单编码分隔符', '0', '9hi11717544381411745792', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:12:20', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:12:20', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1717544893519486976', '审核单编码流水号', '0', '9hi11717544381411745792', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'as4f27606edb27dd416280541088f10efdcf', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:13:08', 'as4f27606edb27dd416280541088f10efdcf', '2023-10-26 22:13:08', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721775556221091840', '采购订单编号编码前缀', '0', '9hi11721775277757054976', '', 'fixedValue', '1', 'PO', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 14:24:16', 'user00000000000000000100000000000000', '2023-11-07 14:24:16', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721775744725696512', '采购订单编号编码年', '0', '9hi11721775277757054976', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 14:25:01', 'user00000000000000000100000000000000', '2023-11-07 14:25:01', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721775818360897536', '采购订单编号编码月', '0', '9hi11721775277757054976', '', 'DATE_M', '1', '', '0', 3, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 14:25:19', 'user00000000000000000100000000000000', '2023-11-07 14:27:24', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721775983381594112', '采购订单编号编码间隔', '0', '9hi11721775277757054976', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 14:25:58', 'user00000000000000000100000000000000', '2023-11-07 14:25:58', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721776153980715008', '采购订单编号编码流水号', '0', '9hi11721775277757054976', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 5, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 14:26:39', 'user00000000000000000100000000000000', '2023-11-07 14:26:39', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721824044401180672', '项目立项', '0', '9hi11721823489977106432', '', 'fixedValue', '', 'QP', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 17:36:57', 'user00000000000000000100000000000000', '2023-11-07 17:36:57', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721824194683092992', '年', '0', '9hi11721823489977106432', '', 'DATE_YYYY', '', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 17:37:33', 'user00000000000000000100000000000000', '2023-11-07 17:37:33', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721824310626238464', '间隔', '0', '9hi11721823489977106432', '', 'fixedValue', '', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 17:38:00', 'user00000000000000000100000000000000', '2023-11-07 17:38:00', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1721824448782417920', '流水号', '0', '9hi11721823489977106432', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '', '', '4', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-07 17:38:33', 'user00000000000000000100000000000000', '2023-11-07 17:38:33', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724352713430548480', '工时预估编码前缀', '0', '9hi11724352551387807744', '', 'fixedValue', '1', 'TM', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:04:58', 'user00000000000000000100000000000000', '2023-11-14 17:04:58', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724352827444314112', '工时预估编码年', '0', '9hi11724352551387807744', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:05:26', 'user00000000000000000100000000000000', '2023-11-14 17:05:26', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724353122173861888', '工时预估编码间隔', '0', '9hi11724352551387807744', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:06:36', 'user00000000000000000100000000000000', '2023-11-14 17:06:36', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724353312981139456', '工时预估编码流水', '0', '9hi11724352551387807744', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:07:21', 'user00000000000000000100000000000000', '2023-11-14 17:07:21', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724353660173041664', '工时填报编码前缀', '0', '9hi11724353480187068416', '', 'fixedValue', '1', 'TF', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:08:44', 'user00000000000000000100000000000000', '2023-11-14 17:08:44', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724353753395642368', '工时填报编码年', '0', '9hi11724353480187068416', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:09:06', 'user00000000000000000100000000000000', '2023-11-14 17:09:06', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724354339520266240', '工时填报编码间隔', '0', '9hi11724353480187068416', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:11:26', 'user00000000000000000100000000000000', '2023-11-14 17:11:26', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724354437197217792', '工时填报编码流水', '0', '9hi11724353480187068416', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:11:49', 'user00000000000000000100000000000000', '2023-11-14 17:11:49', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724354886717554688', '工时填报(项目经理)编码', '0', '9hi11724354627069165568', '', 'fixedValue', '1', 'TD', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:13:37', 'user00000000000000000100000000000000', '2023-11-14 17:13:37', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724354979147431936', '工时填报(项目经理)编码年', '0', '9hi11724354627069165568', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:13:59', 'user00000000000000000100000000000000', '2023-11-14 17:13:59', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724355041600618496', '工时填报(项目经理)编码间隔', '0', '9hi11724354627069165568', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:14:14', 'user00000000000000000100000000000000', '2023-11-14 17:14:14', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1724355146785374208', '工时填报(项目经理)编码流水', '0', '9hi11724354627069165568', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-14 17:14:39', 'user00000000000000000100000000000000', '2023-11-14 17:14:39', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752241628665495552', '接口管理', '0', '9hi11752241454186643456', '', 'fixedValue', '1', 'JKGL', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-30 16:05:34', 'user00000000000000000100000000000000', '2024-01-30 16:05:34', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752241709170966528', '年度', '0', '9hi11752241454186643456', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-30 16:05:53', 'user00000000000000000100000000000000', '2024-01-30 16:05:53', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752241768331624448', '间隔', '0', '9hi11752241454186643456', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-30 16:06:07', 'user00000000000000000100000000000000', '2024-01-30 16:06:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752241865916301312', '流水号', '0', '9hi11752241454186643456', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '0', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-30 16:06:31', 'user00000000000000000100000000000000', '2024-01-30 16:06:31', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752660399775416320', 'PMS', '0', '9hi11752659786522034176', '', 'fixedValue', '', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 19:49:37', 'user00000000000000000100000000000000', '2024-01-31 19:49:37', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752660562212421632', '交付目标', '1', '9hi11752659786522034176', '', 'fixedValue', '', 'IED', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 19:50:16', 'user00000000000000000100000000000000', '2024-01-31 19:50:16', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752660655707652096', '间隔', '0', '9hi11752659786522034176', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 19:50:38', 'user00000000000000000100000000000000', '2024-01-31 19:50:38', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752660782828617728', '流水器', '0', '9hi11752659786522034176', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '-', '5', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 19:51:08', 'user00000000000000000100000000000000', '2024-01-31 19:51:08', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752663615896539136', 'IED基线', '0', '9hi11752662097998811136', '', 'fixedValue', '', 'IEDL', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 20:02:24', 'user00000000000000000100000000000000', '2024-01-31 20:05:06', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752664097981452288', '间隔1', '0', '9hi11752662097998811136', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 20:04:19', 'user00000000000000000100000000000000', '2024-01-31 20:05:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752664272892317696', '日期', '1', '9hi11752662097998811136', '', 'DATE_YYYY', '', '', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 20:05:00', 'user00000000000000000100000000000000', '2024-01-31 20:05:00', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752664373383647232', '间隔2', '0', '9hi11752662097998811136', '', 'fixedValue', '', '-', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 20:05:24', 'user00000000000000000100000000000000', '2024-01-31 20:05:24', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1752664542858694656', '流水器', '0', '9hi11752662097998811136', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 5, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 20:06:05', 'user00000000000000000100000000000000', '2024-01-31 20:06:05', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1753038833412374528', '流水号', '0', '9hi11753037975173636096', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '1', '', '0', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-01 20:53:22', 'user00000000000000000100000000000000', '2024-02-02 11:58:00', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1753266252908052480', '意见单', '0', '9hi11753037975173636096', '', 'fixedValue', '1', 'YJD', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-02 11:57:03', 'user00000000000000000100000000000000', '2024-02-02 11:57:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1753266366338809856', '年度', '0', '9hi11753037975173636096', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-02 11:57:31', 'user00000000000000000100000000000000', '2024-02-02 11:57:31', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1753266447142076416', '间隔', '0', '9hi11753037975173636096', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-02 11:57:50', 'user00000000000000000100000000000000', '2024-02-02 11:57:50', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd17d124b676b944f4b282f48f35c64cdd', '间隔', '0', '9hi13651759a37854a498288d32c27cbca08', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:30:29', 'user00000000000000000100000000000000', '2022-09-20 10:30:29', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1a791bdceabd43a18da43abe5378ec7f', '间隔', '0', '9hi1b0ac4feb23564b4bb014a244c49fc94b', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:16:41', 'user00000000000000000100000000000000', '2022-09-20 10:16:41', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1d36f07ae6ff4a07be9f8d2653e5fce4', '流水码', '0', '9hi1875c3675d9d04089ac673a28b6973b0c', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:48:41', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd1d36f07ae6ff4a07be9f8d2653e5fce4', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd2068117751604c669af4560b58ca24b9', 'PMS', '0', '9hi1e8c3403a5cdf4eae8b7fb61e0692b371', '', 'fixedValue', '1', 'PMS', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-06 18:01:13', 'user00000000000000000100000000000000', '2023-03-06 18:01:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd21bdecc279a34877abf81ad52fde25bf', '任务科目', '0', '9hi1ed2a5d041fd74c7a951c1645ed9600f0', '', 'fixedValue', '1', 'RWKM', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-07 09:40:30', 'user00000000000000000100000000000000', '2023-03-07 09:40:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd21d39a1318514ba4b022dd4c4ee8b74d', '流水器', '0', '9hi13651759a37854a498288d32c27cbca08', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:31:24', 'user00000000000000000100000000000000', '2022-09-20 10:31:24', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd2241c950957646cdbf0bd9a882404ea9', '流水码', '0', '9hi13461e3c81417465495af792fb0547a46', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:50:39', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd2241c950957646cdbf0bd9a882404ea9', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd290d7c51fd2e441da7c5c6c7cb627335', '流水器', '0', '9hi1f405f271183c4ca9b8f89f00c32506b7', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:27:16', 'user00000000000000000100000000000000', '2022-09-20 10:27:16', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd2ac70fe4a3a4418992921c879ab837d1', 'PMS', '1', '9hi1bc4cb1828d1f4d289b5bbd4fbb3d0536', '', 'fixedValue', '1', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:10:26', 'user00000000000000000100000000000000', '2022-09-20 10:10:26', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd30b4177c0f1e4758805a34b08b86a968', '任务', '1', '9hi193022fe4c9b3404fb5e25c5acdd79ca2', '', 'fixedValue', '', 'RW', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:25:01', 'user00000000000000000100000000000000', '2022-09-20 10:25:01', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd3403cf838852480ea86187f9600fcf48', '流水器', '0', '9hi1b81b8e61b0eb4bb385ff43c726778244', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 7, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-06-24 18:03:44', 'user00000000000000000100000000000000', '2022-10-24 16:10:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd3403cf838852480ea86187f9600fcf48', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd3d3e32f7a53249ab9c6865a8b950e176', '流水码', '0', '9hi1370bca7eaaf24ed7b434642e8c191bfc', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '', '', '3', 2, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:10:05', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd3d3e32f7a53249ab9c6865a8b950e176', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd41cc727a704f4a4d8bd394c6c070b4cd', '间隔', '0', '9hi193022fe4c9b3404fb5e25c5acdd79ca2', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:24:44', 'user00000000000000000100000000000000', '2022-09-20 10:24:44', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd4f85bb06581841f2aba21a806044db1c', '项目角色', '1', '9hi14b8799ae20fd4a2c81c5cf1b6bff0b94', '', 'fixedValue', '', 'XMJS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:01:13', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd4f85bb06581841f2aba21a806044db1c', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd5708e6ce4e964e52b9ff35b635ccb1cc', '专业代号', '0', '9hi1b81b8e61b0eb4bb385ff43c726778244', 'dict7efbe67cff224103a57d12b68e10df45', 'dictionaryCode', '', '', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-07-23 15:17:34', 'user00000000000000000100000000000000', '2022-10-24 16:10:16', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd5708e6ce4e964e52b9ff35b635ccb1cc', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd5cec64bb51e74bc28329c57260b54f3e', '预案', '1', '9hi14a6f42bb875841f6bf7e2390e733eaea', '', 'fixedValue', '', 'YA', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:55:00', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd5cec64bb51e74bc28329c57260b54f3e', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd61f3a9eeddfa490ba461cd3d95175e8a', '间隔2', '0', '9hi1b81b8e61b0eb4bb385ff43c726778244', '', 'fixedValue', '', '-', '0', 5, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-06-24 18:02:09', 'user00000000000000000100000000000000', '2022-10-24 16:10:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd61f3a9eeddfa490ba461cd3d95175e8a', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd630a730717ae40b7b7abb2ca4b78928a', '流水码', '0', '9hi1b0ac4feb23564b4bb014a244c49fc94b', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '6', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:46:38', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd630a730717ae40b7b7abb2ca4b78928a', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd63bb4c001c2e48fb9a9f1574deba3261', 'PMS', '0', '9hi13461e3c81417465495af792fb0547a46', '', 'fixedValue', '', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:20:09', 'user00000000000000000100000000000000', '2022-09-20 10:20:09', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd6788bb8405c247cf95f34329508c0d10', 'PMS', '0', '9hi1b0ac4feb23564b4bb014a244c49fc94b', '', 'fixedValue', '1', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:15:40', 'user00000000000000000100000000000000', '2022-09-20 10:16:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd6825d8bc6a4148e984e005c1f256c3e5', '流水码', '0', '9hi1bc4cb1828d1f4d289b5bbd4fbb3d0536', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '3', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:45:11', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd6825d8bc6a4148e984e005c1f256c3e5', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd6a9d9a5ad08f4a2cb76d3a7f1c93c5d3', '流水码', '0', '9hi13fb663b1180e4041a14b8d2b82382e48', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '', '', '3', 2, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:07:13', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd6a9d9a5ad08f4a2cb76d3a7f1c93c5d3', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd6c66592543094539b5f9787157bafd79', '流水码', '0', '9hi14a6f42bb875841f6bf7e2390e733eaea', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:56:30', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd6c66592543094539b5f9787157bafd79', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd6eafe651139a418ea5f8dcd3921fa9b4', '项目代号', '1', '9hi1b81b8e61b0eb4bb385ff43c726778244', '', 'charCode', '', '', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-07-05 16:52:50', 'user00000000000000000100000000000000', '2022-10-24 16:10:18', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd6eafe651139a418ea5f8dcd3921fa9b4', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd7171c7b380fd4c2795918be75f8bb0cf', '项目类型', '1', '9hi1370bca7eaaf24ed7b434642e8c191bfc', '', 'fixedValue', '', 'XMLX', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:09:29', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd7171c7b380fd4c2795918be75f8bb0cf', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd75c08cfe5c71465ba4c5ae9d46d14d02', '间隔', '0', '9hi1f405f271183c4ca9b8f89f00c32506b7', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:26:34', 'user00000000000000000100000000000000', '2022-09-20 10:26:34', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd7e0ff555f0d749bbb218a8ea152af0d1', '干系人', '0', '9hi1e8c3403a5cdf4eae8b7fb61e0692b371', '', 'fixedValue', '1', 'GXR', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-06 17:57:47', 'user00000000000000000100000000000000', '2023-03-06 17:57:47', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd845557bb48064ae393ba5337e619bade', 'PMS', '0', '9hi13651759a37854a498288d32c27cbca08', '', 'fixedValue', '', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:30:19', 'user00000000000000000100000000000000', '2022-09-20 10:30:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd84a11168c6274731998b45418467fdb9', '流水', '0', '9hi1ed2a5d041fd74c7a951c1645ed9600f0', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '1', '', '5', 2, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-07 09:40:58', 'user00000000000000000100000000000000', '2023-03-07 09:41:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd85483d7d60d84604bebb0a490b5aec6c', '年份', '0', '9hi1946e96158bd44854be390b61b59183eb', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-04-03 20:49:34', 'user00000000000000000100000000000000', '2023-04-03 20:49:34', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd890222ecd11a4ba596692f365d59a165', '间隔1', '0', '9hi1946e96158bd44854be390b61b59183eb', '', 'fixedValue', '1', '-', '0', 5, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-04-03 20:51:18', 'user00000000000000000100000000000000', '2023-04-03 20:51:18', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd8cb596b45a474b48aae8f57b65a4320c', '间隔', '0', '9hi1bc4cb1828d1f4d289b5bbd4fbb3d0536', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:11:04', 'user00000000000000000100000000000000', '2022-09-20 10:11:04', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd913b45a7401d4dd097f9f00741b853a3', '间隔', '0', '9hi1946e96158bd44854be390b61b59183eb', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-04-03 20:50:07', 'user00000000000000000100000000000000', '2023-04-03 20:51:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd9328aa6feaa947b5a9412b42cfc572af', '流水号', '0', '9hi1946e96158bd44854be390b61b59183eb', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 4, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-04-03 20:50:49', 'user00000000000000000100000000000000', '2023-04-03 20:50:49', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd9386a719afb241f1b4cef72bd34b7f28', '交付物', '1', '9hi1f405f271183c4ca9b8f89f00c32506b7', '', 'fixedValue', '', 'JF', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:27:04', 'user00000000000000000100000000000000', '2022-09-20 10:27:21', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rda11e1f1040374d1bb93a41ac973278f1', '文件代字', '0', '9hi1b81b8e61b0eb4bb385ff43c726778244', 'dict76ab78bd8fb948c89d545a401895ae50', 'dictionaryCode', '', '-', '0', 6, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-06-24 18:02:52', 'user00000000000000000100000000000000', '2022-10-24 16:10:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rda11e1f1040374d1bb93a41ac973278f1', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rda613a55f580940f88cb8829916ad10a6', 'PMS', '0', '9hi14a6f42bb875841f6bf7e2390e733eaea', '', 'fixedValue', '', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:22:04', 'user00000000000000000100000000000000', '2022-09-20 10:22:04', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rda62a2ce374c44263b3d499300e02a31b', 'PMS', '0', '9hi1ed2a5d041fd74c7a951c1645ed9600f0', '', 'fixedValue', '1', 'PMS', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-07 09:41:37', 'user00000000000000000100000000000000', '2023-03-07 09:41:37', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdba1cf7b358af440494c484dc0893af10', '间隔', '0', '9hi1ed2a5d041fd74c7a951c1645ed9600f0', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-07 09:41:22', 'user00000000000000000100000000000000', '2023-03-07 09:41:22', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdbcabf37ae3e84d108fd72c760e8b3e40', '间隔1', '0', '9hi1b81b8e61b0eb4bb385ff43c726778244', '', 'fixedValue', '', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-06-24 18:01:23', 'user00000000000000000100000000000000', '2022-10-24 16:10:19', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rdbcabf37ae3e84d108fd72c760e8b3e40', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdc661f2c50ca34007893682966f984c9f', '项目状态', '1', '9hi13fb663b1180e4041a14b8d2b82382e48', '', 'fixedValue', '', 'XMZT', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:06:49', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rdc661f2c50ca34007893682966f984c9f', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdc692992586714903b71402087bb4be29', 'PMS', '0', '9hi1f405f271183c4ca9b8f89f00c32506b7', '', 'fixedValue', '', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:26:26', 'user00000000000000000100000000000000', '2022-09-20 10:26:26', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdca7424b7620944cd9c4e7bcbff8fece4', '子流水号', '0', '9hi1946e96158bd44854be390b61b59183eb', 'f9g6718f05aa4146476da08081208d5cdc5b', 'piPer', '1', '', '0', 6, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-04-03 20:52:03', 'user00000000000000000100000000000000', '2023-04-03 20:52:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdcc4cb21f7e74405f9ccc1ff9fa7a933b', '流水号', '0', '9hi1e8c3403a5cdf4eae8b7fb61e0692b371', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '1', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-06 17:58:15', 'user00000000000000000100000000000000', '2023-03-06 17:58:15', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rde0d72c043c964833bb288f40e1dc172d', '间隔', '0', '9hi14a6f42bb875841f6bf7e2390e733eaea', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:22:13', 'user00000000000000000100000000000000', '2022-09-20 10:22:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rde39ea4a01a494093b1028bd548a2a446', '秦山核电', '0', '9hi1946e96158bd44854be390b61b59183eb', '', 'fixedValue', '1', 'QS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-04-03 20:48:55', 'user00000000000000000100000000000000', '2023-04-03 20:48:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rde59cbdd4cf764934baf1ad00d5029795', '阶段代字', '0', '9hi1b81b8e61b0eb4bb385ff43c726778244', '', 'charCode', '', '', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-07-05 16:53:05', 'user00000000000000000100000000000000', '2022-10-24 16:10:17', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rde59cbdd4cf764934baf1ad00d5029795', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rde680fb621dc548d2a12dce03bb5440c3', 'PMS', '0', '9hi193022fe4c9b3404fb5e25c5acdd79ca2', '', 'fixedValue', '', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:24:33', 'user00000000000000000100000000000000', '2022-09-20 10:24:33', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdebd02315af2e4d218ea151a3f5cd248b', '问题', '1', '9hi1875c3675d9d04089ac673a28b6973b0c', '', 'fixedValue', '', 'WT', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 09:48:13', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rdebd02315af2e4d218ea151a3f5cd248b', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rded2daff3ca7d4c328854f5cdb3c5237d', 'PMS', '0', '9hi1875c3675d9d04089ac673a28b6973b0c', '', 'fixedValue', '', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:19:15', 'user00000000000000000100000000000000', '2022-09-20 10:19:15', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdedc01307586743efa9bcc5c33a67f291', '间隔', '0', '9hi1875c3675d9d04089ac673a28b6973b0c', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:19:30', 'user00000000000000000100000000000000', '2022-09-20 10:19:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdf5eb3a45621f46018b105a3968c89e94', '流水器', '0', '9hi193022fe4c9b3404fb5e25c5acdd79ca2', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-20 10:25:18', 'user00000000000000000100000000000000', '2022-09-20 10:25:18', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdf6c3c287243a45b1b4737e2f907c6294', '间隔', '0', '9hi1e8c3403a5cdf4eae8b7fb61e0692b371', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-03-06 17:58:45', 'user00000000000000000100000000000000', '2023-03-06 17:58:45', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdf6f54cc809744b3684541b0b5bff4ed3', '流水码', '0', '9hi14b8799ae20fd4a2c81c5cf1b6bff0b94', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '', '', '3', 2, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-03-16 10:01:39', 'user00000000000000000100000000000000', '2022-10-24 16:44:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rdf6f54cc809744b3684541b0b5bff4ed3', 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rdfa16958888784ebb865feca9c82eef30', '项目代号', '0', '9hi153c4b49b990f452cb2a7d4bfc024ed44', '', 'charCode', '', '', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-06-24 16:57:23', 'user00000000000000000100000000000000', '2022-10-24 16:10:20', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rdfa16958888784ebb865feca9c82eef30', 1);

-- 编码
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11772878383651446784', 'XM22', null, '项目计划类型扩展属性实例', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', null, '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:48:40', '314j1000000000000000000', '2024-03-27 14:55:29', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1, 'db571678a01f4c9e91b324dd59381892', null, null, null, 'A', null, 1, true, true);
INSERT INTO sys_code_rules (id, code_number, department, code_name, classification_id, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, rev_key, next_rev_id, previous_rev_id, code, rev_id, initial_rev_id, rev_order, share, build_in) VALUES ('9hi11772879036394201088', 'XM23', null, '项目计划类型属性', 'zmz81b978c61c24249dd8bb910c75dd6b1c2', 'SysCodeRules', '项目计划类型属性', '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:51:15', '314j1000000000000000000', '2024-03-27 14:55:31', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1, 'd4a1d0011e804f938fcb738fb208b7b3', null, null, null, 'A', null, 1, true, true);

INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1772880333692428288', 'number', '9hi11772878383651446784', 'ProjectPlanType', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:56:25', '314j1000000000000000000', '2024-03-27 14:56:25', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1, true, true);
INSERT INTO sys_code_mapping_relation (id, data_field, code_rules, data_type, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status, share, build_in) VALUES ('e96m1772880476776914944', 'number', '9hi11772879036394201088', 'ProjectPlanTypeAttribute', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:56:59', '314j1000000000000000000', '2024-03-27 14:56:59', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1, true, true);

-- 流水器
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1772878655064858624', '首位固定', '0', '9hi11772878383651446784', '', 'fixedValue', '', 'PLT-', '0', 1, '', '', 'SysCodeSegment', '首位固定', '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:49:45', '314j1000000000000000000', '2024-03-27 14:49:45', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1772878795691483136', '流水器', '0', '9hi11772878383651446784', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 2, '', '', 'SysCodeSegment', '流水器', '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:50:18', '314j1000000000000000000', '2024-03-27 14:50:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1772879229726449664', '首位固定值', '0', '9hi11772879036394201088', '', 'fixedValue', '', 'PLT-ATR-', '0', 1, '', '', 'SysCodeSegment', '首位固定值', '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:52:02', '314j1000000000000000000', '2024-03-27 14:52:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1);
INSERT INTO sys_code_segment (id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status) VALUES ('s3rd1772879331702562816', '流水器', '0', '9hi11772879036394201088', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '5', 2, '', '', 'SysCodeSegment', '流水器', '314j1000000000000000000', '314j1000000000000000000', '2024-03-27 14:52:26', '314j1000000000000000000', '2024-03-27 14:52:26', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', null, 1);
