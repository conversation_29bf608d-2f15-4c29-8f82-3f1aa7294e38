-- -------------------------------------
-- 项目分组
-- -------------------------------------
INSERT INTO `msc_business_node_classification` (`id`, `name`, `parent_id`, `sort`, `status`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('u0rn6ad2a2b3676040eb88443c958928b85a', '项目管理', NULL, 10, 1, 'user00000000000000000100000000000000', '2023-05-02 18:13:08', 'user00000000000000000100000000000000', '2023-05-02 18:13:08', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', '', 1);
-- -------------------------------------
-- 消息
-- -------------------------------------
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1713852174161055744', '项目计划催办发待办给计划负责人', 'pms_project_scheme_urge_rspuser', 'vub01713847397981261824', NULL, 1, '1', '1', '1,2', '您的$schemeName$计划被$urgeUser$催办，$remark$，请处理！', 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-10-16 17:39:35', 'user00000000000000000100000000000000', '2023-10-16 17:42:10', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y0dadd5f3b171450296e4dc9e361e1dab', '项目计划下发抄送待办(责任人)', 'Node_scheme_issue_rsp_user', 'vub0af272c20849b4e1abbfa5503240184f1', 'vub001c9298dbf9d48efb683acf56dc85f3d', 1, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-05-03 15:02:56', 'user00000000000000000100000000000000', '2023-05-11 15:54:47', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y104608afc8594eb897e5eaec25294803', '项目计划的审批通过(责任人)', 'Node_scheme_aggre', 'vub00a14b7c57b46407ba255d21c9411d5b8', 'vub012491ac7a07d4cb8839b3cd4eea0731f', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-06-09 11:00:24', 'as4fb3e2cd10e9474c0f9653a367614461a4', '2023-10-31 11:44:24', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714161118429237248', '项目预警设置-计划临期', 'Node_WARN_JH_LQ', 'vub01714158418115350528', 'vub01714158418115350528', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:07:13', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:40', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714162195488755712', '项目预警设置-计划逾期', 'Node_WARN_JH_YQ', 'vub01714159378837458944', 'vub01714159378837458944', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:11:30', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:41', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714162565740941312', '项目预警设置-风险临期', 'Node_WARN_FX_LQ', 'vub01714159538938236928', 'vub01714159538938236928', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:12:58', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:42', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714162709743980544', '项目预警设置-风险逾期', 'Node_WARN_FX_YQ', 'vub01714159687370461184', 'vub01714159687370461184', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:13:32', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:43', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714162817881526272', '项目预警设置-问题逾期', 'Node_WARN_WT_YQ', 'vub01714159831616770048', 'vub01714159831616770048', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:13:58', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:44', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714162894339493888', '项目预警设置-问题临期', 'Node_WARN_WT_LQ', 'vub01714159959450767360', 'vub01714159959450767360', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:14:16', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:44', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714162986140225536', '项目预警设置-里程碑临期', 'Node_WARN_LCB_LQ', 'vub01714160149259800576', 'vub01714160149259800576', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:14:38', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:45', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1714163075097219072', '项目预警设置-里程碑逾期', 'Node_WARN_LCB_YQ', 'vub01714160266041806848', 'vub01714160266041806848', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:14:59', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 15:55:59', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y23c1d560a961450c9e972de4a68452d3', '项目计划下发抄送(项目成员)', 'Node_scheme_issue_member', 'vub0198caa30d37c4c5aa9fe74dd2aff6301', 'vub0ae94d63a82494f95b1552f428dad23f2', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-05-03 15:04:39', 'user00000000000000000100000000000000', '2023-05-04 18:33:53', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y2d29dbdc3dc740359a13d3d22fe04b01', '资产转固提前完成', 'Node_Assets_Transfer_Complete', 'vub0a13923278ce746aab0f6b9317653e9e9', 'vub06999396a4a974f4cac740ff4679b7da2', 0, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-05-23 19:26:07', 'user00000000000000000100000000000000', '2023-05-23 19:26:12', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0yce3f84242e5043519b5dcd18b68852e8', '项目计划的审批待办驳回', 'Node_scheme_reject', 'vub0ad2e156f61b64c1ea04125e27288397f', NULL, 1, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-06-07 16:35:32', 'user00000000000000000100000000000000', '2023-06-07 16:54:09', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0yd6b2fe36eab145998f3cad891a92a17a', '项目计划调整申请(创建者)', 'Node_scheme_modify_creator', 'vub03380e6cae59a4a3aaa736f2ea4fb377e', NULL, 1, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-05-31 16:26:40', 'user00000000000000000100000000000000', '2023-05-31 16:26:59', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1718094242536177664', '逾期预警提醒-物资/服务计划', 'nood_goods_service_will_expire', 'vub01718092149138079744', 'vub01718092149138079744', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, '逾期预警提醒-物资/服务计划', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:36:03', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:37:06', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1718094391656267776', '已逾期预警提醒-物资/服务计划', 'nood_goods_service_have_expired', 'vub01718093195533041664', 'vub01718093195533041664', 0, '1', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, '已逾期预警提醒-物资/服务计划', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:36:38', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:37:07', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1734411340463923200', '周报提醒', 'Node_weekly_remind', 'vub01734404602872389632', 'vub01734404602872389632', 0, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-12-12 11:14:22', 'user00000000000000000100000000000000', '2023-12-12 11:14:25', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1734411534303682560', '周报审核', 'Node_weekly_audit', 'vub01734410292919721984', 'vub01734410292919721984', 0, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-12-12 11:15:08', 'user00000000000000000100000000000000', '2023-12-12 11:15:12', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1734480577660379136', '周报提交', 'Node_weekly_commit', 'vub01734479808999645184', NULL, 1, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-12-12 15:49:29', 'user00000000000000000100000000000000', '2023-12-12 15:50:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y579cd555c39d4097bbdbd5cebea8eebf', '提醒编制月度反馈', 'investment_feedback_write_note', 'vub0d9194bc90b394e1f807e8e2a2d6eb352', NULL, 1, '1', '1', '1', '$projectName$，$yearName$，$monthName$，', 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2023-05-22 19:52:55', 'user00000000000000000100000000000000', '2023-05-22 19:53:56', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01713847397981261824', '项目计划催办给计划负责人', 'pms_project_scheme_urge_rspuser', '您的$schemeName$计划被$urgeUser$催办，$remark$，请处理！', '1', 1, '', 'user00000000000000000100000000000000', '2023-10-16 17:20:36', 'user00000000000000000100000000000000', '2023-10-16 17:36:10', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub001c9298dbf9d48efb683acf56dc85f3d', '项目计划下发抄送待办(责任人)', 'pms_scheme_issue_rsp_user', '您收到一条项目计划【$projectSchemeName$】，请查看处理', '2', 1, '', 'user00000000000000000100000000000000', '2023-05-03 14:56:12', 'user00000000000000000100000000000000', '2023-05-03 14:57:09', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub012491ac7a07d4cb8839b3cd4eea0731f', '项目计划的审批通过(责任人)', 'pms_scheme_aggre', '您的【$schemeName$】调整申请已审批通过', '2', 1, '', 'user00000000000000000100000000000000', '2023-06-09 10:58:47', 'user00000000000000000100000000000000', '2023-06-09 11:02:55', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714158418115350528', '项目预警设置-计划临期', 'PMS_WARN_JH_LQ', '【$name$】计划任务完成时间提前预警', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 13:56:29', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 13:58:39', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714159378837458944', '项目预警设置-计划逾期', 'PMS_WARN_JH_YQ', '【$name$】计划任务完成时间已超期提醒', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:00:18', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:04:00', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714159538938236928', '项目预警设置-风险临期', 'PMS_WARN_FX_LQ', '【$name$】风险预期完成时间提前预警', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:00:56', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:03:58', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714159687370461184', '项目预警设置-风险逾期', 'PMS_WARN_FX_YQ', '【$name$】风险预期完成时间已超期提醒', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:01:32', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:03:59', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714159831616770048', '项目预警设置-问题逾期', 'PMS_WARN_WT_YQ', '【$name$】问题期望完成时间已超期提醒', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:02:06', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:03:56', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714159959450767360', '项目预警设置-问题临期', 'PMS_WARN_WT_LQ', '【$name$】问题期望完成时间提前预警', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:02:37', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:03:55', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714160149259800576', '项目预警设置-里程碑临期', 'PMS_WARN_LCB_LQ', '【$name$】里程碑完成时间提前预警', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:03:22', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:03:55', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01714160266041806848', '项目预警设置-里程碑逾期', 'PMS_WARN_LCB_YQ', '【$name$】里程碑完成时间已超期提醒', '1', 1, '', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:03:50', 'as4f5bdc7f27202b47bba860912381628b3a', '2023-10-17 14:04:34', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub0ae94d63a82494f95b1552f428dad23f2', '项目计划下发抄送(项目成员)', 'pms_scheme_issue_member', '项目：【$projectName$】中的计划:【$projectSchemeName$】已下发，请悉知', '2', 1, '', 'user00000000000000000100000000000000', '2023-05-03 14:59:20', 'user00000000000000000100000000000000', '2023-05-04 18:22:09', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub06999396a4a974f4cac740ff4679b7da2', '资产转固提前完成', 'pms_assets_transfer_complete', '您有【$projectName$】+【$projectNumber$】项目已提前完成转固，可在下方点击查看详情', '2', 1, '', 'user00000000000000000100000000000000', '2023-05-23 19:24:23', 'user00000000000000000100000000000000', '2023-05-26 09:54:50', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub0af272c20849b4e1abbfa5503240184f1', '项目计划下发抄送待办(责任人)', 'pms_scheme_issue_rsp_user', '您收到一条项目计划【$projectSchemeName$】，请查看处理', '1', 1, '', 'user00000000000000000100000000000000', '2023-05-03 14:56:12', 'user00000000000000000100000000000000', '2023-05-03 14:57:09', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub00a14b7c57b46407ba255d21c9411d5b8', '项目计划的审批通过(责任人)', 'pms_scheme_aggre', '您的【$schemeName$】调整申请已审批通过', '1', 1, '', 'user00000000000000000100000000000000', '2023-06-09 10:58:47', 'user00000000000000000100000000000000', '2023-06-09 11:02:55', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub0198caa30d37c4c5aa9fe74dd2aff6301', '项目计划下发抄送(项目成员)', 'pms_scheme_issue_member', '项目：【$projectName$】中的计划:【$projectSchemeName$】已下发，请悉知', '1', 1, '', 'user00000000000000000100000000000000', '2023-05-03 14:59:20', 'user00000000000000000100000000000000', '2023-05-04 18:22:09', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub0a13923278ce746aab0f6b9317653e9e9', '资产转固提前完成', 'pms_assets_transfer_complete', '您有【$projectName$】+【$projectNumber$】项目已提前完成转固，可在下方点击查看详情', '1', 1, '', 'user00000000000000000100000000000000', '2023-05-23 19:24:23', 'user00000000000000000100000000000000', '2023-05-26 09:54:50', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub0ad2e156f61b64c1ea04125e27288397f', '项目计划的审批待办驳回', 'pms_scheme_reject', '您的【$schemeName$】调整申请已经审批，请查看', '1', 1, '', 'user00000000000000000100000000000000', '2023-06-07 16:34:21', 'user00000000000000000100000000000000', '2023-06-07 16:54:14', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub03380e6cae59a4a3aaa736f2ea4fb377e', '项目计划调整申请（创建者）', 'pms_scheme_modify_creator', '您收到一条关于【$projectName$】的【$schemeName$】的调整申请，请查看处理', '1', 1, '', 'user00000000000000000100000000000000', '2023-05-31 16:25:37', 'user00000000000000000100000000000000', '2023-05-31 16:31:48', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01718092149138079744', '物资/服务计划逾期预警提醒', 'goods_service_will_expire', '项目所需物资【$description$】-【$goodsServiceNumber$】即将到需求日期，请注意跟进', '1', 1, '物资/服务计划逾期预警提醒', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:27:44', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:34:31', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01718093195533041664', '物资/服务计划已逾期提醒', 'goods_service_have_expired', '项目所需物资【$description$】-【$goodsServiceNumber$】已逾期【$days$】请注意跟进', '1', 1, '物资/服务计划已逾期提醒', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:31:53', 'as4fadee5558057b4bc599b68be2a97b2584', '2023-10-28 10:34:30', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01734404602872389632', '周报提醒', 'PMS_WEEKLY_REMIND', '您第【$week$】周（【$weekBegin$】-【$weekEnd$】）周报未提交，请及时处理！', '1', 1, '', 'user00000000000000000100000000000000', '2023-12-12 10:47:36', 'as4f0cab09530b914568ac05c185f2627d57', '2023-12-14 23:28:57', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01734410292919721984', '周报完成审核', 'PMS_WEEKLY_AUDIT', '您第【$week$】周（【$weekBegin$】-【$weekEnd$】）周报已审核，【$score$】分，（【$idea$】）请确认。', '1', 1, '', 'user00000000000000000100000000000000', '2023-12-12 11:10:12', 'as4f0cab09530b914568ac05c185f2627d57', '2023-12-14 23:29:10', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01734479808999645184', '周报提交', 'PMS_WEEKLY_COMMIT', '【$personName$】第【$week$】周（【$weekBegin$】-【$weekEnd$】）周报已提交，需要您审核，请确认并审核。', '1', 1, '', 'user00000000000000000100000000000000', '2023-12-12 15:46:26', 'as4f0cab09530b914568ac05c185f2627d57', '2023-12-14 23:29:28', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub0d9194bc90b394e1f807e8e2a2d6eb352', '提醒编制月度反馈', 'investment_feedback_write_note', '请在20号前填写并提交【$projectName$】【$yearName$】【$monthName$】【月度反馈表】', '1', 1, '', 'user00000000000000000100000000000000', '2023-05-22 19:52:18', 'user00000000000000000100000000000000', '2023-05-22 19:52:21', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');
-- -------------------------------------
-- 消息
-- -------------------------------------
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01780872536356257792', '项目计划-计划反馈提醒', 'project_scheme_feedback', '您有【name】项目计划需要反馈，请及时处理', '1', 1, '', '314j1000000000000000000', '2024-04-18 16:14:34', '314j1000000000000000000', '2024-04-24 11:29:12', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1780874882381479936', '项目计划反馈提醒', 'Node_Project_Scheme_Feedback', 'vub01780872536356257792', 'vub01780872536356257792', 0, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-04-18 16:23:54', '314j1000000000000000000', '2024-04-18 16:24:05', NULL, NULL, NULL, 1, b'1', b'1');
