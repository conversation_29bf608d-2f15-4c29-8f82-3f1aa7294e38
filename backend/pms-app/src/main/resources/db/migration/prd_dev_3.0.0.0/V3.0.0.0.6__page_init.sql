INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1787378567894736896', '概算模版', '/pms/estimateTemplate/estimateTemplateIndex', 'PMSEstimateTemplate', '/infrastructure/estimate-template', 'PMS2901', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-06 15:07:13', '314j1000000000000000000', '2024-05-06 15:08:00', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1787378252713750528', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1787378712371732480', '概算模版详情', '/pms/estimateTemplate/estimateTemplateDetails', 'PMSEstimateTemplateDetails', '/infrastructure/estimate-template-details/:id', 'PMS2902', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-06 15:07:48', '314j1000000000000000000', '2024-05-06 15:07:48', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1787378252713750528', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1788394631009030144', '典型风险库', '/pms/riskPool/riskPoolIndex', 'PMSRiskPool', '/risk-pool', 'PMS7028', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-09 10:24:41', '314j1000000000000000000', '2024-05-09 10:26:39', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1788394413207212032', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1788758380924530688', '质量管控项详情', '/pms/projectLibrary/pages/components/qualityControlItemDetail/index', 'QualityControlItemDetail', '/qualityControlItemDetail/:id', 'PMS_ZLGKX_DETAIL', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-10 10:30:06', '314j1000000000000000000', '2024-05-10 11:03:20', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1789982449225338880', '评审要点库', '/pms/reviewManagement/reviewGistLibrary/index', 'ReviewGistLibrary', '/reviewGistLibrary', 'PMS_PSYDK', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-13 19:34:07', '314j1000000000000000000', '2024-05-13 19:35:45', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1789982165841358848', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1789982826867888128', '评审要点库详情', '/pms/reviewManagement/reviewGistLibraryDetail/index', 'ReviewGistLibraryDetail', '/reviewGistLibraryDetail/:id', 'PMS_PSYDK_DETAIL', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-13 19:35:37', '314j1000000000000000000', '2024-05-13 23:43:25', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1789982165841358848', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1789983280834215936', '人力资源库', '/pms/humanSource/index', 'HumanSource', '/humanSource', 'humanSourceIndex', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-13 19:37:25', '314j1000000000000000000', '2024-05-13 19:37:31', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1789983003045396480', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1789985731683454976', '项目评审详情', '/pms/projectLibrary/pages/components/projectReview/CRUorMWXqDetails', 'CRUorMWXqDetails', '/CRUorMWXqDetails/:id', 'DETAIL_CONTAINER_178288_CRUORMWXQ', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-13 19:47:09', '314j1000000000000000000', '2024-05-14 16:01:26', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1790210439842533376', '费用支出详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/ExpenseManagement/ExpenseManagementDetails', 'ExpenseManagementDetails', '/expenseManagementDetails', 'ExpenseManagementDetails', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-14 10:40:04', '314j1000000000000000000', '2024-05-14 10:40:04', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1790210590753591296', '预算申请详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetRequest/BudgetRequestDetails', 'BudgetRequestDetails', '/budgetRequestDetails', 'BudgetRequestDetails', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-14 10:40:40', '314j1000000000000000000', '2024-05-14 10:40:40', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1790210721980780544', '预算调整详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetAdjustment/BudgetAdjustmentDetails', 'BudgetAdjustmentDetails', '/budgetAdjustmentDetails', 'BudgetAdjustmentDetails', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-14 10:41:11', '314j1000000000000000000', '2024-05-14 10:41:11', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1790210832471330816', '预算管理详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetManage/BudgetAdjustmentDetails', 'BudgetManageDetails', '/budgetManageDetails', 'BudgetManageDetails', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-14 10:41:38', '314j1000000000000000000', '2024-06-20 14:48:37', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1790632817714139136', '典型问题库', '/pms/typicalQuestion/typicalQuestionIndex', 'PMSTypicalQuestionIndex', '/typical-question-index', 'PMS7025', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-15 14:38:27', '314j1000000000000000000', '2024-05-15 16:22:43', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1790632392881483776', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1790633062875402240', '典型问题库详情', '/pms/typicalQuestion/typicalQuestionDetails', 'PMSTypicalQuestionDetails', 'typical-question-details/:id', 'PMS7026', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-15 14:39:25', '314j1000000000000000000', '2024-05-20 10:03:51', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1790632392881483776', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1791762051144835072', '收益管理详情', '/pms/incomeManagement/details/index', 'IncomeManagementDetails', '/incomeManagementDetails/:id', 'IncomeManagementDetails', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-18 17:25:37', '314j1000000000000000000', '2024-05-18 17:25:37', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1792894374460428288', '项目评审详情', '/pms/projectLibrary/pages/components/projectReview/CRUorMWXqDetails', 'CRUorMWXqDetails', '/CRUorMWXqDetails/:id', 'DETAIL_CONTAINER_178288_CRUORMWXQ', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-05-21 20:25:04', '314j1000000000000000000', '2024-05-21 20:25:50', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1797523275367276544', '文档模板库', '/pms/documentTemplateLibrary/home/<USER>', 'DocumentTemplateLibrary', '/documentTemplateLibrary', 'PMS_WDMBK', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-03 14:58:40', '314j1000000000000000000', '2024-06-03 15:02:28', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1797522956407201792', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1797523458343788544', '文档模板库详情', '/pms/documentTemplateLibrary/detail/index', 'DocumentTemplateLibraryDetails', '/documentTemplateLibrary/:id', 'PMS_WDMBK_DETAIL', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-03 14:59:23', '314j1000000000000000000', '2024-06-03 20:26:07', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1797522956407201792', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1797818628964536320', '协同编制详情', '/pms/projectInitiation/pages/EstablishmentTaskDetails', 'EstablishmentTaskDetails', 'establishmentTaskDetails/:id', 'PMS8028', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-04 10:32:18', '314j1000000000000000000', '2024-06-04 10:32:18', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1787665018083536896', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798906991731937280', '列表展示', '/pms/purchaseManage/purchaseModule/projectApplication/index', 'PurchaseProjectApplication', 'purchaseProjectApplication', 'projectApplication001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 10:37:04', '314j1000000000000000000', '2024-06-07 16:26:02', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798699608766386176', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798932789469777920', '资审中供应商', '/pms/supplierManage/suppliersUnderReview/SupplierReviewList', 'SuppliersUnderReview', '/suppliersUnderReview', 'PMS00001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 12:19:34', '314j1000000000000000000', '2024-06-11 09:51:42', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923642326192128', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798938852239740928', '苏州院合格供应商', '/pms/supplierManage/qualifiedSupplier/SupplierInfoList', 'QualifiedSupplier', '/qualifiedSupplier', 'PMS00002', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 12:43:40', '314j1000000000000000000', '2024-06-10 23:30:13', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923512315351040', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798939320445702144', '其他合格供应商', '/pms/supplierManage/anotherQualifiedSupplier/SupplierInfoList', 'AnotherQualifiedSupplier', '/anotherQualifiedSupplier', 'PMS00003', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 12:45:31', '314j1000000000000000000', '2024-06-10 23:30:31', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923591281512448', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798939732099862528', '潜在供应商', '/pms/supplierManage/potentialSupplier/SupplierInfoList', 'PotentialSupplier', '/potentialSupplier', 'PMS00004', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 12:47:09', '314j1000000000000000000', '2024-06-10 23:30:41', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923735947251712', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798967059810750464', '采购申请详情', '/pms/purchaseManage/purchaseModule/projectApplication/pages/projectApplicationItem', 'PurchaseProjectApplicationItem', 'purchaseProjectApplicationItem/:id', 'projectApplicationItem', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 14:35:45', '314j1000000000000000000', '2024-06-07 16:46:06', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798699608766386176', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798983075685076992', '中广核-项目立项', '/pms/newProjectInitiation/index', 'NewProjectInitiation', 'zghProjectInitiation', 'ZghProjectInitiationpms001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 15:39:23', '314j1000000000000000000', '2024-06-13 15:53:22', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798982139667517440', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1798984930074955776', '项目立项详情', '/pms/newProjectInitiation/pages/ProjectApplicationDetail', 'ZghProjectInitiationDetail', 'zghProjectInitiationDetail/:id', 'ZghProjectInitiationDetail001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-07 15:46:46', '314j1000000000000000000', '2024-06-13 15:51:58', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798982139667517440', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800059214289571840', '采购项目实施', '/pms/purchaseManage/purchaseModule/projectOngoing/index', 'ProjectOngoing', 'projectOngoing', 'projectOngoing001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 14:55:35', '314j1000000000000000000', '2024-06-10 14:55:38', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798699728819949568', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800059749839278080', '采购项目实施详情', '/pms/purchaseManage/purchaseModule/projectOngoing/page/projectOngoingItem', 'ProjectOngoingItem', 'projectOngoingItem/:id', 'projectOngoingItem', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 14:57:43', '314j1000000000000000000', '2024-06-10 15:08:57', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798699728819949568', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800109181091844096', '合同主列表', '/pms/purchaseManage/purchaseModule/purchaseContract/index', 'PurchaseContract', 'purchaseContract', 'purchaseContract', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 18:14:08', '314j1000000000000000000', '2024-06-10 18:14:11', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798699941596991488', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800117738793209856', '采购合同详情', '/pms/purchaseManage/purchaseModule/purchaseContract/pages/purchaseContractInfo', 'PurchaseContractInfo', 'purchaseContractInfo/:id', 'purchaseContractInfo001', '5', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 18:48:08', '314j1000000000000000000', '2024-06-13 16:32:33', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798699941596991488', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800168481558499328', '总价合同列表', '/pms/purchaseManage/purchaseModule/purchaseContract/pages/lumpSumContract', 'LumpSumContract', 'lumpSumContract', 'lumpSumContract', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 22:09:46', '314j1000000000000000000', '2024-06-12 11:31:57', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700034299498496', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800169267269079040', '框架合同列表', '/pms/purchaseManage/purchaseModule/purchaseContract/pages/frameworkContract', 'FrameworkContract', 'frameworkContract', 'frameworkContract', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 22:12:54', '314j1000000000000000000', '2024-06-12 11:32:23', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700123327795200', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800170727490850816', '变更/索赔/终止', '/pms/purchaseManage/purchaseModule/purchaseContract/pages/changeClaimAbort', 'ChangeClaimAbort', 'changeClaimAbort', 'changeClaimAbort', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 22:18:42', '314j1000000000000000000', '2024-06-12 11:33:28', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700266244509696', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800174121521647616', '无采购合同', '/pms/purchaseManage/purchaseModule/noPurchaseContract/index', 'NoPurchaseContract', 'noPurchaseContract', 'noPurchaseContract', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-10 22:32:11', '314j1000000000000000000', '2024-06-10 22:32:14', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1800173165878939648', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800347399674605568', '资审中供应商详情页', '/pms/supplierManage/suppliersUnderReview/SupplierReviewDetails', 'SupplierReviewDetails', '/supplierReviewDetails/:id', 'PMS00014', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-11 10:00:44', '314j1000000000000000000', '2024-06-11 10:28:07', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923642326192128', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800364578520379392', '苏州院合格供应商详情页', '/pms/supplierManage/qualifiedSupplier/SupplierInfoDetails', 'SupplierInfoDetails', '/supplierInfoDetails/:id', 'PMS000010', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-11 11:08:59', '314j1000000000000000000', '2024-06-14 18:35:32', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923512315351040', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800365463833096192', '其他合格供应商详情页', '/pms/supplierManage/anotherQualifiedSupplier/SupplierInfoDetails', 'SupplierInfoDetailsAnother', '/supplierInfoDetailsAnother/:id', 'PMS00011', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-11 11:12:30', '314j1000000000000000000', '2024-06-14 18:35:49', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923591281512448', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800366105821655040', '潜在供应商详情页面', '/pms/supplierManage/potentialSupplier/SupplierInfoDetails', 'SupplierInfoDetailsPotential', '/supplierInfoDetailsPotential/:id', 'PMS00013', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-11 11:15:03', '314j1000000000000000000', '2024-06-14 18:36:46', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798923735947251712', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800453610843615232', '集采订单管理', '/pms/purchaseManage/purchaseModule/purchaseContract/collectionOrderManage/index', 'CollectionOrderManage', 'collectionOrderManage', 'collectionOrderManage', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-11 17:02:46', '314j1000000000000000000', '2024-06-11 17:02:49', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700313837277184', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800453885436309504', '集采订单详情', '/pms/purchaseManage/purchaseModule/purchaseContract/collectionOrderManage/collectionOrderItem', 'CollectionOrderItem', 'collectionOrderItem/:id', 'collectionOrderItem', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-11 17:03:52', '314j1000000000000000000', '2024-06-11 17:03:52', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700313837277184', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800505794587209728', '采购供应指标看板', '/pms/purchasingKanbanBoard/PurchasingKanbanBoard', 'ProcurementIndicatorsBoard', '/procurementIndicatorsBoard', 'cgzbkb_001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-11 20:30:08', '314j1000000000000000000', '2024-06-11 22:23:52', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1800505509865295872', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800744059529490432', '采购供应指标', '/pms/purchaseManage/purchaseModule/provisionIndicator/index', 'ProvisionIndicator', 'provisionIndicator', 'provisionIndicator', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-12 12:16:55', '314j1000000000000000000', '2024-06-12 12:16:58', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1800743412231016448', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1800829199949697024', '项目状态', '/pms/projectStatus/typicalQuestionIndex', 'ProjectStatus', '/projectStatus', 'PMS00015', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-12 17:55:14', '314j1000000000000000000', '2024-06-17 12:20:40', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1800828321775718400', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1801079181571198976', '无合同采购详情', '/pms/purchaseManage/purchaseModule/noPurchaseContract/page/NoPurchaseContractInfo', 'NoPurchaseContractInfo', 'noPurchaseContractInfo/:id', 'NoPurchaseContractInfo', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-13 10:28:34', '314j1000000000000000000', '2024-06-13 10:29:06', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1800173165878939648', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1801113039347814400', '采购供应指标详情', '/pms/purchaseManage/purchaseModule/projectApplication/pages/projectApplicationItem', 'ProjectApplicationItem', 'projectApplicationItem/:id', 'projectApplicationItem001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-13 12:43:06', '314j1000000000000000000', '2024-06-13 12:43:06', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1800743412231016448', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1801178607069335552', '总价合同详情', '/pms/purchaseManage/purchaseModule/purchaseContract/pages/lumpSumContractInfo', 'LumpSumContractInfo', 'lumpSumContractInfo/:id', 'lumpSumContractInfoPMS001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-13 17:03:39', '314j1000000000000000000', '2024-06-13 17:03:39', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700034299498496', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1801191542885879808', '框架合同详情', '/pms/purchaseManage/purchaseModule/purchaseContract/pages/frameworkContractInfo', 'FrameworkContractInfo', 'frameworkContractInfo/:id', 'frameworkContractInfoPMS001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-13 17:55:03', '314j1000000000000000000', '2024-06-13 17:55:03', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700123327795200', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1801528546345521152', '变更/索赔/终止-详情', '/pms/purchaseManage/purchaseModule/purchaseContract/pages/changeClaimAbortInfo', 'ChangeClaimAbortInfo', 'changeClaimAbortInfo/:id', 'changeClaimAbortPMS001', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-14 16:14:11', '314j1000000000000000000', '2024-06-14 16:14:11', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1798700266244509696', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1801562285272371200', '不良行为供应商', '/pms/supplierManage/badActionSupplier/SupplierInfoList', 'BadActionSupplier', '/badActionSupplier', 'PMS00005', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-14 18:28:15', '314j1000000000000000000', '2024-06-14 18:28:19', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1801558872044318720', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1801562512393932800', '不良行为供应商-详情', '/pms/supplierManage/badActionSupplier/SupplierInfoDetails', 'SupplierInfoDetailsBad', '/supplierInfoDetailsBad/:id', 'PMS00012', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-14 18:29:09', '314j1000000000000000000', '2024-06-14 18:34:05', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1801558872044318720', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr586d4bd954a043528ea876199f16c8db', '采购申请主表详情页面', '/pmi/dataModel/render/DetailsRender', 'DataModelDetailsRender20te95e5bfe3876c4a1aad6c4cea24ffb7f7', '/cGFnZS1kZXRhaWxzLXJlbmRlcjIwdGU5NWU1YmZlMzg3NmM0YTFhYWQ2YzRjZWEyNGZmYjdmNz9mb3JtSWQ9MjB0ZTk1ZTViZmUzODc2YzRhMWFhZDZjNGNlYTI0ZmZiN2Y3Jm1haW5JZD0yMHRlOTVlNWJmZTM4NzZjNGExYWFkNmM0Y2VhMjRmZmI3Zjc=', 'detail-container-042a7f-BLrYEMAOL', '0', NULL, 'pmi', NULL, b'0', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-06-09 19:51:35', 'user00000000000000000100000000000000', '2024-06-09 19:51:35', NULL, NULL, '7be6dabda5ba439492e47b93a3ceb87b', 1, 'menu672526e22ae446839d76d1d6844f0484', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr5c9cdcb699264f0084248f16cfdadfd4', '商城集采订单（明细表）列表页面', '/pmi/dataModel/render/ListRender', 'DataModelListRender20te709243b8baa040dc8bbd96f08b42ee61', '/cGFnZS1saXN0LXJlbmRlcjIwdGU3MDkyNDNiOGJhYTA0MGRjOGJiZDk2ZjA4YjQyZWU2MT9mb3JtSWQ9MjB0ZTcwOTI0M2I4YmFhMDQwZGM4YmJkOTZmMDhiNDJlZTYxJm1haW5JZD0yMHRlNzA5MjQzYjhiYWEwNDBkYzhiYmQ5NmYwOGI0MmVlNjE=', 'list-container-042a7f-ncfFormPurchOrderDetail', '0', NULL, 'pmi', NULL, b'0', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-06-21 10:50:26', 'user00000000000000000100000000000000', '2024-06-21 10:58:07', NULL, NULL, '60700d8b4d7845b2a2082f0199d80765', 1, 'menufe657dec1b6641ac9f45d867f9c491a0', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr84b84e5f0ad041b1aae0ec806e8d12ed', '商城集采订单（明细表）详情页面', '/pmi/dataModel/render/DetailsRender', 'DataModelDetailsRender20te709243b8baa040dc8bbd96f08b42ee61', '/cGFnZS1kZXRhaWxzLXJlbmRlcjIwdGU3MDkyNDNiOGJhYTA0MGRjOGJiZDk2ZjA4YjQyZWU2MT9mb3JtSWQ9MjB0ZTcwOTI0M2I4YmFhMDQwZGM4YmJkOTZmMDhiNDJlZTYxJm1haW5JZD0yMHRlNzA5MjQzYjhiYWEwNDBkYzhiYmQ5NmYwOGI0MmVlNjE=', 'detail-container-042a7f-ncfFormPurchOrderDetail', '0', NULL, 'pmi', NULL, b'0', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-06-21 10:50:26', 'user00000000000000000100000000000000', '2024-06-21 10:58:07', NULL, NULL, '60700d8b4d7845b2a2082f0199d80765', 1, 'menufe657dec1b6641ac9f45d867f9c491a0', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr9275e655c9c9423c8ea01159b79e6228', '采购申请主表列表页面', '/pmi/dataModel/render/ListRender', 'DataModelListRender20te95e5bfe3876c4a1aad6c4cea24ffb7f7', '/cGFnZS1saXN0LXJlbmRlcjIwdGU5NWU1YmZlMzg3NmM0YTFhYWQ2YzRjZWEyNGZmYjdmNz9mb3JtSWQ9MjB0ZTk1ZTViZmUzODc2YzRhMWFhZDZjNGNlYTI0ZmZiN2Y3Jm1haW5JZD0yMHRlOTVlNWJmZTM4NzZjNGExYWFkNmM0Y2VhMjRmZmI3Zjc=', 'list-container-042a7f-BLrYEMAOL', '0', NULL, 'pmi', NULL, b'0', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-06-09 19:51:35', 'user00000000000000000100000000000000', '2024-06-09 19:51:35', NULL, NULL, '7be6dabda5ba439492e47b93a3ceb87b', 1, 'menu672526e22ae446839d76d1d6844f0484', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tra601f3d2cbf547c59604741e2dde5772', '采购申请行项目表详情页面', '/pmi/dataModel/render/DetailsRender', 'DataModelDetailsRender20tef534f4e0066c4997a8e3c91b0257591a', '/cGFnZS1kZXRhaWxzLXJlbmRlcjIwdGVmNTM0ZjRlMDA2NmM0OTk3YThlM2M5MWIwMjU3NTkxYT9mb3JtSWQ9MjB0ZWY1MzRmNGUwMDY2YzQ5OTdhOGUzYzkxYjAyNTc1OTFhJm1haW5JZD0yMHRlOTVlNWJmZTM4NzZjNGExYWFkNmM0Y2VhMjRmZmI3Zjc=', 'detail-container-042a7f-NVJnjAvgg', '0', NULL, 'pmi', NULL, b'0', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-06-09 19:51:35', 'user00000000000000000100000000000000', '2024-06-09 19:51:35', NULL, NULL, '7be6dabda5ba439492e47b93a3ceb87b', 1, 'menu672526e22ae446839d76d1d6844f0484', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2trcc812f910e0d473b9254117e0712bc74', '采购-商城集采订单（总表）列表页面', '/pmi/dataModel/render/ListRender', 'DataModelListRender20te7d9a0094ebb343418b43735bca021f28', '/cGFnZS1saXN0LXJlbmRlcjIwdGU3ZDlhMDA5NGViYjM0MzQxOGI0MzczNWJjYTAyMWYyOD9mb3JtSWQ9MjB0ZTdkOWEwMDk0ZWJiMzQzNDE4YjQzNzM1YmNhMDIxZjI4Jm1haW5JZD0yMHRlN2Q5YTAwOTRlYmIzNDM0MThiNDM3MzViY2EwMjFmMjg=', 'list-container-042a7f-ncfFormPurchOrderCollect', '0', NULL, 'pmi', NULL, b'0', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-06-21 11:09:10', 'user00000000000000000100000000000000', '2024-06-21 11:36:13', NULL, NULL, '64e32277caa84a27aea6bc8a353d89e6', 1, 'menu9bcec7b9f5a04d18a2e4072e7e7d0726', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2trf2c8016207974987bfa70e2e0e4680d5', '采购-商城集采订单（总表）详情页面', '/pmi/dataModel/render/DetailsRender', 'DataModelDetailsRender20te7d9a0094ebb343418b43735bca021f28', '/cGFnZS1kZXRhaWxzLXJlbmRlcjIwdGU3ZDlhMDA5NGViYjM0MzQxOGI0MzczNWJjYTAyMWYyOD9mb3JtSWQ9MjB0ZTdkOWEwMDk0ZWJiMzQzNDE4YjQzNzM1YmNhMDIxZjI4Jm1haW5JZD0yMHRlN2Q5YTAwOTRlYmIzNDM0MThiNDM3MzViY2EwMjFmMjg=', 'detail-container-042a7f-ncfFormPurchOrderCollect', '0', NULL, 'pmi', NULL, b'0', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-06-21 11:09:10', 'user00000000000000000100000000000000', '2024-06-21 11:36:13', NULL, NULL, '64e32277caa84a27aea6bc8a353d89e6', 1, 'menu9bcec7b9f5a04d18a2e4072e7e7d0726', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe060af2a3b86b4acdb7dc3a808406542d', '成本中心', '/pms/costManage/pages/feeSettings/costCenter/index', 'CostCenter', '/costCenter', 'CostCenter', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-21 15:17:02', '314j1000000000000000000', '2024-05-05 13:46:45', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1786995717215285248', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe0d5e0a8dd87d4309a73cbcfeac73cc9e', '物资服务计划详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/materialManagement/serviceList/pages/materialServicesDetails', 'MaterialServicesDetails', '/materialServicesDetails', 'MaterialServicesDetails', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-01 10:42:51', '314j1000000000000000000', '2024-04-29 10:33:18', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe0e416372d93947b7ba3d6cf770fdd69d', '应收详情', '/pms/projectLibrary/pages/pages/ReceivableDetail', 'ReceivableDetail', 'receivableDetail/:id', 'PMS0010', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-28 14:51:20', '314j1000000000000000000', '2024-04-29 10:33:21', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe1290ab701d9d4469835bb824765f9e86', '计划详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/planDetails/index', 'PlanDetails', '/planManagement/planDetails', 'PMS0027', '0', NULL, 'pms', 1240, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-16 14:02:47', '314j1000000000000000000', '2024-05-08 20:59:17', '计划详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxe1290ab701d9d4469835bb824765f9e86', 1, 'menu1784523943286304768', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe23209740b85f4dbc8af915d28070794f', '需求管理详情', '/pms/projectLaborer/projectLab/pages/projectPlanDetails/demandManagement/demandManagementDetails/index', 'PMSDemandManagementDetails', '/pmsDemandManagementDetails', 'PMSDemandManagementDetails', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-28 14:37:27', '314j1000000000000000000', '2024-04-28 18:05:33', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe26b1e2fad9d348358784a517e1b20ed6', '技术清单详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/technicalList/technicalDetail/index', 'TechnicalDetail', '/technicalDetail/:id', 'PMS_TECHNICAL_TETAIL', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-02-01 09:20:59', '314j1000000000000000000', '2024-04-29 10:33:28', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe2e44a10295584e7fbaf7e3e9e81f581a', '物资服务入库详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/materialManagement/warehousingList/pages/materialServicesDetails', 'MaterialWarehousingDetails', '/materialWarehousingDetails', 'MaterialWarehousingDetails', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-01 10:43:43', '314j1000000000000000000', '2024-04-29 10:33:32', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe2f445797b00a46da8e7529fbcd0826ed', '日历设置', '/pmi/calendarSet/CalendarSet', 'CalendarSet', '/calendar-set', 'HOLIDAY_SET', '0', NULL, 'pmi', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 19:16:29', '314j1000000000000000000', '2024-05-09 22:14:54', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1786995698483523584', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe37e1b2e9b9b9485aa10c73f17421926b', '工作日报信息', '/pms/dayReportDetails/DayReportDetailsIndex', 'DayReportDetails', 'dayReportDetails', 'PMS5000', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-20 15:41:21', '314j1000000000000000000', '2024-04-29 10:33:36', '日报详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe3b5cac75ad394677a985a17fb2563ee2', '传递单详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/icmManagementDetails/icmManagementDetailsIndex', 'IcmManagementDetailsIndex', 'icmManagementDetailsIndex/:id', 'PMS6878', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 22:56:55', '314j1000000000000000000', '2024-04-29 10:33:39', '接口管理-传递单详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe3fbc0de24cd0407da111dcb9fa24c959', '月度计划反馈详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/annualInvestment/monthlyFeedbackDetails', 'MonthlyFeedbackDetails', '/monthlyFeedbackDetails/:id', 'MonthlyFeedbackDetails', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-12-18 15:59:35', '314j1000000000000000000', '2024-04-28 18:05:37', '月度计划反馈详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe424b2451be514243a1e0b0573212c68a', '项目申报', '/pms/projectApplication/ProjectApplication', 'ProjectApplication', 'projectApplication', 'PMS0008', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-19 16:53:26', '314j1000000000000000000', '2024-05-09 19:50:28', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1788536891927298048', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe5360d025554b431c8acdce485707d232', '采购管理详情', '/pms/projectLibrary/pages/pages/ProcureDetails', 'ProcureDetails', 'procureDetails/:id', 'PMS0012', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-08 18:32:22', '314j1000000000000000000', '2024-04-29 10:33:54', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe5780953b19da430b807cfd6181355994', '结项详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/endManagement/endDetails/index', 'EndDetails', '/endDetails', 'PMS0007', '0', NULL, 'pms', 1150, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-15 23:05:00', '314j1000000000000000000', '2024-04-29 10:33:57', '结项详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxe5780953b19da430b807cfd6181355994', 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe5c50ea5d1a6f416a87c91dbe8bc40350', '费用科目', '/pms/costManage/pages/feeSettings/expenseAccount/index', 'ExpenseAccount', '/expenseAccount', 'ExpenseAccount', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-21 15:27:02', '314j1000000000000000000', '2024-05-05 13:46:49', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1786995736995622912', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe5cf401cdee094572ba00c042bc7f2142', '科研需求申报', '/pms/scientificResearchDemandDeclaration/ProjectApplication', 'ScientificResearchDemandDeclaration', '/scientificResearchDemandDeclaration', 'KYXQSB_0001', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-24 18:16:13', '314j1000000000000000000', '2024-05-09 19:51:19', '科研需求申报分页', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1788537135234678784', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe5d6c4d9fb6b347f2b44df62b460549a1', '合同详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/contractManage/contractManageDetail/ContractManageDetail', 'ContractManageDetail', '/contract-details', 'CONTRACT_MANAGE_DETAIL', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'as4f59bedbab7c7349128c57bf01445321f5', 'as4f59bedbab7c7349128c57bf01445321f5', '2023-10-28 14:35:19', '314j1000000000000000000', '2024-04-29 10:34:04', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe5df9a64816be417c90a9e996e00add93', '投资计划详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/investmentPlan/details', 'InvestmentPlanDetails', '/InvestmentPlanDetails/:id', 'InvestmentPlanDetails', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-12-18 15:58:28', '314j1000000000000000000', '2024-04-28 18:05:41', '投资计划详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe5e13fdcd0f794fa399008d0df5989098', '评价详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/projectEvaluate/pages/projectEvaluateDetails', 'ProjectEvaluateDetails', '/projectEvaluateDetails', 'ProjectEvaluateDetails', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-28 14:12:47', '314j1000000000000000000', '2024-04-29 10:34:08', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe605a051befb448ca8a466fe4b90c7aac', '问题详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/questionManage/questionDetailsTabs/index', 'QuestionDetails', '/questionDetails', 'PMS0021', '0', NULL, 'pms', 1210, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-16 10:48:01', '314j1000000000000000000', '2024-04-29 10:34:16', '问题详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxe605a051befb448ca8a466fe4b90c7aac', 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe63d5650ba8824bf4a27f251e5c37184d', '项目集', '/pms/projectLaborer/projectCollection/projectList/index', 'ProjectCollection', '/projectCollection', 'ProjectCollection', '0', NULL, 'pms', NULL, b'1', 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-04-01 11:01:11', '314j1000000000000000000', '2024-04-28 18:09:54', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784476723073662976', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe649ec773aa2b4007be82137917654e12', '风险管理详情', '/pms/projectLaborer/projectLab/projectList/components/BusinessRisk/details', 'PMSRiskManagementDetails', '/risk-management-details/:id', 'PMS7451', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-28 14:36:52', '314j1000000000000000000', '2024-05-21 17:39:59', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe68694c52d0fc4efbab3a9be33a010d85', '项目管理页面001', '/pms/projectManage/index', 'ProjectManage', '/projectManage', 'PMS003', '0', NULL, 'pms', 1, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-04-25 16:34:55', '314j1000000000000000000', '2024-04-26 10:23:41', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe72e43902a1b64923a12a24658318ac81', '项目详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/index', 'MenuComponents', '/menuComponents', 'PMS0004', '0', NULL, 'pms', 1120, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-15 22:25:29', '314j1000000000000000000', '2024-04-28 18:05:49', '项目详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxe72e43902a1b64923a12a24658318ac81', 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe7efa3f38e4a5490da603c95778c3a055', '科研需求申报详情', '/pms/scientificResearchDemandDeclaration/pages/ProjectApplicationDetail', 'ScientificResearchDemandDeclarationDetail', '/scientificResearchDemandDeclarationDetail', 'KYXQSB_0002', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-24 18:16:32', '314j1000000000000000000', '2024-05-09 19:51:12', '科研需求申报详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1788537135234678784', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe95521404e9424c208a159c99b90b201c', '实收详情', '/pms/projectLibrary/pages/pages/RealIncomeDetail', 'RealIncomeDetail', 'realIncomeDetail/:id', 'PMS0011', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-28 14:52:00', '314j1000000000000000000', '2024-04-29 10:34:23', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe981f848b9a24472ab272040f17e0fee7', '项目立项详情', '/pms/projectInitiation/pages/ProjectApplicationDetail', 'ProjectInitiationDetail', 'projectInitiationDetail/:id', 'PMS1026', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-08 19:07:04', '314j1798695505979682816', '2024-06-22 10:58:18', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1787665018083536896', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxe9881c76c1f644858b3c14d97fac8ef0b', '项目申报详情', '/pms/projectApplication/pages/ProjectApplicationDetail', 'ProjectApplicationDetail', 'projectApplicationDetail/:id', 'PMS0009', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-19 16:54:09', '314j1000000000000000000', '2024-05-09 19:50:22', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1788536891927298048', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxea63a75301cd840e9a4e187bee193a8d2', '项目立项', '/pms/projectInitiation/index', 'ProjectInitiation', '/projectInitiation', 'PMS1025', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-08 19:04:32', '314j1798695505979682816', '2024-06-22 10:12:50', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1787665018083536896', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxead28c18508764a1c8a7552ff69360f30', '实际风险详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/riskManagement/realRisk/riskDetails/index', 'RiskDetails', '/riskDetails', 'PMS011', '0', NULL, 'pms', 1190, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-15 23:21:34', '314j1000000000000000000', '2024-04-29 10:34:33', '实际风险详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxead28c18508764a1c8a7552ff69360f30', 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxeb9c977a937754fa6aa7e1029fdfd402a', '计划模板管理', '/pms/planTemplateManagement/index', 'PlanTemplateManagement', '/planTemplateManagement', 'PlanTemplateManagement', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-18 16:20:51', '314j1000000000000000000', '2024-05-05 13:46:39', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1786995674022342656', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxebd9121fff62844f9a46d621f00863542', '项目验收详情', '/pms/projectManage/pages/projectDetail/components/projectAcceptance/pages/ProjectAcceptanceDetail', 'ProjectAcceptanceDetail', '/projectAcceptanceDetail', 'PRO_ACC_Detail', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-13 10:46:26', '314j1000000000000000000', '2024-04-29 10:34:41', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxebe379985713c4678a7115af743cf1fe4', '需求详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/demandManagement/demandDetailsTabs/index', 'DemandDetails', '/demandDetails', 'PMS0020', '0', NULL, 'pms', 1200, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-16 10:47:01', '314j1000000000000000000', '2024-04-29 10:34:44', '需求详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxebe379985713c4678a7115af743cf1fe4', 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxebea65a251f5b4ee9b23f92ebd4325f84', '年度投资计划详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/annualInvestment/details', 'AnnualInvestment', '/annualInvestment/:id', 'AnnualInvestment', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-12-18 15:59:04', '314j1000000000000000000', '2024-04-28 18:05:54', '年度投资计划详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxec0f112a2a88f4c479ad677d191533bf3', '项目集详情', '/pms/projectLaborer/projectCollection/projectList/projectCollectionDetail/index', 'ProjectCollectionDetail', '/projectCollectionDetail', 'ProjectCollectionDetail', '0', NULL, 'pms', NULL, b'1', 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-04-01 11:01:41', '314j1000000000000000000', '2024-04-28 18:09:38', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784476723073662976', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxec39a618ca0ff4bf2aee42327e44e2ff6', '我参与的项目', '/pms/personalWorkbench/projectCenter/participateIn/index', 'ParticipateIn', '/participateIn', 'ParticipateIn', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-17 15:33:20', '314j1000000000000000000', '2024-05-09 20:12:04', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1788542399836979200', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxec74a6b292f394383aadee4e98e27a95a', '问题管理详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/questionDetails', 'PMSQuestionManagementDetails', '/question-management-details/:id', 'PMS7450', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-10-28 14:37:59', '314j1000000000000000000', '2024-05-21 17:41:45', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxec7ad78c69f2944e6b6d914808388c9b6', '里程碑详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/milestoneDetails/index', 'MilestoneDetails', '/planManagement/milestoneDetails', 'PMS0006', '0', NULL, 'pms', 1140, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-15 23:03:59', '314j1000000000000000000', '2024-04-29 10:34:48', '里程碑详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxec7ad78c69f2944e6b6d914808388c9b6', 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxecaae06807ba94880840e5b6a88741168', '工时填报详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/TimeEntry/TimeEntryDetail/TimeEntryDetail', 'TimeEntryDetail', '/TimeEntryDetail', 'GSXQ0001', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-11-20 23:39:45', '314j1000000000000000000', '2024-04-29 10:34:51', '工时填报详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxecd755ac9aee34bc8ac6bf041128af944', '交付物详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/deliverDetails/index', 'DeliverDetails', '/deliverDetails', 'PMS0031', '0', NULL, 'pms', 1280, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-21 13:45:00', '314j1000000000000000000', '2024-04-29 10:34:57', '交付物详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxecd755ac9aee34bc8ac6bf041128af944', 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxed257da41ab374a22818c910d90ca2d7b', '风险预案详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/riskManagement/preRisk/preRiskDetailsTab/index', 'PreRiskDetails', '/preRiskDetails', 'PMS0023', '0', NULL, 'pms', 1220, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-16 10:49:03', '314j1000000000000000000', '2024-05-07 14:03:20', '风险预案详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxed257da41ab374a22818c910d90ca2d7b', 1, 'menu1784523943286304768', 0);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxed36dfaf4599c4406a691f67c67aa2e58', '项目绩效指标库', '/pms/projectPerformanceLibrary/index', 'ProjectPerformanceLibrary', '/projectPerformanceLibrary', 'ProjectPerformanceLibrary', '0', NULL, 'pms', NULL, b'1', 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-04-01 16:49:48', '314j1000000000000000000', '2024-04-28 18:11:45', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784524058319286272', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxedaeaf06643234c6398fe58ac0c977d48', '人力资源', '/pms/humanSource/humanSet', 'HumanSet', '/humanSet', 'PMS5920', '0', NULL, 'pms', NULL, b'1', 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-04-17 15:37:22', '314j1000000000000000000', '2024-05-13 20:21:42', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1789994011218542592', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxedb6a218f15a24c6e9ae67353b5c4732c', '意见单详情', '/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/oIcmManagementDetails/oIcmManagementDetailsIndex', 'OIcmManagementDetailsIndex', 'oIcmManagementDetailsIndex/:id', 'PMS6879', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-01-31 22:57:39', '314j1000000000000000000', '2024-04-29 10:35:13', '接口管理-意见单详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1783375234234302464', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxedcfe790caabe49c4b0059d5dfd7bf550', '项目列表', '/pms/projectLaborer/projectLab/projectList/index', 'ProjectLab', '/projectLab', 'PMS0003', '0', NULL, 'pms', 1250, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-09-16 16:28:40', '314j1000000000000000000', '2024-04-29 09:12:15', '项目列表', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'xaxedcfe790caabe49c4b0059d5dfd7bf550', 1, 'menu1784523943286304768', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxee9ba22b1e5c34b00b2069e815adfc78c', '全生命周期说明模板', '/pms/fullLifeCycleDescriptionTemplate/index', 'FullLifeCycleDescriptionTemplate', '/fullLifeCycleDescriptionTemplate', 'PMS5001', '0', NULL, 'pms', NULL, b'1', 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-04-24 15:53:37', '314j1000000000000000000', '2024-05-05 13:46:52', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1786995757820342272', 1);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxef79cc9920d83454d8f9de5a3b427927e', '计划详情-qs', '/pms/projectLaborer/projectLab/pages/projectPlanDetails/ProPlanDetails', 'ProPlanDetails', '/ProPlanDetails/:id', 'PMS00027', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-09-26 15:58:57', 'user00000000000000000100000000000000', '2023-09-26 15:58:57', '', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', NULL);
INSERT INTO `pmi_page` (`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('xaxefaabcd5caa8a4aa99721159e0f501f29', '工作周报详情', '/pms/weeklyReportDetails/weekReportDetailsIndex', 'WeekReportDetails', '/weekReportDetails', 'PMS_WEEKLY_DETAILS', '0', NULL, 'pms', NULL, b'1', 'Page', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2023-12-14 19:57:40', '314j1000000000000000000', '2024-04-28 18:06:04', '项目内-周报详情', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1784523943286304768', 0);
