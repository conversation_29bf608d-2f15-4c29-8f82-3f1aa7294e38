
CREATE TABLE `pmsx_project_pay_promise` (
                                            `id` varchar(64) NOT NULL  COMMENT '主键',
                                            `class_name` varchar(64)   COMMENT '创建人',
                                            `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                            `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                            `owner_id` varchar(64)   COMMENT '拥有者',
                                            `create_time` datetime NOT NULL  COMMENT '创建时间',
                                            `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                            `remark` varchar(1024)   COMMENT '备注',
                                            `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                            `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                            `status` int NOT NULL  COMMENT '状态',
                                            `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                            `refbn` varchar(50)   COMMENT '参考凭证编码',
                                            `posid` varchar(50)   COMMENT 'WBS元素',
                                            `pspid` varchar(50)   COMMENT '项目定义',
                                            `budat` datetime   COMMENT '借方日期',
                                            `kstar` varchar(50)   COMMENT '成本要素',
                                            `txt_two` varchar(50)   COMMENT '成本要素名称',
                                            `sgtxt` varchar(50)   COMMENT '名称',
                                            `currency` varchar(50)   COMMENT '币种',
                                            `post_one` varchar(50)   COMMENT 'CO对象名称',
                                            `wtgbtr` varchar(50)   COMMENT '业务货币值',
                                            `insert_time` datetime   COMMENT '数据更新时间',
                                            `update_time` datetime   COMMENT '本次数据更新时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='立项金额';





CREATE TABLE `pmsx_project_pay_normal_purchase` (
                                                    `id` varchar(64) NOT NULL  COMMENT '主键',
                                                    `class_name` varchar(64)   COMMENT '创建人',
                                                    `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                    `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                    `owner_id` varchar(64)   COMMENT '拥有者',
                                                    `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                    `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                    `remark` varchar(1024)   COMMENT '备注',
                                                    `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                    `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                    `status` int NOT NULL  COMMENT '状态',
                                                    `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                    `refbn` varchar(50)   COMMENT '参考凭证编码',
                                                    `posid` varchar(50)   COMMENT 'WBS元素',
                                                    `pspid` varchar(50)   COMMENT '项目定义',
                                                    `budat` datetime   COMMENT '借方日期',
                                                    `kstar` varchar(50)   COMMENT '成本要素',
                                                    `txt_two` varchar(50)   COMMENT '成本要素名称',
                                                    `sgtxt` varchar(50)   COMMENT '名称',
                                                    `currency` varchar(50)   COMMENT '币种',
                                                    `post_one` varchar(50)   COMMENT 'CO对象名称',
                                                    `wtgbtr` varchar(50)   COMMENT '业务货币值',
                                                    `insert_time` datetime   COMMENT '数据更新时间',
                                                    `update_time` datetime   COMMENT '本次数据更新时间',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='正常采购金额';


CREATE TABLE `pmsx_project_pay_shop_purchase` (
                                                  `id` varchar(64) NOT NULL  COMMENT '主键',
                                                  `class_name` varchar(64)   COMMENT '创建人',
                                                  `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                  `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                  `owner_id` varchar(64)   COMMENT '拥有者',
                                                  `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                  `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                  `remark` varchar(1024)   COMMENT '备注',
                                                  `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                  `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                  `status` int NOT NULL  COMMENT '状态',
                                                  `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                  `refbn` varchar(50)   COMMENT '参考凭证编码',
                                                  `posid` varchar(50)   COMMENT 'WBS元素',
                                                  `pspid` varchar(50)   COMMENT '项目定义',
                                                  `budat` datetime   COMMENT '借方日期',
                                                  `kstar` varchar(50)   COMMENT '成本要素',
                                                  `txt_two` varchar(50)   COMMENT '成本要素名称',
                                                  `sgtxt` varchar(50)   COMMENT '名称',
                                                  `currency` varchar(50)   COMMENT '币种',
                                                  `post_one` varchar(50)   COMMENT 'CO对象名称',
                                                  `wtgbtr` varchar(50)   COMMENT '业务货币值',
                                                  `insert_time` datetime   COMMENT '数据更新时间',
                                                  `update_time` datetime   COMMENT '本次数据更新时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='商城采购金额';


CREATE TABLE `pmsx_project_pay_actual` (
                                           `id` varchar(64) NOT NULL  COMMENT '主键',
                                           `class_name` varchar(64)   COMMENT '创建人',
                                           `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                           `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                           `owner_id` varchar(64)   COMMENT '拥有者',
                                           `create_time` datetime NOT NULL  COMMENT '创建时间',
                                           `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                           `remark` varchar(1024)   COMMENT '备注',
                                           `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                           `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                           `status` int NOT NULL  COMMENT '状态',
                                           `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                           `psphi` varchar(50)   COMMENT '项目定义',
                                           `objnr` varchar(50)   COMMENT '对象',
                                           `post_one` varchar(50)   COMMENT '对象名称',
                                           `posid` varchar(50) NOT NULL  COMMENT 'WBS编码',
                                           `wogbtr` varchar(50)   COMMENT '对象货币值',
                                           `budat` datetime   COMMENT '过账日期',
                                           `bltxt` varchar(50)   COMMENT '凭证抬头文本',
                                           `belnr` varchar(50)   COMMENT '凭证编码',
                                           `refbn` varchar(50)   COMMENT '参考凭证编码',
                                           `kstar` varchar(64)   COMMENT '陈本要素',
                                           `ktext` varchar(64)   COMMENT '成本要素名称',
                                           `gkont` varchar(50)   COMMENT '冲转科目名',
                                           `ebeln` varchar(50)   COMMENT '采购凭证',
                                           `txz_one` varchar(50)   COMMENT '采购订单文本',
                                           `bukrs` varchar(50)   COMMENT '公司',
                                           `sgtxt` varchar(50)   COMMENT '名称',
                                           `fkber` varchar(64)   COMMENT '功能范围',
                                           `gjahr` varchar(64)   COMMENT '年度',
                                           `insert_time` datetime   COMMENT '数据更新时间',
                                           `update_time` datetime   COMMENT '本次数据更新时间',
                                           PRIMARY KEY (`id`,`posid`)
) ENGINE=InnoDB COMMENT='实际成本金额';


CREATE TABLE `pmsx_project_pay_plan` (
                                         `id` varchar(64) NOT NULL  COMMENT '主键',
                                         `class_name` varchar(64)   COMMENT '创建人',
                                         `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                         `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                         `owner_id` varchar(64)   COMMENT '拥有者',
                                         `create_time` datetime NOT NULL  COMMENT '创建时间',
                                         `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                         `remark` varchar(1024)   COMMENT '备注',
                                         `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                         `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                         `status` int NOT NULL  COMMENT '状态',
                                         `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                         `psphi` varchar(50)   COMMENT '项目定义',
                                         `posid` varchar(50)   COMMENT 'WBS元素',
                                         `post_one` varchar(50)   COMMENT 'WBS元素（描述）',
                                         `wtjhr` varchar(50)   COMMENT 'WBS成本计划',
                                         `kstar` varchar(64)   COMMENT '成本要素',
                                         `ktext` varchar(50)   COMMENT '文本',
                                         `sumwkg` varchar(50)   COMMENT '总计划成本',
                                         `gjahr` varchar(50)   COMMENT '会计年度',
                                         `versn` varchar(50)   COMMENT '版本',
                                         `insert_time` datetime   COMMENT '数据更新时间',
                                         `update_time` datetime   COMMENT '本次数据更新时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='预算金额';
