UPDATE `sys_code_rules` SET `code_number` = 'REQ', `department` = NULL, `code_name` = '需求新增编码', `classification_id` = 'zmz823968efc482c4f658f9635a04036bd71', `class_name` = 'SysCodeRules', `remark` = NULL, `owner_id` = '314j1000000000000000000', `creator_id` = '314j1000000000000000000', `create_time` = '2024-07-19 10:10:53', `modify_id` = '314j1000000000000000000', `modify_time` = '2024-07-19 10:17:52', `status` = 130, `platform_id` = 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', `org_id` = 'rxlm04256c6e0d9d429a89084be972fdfa7f', `unique_key` = NULL, `logic_status` = 1, `rev_key` = '58e0cd2a70b74fe38dfd570d99d9d57a', `next_rev_id` = NULL, `previous_rev_id` = NULL, `code` = NULL, `rev_id` = 'A', `initial_rev_id` = NULL, `rev_order` = 1, `share` = b'1', `build_in` = b'1' WHERE `id` = '9hi11814120694578139136';
