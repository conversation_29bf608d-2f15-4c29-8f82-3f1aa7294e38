-- 作业

-- 默认索引
CREATE INDEX platform_org_index  ON pmsx_job_manage(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pmsx_job_manage(`platform_id`);
CREATE INDEX org_index   ON pmsx_job_manage(`org_id`);
CREATE INDEX status_index   ON pmsx_job_manage(`status`);
CREATE INDEX logic_status_index   ON pmsx_job_manage(`logic_status`);

-- 特有索引
CREATE INDEX plan_scheme_id_index   ON pmsx_job_manage(`plan_scheme_id`);
CREATE INDEX type_index   ON pmsx_job_manage(`type`);
CREATE INDEX number_index   ON pmsx_job_manage(`number`);
CREATE INDEX rsp_user_id_index   ON pmsx_job_manage(`rsp_user_id`);
CREATE INDEX rsp_user_code_index   ON pmsx_job_manage(`rsp_user_code`);
CREATE INDEX n_or_o_index   ON pmsx_job_manage(`n_or_o`);
CREATE INDEX rsp_dept_index   ON pmsx_job_manage(`rsp_dept`);
CREATE INDEX job_base_index   ON pmsx_job_manage(`job_base`);
CREATE INDEX repair_round_index   ON pmsx_job_manage(`repair_round`);
CREATE INDEX project_number_index   ON pmsx_job_manage(`project_number`);
CREATE INDEX phase_index   ON pmsx_job_manage(`phase`);
CREATE INDEX bus_status_index   ON pmsx_job_manage(`bus_status`);

-- 工作包
-- 默认索引
CREATE INDEX platform_org_index  ON pmsx_job_package(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pmsx_job_package(`platform_id`);
CREATE INDEX org_index   ON pmsx_job_package(`org_id`);
CREATE INDEX status_index   ON pmsx_job_package(`status`);
CREATE INDEX logic_status_index   ON pmsx_job_package(`logic_status`);

-- 特有索引
CREATE INDEX job_id_index   ON pmsx_job_package(`job_id`);

-- 作业风险
CREATE INDEX platform_org_index  ON pmsx_job_risk(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pmsx_job_risk(`platform_id`);
CREATE INDEX org_index   ON pmsx_job_risk(`org_id`);
CREATE INDEX status_index   ON pmsx_job_risk(`status`);
CREATE INDEX logic_status_index   ON pmsx_job_risk(`logic_status`);

CREATE INDEX job_id_index           ON pmsx_job_risk(`job_id`);
CREATE INDEX encry_key_index           ON pmsx_job_risk(`encry_key`);

-- 安措
CREATE INDEX platform_org_index  ON pmsx_job_security_measure(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pmsx_job_security_measure(`platform_id`);
CREATE INDEX org_index   ON pmsx_job_security_measure(`org_id`);
CREATE INDEX status_index   ON pmsx_job_security_measure(`status`);
CREATE INDEX logic_status_index   ON pmsx_job_security_measure(`logic_status`);

CREATE INDEX job_id_index      ON pms_project_role_user(`job_id`);
CREATE INDEX encry_key_index           ON pms_project_role_user(`encry_key`);




