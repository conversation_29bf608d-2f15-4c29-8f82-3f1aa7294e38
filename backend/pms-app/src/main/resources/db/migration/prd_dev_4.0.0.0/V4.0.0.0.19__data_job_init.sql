
-- 新增定时器
INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (101, 5, '项目领导-初始化', '2024-07-15 20:27:50', '2024-07-15 20:34:04', 'orion', '', 'CRON', '0 45 2 * * ?', 'DO_NOTHING', 'FIRST', 'projectLeadUserInitXxlJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', '项目领导-初始化', '2024-07-15 20:27:50', '', 0, 0, 0);
