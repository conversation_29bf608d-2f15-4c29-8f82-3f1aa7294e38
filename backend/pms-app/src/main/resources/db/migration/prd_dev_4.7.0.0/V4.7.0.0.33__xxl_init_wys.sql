-- 新增定时器
INSERT INTO xxl_job_info(id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES(3994, 5, '项目计划下发-48H状态推动重试机制', '2025-03-06 09:16:28', '2025-03-06 09:16:28', 'orion', '', 'CRON', '0 45 23 * * ? *', 'DO_NOTHING', 'FIRST', 'projectSchemeTryUpdateStatus', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-06 09:16:28', '', 0, 0, 0);

