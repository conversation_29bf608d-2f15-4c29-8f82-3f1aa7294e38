DROP TABLE IF EXISTS pms_job_to_safety_quality_env;
CREATE TABLE `pms_job_to_safety_quality_env`
(
    `id`                    varchar(64) NOT NULL COMMENT '主键',
    `class_name`            varchar(64) NOT NULL COMMENT '创建人',
    `creator_id`            varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`           datetime    NOT NULL COMMENT '修改时间',
    `owner_id`              varchar(64) NOT NULL COMMENT '拥有者',
    `create_time`           datetime    NOT NULL COMMENT '创建时间',
    `modify_id`             varchar(64) NOT NULL COMMENT '修改人',
    `remark`                varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`          int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `project_id`            varchar(64) NOT NULL COMMENT '项目Id',
    `job_manage_id`         varchar(64) NOT NULL COMMENT '作业id',
    `safety_quality_env_id` varchar(64) NOT NULL COMMENT '隐患id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作业与隐患信息';