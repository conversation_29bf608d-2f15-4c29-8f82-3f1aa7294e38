

CREATE TABLE `pmsx_voucher_num` (
  `id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '主键',
  `class_name` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '创建人',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `owner_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '修改人',
  `remark` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `voucher_num` varchar(64) DEFAULT NULL COMMENT '凭证编号',
  `posting_date` datetime DEFAULT NULL COMMENT '过账日期',
  `confirm_revenue_amount` varchar(255) DEFAULT NULL COMMENT '确认收入金额',
  `reverse_amount` varchar(255) DEFAULT NULL COMMENT '冲销暂估金额',
  `revenue_plan_num` varchar(64) DEFAULT NULL COMMENT '收入计划编号',
  KEY `voucher_num_IDX` (`voucher_num`) USING BTREE,
  KEY `revenue_plan_num_IDX` (`revenue_plan_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='凭证编号关联表';



CREATE TABLE `pmsx_revenue_plan_num` (
  `id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '主键',
  `class_name` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '创建人',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `owner_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '修改人',
  `remark` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) CHARACTER SET utf8 NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `six_num` varchar(6) DEFAULT NULL COMMENT '六位码',
  `revenue_plan_num` varchar(64) DEFAULT NULL COMMENT '收入计划编号',
  `process_theme_fis` varchar(255) DEFAULT NULL COMMENT 'fis流程主题',
  `process_theme_upm` varchar(255) DEFAULT NULL COMMENT 'upm流程主题',
  `current_stage_fis` varchar(255) DEFAULT NULL COMMENT 'fis当前环节',
  `current_stage_upm` varchar(255) DEFAULT NULL COMMENT 'upm当前环节',
  `current_stage_executor_fis` varchar(64) DEFAULT NULL COMMENT 'fis当前环节执行人',
  `current_stage_executor_upm` varchar(64) DEFAULT NULL COMMENT 'upm当前环节执行人',
  KEY `revenue_plan_num_IDX` (`revenue_plan_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收入计划编号关联表';