-- 新增定时器 状态变更
INSERT INTO `xxl_job_info` (`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (1145, 5, '状态变更-作业管理', '2024-11-27 10:42:24', '2024-11-27 10:42:51', 'orion', '', 'CRON', '0 30 5 * * ?', 'DO_NOTHING', 'FIRST', 'matchUpJobManage', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', '状态变更-作业管理', '2024-11-27 10:42:24', '', 1, 0, 1732723199000);
