
-- 建表
CREATE TABLE `pmsx_major_job_start_work_infor` (
                                                   `id` varchar(64) NOT NULL COMMENT '主键',
                                                   `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                   `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                   `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                   `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                                   `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                   `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                   `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                   `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                   `status` int(11) NOT NULL COMMENT '状态',
                                                   `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                   `job_number` varchar(64) DEFAULT NULL COMMENT '工单编号',
                                                   `start_work_date` datetime DEFAULT NULL COMMENT '开工日期',
                                                   `start_work_date_str` varchar(10) DEFAULT NULL COMMENT '开工日期-字符串',
                                                   `morning_status` bit(1) DEFAULT NULL COMMENT '上午开工状态',
                                                   `afternoon_status` bit(1) DEFAULT NULL COMMENT '下午开工状态',
                                                   `night_status` bit(1) DEFAULT NULL COMMENT '夜间状态',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大修工单开工信息';

ALTER TABLE `pmsx_major_job_start_work_infor` ADD INDEX `idx_job_number` (`job_number`);
ALTER TABLE `pmsx_major_job_start_work_infor` ADD INDEX `idx_start_work_date_str` (`start_work_date_str`);
ALTER TABLE `pmsx_major_job_start_work_infor` ADD INDEX `idx_org_id` (`org_id`);
ALTER TABLE `pmsx_major_job_start_work_infor` ADD INDEX `idx_platform_id` (`platform_id`);



CREATE TABLE `pmsx_common_data_auth_role` (
                                              `id` varchar(64) NOT NULL COMMENT '主键',
                                              `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                              `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                              `modify_time` datetime NOT NULL COMMENT '修改时间',
                                              `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                              `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                              `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                              `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                              `status` int(11) NOT NULL COMMENT '状态',
                                              `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                              `data_type` varchar(64) DEFAULT NULL COMMENT '数据类型',
                                              `data_id` varchar(64) DEFAULT NULL COMMENT '数据ID',
                                              `auth_object` varchar(64) DEFAULT NULL COMMENT '授权对象：Role:User',
                                              `role_code` varchar(64) DEFAULT NULL COMMENT '权限code：read,edit',
                                              `object_value` varchar(64) DEFAULT NULL COMMENT '对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用数据权限';

-- 索引
ALTER TABLE `pmsx_common_data_auth_role` ADD INDEX `idx_data_type` (`data_type`);
ALTER TABLE `pmsx_common_data_auth_role` ADD INDEX `idx_data_id` (`data_id`);
ALTER TABLE `pmsx_common_data_auth_role` ADD INDEX `idx_auth_object` (`auth_object`);
ALTER TABLE `pmsx_common_data_auth_role` ADD INDEX `idx_role_code` (`role_code`);
ALTER TABLE `pmsx_common_data_auth_role` ADD INDEX `idx_object_value` (`object_value`);
ALTER TABLE `pmsx_common_data_auth_role` ADD INDEX `idx_org_id` (`org_id`);
ALTER TABLE `pmsx_common_data_auth_role` ADD INDEX `idx_platform_id` (`platform_id`);



CREATE TABLE `pmsx_major_repair_org` (
                                         `id` varchar(64) NOT NULL COMMENT '主键',
                                         `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                         `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                         `modify_time` datetime NOT NULL COMMENT '修改时间',
                                         `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                         `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                         `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                         `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                         `status` int(11) NOT NULL COMMENT '状态',
                                         `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                         `rsp_user_code` varchar(64) DEFAULT NULL COMMENT '责任人编码',
                                         `rsp_user_name` varchar(64) DEFAULT NULL COMMENT '责任人名称',
                                         `rsp_user_id` varchar(64) DEFAULT NULL COMMENT '责任人id',
                                         `parent_id` varchar(64) DEFAULT NULL COMMENT '父级ID',
                                         `chain_path` varchar(1024) DEFAULT NULL COMMENT '全路径',
                                         `repair_round` varchar(64) DEFAULT NULL COMMENT '大修伦次',
                                         `sort` int(11) DEFAULT NULL COMMENT '排序',
                                         `type` varchar(64) DEFAULT NULL COMMENT '组织类型：',
                                         `name` varchar(64) DEFAULT NULL COMMENT '组织名称',
                                         `code` varchar(64) DEFAULT NULL COMMENT '组织编号',
                                         `level` int(11) DEFAULT NULL COMMENT '组织层级',
                                         `begin_time` datetime DEFAULT NULL COMMENT '计划开始时间',
                                         `end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
                                         `actual_begin_time` datetime DEFAULT NULL COMMENT '实际开始时间',
                                         `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
                                         `level_type` varchar(32) DEFAULT NULL COMMENT '大修组织类型 repairRole(大修指挥部角色)，executionSpecialty(执行专业) ,managementRole(管理组-角色)，specialtyTeam(专业班组)',
                                         `repair_org_type` varchar(32) DEFAULT NULL,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大修组织';

-- 索引
ALTER TABLE `pmsx_major_repair_org` ADD INDEX `idx_parent_id` (`parent_id`);
ALTER TABLE `pmsx_major_repair_org` ADD INDEX `idx_repair_round` (`repair_round`);
ALTER TABLE `pmsx_major_repair_org` ADD INDEX `idx_level_type` (`level_type`);
ALTER TABLE `pmsx_major_repair_org` ADD INDEX `idx_org_id` (`org_id`);
ALTER TABLE `pmsx_major_repair_org` ADD INDEX `idx_platform_id` (`platform_id`);
ALTER TABLE `pmsx_major_repair_org` ADD INDEX `idx_parent_id_repair_round` (`parent_id`,`repair_round`);


CREATE TABLE `pmsx_relation_job_assist_to_org` (
                                                   `id` varchar(64) NOT NULL COMMENT '主键',
                                                   `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                   `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                   `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                   `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                                   `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                   `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                   `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                   `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                   `status` int(11) NOT NULL COMMENT '状态',
                                                   `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                   `job_number` varchar(64) DEFAULT NULL COMMENT '工单号',
                                                   `major_repair_org_id` varchar(64) DEFAULT NULL COMMENT '大修组织ID：基本是执行班组',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作业专业协助关系表';

ALTER TABLE `pmsx_relation_job_assist_to_org` ADD INDEX `idx_job_number` (`job_number`);
ALTER TABLE `pmsx_relation_job_assist_to_org` ADD INDEX `idx_major_repair_org_id` (`major_repair_org_id`);
ALTER TABLE `pmsx_relation_job_assist_to_org` ADD INDEX `idx_org_id` (`org_id`);
ALTER TABLE `pmsx_relation_job_assist_to_org` ADD INDEX `idx_platform_id` (`platform_id`);


CREATE TABLE `pmsx_relation_org_to_job` (
                                            `id` varchar(64) NOT NULL COMMENT '主键',
                                            `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                            `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                            `modify_time` datetime NOT NULL COMMENT '修改时间',
                                            `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                            `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                            `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                            `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                            `status` int(11) NOT NULL COMMENT '状态',
                                            `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                            `repair_org_id` varchar(64) DEFAULT NULL COMMENT '大修组织ID',
                                            `job_number` varchar(64) DEFAULT NULL COMMENT '作业工单号',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关系-大修组织工单关系';

ALTER TABLE `pmsx_relation_org_to_job` ADD INDEX `idx_job_number` (`job_number`);
ALTER TABLE `pmsx_relation_org_to_job` ADD INDEX `idx_major_repair_org_id` (`repair_org_id`);
ALTER TABLE `pmsx_relation_org_to_job` ADD INDEX `idx_org_id` (`org_id`);
ALTER TABLE `pmsx_relation_org_to_job` ADD INDEX `idx_platform_id` (`platform_id`);




-- CREATE TABLE `pmsx_relation_org_to_material` (
--                                                  `id` varchar(64) NOT NULL COMMENT '主键',
--                                                  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
--                                                  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
--                                                  `modify_time` datetime NOT NULL COMMENT '修改时间',
--                                                  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
--                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
--                                                  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
--                                                  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
--                                                  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
--                                                  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
--                                                  `status` int(11) NOT NULL COMMENT '状态',
--                                                  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
--                                                  `repair_org_id` varchar(64) DEFAULT NULL COMMENT '大修组织id',
--                                                  `material_id` varchar(64) DEFAULT NULL COMMENT '物质管理id',
--                                                  `material_number` varchar(64) DEFAULT NULL COMMENT '物质编码',
--                                                  PRIMARY KEY (`id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大修组织和大修组织物资关系';


-- 索引
ALTER TABLE `pmsx_relation_org_to_material` ADD INDEX `idx_repair_org_id` (`repair_org_id`);
ALTER TABLE `pmsx_relation_org_to_material` ADD INDEX `idx_material_id` (`material_id`);
ALTER TABLE `pmsx_relation_org_to_material` ADD INDEX `idx_material_number` (`material_number`);
ALTER TABLE `pmsx_relation_org_to_material` ADD INDEX `idx_org_id` (`org_id`);
ALTER TABLE `pmsx_relation_org_to_material` ADD INDEX `idx_platform_id` (`platform_id`);



-- CREATE TABLE `pmsx_relation_org_to_person` (
--                                                `id` varchar(64) NOT NULL COMMENT '主键',
--                                                `creator_id` varchar(64) NOT NULL COMMENT '创建人',
--                                                `create_time` datetime NOT NULL COMMENT '创建时间',
--                                                `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
--                                                `person_id` varchar(64) DEFAULT NULL COMMENT '人员id',
--                                                `repair_org_id` varchar(64) DEFAULT NULL COMMENT '大修组织id',
--                                                PRIMARY KEY (`id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大修组织人员关系表';

ALTER TABLE `pmsx_relation_org_to_person` ADD INDEX `idx_person_id` (`person_id`);
ALTER TABLE `pmsx_relation_org_to_person` ADD INDEX `idx_repair_org_id` (`repair_org_id`);
ALTER TABLE `pmsx_relation_org_to_person` ADD INDEX `idx_logic_status` (`logic_status`);
