CREATE TABLE `pmsx_project_full_size_report` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
  `project_number` varchar(200) DEFAULT NULL COMMENT '项目编码',
  `internal_external` varchar(100) DEFAULT NULL COMMENT '集团内外',
  `nuclear_power` varchar(100) DEFAULT NULL COMMENT '核电',
  `base` varchar(200) DEFAULT NULL COMMENT '基地',
  `business_classification` varchar(200) DEFAULT NULL COMMENT '业务分类',
  `operating_income` decimal(10,0) DEFAULT NULL COMMENT '营业收入',
  `direct_purchase_cost` decimal(10,0) DEFAULT NULL COMMENT '直接采购成本',
  `direct_travel_cost` decimal(10,0) DEFAULT NULL COMMENT '直接差旅成本',
  `labor_cost` decimal(10,0) DEFAULT NULL COMMENT '人工成本',
  `technical_configuration` decimal(10,0) DEFAULT NULL COMMENT '技术配置',
  `project_direct_cost_gross` decimal(10,0) DEFAULT NULL COMMENT '项目直接成本毛利',
  `project_direct_cost_gross_margin` decimal(10,0) DEFAULT NULL COMMENT '项目直接成本毛利利率',
  `daily_administrative_expenses` decimal(10,0) DEFAULT NULL COMMENT '日常行政管理费',
  `software_usage_fee` decimal(10,0) DEFAULT NULL COMMENT '设备/软件使用费',
  `taxe_surcharge` decimal(10,0) DEFAULT NULL COMMENT '税金及附加',
  `project_gross_profit` decimal(10,0) DEFAULT NULL COMMENT '项目毛利',
  `project_gross_margin` decimal(10,0) DEFAULT NULL COMMENT '项目毛利率',
  `management_fee` decimal(10,0) DEFAULT NULL COMMENT '管理费',
  `project_profit` decimal(10,0) DEFAULT NULL COMMENT '项目利润',
  `project_profit_margin` decimal(10,0) DEFAULT NULL COMMENT '项目利润率',
  `year` int(11) DEFAULT NULL COMMENT '年度',
  `project_name` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `company_id` varchar(200) DEFAULT NULL COMMENT '公司名称',
  `wbs_expertise_center` varchar(255) DEFAULT NULL COMMENT '专业中心',
  `parent_id` varchar(64) DEFAULT NULL COMMENT '父级id',
  `type` varchar(255) DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`id`),
  KEY `business_classification` (`business_classification`),
  KEY `project_id` (`project_id`),
  KEY `wbs_expertise_center` (`wbs_expertise_center`),
  KEY `company_id` (`company_id`),
  KEY `year` (`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目全口径报表';

CREATE TABLE `pmsx_cost_share` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `project_id` varchar(64) DEFAULT NULL COMMENT '项目Id',
  `project_number` varchar(100) DEFAULT NULL COMMENT '项目编码',
  `company_id` varchar(64) DEFAULT NULL COMMENT '公司Id',
  `company_number` varchar(200) DEFAULT NULL COMMENT '公司编码',
  `internal_external` varchar(100) DEFAULT NULL COMMENT '集团内外',
  `nuclear_power` varchar(100) DEFAULT NULL COMMENT '核电',
  `base` varchar(100) DEFAULT NULL COMMENT '基地',
  `wbs_object` varchar(200) DEFAULT NULL COMMENT 'WBS对象',
  `wbs_object_name` varchar(255) DEFAULT NULL COMMENT 'wbs对象名称',
  `year` int(11) DEFAULT NULL COMMENT '年度',
  `wbs_expertise_center` varchar(255) DEFAULT NULL COMMENT 'WBS所属专业中心',
  `wbs_professional_center` varchar(200) DEFAULT NULL COMMENT 'WBS所属利润中心',
  `business_classification` varchar(200) DEFAULT NULL COMMENT '业务分类',
  `amount` decimal(10,0) DEFAULT NULL COMMENT '金额',
  `apportionment_classification` varchar(200) DEFAULT NULL COMMENT '分摊分类',
  `cost_type` varchar(100) DEFAULT NULL COMMENT '成本类型',
  `cost_element_categorie` varchar(200) DEFAULT NULL COMMENT '成本元素大类',
  `cost_element` varchar(200) DEFAULT NULL COMMENT '成本元素',
  `send_dept_id` varchar(64) DEFAULT NULL COMMENT '发送部门',
  `source_send_dept` varchar(200) DEFAULT NULL COMMENT '源发送部门',
  `period` varchar(100) DEFAULT NULL COMMENT '期间',
  `credential_code` varchar(200) DEFAULT NULL COMMENT '凭证编码',
  `voucher_date` datetime DEFAULT NULL COMMENT '凭证日期',
  `subject_code` varchar(100) DEFAULT NULL COMMENT '科目代码',
  `subejct_name` varchar(100) DEFAULT NULL COMMENT '科目名称',
  `voucher_profit_center_code` varchar(100) DEFAULT NULL COMMENT '凭证利润中心代码',
  `voucher_profit_center_name` varchar(100) DEFAULT NULL COMMENT '凭证利润中心名称',
  `voucher_cost_center_code` varchar(100) DEFAULT NULL COMMENT '凭证成本中心代码',
  `voucher_cost_center_name` varchar(100) DEFAULT NULL COMMENT '凭证成本中心名称',
  PRIMARY KEY (`id`),
  KEY `project_id` (`project_id`),
  KEY `company_id` (`company_id`),
  KEY `year` (`year`),
  KEY `business_classification` (`business_classification`),
  KEY `wbs_expertise_center` (`wbs_expertise_center`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成本分摊';