CREATE TABLE `pmsx_mile_stone_log` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`milestone_id` varchar(64)   COMMENT '里程碑id',
`edit_desc` varchar(256)   COMMENT '执行操作',
`edit_message` varchar(512)   COMMENT '执行说明',
`file_count` int   COMMENT '附件数',
`edit_person` varchar(256)   COMMENT '执行人',
`edit_time` datetime   COMMENT '执行时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='里程碑执行记录';



