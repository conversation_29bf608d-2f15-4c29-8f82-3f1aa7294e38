INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818526572160352256', '市场经营-需求管理-新增需求', 'PMS_REQUIREMENT_ADD', 'vub01818475890292162560', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 13:58:16', 'user00000000000000000100000000000000', '2024-07-31 13:59:04', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818540492967247872', '市场经营-需求管理-需求分发', 'PMS_REQUIREMENT_EDIT', 'vub01818479410462457856', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:53:35', 'user00000000000000000100000000000000', '2024-07-31 14:56:54', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818540584541487104', '市场经营-需求管理-生成报价', 'PMS_REQUIREMENT_OFFER', 'vub01818479790965522432', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:53:57', 'user00000000000000000100000000000000', '2024-07-31 14:56:55', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818540651981701120', '市场经营-需求管理-可投标', 'PMS_REQUIREMENT_KTB', 'vub01818480198802866176', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:54:13', 'user00000000000000000100000000000000', '2024-07-31 14:56:55', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818540708214734848', '市场经营-需求管理-已中标', 'PMS_REQUIREMENT_YZB', 'vub01818480961801289728', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:54:26', 'user00000000000000000100000000000000', '2024-07-31 14:56:56', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818541013706866688', '市场经营-需求管理-未中标', 'PMS_REQUIREMENT_WZB', 'vub01818481117225418752', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:55:39', 'user00000000000000000100000000000000', '2024-07-31 14:56:57', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818541098117234688', '市场经营-合同管理-生成合同', 'PMS_CONTRACT_ADD', 'vub01818481610332962816', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:55:59', 'user00000000000000000100000000000000', '2024-07-31 14:56:58', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818541162948591616', '市场经营-合同管理-待签署', 'PMS_CONTRACT_DQS', 'vub01818481873294852096', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:56:15', 'user00000000000000000100000000000000', '2024-07-31 14:56:58', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1818541313679294464', '市场经营-合同管理-履行中', 'PMS_CONTRACT_LXZ', 'vub01818482524095643648', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, 'user00000000000000000100000000000000', '2024-07-31 14:56:51', 'user00000000000000000100000000000000', '2024-07-31 14:56:59', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');


INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818475890292162560', '市场经营-需求管理-新增需求', 'PMS_REQUIREMENT_ADD', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 10:36:53', 'user00000000000000000100000000000000', '2024-07-31 14:59:52', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818479410462457856', '市场经营-需求管理-需求分发', 'PAS_REQUIREMENT_ISSUE', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 10:50:52', 'user00000000000000000100000000000000', '2024-07-31 14:59:51', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818479790965522432', '市场经营-需求管理-生成报价', 'PAS_REQUIREMENT_OFFER', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 10:52:23', 'user00000000000000000100000000000000', '2024-07-31 14:59:51', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818480198802866176', '市场经营-需求管理-可投标', 'PAS_REQUIREMENT_KTB', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 10:54:00', 'user00000000000000000100000000000000', '2024-07-31 14:59:50', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818480961801289728', '市场经营-需求管理-已中标', 'PAS_REQUIREMENT_YZB', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 10:57:02', 'user00000000000000000100000000000000', '2024-07-31 14:59:50', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818481117225418752', '市场经营-需求管理-未中标', 'PAS_REQUIREMENT_WZB', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 10:57:39', 'user00000000000000000100000000000000', '2024-07-31 14:59:49', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818481610332962816', '市场经营-合同管理-生成合同', 'PAS_CONTRACT_ADD', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 10:59:36', 'user00000000000000000100000000000000', '2024-07-31 14:59:49', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818481873294852096', '市场经营-合同管理-待签署', 'PAS_CONTRACT_DQS', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 11:00:39', 'user00000000000000000100000000000000', '2024-07-31 14:59:48', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01818482524095643648', '市场经营-合同管理-履行中', 'PAS_CONTRACT_LXZ', '$desc$', '1', 1, '', 'user00000000000000000100000000000000', '2024-07-31 11:03:14', 'user00000000000000000100000000000000', '2024-07-31 14:59:48', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');


 