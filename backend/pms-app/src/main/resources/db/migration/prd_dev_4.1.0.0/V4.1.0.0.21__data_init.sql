INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (112, 5, '里程碑验收日期临期提醒', '2024-08-08 20:01:10', '2024-08-08 20:02:17', 'orion', '', 'CRON', '0 30 8 * * ?', 'DO_NOTHING', 'FIRST', 'milestoneDeadlineXxlJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-08-08 20:01:10', '', 0, 0, 0);
INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (113, 4, '公司任务消息未录邮件', '2024-08-08 20:07:11', '2024-08-08 20:07:31', 'Orion', '', 'CRON', '0 0 0 18 * ?', 'DO_NOTHING', 'FIRST', 'CompanyTaskEmailJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-08-08 20:07:11', '', 0, 0, 0);

INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1821515375418421248', '里程碑验收日期提醒', 'NODE_MILESTONE_NOTIFY', 'vub01821513140496117760', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-08-08 19:54:42', '314j1000000000000000000', '2024-08-08 19:54:45', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1821427430141341696', '报价临期截止提醒', 'NODE_QUTOTATION_DEADLINE', 'vub01821426005076226048', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1820705362382827520', 1, NULL, '314j1000000000000000000', '2024-08-08 14:05:14', '314j1000000000000000000', '2024-08-08 14:05:17', NULL, NULL, NULL, 1, b'1', b'1');


INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01821513939427143680', '市场经营-里程碑-验收日期临近提醒', 'PMS_MILESTONE_NOTIFY', '您好！\n您所参与的【$contractName$】合同的里程碑【$milestoneName$】预计验收日期为【$deadline$】，已临近，请知悉。', '1', 1, '', '314j1000000000000000000', '2024-08-08 19:49:00', '314j1000000000000000000', '2024-08-08 19:49:11', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01821513140496117760', '市场经营-里程碑-验收日期临近提醒-标题', 'PMS_MILESTONE_NOTIFY_TITLE', '市场合同里程碑临期通知', '1', 1, '', '314j1000000000000000000', '2024-08-08 19:45:49', '314j1000000000000000000', '2024-08-08 19:49:10', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01821426847669956608', '市场经营-报价到期提醒', 'PMS_QUOTATION_DEADLINE', '您好！\n您可参与的【$name$】项目已临近报价截止时间【$deadline$】，请知悉。请至少在截止日期前一天完成报价审批流。', '1', 1, '', '314j1000000000000000000', '2024-08-08 14:02:56', '314j1000000000000000000', '2024-08-08 14:02:57', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01821426005076226048', '市场经营-报价到期提醒-标题', 'PMS_QUOTATION_DEADLINE_TITLE', '报价临近截止日期提醒', '1', 1, '', '314j1000000000000000000', '2024-08-08 13:59:35', '314j1000000000000000000', '2024-08-08 14:02:59', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
