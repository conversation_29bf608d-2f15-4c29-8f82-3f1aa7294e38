CREATE TABLE `pms_amperering_config_dept` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `dept_code` varchar(100) DEFAULT NULL COMMENT '部门编码',
  `dept_number` varchar(100) DEFAULT NULL COMMENT '部门编码',
  `is_dept_score_show` bit(1) DEFAULT NULL COMMENT '部门绩效考核是否展示 0 否，1 是',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人id',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织id',
  `check_problems_sort` int(11) DEFAULT NULL COMMENT '问题排查的序号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人id',
  `standard_score` double(10,2) DEFAULT NULL COMMENT '标准分',
  `is_check_problems_show` bit(1) DEFAULT NULL COMMENT '隐患排查是否展示',
  `class_name` varchar(100) DEFAULT NULL,
  `owner_id` varchar(100) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  `platform_id` varchar(64) DEFAULT NULL COMMENT '平台id',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `logic_status` int(11) DEFAULT NULL COMMENT '是否使用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='安质环部门维护';

CREATE TABLE `pms_amperering_config_job` (
  `id` varchar(64) NOT NULL COMMENT '主键id',
  `job_name` varchar(64) DEFAULT NULL COMMENT '作业名称',
  `is_show` bit(1) DEFAULT NULL COMMENT '是否看板展示',
  `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织id',
  `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人id',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `class_name` varchar(100) DEFAULT NULL,
  `owner_id` varchar(100) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  `platform_id` varchar(64) DEFAULT NULL COMMENT '平台id',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `logic_status` int(11) DEFAULT NULL COMMENT '是否使用',
  `job_code` varchar(64) DEFAULT NULL COMMENT '作业编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='安质环作业信息维护';

CREATE TABLE `pms_amperering_config_kpi` (
  `id` varchar(64) NOT NULL COMMENT '主键id',
  `event_code` varchar(100) DEFAULT NULL COMMENT '事件等级code',
  `event_level` varchar(64) NOT NULL COMMENT '事件等级',
  `kpi_code` varchar(64) DEFAULT NULL COMMENT 'kpi指标code',
  `sort` int(11) DEFAULT NULL COMMENT '序号',
  `org_id` varchar(64) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人id',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人id',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `class_name` varchar(100) DEFAULT NULL,
  `owner_id` varchar(100) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  `platform_id` varchar(64) DEFAULT NULL COMMENT '平台id',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `logic_status` int(11) DEFAULT NULL COMMENT '是否使用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='安质环考核指标';

CREATE TABLE `pms_amperering_event_check_data_info` (
  `id` varchar(100) NOT NULL COMMENT '主键',
  `check_number` varchar(64) DEFAULT NULL COMMENT '检查问题编号',
  `check_subject` varchar(200) DEFAULT NULL COMMENT '检查主题',
  `event_level` varchar(200) DEFAULT NULL COMMENT '事件等级',
  `event_type` varchar(200) DEFAULT NULL COMMENT '事件类型',
  `event_address` varchar(255) DEFAULT NULL COMMENT '事件地点',
  `event_address_code` varchar(100) DEFAULT NULL COMMENT '事件地点code',
  `event_date` datetime DEFAULT NULL COMMENT '事件发生时间',
  `check_person` varchar(50) DEFAULT NULL COMMENT '监察人',
  `check_person_dept` varchar(50) DEFAULT NULL COMMENT '监察人所在部门',
  `is_find` bit(1) DEFAULT NULL COMMENT '是否监督发现',
  `event_desc` varchar(500) DEFAULT NULL COMMENT '时间描述',
  `person_in_charge` varchar(200) DEFAULT NULL COMMENT '直接责任人',
  `zr_dept_code` varchar(200) DEFAULT NULL COMMENT '直接部门',
  `gk_dept_code` varchar(200) DEFAULT NULL COMMENT '直接归口部门',
  `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织id',
  `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `class_name` varchar(100) DEFAULT NULL,
  `owner_id` varchar(100) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  `platform_id` varchar(64) DEFAULT NULL COMMENT '平台id',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `logic_status` int(11) DEFAULT NULL COMMENT '是否使用',
  `reviewer_name` varchar(64) DEFAULT NULL COMMENT '检查人名称',
  `dept_id` varchar(64) DEFAULT NULL COMMENT '检查人所在部门ID',
  `dept_name` varchar(64) DEFAULT NULL COMMENT '检查人所在部门名称',
  `dept_code` varchar(64) DEFAULT NULL COMMENT '检测人所在部门编号',
  `is_major_repair` varchar(64) DEFAULT NULL COMMENT '是否大修',
  `hidden_event` varchar(64) DEFAULT NULL COMMENT '隐患',
  `is_closed` varchar(64) DEFAULT NULL COMMENT '是否已关闭',
  `current_process` varchar(64) DEFAULT NULL COMMENT '当前流程',
  `rsp_dept_code` varchar(64) DEFAULT NULL COMMENT '直接责任部门code',
  `assessment_level` varchar(64) DEFAULT NULL COMMENT '考核级别',
  `major_repair_turn` varchar(64) DEFAULT NULL COMMENT '大修轮次',
  `is_assessed` bit(1) DEFAULT NULL COMMENT '是否考核',
  `pyramid_category` varchar(256) DEFAULT NULL COMMENT '金字塔类别',
  `reviewer_number` varchar(64) DEFAULT NULL COMMENT '检查人编号',
  `rsp_dept_name` varchar(64) DEFAULT NULL COMMENT '直接责任部门名称',
  `event_position` varchar(256) DEFAULT NULL COMMENT '事件位置',
  `classification_type` varchar(128) DEFAULT NULL COMMENT '分类类型',
  `hidden_danger_type` varchar(64) DEFAULT NULL COMMENT '隐患类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='安质环事件检查数据';

CREATE TABLE `pms_amperering_event_code` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `event_code` varchar(32) DEFAULT NULL COMMENT '事件编码',
  `event_level` varchar(64) DEFAULT NULL COMMENT '事件等级',
  `score` decimal(50,2) DEFAULT NULL COMMENT '考核分数',
  `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织id',
  `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人id',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_delete` bit(1) DEFAULT NULL COMMENT '状态 1 正常  0 ',
  `is_group_kpi` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否集团考核指标：1 是 ，0 否',
  `is_monitor_index` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否监控指标：1 是，0 否',
  `is_calculate_days` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否计算天数：1 是，0 否',
  `class_name` varchar(64) DEFAULT NULL,
  `owner_id` varchar(100) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  `platform_id` varchar(64) DEFAULT NULL COMMENT '平台id',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `logic_status` int(11) DEFAULT NULL COMMENT '是否使用',
  `parent_name` varchar(64) DEFAULT NULL COMMENT '父类名称',
  `parent_id` varchar(100) DEFAULT NULL COMMENT '父类id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `pms_amperering_event_code_eventcode_un` (`event_code`),
  UNIQUE KEY `pms_amperering_event_code_eventlevel_un` (`event_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='安质环事件码表';

CREATE TABLE `pms_amperering_kpi_score` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `check_subject` varchar(64) DEFAULT NULL COMMENT '事件主题',
  `event_address` varchar(64) DEFAULT NULL COMMENT '事件地点',
  `event_level` varchar(64) DEFAULT NULL COMMENT '事件等级',
  `event_date` datetime DEFAULT NULL COMMENT '事发时间',
  `score` double DEFAULT NULL COMMENT '考核得分',
  `duty_person_dept` varchar(200) DEFAULT NULL COMMENT '被考核人/部门',
  `event_desc` varchar(200) DEFAULT NULL COMMENT '事件描述',
  `is_find` bit(1) DEFAULT NULL COMMENT '是否外部监督发现',
  `person_in_charge` varchar(64) DEFAULT NULL COMMENT '直接负责人',
  `zr_dept_code` varchar(64) DEFAULT NULL COMMENT '直接责任部门code',
  `gk_dept_code` varchar(64) DEFAULT NULL COMMENT '归口责任部门',
  `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织id',
  `class_name` varchar(64) DEFAULT NULL,
  `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
  `owner_id` varchar(64) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) DEFAULT NULL COMMENT '平台id',
  `status` varchar(10) DEFAULT NULL COMMENT '状态',
  `logic_status` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='安质环安全责任考核分';