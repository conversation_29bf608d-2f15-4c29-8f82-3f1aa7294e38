/*xxlJob*/
INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (116, 5, '大修专项日常培训计划', '2024-08-13 09:56:29', '2024-08-13 09:56:29', 'orion', '', 'CRON', '0 0 10 * * ?', 'DO_NOTHING', 'FIRST', 'repairTrainingNoticeJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-08-13 09:56:29', '', 0, 0, 0);
INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (115, 5, '日常作业-作业进展反馈', '2024-08-12 19:59:46', '2024-08-12 19:59:46', 'orion', '', 'CRON', '0 0 10 * * ?', 'DO_NOTHING', 'FIRST', 'jobManageProcessFeedback', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-08-12 19:59:46', '', 0, 0, 0);
INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (114, 5, '每日工作进展反馈', '2024-08-12 19:02:03', '2024-08-12 19:02:03', 'orion', '', 'CRON', '0 0 10 * * ?', 'DO_NOTHING', 'FIRST', 'majorRepairPlanProcessHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-08-12 19:02:03', '', 0, 0, 0);

/*template*/
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01823181528217821184', '大修日常专项培训-内容', 'PMS_REPAIR_TRAIN', '您好\n您于【$startTime$】组织的【$trainBase$】【$trainName$】培训即将到期，请完成培训，并上传培训证明材料。', '1', 1, '', '314j1000000000000000000', '2024-08-13 10:15:24', '314j1000000000000000000', '2024-08-13 10:37:31', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01823181340434636800', '大修日常专项培训-标题', 'PMS_REPAIR_TRAIN_TITLE', '培训证明上传通知', '1', 1, '', '314j1000000000000000000', '2024-08-13 10:14:39', '314j1000000000000000000', '2024-08-13 10:14:41', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822967133961330688', '每日任务工作进展反馈-内容', 'PMS_JOB_PROCESS_FB', '您好\n请登陆【精益一体化系统】及时更新【工作名称】【工作抬头】本日的工作进展。', '1', 1, '', '314j1000000000000000000', '2024-08-12 20:03:28', '314j1000000000000000000', '2024-08-12 20:03:30', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822966932345331712', '每日任务工作进展-标题', 'PMS_JOB_PROCESS_FB_TITLE', '工作进展反馈通知', '1', 1, '', '314j1000000000000000000', '2024-08-12 20:02:40', '314j1000000000000000000', '2024-08-12 20:03:31', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822952382128594944', '大修-工作进展反馈通知-内容', 'PMS_MAJOR_PROCESS', '您好\n请登陆【精益一体化系统】及时更新【$workName$】【$workTitle$】本日的工作进展。', '1', 1, '', '314j1000000000000000000', '2024-08-12 19:04:51', '314j1000000000000000000', '2024-08-12 19:04:53', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822952058651287552', '大修-每日工作进展反馈-标题', 'PMS_MAJOR_PROCESS_TITLE', '工作进展反馈通知', '1', 1, '', '314j1000000000000000000', '2024-08-12 19:03:34', '314j1000000000000000000', '2024-08-12 19:03:36', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822911705252503552', '大修管理-重大项目选择', 'PMS_REPAIR_PROJECT', '您好\n您所负责的【$workName$】【$workTitle$】由大修指挥部认定为重大项目。请进入【精益一体化系统】查看。', '1', 1, '', '314j1000000000000000000', '2024-08-12 16:23:13', '314j1000000000000000000', '2024-08-12 16:23:15', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822911340184477696', '大修管理-重大项目选择-标题', 'PMS_REPAIR_IMPORTANT_TITLE', '重大项目作业认定通知', '1', 1, '', '314j1000000000000000000', '2024-08-12 16:21:46', '314j1000000000000000000', '2024-08-12 16:22:08', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822888114616147968', '项目计划-计划下发-大修作业-大修指挥部', 'PMS_PLAN_ALLOCATION_LEADER', '您好\n【$name$】项目已完成涉及大修的计划任务制定。请进入【精益一体化系统】查看。', '1', 1, '', '314j1000000000000000000', '2024-08-12 14:49:29', '314j1000000000000000000', '2024-08-12 14:49:30', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822887832045887488', '项目计划-计划下发-大修作业-大修指挥部-标题', 'PMS_PLAN_ALLOCATION_LEADER_TITLE', '项目计划下发通知', '1', 1, '', '314j1000000000000000000', '2024-08-12 14:48:21', '314j1000000000000000000', '2024-08-12 14:48:24', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822842472795611136', '项目计划-计划下发-计划负责人-标题', 'NODE_PLAN_ALLOCATION_HEAD_TITLE', '项目计划下发通知', '1', 1, '', '314j1000000000000000000', '2024-08-12 11:48:07', '314j1000000000000000000', '2024-08-12 11:48:09', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01822842280499355648', '项目计划-计划下发-计划负责人', 'NODE_PLAN_ALLOCATION_HEAD', '您好\n【$name$】项目已完成计划任务制定。您所负责的项目计划任务已下发，请进入【精益一体化系统】查看。', '1', 1, '', '314j1000000000000000000', '2024-08-12 11:47:21', '314j1000000000000000000', '2024-08-12 11:47:24', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');

/*node*/
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1823182962883698688', '大修专项日常培训通知', 'NODE_REPAIR_TRAIN', 'vub01823181340434636800', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn13893df059794c34ab19f3e2160d187e', 1, NULL, '314j1000000000000000000', '2024-08-13 10:21:06', '314j1000000000000000000', '2024-08-13 10:21:10', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1822968395087880192', '每日任务工作进展反馈', 'NODE_JOB_PROCESS_FB', 'vub01822966932345331712', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn13893df059794c34ab19f3e2160d187e', 1, NULL, '314j1000000000000000000', '2024-08-12 20:08:29', '314j1000000000000000000', '2024-08-12 20:08:34', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1822952876205023232', '大修-工作进展反馈通知', 'NODE_MAJOR_PROCESS', 'vub01822952058651287552', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn13893df059794c34ab19f3e2160d187e', 1, NULL, '314j1000000000000000000', '2024-08-12 19:06:49', '314j1000000000000000000', '2024-08-12 19:06:53', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1822912225518166016', '大修重大项目选择提醒', 'NODE_REPAIR_IMPROTANT', 'vub01822911340184477696', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn13893df059794c34ab19f3e2160d187e', 1, NULL, '314j1000000000000000000', '2024-08-12 16:25:17', '314j1000000000000000000', '2024-08-12 16:25:21', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1822889380880719872', '项目计划-计划下发-大修作业', 'NODE_PLAN_ALLOCATION_LEADER', 'vub01822887832045887488', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-08-12 14:54:31', '314j1000000000000000000', '2024-08-12 16:19:34', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1822843065463349248', '项目计划-计划分配-计划下发-负责人', 'PMS_PLAN_ALLOCATION_HEAD', 'vub01822842472795611136', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn13893df059794c34ab19f3e2160d187e', 1, NULL, '314j1000000000000000000', '2024-08-12 11:50:28', '314j1000000000000000000', '2024-08-12 11:50:42', NULL, NULL, NULL, 1, b'1', b'1');
