ALTER  TABLE pmsx_job_manage
    add COLUMN supervisory_staff_id VARCHAR(64) COMMENT '监管人员Id',
	add COLUMN supervisory_staff_code VARCHAR(64) COMMENT '监管人员工号',
	add COLUMN supervisory_staff_name VA<PERSON>HAR(64) COMMENT '监管人员名称',
	add COLUMN manage_person_id VARCHAR(64) COMMENT '管理人员Id',
	add COLUMN manage_person_code VARCHAR(64) COMMENT '管理人员工号',
	add COLUMN manage_person_name VARCHAR(64) COMMENT '管理人员名称',
	add COLUMN work_place VARCHAR(64) COMMENT '工作地址',
	add COLUMN job_dept_id VARCHAR(64) COMMENT '作业部门ID',
	add COLUMN job_dept_code VARCHAR(64) COMMENT '作业部门编码',
	add COLUMN job_dept_name VARCHAR(64) COMMENT '作业部门名称';

-- ALTER TABLE pmsx_job_material
--     add COLUMN in_date datetime COMMENT '计划入场日期',
-- 	add COLUMN out_date datetime COMMENT '计划出场日期';

ALTER TABLE pmsx_person_job_post_Equ
    CHANGE form_base_code  from_record_id VARCHAR(64) COMMENT '被等效的落地(人员岗位)ID';


DROP TABLE IF EXISTS pmsx_job_height_risk;
CREATE TABLE `pmsx_job_height_risk` (
                                        `id` varchar(64) NOT NULL  COMMENT '主键',
                                        `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                        `create_time` datetime NOT NULL  COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                        `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                        `status` int NOT NULL  COMMENT '状态',
                                        `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                        `risk_level` varchar(64)   COMMENT '风险级别',
                                        `risk_type_name` varchar(128)   COMMENT '风险类型',
                                        `judgment_standards` varchar(600)   COMMENT '判断标准',
                                        `job_number` varchar(64)   COMMENT '作业编号',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业高风险';



DROP TABLE IF EXISTS pmsx_job_node_status;
CREATE TABLE `pmsx_job_node_status` (
                                        `id` varchar(64) NOT NULL  COMMENT '主键',
                                        `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                        `create_time` datetime NOT NULL  COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                        `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                        `status` int NOT NULL  COMMENT '状态',
                                        `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                        `job_id` varchar(64)   COMMENT '作业ID',
                                        `node_key_json` varchar(500)   COMMENT '存放所有节点key,拼接（拥有就表示点亮）',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业节点执行状态表';




ALTER TABLE pmsx_person_mange
    change COLUMN actual_enter_date act_in_date  datetime DEFAULT NULL COMMENT '实际入场日期',
    change COLUMN actual_leave_date act_out_date datetime DEFAULT NULL COMMENT '实际离场日期',
    change COLUMN plan_enter_date in_date  datetime DEFAULT NULL COMMENT '计划入场日期',
    change COLUMN plan_leave_date out_date datetime DEFAULT NULL COMMENT '计划离场日期';



ALTER TABLE pmsx_person_manage_ledger
    change COLUMN actual_enter_date act_in_date  datetime DEFAULT NULL COMMENT '实际入场日期',
    change COLUMN actual_leave_date act_out_date datetime DEFAULT NULL COMMENT '实际离场日期',
    change COLUMN plan_enter_date in_date  datetime DEFAULT NULL COMMENT '计划入场日期',
    change COLUMN plan_leave_date out_date datetime DEFAULT NULL COMMENT '计划离场日期';


ALTER TABLE pmsx_material_manage
    add COLUMN in_date  datetime DEFAULT NULL COMMENT '计划入场日期',
	add COLUMN act_out_date datetime DEFAULT NULL COMMENT '实际离场日期',
	change COLUMN enter_date act_in_date datetime DEFAULT NULL COMMENT '实际入场日期';

ALTER TABLE pmsx_material_manage
    add COLUMN out_date  datetime DEFAULT NULL COMMENT '计划离场日期';



ALTER TABLE pmsx_material_out_manage
    change COLUMN out_date act_out_date datetime DEFAULT NULL COMMENT '实际离场日期',
    change COLUMN enter_date act_in_date datetime DEFAULT NULL COMMENT '实际入场日期';
ALTER TABLE pmsx_material_out_manage
    add COLUMN in_date  datetime DEFAULT NULL COMMENT '计划入场日期',
	add COLUMN out_date datetime DEFAULT NULL COMMENT '计划离场日期';

ALTER TABLE pmsx_job_material
    add COLUMN in_date  datetime DEFAULT NULL COMMENT '计划入场日期',
	add COLUMN out_date datetime DEFAULT NULL COMMENT '计划离场日期',
	add COLUMN act_in_date  datetime DEFAULT NULL COMMENT '实际入场日期',
	add COLUMN act_out_date datetime DEFAULT NULL COMMENT '实际离场日期';



ALTER TABLE pmsx_job_post_authorize
    add COLUMN person_manage_id VARCHAR(64) COMMENT '人员管理ID',
	add COLUMN person_ledger_id VARCHAR(64)  COMMENT '人员台账ID';

ALTER TABLE pmsx_person_manage_ledger
    add COLUMN person_manage_id VARCHAR(64) COMMENT '人员管理ID';


ALTER TABLE pmsx_person_mange
    add COLUMN main_Work_Center VARCHAR(64) COMMENT '主工作中心',
	add COLUMN is_job bit(1) DEFAULT b'1' COMMENT '是否作业';

ALTER TABLE pmsx_person_manage_ledger
    add COLUMN main_Work_Center VARCHAR(64) COMMENT '主工作中心',
	add COLUMN is_job bit(1) DEFAULT b'1' COMMENT '是否作业';


ALTER TABLE pmsx_person_mange
    add COLUMN is_finish_out_handover bit(1) DEFAULT b'1' COMMENT '是否完成离厂交接，离场WBC测量(必要时)',
	add COLUMN is_again_in bit(1) DEFAULT b'1' COMMENT '是否再次入场';
--  未执行


ALTER TABLE pmsx_material_manage
    add COLUMN is_again_in bit(1) DEFAULT b'1' COMMENT '是否再次入场';

ALTER TABLE pmsx_material_out_manage
    add COLUMN source_id  varchar(64) DEFAULT NULL COMMENT '数据来源ID';

ALTER TABLE pmsx_material_out_manage
    add COLUMN is_pass  bit(1) DEFAULT NULL COMMENT '是否合格',
	add COLUMN is_available  bit(1) DEFAULT NULL COMMENT '是否可用';


ALTER TABLE pmsx_material_manage
    add COLUMN is_available  bit(1) DEFAULT NULL COMMENT '是否可用';


CREATE TABLE `pmsx_job_person_record` (
                                          `id` varchar(64) NOT NULL  COMMENT '主键',
                                          `class_name` varchar(64)   COMMENT '创建人',
                                          `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                          `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                          `owner_id` varchar(64)   COMMENT '拥有者',
                                          `create_time` datetime NOT NULL  COMMENT '创建时间',
                                          `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                          `remark` varchar(1024)   COMMENT '备注',
                                          `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                          `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                          `status` int NOT NULL  COMMENT '状态',
                                          `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                          `job_id` varchar(64)   COMMENT '作业ID',
                                          `user_code` varchar(64)   COMMENT '人员code',
                                          `person_manage_id` varchar(64)   COMMENT '人员管理ID',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业人员记录表';


CREATE TABLE `pmsx_job_material_record` (
                                            `id` varchar(64) NOT NULL  COMMENT '主键',
                                            `class_name` varchar(64)   COMMENT '创建人',
                                            `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                            `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                            `owner_id` varchar(64)   COMMENT '拥有者',
                                            `create_time` datetime NOT NULL  COMMENT '创建时间',
                                            `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                            `remark` varchar(1024)   COMMENT '备注',
                                            `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                            `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                            `status` int NOT NULL  COMMENT '状态',
                                            `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                            `job_id` varchar(64) NOT NULL  COMMENT '作业ID',
                                            `materia_code` varchar(64)   COMMENT '物资code',
                                            `materia_manage_id` varchar(64)   COMMENT '物资管理ID',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='作业物资记录表';


ALTER  TABLE pmsx_person_job_post_authorize MODIFY `job_code` varchar(64) DEFAULT NULL COMMENT '作业编号';

	
	