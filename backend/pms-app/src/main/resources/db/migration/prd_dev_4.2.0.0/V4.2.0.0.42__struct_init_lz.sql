CREATE TABLE `pmsx_scheme_to_material`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64) COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime    NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64) COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) NOT NULL COMMENT '修改人',
    `remark`          varchar(1024) COMMENT '备注',
    `platform_id`     varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`          int         NOT NULL COMMENT '状态',
    `logic_status`    int         NOT NULL COMMENT '逻辑删除字段',
    `plan_scheme_id`  varchar(64) COMMENT '项目计划ID',
    `repair_round`    varchar(64) COMMENT '大修伦次',
    `material_number` varchar(64) COMMENT '物资编码（固定资产编码）',
    `material_id`     varchar(64) COMMENT '物资ID：物资管理的Id',
    `material_name`   varchar(128) COMMENT '物资名称',
    `base_code`       varchar(64) COMMENT '基地编码',
    `demand_num`      int COMMENT '需求数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='计划相关的物资';

INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl1823959393200967680', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_scheme_to_material', 'SchemeToMaterial', '9wrf', NULL, 'pmsx', 'schemetomaterial', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '计划相关的物资', '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);

INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270850', 'gtjl1823959393200967680', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270851', 'gtjl1823959393200967680', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270852', 'gtjl1823959393200967680', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270853', 'gtjl1823959393200967680', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270854', 'gtjl1823959393200967680', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270855', 'gtjl1823959393200967680', 'remark', 'Varchar', 1024, NULL, 'remark', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270856', 'gtjl1823959393200967680', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270857', 'gtjl1823959393200967680', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270858', 'gtjl1823959393200967680', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823959393272270859', 'gtjl1823959393200967680', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 13:46:22', '314j1000000000000000000', '2024-08-15 13:46:22', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823965122351796224', 'gtjl1823959393200967680', 'planSchemeId', 'Varchar', 64, NULL, 'planschemeid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 14:09:07', '314j1000000000000000000', '2024-08-15 14:09:07', '项目计划ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823965181172715520', 'gtjl1823959393200967680', 'repairRound', 'Varchar', 64, NULL, 'repairround', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 14:09:21', '314j1000000000000000000', '2024-08-15 14:09:21', '大修伦次', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823965863976046592', 'gtjl1823959393200967680', 'materialNumber', 'Varchar', 64, NULL, 'materialmumber', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 14:12:04', '314j1000000000000000000', '2024-08-15 14:12:14', '物资编码（固定资产编码）', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823966079911399424', 'gtjl1823959393200967680', 'materialId', 'Varchar', 64, NULL, 'materialid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 14:12:56', '314j1000000000000000000', '2024-08-15 18:29:05', '物资ID：物资管理的Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823966283419029504', 'gtjl1823959393200967680', 'materialName', 'Varchar', 128, NULL, 'materialname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 14:13:44', '314j1000000000000000000', '2024-08-15 14:13:44', '物资名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1823966362590711808', 'gtjl1823959393200967680', 'baseCode', 'Varchar', 64, NULL, 'basecode', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-15 14:14:03', '314j1000000000000000000', '2024-08-15 14:14:03', '基地编码', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1826875828587884544', 'gtjl1823959393200967680', 'demandNum', 'Integer', 11, NULL, 'demandnum', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-23 14:55:14', '314j1000000000000000000', '2024-08-23 14:55:14', '需求数量', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');