INSERT INTO pmi_data_power_rule (id,code,name,conf,data_obj_id,data_obj_name,data_obj_table) VALUES
('8','BasicUserVO','员工能力库权限','{"ops":"OR","children":[{"key":{"value":"dept_code","label":"部门code","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@leaderFindDepartmentByDeptCodeExp.apply()}","paramMethodExp":"@leaderFindDepartmentByDeptCodeExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"user_code","label":"用户code","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@userCodeExp.apply()}","paramMethodExp":"@userCodeExp.exp(#param)","methodParam":"1","type":"CUSTOM"}}]}','','BasicUserVO','pmsx_basic_user')
;

INSERT INTO pmi_data_power_rule (id,code,name,conf,data_obj_id,data_obj_name,data_obj_table) VALUES
('9','TrainEquivalent','培训等效','{"ops":"OR","children":[{"key":{"value":"user_code","label":"用户code","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@userCodeExp.apply()}","paramMethodExp":"@userCodeExp.exp(#param)","methodParam":"1","type":"CUSTOM"}}]}',NULL,'TrainEquivalent','pmsx_train_equivalent')
;