DROP TABLE IF EXISTS pmsx_job_height_risk;
CREATE TABLE `pmsx_job_height_risk` (
                                        `id` varchar(64) NOT NULL COMMENT '主键',
                                        `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                        `modify_time` datetime NOT NULL COMMENT '修改时间',
                                        `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                        `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                        `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                        `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                        `status` int(11) NOT NULL COMMENT '状态',
                                        `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                        `risk_level` varchar(64) DEFAULT NULL COMMENT '风险级别',
                                        `risk_type_name` varchar(128) DEFAULT NULL COMMENT '风险类型',
                                        `judgment_standards` varchar(600) DEFAULT NULL COMMENT '判断标准',
                                        `job_number` varchar(64) DEFAULT NULL COMMENT '作业编号',
                                        `is_height_risk` bit(1) DEFAULT NULL COMMENT '是否高风险',
                                        `work_topics` varchar(200) DEFAULT NULL COMMENT '工作主题',
                                        `work_order_no` varchar(64) DEFAULT NULL COMMENT '工单号',
                                        `job_address` varchar(200) DEFAULT NULL COMMENT '作业地点',
                                        `plan_commencement_date` datetime DEFAULT NULL COMMENT '计划开工时间',
                                        `job_content` varchar(255) DEFAULT NULL COMMENT '工作描述',
                                        `operating_dept` varchar(64) DEFAULT NULL COMMENT '作业部门',
                                        `work_owner_name` varchar(64) DEFAULT NULL COMMENT '项目负责人',
                                        `work_owner_phone` varchar(20) DEFAULT NULL COMMENT '负责人电话',
                                        `manager_name` varchar(64) DEFAULT NULL COMMENT '管理人',
                                        `manager_phone` varchar(20) DEFAULT NULL COMMENT '管理人电话',
                                        `current_phase` varchar(64) DEFAULT NULL COMMENT '作业过程状态',
                                        `process_status` int(11) DEFAULT NULL COMMENT '过程状态',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作业高风险';