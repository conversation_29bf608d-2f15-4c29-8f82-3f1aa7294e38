CREATE OR REPLACE VIEW `quotation_requirement_view` AS
SELECT `pqm`.`id`                    AS `id`,
       `pqm`.`class_name`            AS `class_name`,
       `pqm`.`creator_id`            AS `creator_id`,
       `pqm`.`modify_time`           AS `modify_time`,
       `pqm`.`owner_id`              AS `owner_id`,
       `pqm`.`create_time`           AS `create_time`,
       `pqm`.`modify_id`             AS `modify_id`,
       `pqm`.`remark`                AS `remark`,
       `pqm`.`platform_id`           AS `platform_id`,
       `pqm`.`org_id`                AS `org_id`,
       `pqm`.`status`                AS `status`,
       `pqm`.`logic_status`          AS `logic_status`,
       `pqm`.`requirement_number`    AS `requirement_number`,
       `pqm`.`quotation_id`          AS `quotation_id`,
       `pqm`.`busi_goal`             AS `busi_goal`,
       `pqm`.`busi_goal_cont`        AS `busi_goal_cont`,
       `pqm`.`busi_info`             AS `busi_info`,
       `pqm`.`busi_info_cont`        AS `busi_info_cont`,
       `pqm`.`cost_est_res`          AS `cost_est_res`,
       `pqm`.`cost_est_res_cont`     AS `cost_est_res_cont`,
       `pqm`.`cost_est_rr_res`       AS `cost_est_rr_res`,
       `pqm`.`cost_est_hr_res_cont`  AS `cost_est_hr_res_cont`,
       `pqm`.`rev_anal`              AS `rev_anal`,
       `pqm`.`rev_anal_cont`         AS `rev_anal_cont`,
       `pqm`.`other_info`            AS `other_info`,
       `pqm`.`other_info_cont`       AS `other_info_cont`,
       `pqm`.`quote_content`         AS `quote_content`,
       `pqm`.`quote_plan_detail`     AS `quote_plan_detail`,
       `pqm`.`quote_amt`             AS `quote_amt`,
       `pqm`.`currency`              AS `currency`,
       `pqm`.`floor_price`           AS `floor_price`,
       `pqm`.`issue_time`            AS `issue_time`,
       `pqm`.`issuer`                AS `issuer`,
       `pqm`.`result`                AS `result`,
       `pqm`.`result_note`           AS `result_note`,
       `pqm`.`quotation_status`      AS `quotation_status`,
       `pqm`.`requirement_id`        AS `requirement_id`,
       `pqm`.`fieldwork`             AS `fieldwork`,
       `pqm`.`incl_financing_trade`  AS `incl_financing_trade`,
       `pqm`.`quote_accept_pen`      AS `quote_accept_pen`,
       `pqm`.`quote_accept_com`      AS `quote_accept_com`,
       `pqm`.`issue_way`             AS `issue_way`,
       `pqm`.`quotation_name`        AS `quotation_name`,
       `pqm`.`quote_remark`          AS `quote_remark`,
       `pqm`.`obsolete_reason`       AS `obsolete_reason`,
       `pqm`.`re_quote_reason`       AS `re_quote_reason`,
       `pqm`.`quote_execu_condition` AS `quote_execu_condition`,
       `pqm`.`fin_trade_bus`         AS `fin_trade_bus`,
       `pqm`.`business_type`         AS `business_type`,
       `prm`.`req_ownership`         AS `req_ownership`,
       `prm`.`tech_res`              AS `tech_res`,
       `prm`.`business_person`       AS `business_person`,
       `pqm`.`re_quotation_id`       AS `re_quotation_id`
FROM (
         `pms_requirement_mangement` `prm`
             LEFT JOIN `pmsx_quotation_management` `pqm` ON ((
             `prm`.`id` = CONVERT(`pqm`.`requirement_id` USING utf8mb4))));