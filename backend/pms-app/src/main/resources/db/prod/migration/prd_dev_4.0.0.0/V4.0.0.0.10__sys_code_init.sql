delete from sys_code_mapping_relation where id in ('e96m1b52b30a752840218f79c42872a99999','e96m522acbcbc6b14e80b5474d93bc8348e2');
INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1b52b30a752840218f79c42872a99999', 'number', '9hi1844640d89a4a4423bf05fa9ece35a49f', 'RiskType', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-11-09 16:36:22', 'user00000000000000000100000000000000', '2022-11-09 16:36:22', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'e96m1b52b30a752840218f79c42872a33333', 1, b'1', b'1');

INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m522acbcbc6b14e80b5474d93bc8348e2', 'number', '9hi1844640d89a4a4423bf05fa9ece35a49f', 'RiskTypeAttribute', 'SysCodeMappingRelation', '风险类型属性', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2022-11-26 11:27:56', 'user00000000000000000100000000000000', '2022-11-26 11:27:56', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, NULL, 1, b'1', b'1');




delete from sys_code_segment where id in ('s3rd3082c17eadaf4caaa3b1324c9435da69','s3rd45f47d8c67154e8d9dc69ad9944cf114','s3rd56873a3f4add4914b4cc60e6eb51400f','s3rdae3ddec1a41d4e65809019a524052c8f','s3rdf9f92a56906f47ebb1a48351f735ab2e');
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd3082c17eadaf4caaa3b1324c9435da69', '流水码', '0', '9hi1844640d89a4a4423bf05fa9ece35a49f', 'f9g64bbe8ee7dd5b4ebcb8a9327cd96e6f06', 'piPer', '', '', '6', 5, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-12-27 16:57:11', 'user00000000000000000100000000000000', '2022-10-24 16:10:14', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd3082c17eadaf4caaa3b1324c9435da69', 1);

INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd45f47d8c67154e8d9dc69ad9944cf114', '间隔2', '0', '9hi1844640d89a4a4423bf05fa9ece35a49f', '', 'fixedValue', '', '_', '0', 4, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-12-27 16:56:46', 'user00000000000000000100000000000000', '2022-10-24 16:10:14', 1, 'ykovb40e9fb1061b46fb9 6c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd45f47d8c67154e8d9dc69ad9944cf114', 1);

INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd56873a3f4add4914b4cc60e6eb51400f', '间隔1', '0', '9hi1844640d89a4a4423bf05fa9ece35a49f', '', 'fixedValue', '', '_', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-12-27 16:56:15', 'user00000000000000000100000000000000', '2022-10-24 16:10:14', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rd56873a3f4add4914b4cc60e6eb51400f', 1);

INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rdae3ddec1a41d4e65809019a524052c8f', '参数集模板', '0', '9hi1844640d89a4a4423bf05fa9ece35a49f', '', 'fixedValue', '', 'CSJM', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-12-27 16:56:33', 'user00000000000000000100000000000000', '2022-10-24 16:10:14', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rdae3ddec1a41d4e65809019a524052c8f', 1);

INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rdf9f92a56906f47ebb1a48351f735ab2e', '数据中心', '0', '9hi1844640d89a4a4423bf05fa9ece35a49f', '', 'fixedValue', '', 'PDM', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-12-27 16:56:02', 'user00000000000000000100000000000000', '2022-10-24 16:10:15', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 's3rdf9f92a56906f47ebb1a48351f735ab2e', 1);





delete from sys_code_rules where id  = '9hi1844640d89a4a4423bf05fa9ece35a49f';

INSERT INTO `sys_code_rules`(`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi1844640d89a4a4423bf05fa9ece35a49f', 'PDM_008', NULL, '参数集模板', 'zmz8a70b51ad1da841ff87a4c2a24f8c2e73', 'SysCodeRules', NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2021-12-27 16:48:47', 'user00000000000000000100000000000000', '2022-10-24 16:10:12', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, '9hi1844640d89a4a4423bf05fa9ece35a49f', 1, 'dc06fe273f824b0f834231d5ce8fe045', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');
