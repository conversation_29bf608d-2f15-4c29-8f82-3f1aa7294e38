--  sql
alter table pms_project_scheme add COLUMN  `is_work_job` bit(1)   COMMENT '是否作业';
alter table pms_project_scheme add COLUMN  `enforce_type` varchar(64)   COMMENT '实施类型';
alter table pms_project_scheme add COLUMN  `enforce_scope` varchar(64)   COMMENT '实施区域/范围';
alter table pms_project_scheme add COLUMN  `work_content` varchar(256)   COMMENT '工作内容';
alter table pms_project_scheme add COLUMN  `repair_round` varchar(64)   COMMENT '大修轮次';
alter table pms_project_scheme add COLUMN  `is_carry_materials` bit(1)  COMMENT '是否携带物资';
alter table pms_project_scheme add COLUMN `enforce_base_place`  varchar(64)  COMMENT '实施地点';
alter table pms_project_scheme add COLUMN `enforce_base_place_name`  varchar(128)  COMMENT '实施地点名称';