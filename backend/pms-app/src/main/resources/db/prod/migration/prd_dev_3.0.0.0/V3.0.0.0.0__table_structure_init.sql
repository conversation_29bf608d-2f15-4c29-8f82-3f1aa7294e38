-- DDL for pms_acceptance_form:
DROP TABLE IF EXISTS pms_acceptance_form;
CREATE TABLE `pms_acceptance_form`
(
    `id`               varchar(64) NOT NULL COMMENT '主键',
    `complete_user_id` varchar(64)   DEFAULT NULL COMMENT '验收完成人Id',
    `modify_id`        varchar(64) NOT NULL COMMENT '修改人',
    `remark`           varchar(1024) DEFAULT NULL COMMENT '备注',
    `project_id`       varchar(64)   DEFAULT NULL COMMENT '项目Id',
    `creator_id`       varchar(64) NOT NULL COMMENT '创建人',
    `class_name`       varchar(64)   DEFAULT NULL COMMENT '创建人',
    `org_id`           varchar(64) NOT NULL COMMENT '业务组织Id',
    `logic_status`     int(11)     NOT NULL COMMENT '逻辑删除字段',
    `type`             varchar(20)   DEFAULT NULL COMMENT '验收单类型',
    `modify_time`      datetime    NOT NULL COMMENT '修改时间',
    `complete_time`    datetime      DEFAULT NULL COMMENT '验收完成时间',
    `create_time`      datetime    NOT NULL COMMENT '创建时间',
    `status`           int(11)     NOT NULL COMMENT '状态',
    `owner_id`         varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `number`           varchar(64)   DEFAULT NULL COMMENT '验收单编号',
    `platform_id`      varchar(64) NOT NULL COMMENT '平台ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `pms_acceptance_form_projectId_idx` (`project_id`) USING BTREE,
    KEY `pms_acceptance_form_type_idx` (`type`) USING BTREE,
    KEY `pms_acceptance_form_number_idx` (`number`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='验收单';

-- DDL for pms_acceptance_form_file:
DROP TABLE IF EXISTS pms_acceptance_form_file;
CREATE TABLE `pms_acceptance_form_file`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `acceptance_form_id` varchar(64)   DEFAULT NULL COMMENT '验收单Id',
    `owner_id`           varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `org_id`             varchar(64) NOT NULL COMMENT '业务组织Id',
    `class_name`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`        datetime    NOT NULL COMMENT '修改时间',
    `file_id`            varchar(64)   DEFAULT NULL COMMENT 'res文件Id',
    `remark`             varchar(1024) DEFAULT NULL COMMENT '备注',
    `status`             int(11)     NOT NULL COMMENT '状态',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `logic_status`       int(11)     NOT NULL COMMENT '逻辑删除字段',
    `platform_id`        varchar(64) NOT NULL COMMENT '平台ID',
    `modify_id`          varchar(64) NOT NULL COMMENT '修改人',
    `creator_id`         varchar(64) NOT NULL COMMENT '创建人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='验收文件';

-- DDL for pms_acceptance_form_item:
DROP TABLE IF EXISTS pms_acceptance_form_item;
CREATE TABLE `pms_acceptance_form_item`
(
    `id`                     varchar(64) NOT NULL COMMENT '主键',
    `logic_status`           int(11)     NOT NULL COMMENT '逻辑删除字段',
    `status`                 int(11)     NOT NULL COMMENT '状态',
    `acceptance_form_id`     varchar(64)   DEFAULT NULL COMMENT '验收单Id',
    `owner_id`               varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `remark`                 varchar(1024) DEFAULT NULL COMMENT '备注',
    `modify_id`              varchar(64) NOT NULL COMMENT '修改人',
    `class_name`             varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`            datetime    NOT NULL COMMENT '修改时间',
    `creator_id`             varchar(64) NOT NULL COMMENT '创建人',
    `platform_id`            varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) NOT NULL COMMENT '业务组织Id',
    `supplies_management_id` varchar(64)   DEFAULT NULL COMMENT '采购计划立项Id',
    `create_time`            datetime    NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `pms_acceptance_form_item_combine_idx` (`acceptance_form_id`, `supplies_management_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='验收单关联明细';

-- DDL for pms_before_after_to_plan:
DROP TABLE IF EXISTS pms_before_after_to_plan;
CREATE TABLE `pms_before_after_to_plan`
(
    `id`          varchar(64) NOT NULL COMMENT 'id',
    `type`        int(11)     NOT NULL COMMENT '前后置类型',
    `to_id`       varchar(64) DEFAULT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) DEFAULT NULL,
    `from_id`     varchar(64) DEFAULT NULL,
    `creator_id`  varchar(64) DEFAULT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    DEFAULT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='前后置关系对于计划';

-- DDL for pms_budget_supplementary_information:
DROP TABLE IF EXISTS pms_budget_supplementary_information;
CREATE TABLE `pms_budget_supplementary_information`
(
    `id`                            varchar(64) NOT NULL COMMENT '主键',
    `class_name`                    varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id`                    varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`                   datetime    NOT NULL COMMENT '修改时间',
    `owner_id`                      varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time`                   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`                     varchar(64) NOT NULL COMMENT '修改人',
    `remark`                        text COMMENT '备注',
    `platform_id`                   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                        varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                        int(11)     NOT NULL COMMENT '状态',
    `logic_status`                  int(11)     NOT NULL COMMENT '逻辑删除字段',
    `budget_number`                 varchar(64) DEFAULT NULL COMMENT '预算编号（预算编码对应POSID）',
    `duty_cost_center`              varchar(20) DEFAULT NULL COMMENT '责任成本中心(fkstl)',
    `duty_cost_center_name`         varchar(10) DEFAULT NULL COMMENT '责任成本中心名称(ktext_fkstl',
    `approval_cost_center`          varchar(20) DEFAULT NULL COMMENT '立项归口成本中心(zlxgk)',
    `project_definition`            varchar(40) DEFAULT NULL COMMENT '项目定义(pspid)',
    `request_cost_center`           varchar(20) DEFAULT NULL COMMENT '请求成本中心(akstl)',
    `product_big_class`             varchar(1)  DEFAULT NULL COMMENT '项目产品大类(zxmcpdl)',
    `product_little_class`          varchar(1)  DEFAULT NULL COMMENT '项目产品小类(zxmcpfl)',
    `unit`                          varchar(20) DEFAULT NULL COMMENT '机组(zjz)',
    `unit_desc`                     varchar(20) DEFAULT NULL COMMENT '机组描述(zjzms)',
    `tax_rate_grade`                varchar(6)  DEFAULT NULL COMMENT '税率档（zsld）',
    `tax_rate_grade_desc`           varchar(50) DEFAULT NULL COMMENT '税率档描述(zsldt)',
    `is_control`                    varchar(1)  DEFAULT NULL COMMENT '是否可控（zis_control）',
    `is_pay_cash`                   varchar(1)  DEFAULT NULL COMMENT '是否付现(zsffx )',
    `is_technology_input`           varchar(1)  DEFAULT NULL COMMENT '是否科技投入 (zsfkjtr)',
    `is_reasonable_reserve`         varchar(1)  DEFAULT NULL COMMENT '是否使用专项储备(zsfsyzxcb)',
    `is_pm_system`                  varchar(1)  DEFAULT NULL COMMENT 'is_pm_system',
    `is_capitalization`             varchar(2)  DEFAULT NULL COMMENT '是否资本化(wbs)(prps_zsfzbh)',
    `approval_cost_center_name`     varchar(20) DEFAULT NULL COMMENT '立项归口成本中心名称(ktext_lxgk)',
    `project_cefinition_name`       varchar(40) DEFAULT NULL COMMENT '项目定义描述(proj_post1)',
    `request_cost_center_name`      varchar(20) DEFAULT NULL COMMENT '项目定义描述(proj_post1)',
    `post_account_cost_center`      varchar(20) DEFAULT NULL COMMENT '过账成本中心（kostl ）',
    `post_account_cost_center_name` varchar(40) DEFAULT NULL COMMENT '过账成本中心名称(kostlms)',
    `product_big_class_name`        varchar(40) DEFAULT NULL COMMENT '项目产品大类名称(zxmcpdlmc)',
    `product_little_class_name`     varchar(40) DEFAULT NULL COMMENT '项目产品小类名称(zxmcpxlmc)',
    `is_lower_middle_put_fund`      varchar(1)  DEFAULT NULL COMMENT '是否使用中低放处置基金(zzdfczjj)',
    `budget_project_number`         varchar(20) DEFAULT NULL COMMENT '预算项目编码(zysxmbm)',
    `budget_project_number_desc`    varchar(50) DEFAULT NULL COMMENT '预算项目描述(zysxmbmms)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='预算补充信息表';

-- DDL for pms_change_management:
DROP TABLE IF EXISTS pms_change_management;
CREATE TABLE `pms_change_management`
(
    `id`           varchar(64)  NOT NULL COMMENT 'id',
    `reason`       varchar(255) DEFAULT NULL COMMENT '原因',
    `preview_path` varchar(128) DEFAULT NULL COMMENT '预览图',
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='变更管理';

-- DDL for pms_contract_our_signed_main:
DROP TABLE IF EXISTS pms_contract_our_signed_main;
CREATE TABLE `pms_contract_our_signed_main`
(
    `id`                     varchar(64)  NOT NULL COMMENT '主键',
    `class_name`             varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`            datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime     NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`                 int(11)      NOT NULL COMMENT '状态',
    `logic_status`           int(11)      NOT NULL COMMENT '逻辑删除字段',
    `contract_number`        varchar(64)  NOT NULL COMMENT '合同编号',
    `contract_id`            varchar(64)  NOT NULL COMMENT '合同id',
    `signed_main_name`       varchar(100) NOT NULL COMMENT '签约主体全称',
    `company_duty_paragraph` varchar(100) NOT NULL COMMENT '公司税号',
    `bus_contact_person`     varchar(100)  DEFAULT NULL COMMENT '商务联系人',
    `bus_contact_phone`      varchar(20)   DEFAULT NULL COMMENT '商务联系人电话',
    `project_contact_person` varchar(100)  DEFAULT NULL COMMENT '项目联系人',
    `project_contact_phone`  varchar(20)   DEFAULT NULL COMMENT '项目联系人电话',
    `contract_email`         varchar(100)  DEFAULT NULL COMMENT '联系邮箱',
    `contact_address`        varchar(100)  DEFAULT NULL COMMENT '联系地址',
    `sort`                   int(11)       DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='甲方签约主体';

-- DDL for pms_contract_pay_node:
DROP TABLE IF EXISTS pms_contract_pay_node;
CREATE TABLE `pms_contract_pay_node`
(
    `id`                 varchar(64)    NOT NULL COMMENT '主键',
    `class_name`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64)    NOT NULL COMMENT '创建人',
    `modify_time`        datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime       NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64)    NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64)    NOT NULL COMMENT '业务组织Id',
    `status`             int(11)        NOT NULL COMMENT '状态',
    `logic_status`       int(11)        NOT NULL COMMENT '逻辑删除字段',
    `contract_number`    varchar(64)    NOT NULL COMMENT '合同编号',
    `contract_id`        varchar(64)    NOT NULL COMMENT '合同id',
    `settlement_type`    varchar(64)    NOT NULL COMMENT '结算类型',
    `pay_type`           varchar(64)    NOT NULL COMMENT '支付类型',
    `init_plan_pay_date` datetime       NOT NULL COMMENT '初始计划支付时间',
    `init_plan_pay_amt`  decimal(20, 2) NOT NULL COMMENT '初始计划支付金额',
    `pay_percentage`     decimal(5, 2)  NOT NULL COMMENT '支付百分比',
    `pay_desc`           varchar(100)  DEFAULT NULL COMMENT '支付说明',
    `sort`               int(11)       DEFAULT NULL COMMENT '排序',
    `pay_date`           datetime      DEFAULT NULL COMMENT '支付日期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='合同支付节点信息';

-- DDL for pms_contract_pay_node_change:
DROP TABLE IF EXISTS pms_contract_pay_node_change;
CREATE TABLE `pms_contract_pay_node_change`
(
    `id`                 varchar(64)    NOT NULL COMMENT '主键',
    `class_name`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64)    NOT NULL COMMENT '创建人',
    `modify_time`        datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime       NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64)    NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64)    NOT NULL COMMENT '业务组织Id',
    `status`             int(11)        NOT NULL COMMENT '状态',
    `logic_status`       int(11)        NOT NULL COMMENT '逻辑删除字段',
    `contract_number`    varchar(64)    NOT NULL COMMENT '合同编号',
    `contract_id`        varchar(64)    NOT NULL COMMENT '合同id',
    `settlement_type`    varchar(64)    NOT NULL COMMENT '结算类型',
    `pay_type`           varchar(64)    NOT NULL COMMENT '支付类型',
    `init_plan_pay_date` datetime       NOT NULL COMMENT '初始计划支付时间',
    `init_plan_pay_amt`  decimal(20, 2) NOT NULL COMMENT '初始计划支付金额',
    `pay_percentage`     decimal(5, 2)  NOT NULL COMMENT '支付百分比',
    `pay_desc`           varchar(1000) DEFAULT NULL COMMENT '支付说明',
    `sort`               int(11)       DEFAULT NULL COMMENT '排序',
    `change_id`          varchar(64)    NOT NULL COMMENT '变更申请单id',
    `change_number`      varchar(64)    NOT NULL COMMENT '变更申请单号',
    `node_id`            varchar(64)   DEFAULT NULL COMMENT '节点id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='合同支付节点信息';

-- DDL for pms_contract_pay_node_confirm:
DROP TABLE IF EXISTS pms_contract_pay_node_confirm;
CREATE TABLE `pms_contract_pay_node_confirm`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`          datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime    NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`               int(11)     NOT NULL COMMENT '状态',
    `logic_status`         int(11)     NOT NULL COMMENT '逻辑删除字段',
    `number`               varchar(64) NOT NULL COMMENT '审核编号',
    `audit_user_id`        varchar(64) NOT NULL COMMENT '材料审核人',
    `submit_user_id`       varchar(64) NOT NULL COMMENT '材料提交人',
    `audit_date`           datetime      DEFAULT NULL COMMENT '审核时间',
    `submit_date`          datetime      DEFAULT NULL COMMENT '提交时间',
    `service_complete`     varchar(64)   DEFAULT NULL COMMENT '服务确认是否已完成',
    `confirm_desc`         text COMMENT '节点确认说明',
    `audit_desc`           text COMMENT '审核意见',
    `contract_id`          varchar(64) NOT NULL COMMENT '项目合同id',
    `actual_audit_user_id` varchar(64)   DEFAULT NULL COMMENT '实际材料审核人',
    `audit_id`             varchar(64)   DEFAULT NULL COMMENT '审核记录id',
    `submit_id`            varchar(64)   DEFAULT NULL COMMENT '提交Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同支付节点确认';

-- DDL for pms_contract_pay_node_confirm_audit_record:
DROP TABLE IF EXISTS pms_contract_pay_node_confirm_audit_record;
CREATE TABLE `pms_contract_pay_node_confirm_audit_record`
(
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`   datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`        int(11)     NOT NULL COMMENT '状态',
    `logic_status`  int(11)     NOT NULL COMMENT '逻辑删除字段',
    `audit_date`    datetime    NOT NULL COMMENT '审核时间',
    `audit_comment` text COMMENT '审核意见',
    `audit_user_id` varchar(64) NOT NULL COMMENT '审核用户Id',
    `confirm_id`    varchar(64) NOT NULL COMMENT '支付节点确认id',
    `submit_id`     varchar(64)   DEFAULT NULL COMMENT '提交Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='合同支付节点确认审核记录';

-- DDL for pms_contract_pay_node_confirm_node:
DROP TABLE IF EXISTS pms_contract_pay_node_confirm_node;
CREATE TABLE `pms_contract_pay_node_confirm_node`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `confirm_id`   varchar(64) NOT NULL COMMENT '确认id',
    `node_id`      varchar(64) NOT NULL COMMENT '节点id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同支付节点确认节点信息';

-- DDL for pms_contract_pay_node_status_confirm:
DROP TABLE IF EXISTS pms_contract_pay_node_status_confirm;
CREATE TABLE `pms_contract_pay_node_status_confirm`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `contract_id`  varchar(64) NOT NULL COMMENT '合同id',
    `node_id`      varchar(64) NOT NULL COMMENT '节点id',
    `confirm_id`   varchar(64) NOT NULL COMMENT '确认id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同支付节点状态确认';

-- DDL for pms_contract_supplier_signed_main:
DROP TABLE IF EXISTS pms_contract_supplier_signed_main;
CREATE TABLE `pms_contract_supplier_signed_main`
(
    `id`                     varchar(64)  NOT NULL COMMENT '主键',
    `class_name`             varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`            datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime     NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`                 int(11)      NOT NULL COMMENT '状态',
    `logic_status`           int(11)      NOT NULL COMMENT '逻辑删除字段',
    `contract_id`            varchar(64)  NOT NULL COMMENT '合同id',
    `contract_number`        varchar(64)  NOT NULL COMMENT '合同编号',
    `signed_main_name`       varchar(100) NOT NULL COMMENT '签约主体全称',
    `company_duty_paragraph` varchar(100) NOT NULL COMMENT '公司税号',
    `bus_contact_person`     varchar(100)  DEFAULT NULL COMMENT '商务联系人',
    `bus_contact_phone`      varchar(20)   DEFAULT NULL COMMENT '商务联系人电话',
    `project_contact_person` varchar(100)  DEFAULT NULL COMMENT '项目联系人',
    `project_contact_phone`  varchar(20)   DEFAULT NULL COMMENT '项目联系人电话',
    `contract_email`         varchar(100)  DEFAULT NULL COMMENT '联系邮箱',
    `contact_address`        varchar(100)  DEFAULT NULL COMMENT '联系地址',
    `sort`                   int(11)       DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='乙方签约主体';

-- DDL for pms_deliver_goals:
DROP TABLE IF EXISTS pms_deliver_goals;
CREATE TABLE `pms_deliver_goals`
(
    `id`               varchar(64)  NOT NULL COMMENT '主键',
    `name`             varchar(255) NOT NULL COMMENT '名称',
    `class_name`       varchar(64)  DEFAULT NULL COMMENT '创建人',
    `creator_id`       varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`      datetime     NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)  NOT NULL COMMENT '拥有者',
    `create_time`      datetime     NOT NULL COMMENT '创建时间',
    `modify_id`        varchar(64)  NOT NULL COMMENT '修改人',
    `platform_id`      varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`           varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`           int(11)      NOT NULL COMMENT '状态',
    `logic_status`     int(11)      NOT NULL COMMENT '逻辑删除字段',
    `number`           varchar(128) NOT NULL COMMENT '编号',
    `plan_submit_time` datetime     NOT NULL COMMENT '计划提交时间',
    `writer`           varchar(64)  NOT NULL COMMENT '编写人',
    `res_person`       varchar(64)  NOT NULL COMMENT '责任人',
    `res_dept`         varchar(64)  NOT NULL COMMENT '责任部门',
    `type`             varchar(64)  DEFAULT NULL COMMENT '类型',
    `file_status`      varchar(64)  DEFAULT NULL COMMENT '文件状态',
    `rev_id`           varchar(64)  DEFAULT NULL COMMENT '版本',
    `remark`           varchar(255) DEFAULT NULL COMMENT '备注',
    `sort`             int(11)      DEFAULT NULL COMMENT '排序',
    `project_id`       varchar(64)  NOT NULL COMMENT '项目id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交付目标（ied）';

-- DDL for pms_deliver_goals_to_deliverable:
DROP TABLE IF EXISTS pms_deliver_goals_to_deliverable;
CREATE TABLE `pms_deliver_goals_to_deliverable`
(
    `id`          varchar(64) NOT NULL COMMENT '主键',
    `creator_id`  varchar(64) NOT NULL COMMENT '创建人',
    `create_time` datetime    NOT NULL COMMENT '创建时间',
    `from_id`     varchar(64) DEFAULT NULL COMMENT 'From主键',
    `to_id`       varchar(64) DEFAULT NULL COMMENT 'To主键',
    `from_class`  varchar(64) DEFAULT NULL COMMENT 'From类',
    `to_class`    varchar(64) DEFAULT NULL COMMENT 'To类',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交付目标与交付物的关系';

-- DDL for pms_deliverable:
DROP TABLE IF EXISTS pms_deliverable;
CREATE TABLE `pms_deliverable`
(
    `id`                    varchar(64)  NOT NULL COMMENT 'id',
    `predict_delivery_time` datetime     DEFAULT NULL COMMENT '预计交付时间',
    `plan_id`               varchar(64)  NOT NULL COMMENT '计划ID',
    `project_id`            varchar(64)  NOT NULL COMMENT '项目Id',
    `principal_name`        varchar(64)  DEFAULT NULL COMMENT '负责人名称',
    `principal_id`          varchar(64)  DEFAULT NULL COMMENT '负责人id',
    `delivery_time`         datetime     DEFAULT NULL COMMENT '交付时间',
    `modify_id`             varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`            varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`           datetime     NOT NULL COMMENT '创建时间',
    `class_name`            varchar(128) NOT NULL COMMENT '类名称',
    `remark`                text COMMENT '备注',
    `modify_time`           datetime     NOT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`                varchar(128) DEFAULT NULL COMMENT '编码',
    `name`                  varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`                  int(11)      DEFAULT NULL COMMENT '排序',
    `rev_key`               varchar(64)  DEFAULT NULL,
    `next_rev_id`           varchar(64)  DEFAULT NULL,
    `previous_rev_id`       varchar(64)  DEFAULT NULL,
    `rev_id`                varchar(64)  DEFAULT NULL,
    `initial_rev_id`        varchar(64)  DEFAULT NULL,
    `rev_order`             int(11)      DEFAULT NULL,
    `status`                int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`          int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`           varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`                varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `document_id`           varchar(64)  DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='交付物';

-- DDL for pms_demand_management:
DROP TABLE IF EXISTS pms_demand_management;
CREATE TABLE `pms_demand_management`
(
    `id`               varchar(64)  NOT NULL COMMENT 'id',
    `principal_id`     varchar(64)  DEFAULT NULL COMMENT '负责人id',
    `recipient_id`     varchar(64)  DEFAULT NULL COMMENT '接收人Id',
    `source`           varchar(64)  DEFAULT NULL COMMENT '需求来源',
    `parent_id`        varchar(64)  DEFAULT NULL COMMENT '父级ID',
    `predict_end_time` datetime     DEFAULT NULL COMMENT '期望完成日期',
    `priority_level`   varchar(64)  DEFAULT NULL COMMENT '优先级',
    `proposed_time`    datetime     DEFAULT NULL COMMENT '提出时间',
    `type`             varchar(64)  DEFAULT NULL COMMENT '需求类型',
    `exhibitor`        varchar(64)  DEFAULT NULL COMMENT '提出人',
    `project_id`       varchar(64)  DEFAULT NULL COMMENT '项目ID',
    `schedule`         double       DEFAULT NULL COMMENT '进度',
    `principal_name`   varchar(64)  DEFAULT NULL COMMENT '负责人名称',
    `recipient_name`   varchar(64)  DEFAULT NULL COMMENT '接收人名称',
    `exhibitor_name`   varchar(64)  DEFAULT NULL COMMENT '提出人名称',
    `modify_id`        varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`       varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`      datetime     NOT NULL COMMENT '创建时间',
    `class_name`       varchar(128) NOT NULL COMMENT '类名称',
    `remark`           text COMMENT '备注',
    `modify_time`      datetime     NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`           varchar(128) DEFAULT NULL COMMENT '编码',
    `name`             varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`             int(11)      DEFAULT NULL COMMENT '排序',
    `status`           int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`     int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`      varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`           varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `document_id`      varchar(64)  DEFAULT NULL,
    `rev_key`          varchar(64)  DEFAULT NULL COMMENT '版本key',
    `next_rev_id`      varchar(64)  DEFAULT NULL COMMENT '下一版本',
    `previous_rev_id`  varchar(64)  DEFAULT NULL COMMENT '上一版本',
    `rev_id`           varchar(64)  DEFAULT NULL COMMENT '版本号',
    `initial_rev_id`   varchar(64)  DEFAULT NULL COMMENT '初始版本',
    `rev_order`        int(11)      DEFAULT NULL COMMENT '版本号',
    `root_id`          varchar(64)  DEFAULT NULL COMMENT '根id',
    `report_id`        varchar(64)  DEFAULT NULL COMMENT '分析id',
    `stage`            varchar(64)  DEFAULT NULL COMMENT '阶段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='需求管理';

-- DDL for pms_demand_to_plan:
DROP TABLE IF EXISTS pms_demand_to_plan;
CREATE TABLE `pms_demand_to_plan`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='需求和计划的关系类';

-- DDL for pms_document_type:
DROP TABLE IF EXISTS pms_document_type;
CREATE TABLE `pms_document_type`
(
    `id`           varchar(64)  NOT NULL,
    `parent_id`    varchar(64)  NOT NULL COMMENT '父级ID',
    `project_id`   varchar(64)  NOT NULL COMMENT '项目ID',
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `document_id`  varchar(64)  DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='文档类型';

-- DDL for pms_file_info:
DROP TABLE IF EXISTS pms_file_info;
CREATE TABLE `pms_file_info`
(
    `id`           varchar(64)  NOT NULL COMMENT 'id',
    `data_id`      varchar(64)  NOT NULL COMMENT '所属数据ID',
    `file_size`    int(11)      DEFAULT NULL COMMENT '文件大小',
    `file_postfix` varchar(64)  DEFAULT NULL COMMENT '文件后缀',
    `file_path`    varchar(128) DEFAULT NULL COMMENT '文件路径',
    `project_id`   varchar(64)  DEFAULT NULL COMMENT '所属项目的Id',
    `rev_id`       varchar(64)  DEFAULT NULL COMMENT '版本',
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='文件信息表';

-- DDL for pms_ied_base_line:
DROP TABLE IF EXISTS pms_ied_base_line;
CREATE TABLE `pms_ied_base_line`
(
    `id`                varchar(64)  NOT NULL COMMENT '主键',
    `class_name`        varchar(64)  DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`       datetime     NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime     NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64)  NOT NULL COMMENT '修改人',
    `remark`            varchar(255) DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`            int(11)      NOT NULL COMMENT '状态',
    `logic_status`      int(11)      NOT NULL COMMENT '逻辑删除字段',
    `plan_submit_time`  datetime     NOT NULL COMMENT '计划提交时间',
    `writer`            varchar(64)  NOT NULL COMMENT '编写人',
    `res_person`        varchar(64)  NOT NULL COMMENT '责任人',
    `res_dept`          varchar(64)  NOT NULL COMMENT '责任部门',
    `type`              varchar(64)  DEFAULT NULL COMMENT '类型',
    `file_status`       varchar(64)  DEFAULT NULL COMMENT '文件状态',
    `rev_id`            varchar(64)  DEFAULT NULL COMMENT '版本',
    `project_id`        varchar(64)  NOT NULL COMMENT '项目id',
    `old_id`            varchar(64)  NOT NULL COMMENT '原ied主键',
    `base_line_id`      varchar(64)  NOT NULL COMMENT '基线ID',
    `number`            varchar(128) NOT NULL COMMENT '编码',
    `name`              varchar(255) NOT NULL COMMENT '名称',
    `sort`              int(11)      DEFAULT NULL COMMENT '排序',
    `exist_deliverable` varchar(10)  DEFAULT NULL COMMENT '是否载挂技术文件',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='ied基线';

-- DDL for pms_ied_base_line_info:
DROP TABLE IF EXISTS pms_ied_base_line_info;
CREATE TABLE `pms_ied_base_line_info`
(
    `id`           varchar(64)  NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)  NOT NULL COMMENT '创建人',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)  NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)  NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  NOT NULL COMMENT '业务组织Id',
    `status`       int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除字段',
    `project_id`   varchar(64)  NOT NULL COMMENT '项目ID',
    `number`       varchar(128) NOT NULL COMMENT '编码',
    `name`         varchar(64)  NOT NULL COMMENT '名称',
    `sort`         int(11)       DEFAULT NULL COMMENT '排序',
    `sum_number`   int(11)       DEFAULT '0' COMMENT '复制的IED数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='ied基线信息表';

-- DDL for pms_investment_scheme:
DROP TABLE IF EXISTS pms_investment_scheme;
CREATE TABLE `pms_investment_scheme`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `org_id`       varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `name`         varchar(255)  DEFAULT NULL COMMENT '计划名称',
    `logic_status` int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `creator_id`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `number`       varchar(64)   DEFAULT NULL COMMENT '计划编号',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `platform_id`  varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `project_id`   varchar(64)   DEFAULT NULL COMMENT '项目Id',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `status`       int(11)       DEFAULT NULL COMMENT '状态',
    `close_flag`   bit(1)        DEFAULT NULL COMMENT '是否关闭',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='投资计划';

-- DDL for pms_investment_scheme_estimate:
DROP TABLE IF EXISTS pms_investment_scheme_estimate;
CREATE TABLE `pms_investment_scheme_estimate`
(
    `logic_status`         int(11)        DEFAULT NULL COMMENT '逻辑删除字段',
    `other`                decimal(20, 2) DEFAULT NULL COMMENT '其他费用',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `status`               int(11)        DEFAULT NULL COMMENT '状态',
    `estimate`             decimal(20, 2) DEFAULT NULL COMMENT '概算',
    `owner_id`             varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `remark`               varchar(1024)  DEFAULT NULL COMMENT '备注',
    `class_name`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `creator_id`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `device`               decimal(20, 2) DEFAULT NULL COMMENT '设备投资',
    `installation`         decimal(20, 2) DEFAULT NULL COMMENT '安装工程',
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `modify_id`            varchar(64)    DEFAULT NULL COMMENT '修改人',
    `architecture`         decimal(20, 2) DEFAULT NULL COMMENT '建筑工程',
    `investment_scheme_id` varchar(64)    DEFAULT NULL COMMENT '投资计划Id',
    `org_id`               varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `source`               varchar(64)    DEFAULT NULL COMMENT '估算/概算版本',
    `platform_id`          varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `estimate_reserve`     decimal(20, 2) DEFAULT NULL COMMENT '预备费',
    `project_id`           varchar(255)   DEFAULT NULL COMMENT '项目Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='概算';

-- DDL for pms_man_hour:
DROP TABLE IF EXISTS pms_man_hour;
CREATE TABLE `pms_man_hour`
(
    `id`               varchar(64)   NOT NULL COMMENT 'id',
    `member_name`      varchar(64)  DEFAULT NULL COMMENT '成员名称',
    `reality_man_hour` decimal(7, 1) NOT NULL COMMENT '实际工时',
    `member_id`        varchar(64)   NOT NULL COMMENT '成员ID',
    `start_time`       datetime     DEFAULT NULL COMMENT '开始时间',
    `plan_id`          varchar(64)   NOT NULL COMMENT '所属计划',
    `modify_id`        varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`       varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`      datetime      NOT NULL COMMENT '创建时间',
    `class_name`       varchar(128)  NOT NULL COMMENT '类名称',
    `remark`           text COMMENT '备注',
    `modify_time`      datetime      NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`           varchar(128) DEFAULT NULL COMMENT '编码',
    `name`             varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`             int(11)      DEFAULT NULL COMMENT '排序',
    `status`           int(11)       NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`     int(11)       NOT NULL COMMENT '逻辑删除状态',
    `platform_id`      varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`           varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='工时表';

-- DDL for pms_month_investment_scheme:
DROP TABLE IF EXISTS pms_month_investment_scheme;
CREATE TABLE `pms_month_investment_scheme`
(
    `creator_id`   varchar(64)    DEFAULT NULL COMMENT '创建人',
    `project_id`   varchar(64)    DEFAULT NULL COMMENT '项目Id',
    `status`       int(11)        DEFAULT NULL COMMENT '状态',
    `actual`       decimal(20, 2) DEFAULT NULL COMMENT '实际',
    `modify_time`  datetime       DEFAULT NULL COMMENT '修改时间',
    `predicate`    decimal(20, 2) DEFAULT NULL COMMENT '预计',
    `remark`       varchar(1024)  DEFAULT NULL COMMENT '备注',
    `class_name`   varchar(64)    DEFAULT NULL COMMENT '创建人',
    `platform_id`  varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `logic_status` int(11)        DEFAULT NULL COMMENT '逻辑删除字段',
    `modify_id`    varchar(64)    DEFAULT NULL COMMENT '修改人',
    `owner_id`     varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime       DEFAULT NULL COMMENT '创建时间',
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `year_id`      varchar(64)    DEFAULT NULL COMMENT '年度投资计划Id',
    `name`         varchar(225)   DEFAULT NULL COMMENT '月份',
    `has_do`       int(10)        DEFAULT NULL COMMENT '是否已执行',
    `feedback_id`  varchar(255)   DEFAULT NULL COMMENT '关联计划反馈',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='月投资计划';

-- DDL for pms_object:
DROP TABLE IF EXISTS pms_object;
CREATE TABLE `pms_object`
(
    `id`           varchar(64) NOT NULL COMMENT 'id',
    `owner_id`     varchar(64) NOT NULL COMMENT '拥有者ID',
    `number`       varchar(64) NOT NULL COMMENT '编码',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `remark`       longtext COMMENT '描述/备注',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `name`         varchar(64) NOT NULL COMMENT '名称',
    `logic_status` int(11)     NOT NULL COMMENT 'logicStatus 逻辑删除字段',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改者ID',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建者id',
    `class_name`   varchar(64) NOT NULL COMMENT '类名称',
    `sort`         int(11)     DEFAULT NULL COMMENT '排序字段',
    `platform_id`  varchar(64) DEFAULT NULL COMMENT '平台id',
    `org_id`       varchar(64) DEFAULT NULL COMMENT '组织id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='Pmsx 基类';

-- DDL for pms_plan:
DROP TABLE IF EXISTS pms_plan;
CREATE TABLE `pms_plan`
(
    `id`                      varchar(64)  NOT NULL COMMENT 'id',
    `project_id`              varchar(64)  NOT NULL COMMENT '项目id',
    `plan_predict_start_time` datetime        DEFAULT NULL COMMENT '预计开始时间',
    `schedule`                decimal(65, 30) DEFAULT NULL COMMENT '计划进度',
    `parent_id`               varchar(64)  NOT NULL COMMENT '父级Id',
    `plan_type`               varchar(64)     DEFAULT NULL COMMENT '计划类型ID',
    `plan_end_time`           datetime        DEFAULT NULL COMMENT '计划结束时间',
    `plan_start_time`         datetime        DEFAULT NULL COMMENT '计划开始时间',
    `plan_predict_end_time`   datetime        DEFAULT NULL COMMENT '预计结束时间',
    `plan_image`              varchar(128)    DEFAULT NULL COMMENT '计划图片地址',
    `principal_name`          varchar(64)     DEFAULT NULL COMMENT '负责人名称',
    `principal_id`            varchar(64)     DEFAULT NULL COMMENT '负责人Id',
    `priority_level`          varchar(2)      DEFAULT NULL COMMENT '优先级 1-较低 2普通 3-较高 4-最高',
    `pm_id`                   varchar(64)     DEFAULT NULL COMMENT '壳id',
    `man_hour`                double          DEFAULT NULL COMMENT '预计工时',
    `reality_man_hour`        double          DEFAULT NULL COMMENT '实际工时',
    `residue_man_hour`        double          DEFAULT NULL COMMENT '剩余工时',
    `man_hour_schedule`       double          DEFAULT NULL COMMENT '工时进度',
    `deviation_man_hour`      double          DEFAULT NULL COMMENT '偏差工时',
    `task_status_id`          varchar(64)     DEFAULT NULL COMMENT '状态ID',
    `modify_id`               varchar(64)     DEFAULT NULL COMMENT '修改人ID',
    `creator_id`              varchar(64)     DEFAULT NULL COMMENT '创建人ID',
    `create_time`             datetime     NOT NULL COMMENT '创建时间',
    `class_name`              varchar(128) NOT NULL COMMENT '类名称',
    `remark`                  text COMMENT '备注',
    `modify_time`             datetime     NOT NULL COMMENT '修改时间',
    `owner_id`                varchar(64)     DEFAULT NULL COMMENT '拥有者',
    `number`                  varchar(128)    DEFAULT NULL COMMENT '编码',
    `name`                    varchar(128)    DEFAULT NULL COMMENT '名称',
    `sort`                    int(11)         DEFAULT NULL COMMENT '排序',
    `status`                  int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`            int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`             varchar(64)     DEFAULT NULL COMMENT '平台ID',
    `org_id`                  varchar(64)     DEFAULT NULL COMMENT '组织ID',
    `document_id`             varchar(64)     DEFAULT NULL,
    `manage_node`             varchar(255)    DEFAULT NULL COMMENT '管理节点',
    `risk_item`               varchar(255)    DEFAULT NULL COMMENT '风险项',
    `speed_status`            smallint(6)     DEFAULT NULL COMMENT '进度状态',
    `res_org`                 varchar(255)    DEFAULT NULL COMMENT '责任单位',
    `join_org`                varchar(255)    DEFAULT NULL COMMENT '参与单位',
    `res_dept`                varchar(255)    DEFAULT NULL COMMENT '责任科室',
    `res_user`                varchar(255)    DEFAULT NULL COMMENT '责任人',
    `join_dept`               varchar(255)    DEFAULT NULL COMMENT '参与科室'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划';

-- ----------------------------
-- Table structure for pms_plan_base_line
-- ----------------------------
DROP TABLE IF EXISTS `pms_plan_base_line`;
CREATE TABLE `pms_plan_base_line`
(
    `id`                      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL COMMENT 'id',
    `parent_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '父级id',
    `project_id`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '项目Id',
    `plan_predict_end_time`   datetime                                                 NULL DEFAULT NULL COMMENT '计划预计结束时间',
    `principal_name`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '责任人名称',
    `plan_type_name`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '计划类型名称',
    `plan_type`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '计划类型',
    `status_name`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '状态名称',
    `principal_id`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '责任人ID',
    `plan_end_time`           datetime                                                 NULL DEFAULT NULL COMMENT '计划结束时间',
    `status_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '状态id',
    `priority_level`          varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci    NULL DEFAULT NULL COMMENT '优先级别',
    `version`                 varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '版本信息',
    `base_line_id`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '基线ID',
    `schedule`                decimal(65, 30)                                          NULL DEFAULT NULL COMMENT '进度',
    `plan_start_time`         datetime                                                 NULL DEFAULT NULL COMMENT '计划开始时间',
    `plan_predict_start_time` datetime                                                 NULL DEFAULT NULL COMMENT '计划预计开始时间',
    `old_id`                  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '袁计划ID',
    `priority_level_name`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '优先级名称',
    `man_hour`                decimal(65, 30)                                          NULL DEFAULT NULL COMMENT '工时',
    `task_name`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '前置任务名称（多个拼接）',
    `task_id`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '前置任务Id名称（多个拼接）',
    `modify_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '修改人ID',
    `creator_id`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '创建人ID',
    `create_time`             datetime                                                 NULL DEFAULT NULL COMMENT '创建时间',
    `class_name`              varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '类名称',
    `remark`                  text CHARACTER SET utf8 COLLATE utf8_general_ci          NULL COMMENT '备注',
    `modify_time`             datetime                                                 NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '拥有者',
    `number`                  varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '编码',
    `name`                    varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '名称',
    `sort`                    int(11)                                                  NULL DEFAULT NULL COMMENT '排序',
    `status`                  int(11)                                                  NULL DEFAULT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`            int(11)                                                  NULL DEFAULT NULL COMMENT '逻辑删除状态',
    `platform_id`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '平台ID',
    `org_id`                  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '组织ID',
    `scheme_number`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '计划编号',
    `icon`                    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT 'icon',
    `project_number`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '项目编号',
    `level`                   int(11)                                                  NULL DEFAULT NULL COMMENT '层级',
    `parent_chain`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '父级链',
    `node_type`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '计划类型',
    `rsp_sub_dept`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '责任处室',
    `rsp_section_id`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '责任科室',
    `rsp_user`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '责任人',
    `rsp_user_code`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '责任人编号',
    `begin_time`              datetime                                                 NULL DEFAULT NULL COMMENT '计划开始时间',
    `end_time`                datetime                                                 NULL DEFAULT NULL COMMENT '计划结束时间',
    `circumstance`            int(11)                                                  NULL DEFAULT NULL COMMENT '计划情况',
    `actual_end_time`         datetime                                                 NULL DEFAULT NULL COMMENT '实际结束时间',
    `actual_begin_time`       datetime                                                 NULL DEFAULT NULL COMMENT '实际开始时间',
    `scheme_desc`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '项目计划描述',
    `execute_desc`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '执行情况说明',
    `issue_time`              datetime                                                 NULL DEFAULT NULL COMMENT '计划下发时间',
    `delay_end_Flag`          bit(1)                                                   NULL DEFAULT NULL COMMENT '是否超时完成 1 是，0 否',
    `delay_end_reason`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '超时原因',
    `process_flag`            bit(1)                                                   NULL DEFAULT NULL COMMENT '是否关联流程 1 是，0 否',
    `process_id`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '关联流程实例Id',
    `duration_days`           int(11)                                                  NULL DEFAULT NULL COMMENT '计划工期',
    `top_sort`                int(11)                                                  NULL DEFAULT NULL COMMENT '置顶',
    `issued_user`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '下达人',
    `participant_users`       varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参与人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '基线'
  ROW_FORMAT = DYNAMIC;

-- DDL for pms_plan_base_line_info:
DROP TABLE IF EXISTS pms_plan_base_line_info;
CREATE TABLE `pms_plan_base_line_info`
(
    `id`           varchar(64)  NOT NULL COMMENT 'id',
    `project_id`   varchar(64)  NOT NULL COMMENT '计划ID',
    `version`      varchar(64)  DEFAULT NULL COMMENT '版本',
    `version_key`  varchar(64)  NOT NULL COMMENT '版本key',
    `sum_number`   int(11)      DEFAULT NULL,
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='基线信息表';


-- DDL for pms_plan_count:
DROP TABLE IF EXISTS pms_plan_count;
CREATE TABLE `pms_plan_count`
(
    `id`              varchar(64)  NOT NULL COMMENT 'id',
    `finish_count`    int(11)      NOT NULL COMMENT '完成数量',
    `now_day`         datetime     NOT NULL COMMENT '时间',
    `un_finish_count` int(11)      NOT NULL COMMENT '未完成统计数量',
    `finishing_count` int(11)      NOT NULL COMMENT '进行中数量',
    `type_id`         varchar(64)  NOT NULL COMMENT '计划类型ID',
    `uk`              varchar(255) NOT NULL,
    `date_str`        varchar(64)  NOT NULL,
    `project_id`      varchar(64)  NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划统计';

-- DDL for pms_plan_main:
DROP TABLE IF EXISTS pms_plan_main;
CREATE TABLE `pms_plan_main`
(
    `id`           varchar(64)  NOT NULL COMMENT 'id',
    `project_id`   varchar(64)  NOT NULL COMMENT 'x项目ID',
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划主表';

-- DDL for pms_plan_to_component:
DROP TABLE IF EXISTS pms_plan_to_component;
CREATE TABLE `pms_plan_to_component`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划和pdm零组件关系';

-- DDL for pms_plan_to_deliver_goals:
DROP TABLE IF EXISTS pms_plan_to_deliver_goals;
CREATE TABLE `pms_plan_to_deliver_goals`
(
    `id`          varchar(64) NOT NULL COMMENT '主键',
    `creator_id`  varchar(64) NOT NULL COMMENT '创建人',
    `create_time` datetime    NOT NULL COMMENT '创建时间',
    `from_id`     varchar(64) DEFAULT NULL COMMENT 'From主键',
    `to_id`       varchar(64) DEFAULT NULL COMMENT 'To主键',
    `from_class`  varchar(64) DEFAULT NULL COMMENT 'From类',
    `to_class`    varchar(64) DEFAULT NULL COMMENT 'To类',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='计划和交付目标的关系';

-- DDL for pms_plan_to_demand_management:
DROP TABLE IF EXISTS pms_plan_to_demand_management;
CREATE TABLE `pms_plan_to_demand_management`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='需求和计划的关系类';

-- DDL for pms_plan_to_member:
DROP TABLE IF EXISTS pms_plan_to_member;
CREATE TABLE `pms_plan_to_member`
(
    `id`          varchar(64) NOT NULL COMMENT '主键',
    `type`        smallint(6) DEFAULT NULL COMMENT '1 负责人  2 参与人',
    `to_id`       varchar(64) DEFAULT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) DEFAULT NULL,
    `from_id`     varchar(64) DEFAULT NULL,
    `creator_id`  varchar(64) DEFAULT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    DEFAULT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;

-- DDL for pms_plan_to_plan:
DROP TABLE IF EXISTS pms_plan_to_plan;
CREATE TABLE `pms_plan_to_plan`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11) DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) NOT NULL,
    `to_class`    varchar(64) NOT NULL,
    `from_class`  varchar(64) NOT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划和计划关系';

-- DDL for pms_plan_to_question_management:
DROP TABLE IF EXISTS pms_plan_to_question_management;
CREATE TABLE `pms_plan_to_question_management`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划和问题管理';

-- DDL for pms_plan_to_resource:
DROP TABLE IF EXISTS pms_plan_to_resource;
CREATE TABLE `pms_plan_to_resource`
(
    `id`             varchar(64) NOT NULL,
    `to_id`          varchar(64) NOT NULL,
    `sort`           int(11) DEFAULT NULL,
    `modify_id`      varchar(64) NOT NULL,
    `from_id`        varchar(64) NOT NULL,
    `creator_id`     varchar(64) NOT NULL,
    `class_name`     varchar(64) NOT NULL,
    `to_class`       varchar(64) NOT NULL,
    `from_class`     varchar(64) NOT NULL,
    `create_time`    datetime    NOT NULL,
    `status`         int(11)     NOT NULL,
    `modify_time`    datetime    NOT NULL,
    `resource_count` int(11)     NOT NULL,
    `type`           varchar(64) NOT NULL,
    `remark`         text        NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划和资源的关系';

-- DDL for pms_plan_to_risk_management:
DROP TABLE IF EXISTS pms_plan_to_risk_management;
CREATE TABLE `pms_plan_to_risk_management`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划和风险管理的关系';

-- DDL for pms_plan_to_type:
DROP TABLE IF EXISTS pms_plan_to_type;
CREATE TABLE `pms_plan_to_type`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='计划和计划类型';

-- DDL for pms_post_project:
DROP TABLE IF EXISTS pms_post_project;
CREATE TABLE `pms_post_project`
(
    `id`             varchar(64)  NOT NULL COMMENT 'id',
    `type`           varchar(64)  NOT NULL COMMENT '结项类型',
    `reason`         varchar(255) DEFAULT NULL COMMENT '理由',
    `principal_id`   varchar(64)  DEFAULT NULL COMMENT '负责人ID',
    `end_time`       datetime     DEFAULT NULL COMMENT '结束时间',
    `principal_name` varchar(64)  DEFAULT NULL COMMENT '负责人名',
    `start_time`     datetime     DEFAULT NULL COMMENT '开始时间',
    `project_id`     varchar(64)  NOT NULL COMMENT '项目ID',
    `modify_id`      varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`     varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`    datetime     NOT NULL COMMENT '创建时间',
    `class_name`     varchar(128) NOT NULL COMMENT '类名称',
    `remark`         text COMMENT '备注',
    `modify_time`    datetime     NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`         varchar(128) DEFAULT NULL COMMENT '编码',
    `name`           varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`           int(11)      DEFAULT NULL COMMENT '排序',
    `status`         int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`   int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`    varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`         varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `document_id`    varchar(64)  DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='结项';

-- ----------------------------
-- Table structure for pms_project
-- ----------------------------
DROP TABLE IF EXISTS `pms_project`;
CREATE TABLE `pms_project`
(
    `id`                        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL COMMENT 'id',
    `project_image`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '项目图片路径/hdfs',
    `project_approve_time`      datetime                                                     NULL DEFAULT NULL COMMENT '项目立项时间',
    `project_end_time`          datetime                                                     NULL DEFAULT NULL COMMENT '项目结束时间',
    `project_start_time`        datetime                                                     NULL DEFAULT NULL COMMENT '项目开始时间',
    `schedule`                  double                                                       NOT NULL COMMENT '0',
    `product_id`                varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '产品ID',
    `pm`                        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '项目经理',
    `status_id`                 varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '关联状态ID',
    `modify_id`                 varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '修改人ID',
    `creator_id`                varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '创建人ID',
    `create_time`               datetime                                                     NOT NULL COMMENT '创建时间',
    `class_name`                varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '类名称',
    `remark`                    text CHARACTER SET utf8 COLLATE utf8_general_ci              NULL COMMENT '备注',
    `modify_time`               datetime                                                     NOT NULL COMMENT '修改时间',
    `owner_id`                  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '拥有者',
    `number`                    varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci      NULL DEFAULT NULL COMMENT '编码',
    `name`                      varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci      NULL DEFAULT NULL COMMENT '名称',
    `sort`                      int(11)                                                      NULL DEFAULT NULL COMMENT '排序',
    `status`                    int(11)                                                      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`              int(11)                                                      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '平台ID',
    `org_id`                    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '组织ID',
    `acceptance_form_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目验收单Id',
    `acceptance_form_number`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目验收单编号',
    `finish_time`               datetime                                                     NULL DEFAULT NULL COMMENT '项目完工时间',
    `predict_start_time`        datetime                                                     NULL DEFAULT NULL COMMENT '舍得首页查询',
    `predict_end_time`          datetime                                                     NULL DEFAULT NULL COMMENT '舍得首页查询',
    `res_administrative_office` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL,
    `is_declare`                tinyint(1)                                                   NULL DEFAULT NULL COMMENT '是否需要申报',
    `project_type`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '项目类型',
    `project_sub_type`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '项目子类型',
    `res_person`                varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL,
    `res_dept`                  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL,
    `res_team_group`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL,
    `project_source`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '项目来源',
    `is_approval`               tinyint(4)                                                   NULL DEFAULT NULL COMMENT '是否立项',
    `scientific_declare_id`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '科研需求申报id',
    `place`                     varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci      NULL DEFAULT NULL COMMENT '项目地址',
    `examine_user`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '审批人',
    `examine_type`              varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '评分状态',
    `research`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci      NULL DEFAULT NULL,
    `level`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci      NULL DEFAULT NULL,
    `direction`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci      NULL DEFAULT NULL,
    `product_type`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci      NULL DEFAULT NULL,
    `need_work_flow`            tinyint(1)                                                   NULL DEFAULT NULL,
    `ecr_time`                  datetime                                                     NULL DEFAULT NULL COMMENT '变更时间',
    `last_status`               int(11)                                                      NULL DEFAULT NULL COMMENT '上个状态',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '项目'
  ROW_FORMAT = DYNAMIC;


-- DDL for pms_project_contract:
DROP TABLE IF EXISTS pms_project_contract;
CREATE TABLE `pms_project_contract`
(
    `id`                  varchar(64)    NOT NULL COMMENT '主键',
    `class_name`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64)    NOT NULL COMMENT '创建人',
    `modify_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime       NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`              varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64)    NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64)    NOT NULL COMMENT '业务组织Id',
    `status`              int(11)        NOT NULL COMMENT '状态',
    `logic_status`        int(11)        NOT NULL COMMENT '逻辑删除字段',
    `name`                varchar(64)    NOT NULL COMMENT '合同名称',
    `contract_category`   varchar(64)    NOT NULL COMMENT '合同类别',
    `contract_type`       varchar(64)    NOT NULL COMMENT '合同类型',
    `principal_id`        varchar(64)    NOT NULL COMMENT '合同负责人id',
    `rsp_dept_id`         varchar(64)    NOT NULL COMMENT '责任部门id',
    `contract_money`      decimal(20, 2) NOT NULL COMMENT '合同金额',
    `currency`            varchar(64)    NOT NULL COMMENT '币种',
    `start_date`          datetime       NOT NULL COMMENT '合同开始日期',
    `end_date`            datetime       NOT NULL COMMENT '合同结束日期',
    `is_guarantee_period` bit(1)         NOT NULL COMMENT '是否具有质保期',
    `is_guarantee_money`  bit(1)         NOT NULL COMMENT '是否具有质保金',
    `guarantee_end_date`  datetime       DEFAULT NULL COMMENT '预计质保期到期日期',
    `guarantee_amt`       decimal(20, 2) DEFAULT NULL COMMENT '质保金额',
    `contract_other_info` text COMMENT '合同其他信息',
    `number`              varchar(64)    NOT NULL COMMENT '合同编号',
    `project_id`          varchar(64)    DEFAULT NULL COMMENT '项目id',
    `project_number`      varchar(64)    DEFAULT NULL COMMENT '项目编码',
    `sort`                int(11)        DEFAULT NULL COMMENT '排序',
    `sign_date`           datetime       DEFAULT NULL COMMENT '合同签订日期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同信息';

-- DDL for pms_project_contract_change:
DROP TABLE IF EXISTS pms_project_contract_change;
CREATE TABLE `pms_project_contract_change`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`          varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`          int(11)     NOT NULL COMMENT '状态',
    `logic_status`    int(11)     NOT NULL COMMENT '逻辑删除字段',
    `change_id`       varchar(64) NOT NULL COMMENT '变更申请单id',
    `change_number`   varchar(64) NOT NULL COMMENT '变更申请单号',
    `field_type`      varchar(64) NOT NULL COMMENT '字段类型',
    `field_name`      varchar(64) NOT NULL COMMENT '字段名称',
    `field_code`      varchar(64) NOT NULL COMMENT '字段编码',
    `old_value`       varchar(64) NOT NULL COMMENT '修改前值',
    `new_value`       varchar(64) NOT NULL COMMENT '修改后值',
    `contract_id`     varchar(64) NOT NULL COMMENT '合同id',
    `contract_number` varchar(64) NOT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同信息(变更)';

-- DDL for pms_project_contract_change_apply:
DROP TABLE IF EXISTS pms_project_contract_change_apply;
CREATE TABLE `pms_project_contract_change_apply`
(
    `id`                      varchar(64) NOT NULL COMMENT '主键',
    `class_name`              varchar(64)          DEFAULT NULL COMMENT '创建人',
    `creator_id`              varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`             datetime             DEFAULT NULL COMMENT '修改时间',
    `owner_id`                varchar(64)          DEFAULT NULL COMMENT '拥有者',
    `create_time`             datetime    NOT NULL COMMENT '创建时间',
    `modify_id`               varchar(64)          DEFAULT NULL COMMENT '修改人',
    `remark`                  varchar(1024)        DEFAULT NULL COMMENT '备注',
    `platform_id`             varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                  varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                  int(11)     NOT NULL COMMENT '状态',
    `logic_status`            int(11)     NOT NULL COMMENT '逻辑删除字段',
    `contract_number`         varchar(64) NOT NULL COMMENT '合同编号',
    `contract_id`             varchar(64) NOT NULL COMMENT '合同id',
    `number`                  varchar(64) NOT NULL COMMENT '变更申请单号',
    `apply_user_id`           varchar(64) NOT NULL COMMENT '申请人id',
    `apply_date`              datetime    NOT NULL COMMENT '申请日期',
    `change_reason`           text        NOT NULL COMMENT '变更原因',
    `is_contract_info`        bit(1)      NOT NULL DEFAULT b'0' COMMENT '合同基本信息是否变更',
    `is_our_signed_main`      bit(1)      NOT NULL DEFAULT b'0' COMMENT '甲方签约主体是否变更',
    `is_supplier_signed_main` bit(1)      NOT NULL DEFAULT b'0' COMMENT '乙方签约主体是否变更',
    `is_pay_node`             bit(1)      NOT NULL DEFAULT b'0' COMMENT '合同节点是否变更',
    `is_contract_file`        bit(1)      NOT NULL DEFAULT b'0' COMMENT '合同附件是否变更',
    `parent_id`               varchar(64)          DEFAULT NULL COMMENT '父变更id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同变更申请信息';

-- DDL for pms_project_contract_change_form:
DROP TABLE IF EXISTS pms_project_contract_change_form;
CREATE TABLE `pms_project_contract_change_form`
(
    `id`           varchar(64)   NOT NULL COMMENT '主键',
    `class_name`   varchar(64)            DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)            DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime               DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)            DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime               DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)            DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024)          DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)            DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)            DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11)       NOT NULL COMMENT '状态',
    `logic_status` int(11)       NOT NULL COMMENT '逻辑删除字段',
    `field_code`   varchar(30)   NOT NULL COMMENT '字段编码',
    `field_name`   varchar(64)   NOT NULL COMMENT '字段名称',
    `field_type`   varchar(1000) NOT NULL COMMENT '字段类型',
    `is_null`      bit(1)        NOT NULL DEFAULT b'0' COMMENT '是否必填',
    `key_value`    varchar(64)            DEFAULT NULL COMMENT '关键key',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同变更表单';

-- DDL for pms_project_contract_close_apply:
DROP TABLE IF EXISTS pms_project_contract_close_apply;
CREATE TABLE `pms_project_contract_close_apply`
(
    `id`              varchar(64)   NOT NULL COMMENT '主键',
    `class_name`      varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64)   NOT NULL COMMENT '创建人',
    `modify_time`     datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime      NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`          varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64)   NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64)   NOT NULL COMMENT '业务组织Id',
    `status`          int(11)       NOT NULL COMMENT '状态',
    `logic_status`    int(11)       NOT NULL COMMENT '逻辑删除字段',
    `contract_id`     varchar(64)   NOT NULL COMMENT '合同id',
    `contract_number` varchar(64)   NOT NULL COMMENT '合同编号',
    `number`          varchar(64)   NOT NULL COMMENT '合同关闭申请单号',
    `apply_user_id`   varchar(64)   NOT NULL COMMENT '申请人id',
    `apply_date`      bit(1)        NOT NULL COMMENT '申请日期',
    `close_reason`    varchar(1000) NOT NULL COMMENT '关闭原因',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目合同关闭申请';

-- DDL for pms_project_daily_statement:
DROP TABLE IF EXISTS pms_project_daily_statement;
CREATE TABLE `pms_project_daily_statement`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime    NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64) NOT NULL COMMENT '修改人',
    `remark`         varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int(11)     NOT NULL COMMENT '状态',
    `logic_status`   int(11)     NOT NULL COMMENT '逻辑删除字段',
    `daily`          datetime       DEFAULT NULL COMMENT '日期',
    `resp`           varchar(40)    DEFAULT NULL COMMENT '责任人',
    `bus_status`     int(11)     NOT NULL COMMENT '日报状态',
    `evaluate`       varchar(800)   DEFAULT NULL COMMENT '评价',
    `score`          decimal(10, 0) DEFAULT NULL COMMENT '评分',
    `reviewed_by`    varchar(60)    DEFAULT NULL COMMENT '审核人',
    `carbon_copy_by` varchar(400)   DEFAULT NULL COMMENT '抄送人（英文逗号分割）',
    `summary`        varchar(800)   DEFAULT NULL COMMENT '汇报总结',
    `project_id`     varchar(64)    DEFAULT NULL,
    `commit_time`    datetime       DEFAULT NULL COMMENT '提交日期',
    `evaluate_date`  datetime       DEFAULT NULL COMMENT '评价时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='计划日报';

-- DDL for pms_project_daily_statement_content:
DROP TABLE IF EXISTS pms_project_daily_statement_content;
CREATE TABLE `pms_project_daily_statement_content`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`        datetime    NOT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64) NOT NULL COMMENT '修改人',
    `remark`             varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`             int(11)     NOT NULL COMMENT '状态',
    `logic_status`       int(11)     NOT NULL COMMENT '逻辑删除字段',
    `content`            varchar(600)   DEFAULT NULL COMMENT '工作内容',
    `task_time`          decimal(10, 1) DEFAULT NULL COMMENT '工时',
    `the_plan`           bigint(20)     DEFAULT NULL COMMENT '是否计划内',
    `relationship`       varchar(200)   DEFAULT NULL COMMENT '关联关系',
    `daily_statement_id` varchar(40)    DEFAULT NULL COMMENT '关联日报',
    `relation_type`      varchar(60)    DEFAULT NULL COMMENT '关联类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='日报内容表';

-- DDL for pms_project_declare:
DROP TABLE IF EXISTS pms_project_declare;
CREATE TABLE `pms_project_declare`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`        datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`             int(11)        DEFAULT NULL COMMENT '状态',
    `logic_status`       int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_id`         varchar(64) NOT NULL COMMENT '项目id',
    `project_number`     varchar(64) NOT NULL COMMENT '项目编号',
    `estimate_amt`       decimal(10, 2) DEFAULT NULL COMMENT '项目预估金额',
    `apply_reason`       text COMMENT '项目申请理由',
    `number`             varchar(64)    DEFAULT NULL COMMENT '项目申报申请单编码',
    `project_background` text COMMENT '项目背景摘要',
    `project_target`     text COMMENT '项目目标摘要',
    `technology_plan`    text COMMENT '技术方案摘要',
    `sort`               int(11)        DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目申报';

-- DDL for pms_project_declare_file_info:
DROP TABLE IF EXISTS pms_project_declare_file_info;
CREATE TABLE `pms_project_declare_file_info`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`        datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`             varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`             int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status`       int(11)     NOT NULL COMMENT '逻辑删除字段',
    `type`               varchar(64) NOT NULL COMMENT '类型',
    `project_declare_id` varchar(64) NOT NULL COMMENT '项目申报id',
    `file_data_id`       varchar(64) NOT NULL COMMENT '文件数据id',
    `sort`               int(11)       DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目申报文件信息';

-- DDL for pms_project_lifecycle_node:
DROP TABLE IF EXISTS pms_project_lifecycle_node;
CREATE TABLE `pms_project_lifecycle_node`
(
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `node_key`      varchar(64)          DEFAULT NULL COMMENT '节点Key',
    `name`          varchar(50)          DEFAULT NULL COMMENT '节点名',
    `node_type`     varchar(64)          DEFAULT NULL COMMENT '节点类型',
    `actions`       text COMMENT '节点操作',
    `attachments`   text COMMENT '附件',
    `content`       text COMMENT '内容',
    `abscissa`      int(11)     NOT NULL DEFAULT '0' COMMENT '横坐标',
    `ordinate`      int(11)     NOT NULL DEFAULT '0' COMMENT '纵坐标',
    `class_name`    varchar(64)          DEFAULT NULL COMMENT '类',
    `remark`        varchar(1024)        DEFAULT NULL COMMENT '备注',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64) NOT NULL COMMENT '修改人',
    `modify_time`   datetime    NOT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)          DEFAULT NULL COMMENT '拥有者',
    `status`        int(11)     NOT NULL COMMENT '状态',
    `logic_status`  int(11)     NOT NULL COMMENT '逻辑删除字段',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) NOT NULL COMMENT '业务组织Id',
    `is_attachment` bit(1)               DEFAULT NULL COMMENT '是否需要流程模版',
    `project_type`  varchar(64)          DEFAULT NULL COMMENT '项目类型',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `index_name` (`node_key`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目全生命周期流程节点';

-- DDL for pms_project_purchase_order_detail:
DROP TABLE IF EXISTS pms_project_purchase_order_detail;
CREATE TABLE `pms_project_purchase_order_detail`
(
    `id`                   varchar(64)    NOT NULL COMMENT '主键',
    `class_name`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)    NOT NULL COMMENT '创建人',
    `modify_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime       NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)    NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64)    NOT NULL COMMENT '业务组织Id',
    `status`               int(11)        NOT NULL COMMENT '状态',
    `logic_status`         int(11)        NOT NULL COMMENT '逻辑删除字段',
    `source_number`        varchar(64)    DEFAULT NULL COMMENT '来源单号',
    `plan_number`          varchar(64)    DEFAULT NULL COMMENT '物资/服务计划编号',
    `goods_service_number` varchar(64)    NOT NULL COMMENT '物资/服务编码',
    `description`          varchar(100)   NOT NULL COMMENT '描述',
    `norms_model`          varchar(64)    NOT NULL COMMENT '规格型号',
    `unit_code`            varchar(64)    NOT NULL COMMENT '计量单位',
    `purchase_amount`      decimal(10, 2) NOT NULL COMMENT '采购数量',
    `demand_date`          datetime       NOT NULL COMMENT '需求日期',
    `no_tax_price`         decimal(10, 2) NOT NULL COMMENT '单价（不含税）',
    `no_tax_total_amt`     decimal(10, 2) DEFAULT NULL COMMENT '总金额（不含税）',
    `tax_rate`             decimal(10, 2) NOT NULL COMMENT '税率',
    `have_tax_price`       decimal(10, 2) DEFAULT NULL COMMENT '单价（含税）',
    `have_tax_total_amt`   decimal(10, 2) DEFAULT NULL COMMENT '总金额（含税）',
    `purchase_id`          varchar(64)    NOT NULL COMMENT '采购订单id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目采购订单明细';

-- DDL for pms_project_purchase_order_info:
DROP TABLE IF EXISTS pms_project_purchase_order_info;
CREATE TABLE `pms_project_purchase_order_info`
(
    `id`                    varchar(64)    NOT NULL COMMENT '主键',
    `class_name`            varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`            varchar(64)    NOT NULL COMMENT '创建人',
    `modify_time`           datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`           datetime       NOT NULL COMMENT '创建时间',
    `modify_id`             varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64)    NOT NULL COMMENT '平台ID',
    `org_id`                varchar(64)    NOT NULL COMMENT '业务组织Id',
    `status`                int(11)        NOT NULL COMMENT '状态',
    `logic_status`          int(11)        NOT NULL COMMENT '逻辑删除字段',
    `number`                varchar(64)    NOT NULL COMMENT '订单编号',
    `name`                  varchar(64)    NOT NULL COMMENT '订单名称',
    `res_user_id`           varchar(64)    NOT NULL COMMENT '采购员',
    `rsp_dept_id`           varchar(64)    NOT NULL COMMENT '采购负责部门',
    `have_tax_total_amt`    decimal(10, 2) NOT NULL COMMENT '含税总金额',
    `currency`              varchar(64)    NOT NULL COMMENT '币种',
    `purchase_total_amount` decimal(10, 2) NOT NULL COMMENT '采购总数量',
    `purchase_type`         varchar(64)    NOT NULL COMMENT '采购类型',
    `order_arrival_date`    datetime       NOT NULL COMMENT '订单到货日期',
    `order_desc`            text COMMENT '订单说明',
    `project_id`            varchar(64)    NOT NULL COMMENT '项目id',
    `order_type`            varchar(64)   DEFAULT NULL COMMENT '订单类型',
    `order_excute_status`   varchar(64)   DEFAULT NULL COMMENT '订单执行状态',
    `order_source`          varchar(64)   DEFAULT NULL COMMENT '订单来源',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='采购订单基本信息';

-- DDL for pms_project_purchase_receive_info:
DROP TABLE IF EXISTS pms_project_purchase_receive_info;
CREATE TABLE `pms_project_purchase_receive_info`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`          varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`          int(11)     NOT NULL COMMENT '状态',
    `logic_status`    int(11)     NOT NULL COMMENT '逻辑删除字段',
    `receive_person`  varchar(64) NOT NULL COMMENT '收货人姓名',
    `receive_phone`   varchar(20) NOT NULL COMMENT '收货人电话',
    `receive_address` varchar(64)   DEFAULT NULL COMMENT '收货地址',
    `receive_email`   varchar(64)   DEFAULT NULL COMMENT '收货人邮箱',
    `purchase_id`     varchar(64) NOT NULL COMMENT '采购单id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目采购收货方信息';

-- DDL for pms_project_purchase_supplier_info:
DROP TABLE IF EXISTS pms_project_purchase_supplier_info;
CREATE TABLE `pms_project_purchase_supplier_info`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`       varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`         varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int(11)     NOT NULL COMMENT '状态',
    `logic_status`   int(11)     NOT NULL COMMENT '逻辑删除字段',
    `supplier_name`  varchar(64) NOT NULL COMMENT '供应商',
    `contact_person` varchar(64) NOT NULL COMMENT '联系人',
    `contact_phone`  varchar(20) NOT NULL COMMENT '联系人电话',
    `contact_email`  varchar(64)   DEFAULT NULL COMMENT '联系人邮箱',
    `purchase_id`    varchar(64) NOT NULL COMMENT '采购订单id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目采购供应商信息';

-- DDL for pms_project_role:
DROP TABLE IF EXISTS pms_project_role;
CREATE TABLE `pms_project_role`
(
    `id`           varchar(64)  NOT NULL COMMENT 'id',
    `parent_id`    varchar(64)                       DEFAULT NULL COMMENT '父级id',
    `chain`        text COMMENT '父级链',
    `code`         varchar(32)                       DEFAULT NULL COMMENT '编号',
    `sort`         int(11)                           DEFAULT NULL COMMENT '显示顺序',
    `layer`        int(11)                           DEFAULT NULL COMMENT '层级',
    `description`  text COMMENT '描述',
    `take_effect`  int(11)                           DEFAULT NULL COMMENT '启用禁用 0:禁用 1：启用',
    `business_id`  varchar(255)                      DEFAULT NULL COMMENT '业务角色ID',
    `project_id`   varchar(64)                       DEFAULT NULL,
    `modify_id`    varchar(64)                       DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)                       DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)                       DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128)                      DEFAULT NULL COMMENT '编码',
    `name`         varchar(128)                      DEFAULT NULL COMMENT '名称',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)                       DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)                       DEFAULT NULL COMMENT '组织ID',
    `role_code`    varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;

-- DDL for pms_project_role_user:
DROP TABLE IF EXISTS pms_project_role_user;
CREATE TABLE `pms_project_role_user`
(
    `id`              varchar(64)  NOT NULL COMMENT 'ID',
    `user_id`         varchar(64)  DEFAULT NULL COMMENT '用户id',
    `project_role_id` varchar(64)  DEFAULT NULL COMMENT '角色id',
    `project_id`      varchar(64)  DEFAULT NULL COMMENT '项目id',
    `modify_id`       varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`      varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`     datetime     NOT NULL COMMENT '创建时间',
    `class_name`      varchar(128) NOT NULL COMMENT '类名称',
    `remark`          text COMMENT '备注',
    `modify_time`     datetime     NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`          varchar(128) DEFAULT NULL COMMENT '编码',
    `name`            varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`            int(11)      DEFAULT NULL COMMENT '排序',
    `status`          int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`    int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`     varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`          varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `code`            varchar(64)  DEFAULT NULL COMMENT '用户代码',
    `email`           varchar(64)  DEFAULT NULL COMMENT '邮箱',
    `mobile`          varchar(64)  DEFAULT NULL COMMENT '电话',
    `default_flag`    int(11)      DEFAULT NULL COMMENT '是否默认添加，1 是 2 否',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目角色用户表 项目关联的角色和用户';

-- ----------------------------
-- Table structure for pms_project_scheme
-- ----------------------------
DROP TABLE IF EXISTS `pms_project_scheme`;
CREATE TABLE `pms_project_scheme`
(
    `id`                         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `sort`                       int(11)                                                        NULL DEFAULT NULL COMMENT '排序',
    `name`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '计划名称',
    `icon`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '计划icon',
    `project_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '项目id',
    `project_number`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '项目编号',
    `level`                      int(11)                                                        NULL DEFAULT NULL COMMENT '层级',
    `parent_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '父id',
    `type`                       int(11)                                                        NULL DEFAULT NULL COMMENT '计划类型',
    `status`                     int(11)                                                        NULL DEFAULT NULL COMMENT '状态',
    `rsp_sub_dept`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '责任处室',
    `rsp_user`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '责任人',
    `begin_time`                 datetime                                                       NULL DEFAULT NULL COMMENT '计划开始时间',
    `end_time`                   datetime                                                       NULL DEFAULT NULL COMMENT '计划结束时间',
    `circumstance`               int(11)                                                        NULL DEFAULT NULL COMMENT '计划情况',
    `actual_end_time`            datetime                                                       NULL DEFAULT NULL COMMENT '实际结束时间',
    `actual_begin_time`          datetime                                                       NULL DEFAULT NULL COMMENT '实际开始时间',
    `platform_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '平台Id',
    `org_id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '组织Id',
    `logic_status`               int(11)                                                        NOT NULL COMMENT '逻辑删除状态',
    `creator_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '创建者',
    `create_time`                datetime                                                       NULL DEFAULT NULL,
    `modify_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人ID',
    `modify_time`                datetime                                                       NULL DEFAULT NULL,
    `parent_chain`               varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级链',
    `remark`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '备注',
    `class_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '类名',
    `owner_id`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL,
    `scheme_desc`                varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划描述',
    `top_sort`                   int(11)                                                        NULL DEFAULT NULL COMMENT '置顶序号（0：取消置顶）',
    `execute_desc`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '执行情况说明',
    `scheme_number`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '计划编号',
    `issue_time`                 datetime(3)                                                    NULL DEFAULT NULL COMMENT '下发计划时间',
    `rsp_section_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '责任科室',
    `rsp_user_code`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '责任人编号',
    `delay_end_Flag`             bit(1)                                                         NULL DEFAULT NULL COMMENT '是否超时完成 1 是，0 否',
    `delay_end_reason`           varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '超时原因',
    `process_flag`               bit(1)                                                         NULL DEFAULT NULL COMMENT '是否关联流程 1 是，0 否',
    `process_id`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '流程实例Id',
    `duration_days`              int(11)                                                        NULL DEFAULT NULL COMMENT '计划工期',
    `number`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '编码',
    `node_type`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '计划类型',
    `form_code_id`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '表单Id',
    `issued_user`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '下达人',
    `participant_users`          varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参与人',
    `examine_user`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '审批人',
    `examine_type`               varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '评分状态',
    `issue_remind_interval`      int(11)                                                        NULL DEFAULT NULL COMMENT '下发提醒间隔',
    `issue_remind_interval_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '下发提醒间隔单位',
    `plan_active`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '计划活动项',
    `base_line`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '选择基线',
    `identification`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '标识',
    `reason_confirmation`        varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '确认理由',
    `suspend_time`               datetime                                                       NULL DEFAULT NULL COMMENT '暂停时间',
    `terminate_time`             datetime                                                       NULL DEFAULT NULL COMMENT '终止时间',
    `start_time`                 datetime                                                       NULL DEFAULT NULL COMMENT '启动时间',
    `suspend_reason`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '暂停理由',
    `terminate_reason`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '终止理由',
    `start_reason`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '启动理由',
    `last_status`                int(11)                                                        NULL DEFAULT NULL COMMENT '上一个状态',
    `issue_remind_time`          time                                                           NULL DEFAULT NULL COMMENT '下发提醒时间',
    `product_id`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '产品id',
    `reason_transfer`            text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          NULL COMMENT '转办理由',
    `design_task_number`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '编码',
    `paused_cycle`               int(255)                                                       NULL DEFAULT NULL COMMENT '项目暂停周期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '项目计划管理表'
  ROW_FORMAT = DYNAMIC;

-- DDL for pms_project_scheme_apply_approval:
DROP TABLE IF EXISTS pms_project_scheme_apply_approval;
CREATE TABLE `pms_project_scheme_apply_approval`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64)  DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `remark`            varchar(255) DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`            int(11)     NOT NULL COMMENT '状态',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_id`        varchar(64)  DEFAULT NULL COMMENT '项目id',
    `project_scheme_id` varchar(64)  DEFAULT NULL COMMENT '项目计划id',
    `content`           longtext COMMENT '内容',
    `certifier`         varchar(64)  DEFAULT NULL COMMENT '审批人',
    `agreement`         varchar(5)   DEFAULT NULL COMMENT '是否通过',
    `feed_back`         text COMMENT '审批意见',
    `sort`              int(11)      DEFAULT NULL COMMENT '排序',
    `name`              varchar(255) DEFAULT NULL COMMENT '名称',
    `number`            varchar(255) DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目计划申请审批';

-- DDL for pms_project_scheme_content:
DROP TABLE IF EXISTS pms_project_scheme_content;
CREATE TABLE `pms_project_scheme_content`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `org_id`            varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `platform_id`       varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `project_id`        varchar(64)  DEFAULT NULL COMMENT '项目id',
    `project_scheme_id` varchar(64)  DEFAULT NULL COMMENT '项目计划id',
    `content`           text COMMENT '内容',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除状态',
    `creator_id`        varchar(64)  DEFAULT NULL COMMENT '创建者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `modify_time`       datetime     DEFAULT NULL COMMENT '修改时间',
    `class_name`        varchar(64)  DEFAULT NULL COMMENT '类名称',
    `owner_id`          varchar(64)  DEFAULT NULL COMMENT '拥有者ID',
    `remark`            varchar(255) DEFAULT NULL COMMENT '备注',
    `status`            int(11)      DEFAULT NULL COMMENT '状态',
    `sort`              int(11)      DEFAULT NULL COMMENT '排序',
    `name`              varchar(255) DEFAULT NULL COMMENT '名称',
    `number`            varchar(255) DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目计划记录内容';

-- DDL for pms_project_scheme_document:
DROP TABLE IF EXISTS pms_project_scheme_document;
CREATE TABLE `pms_project_scheme_document`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `org_id`            varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`            int(11)     NOT NULL COMMENT '状态',
    `project_scheme_id` varchar(64)   DEFAULT NULL COMMENT '项目计划id',
    `class_name`        varchar(64)   DEFAULT NULL COMMENT '创建人',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除字段',
    `owner_id`          varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `remark`            varchar(1024) DEFAULT NULL COMMENT '备注',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `file_id`           varchar(64)   DEFAULT NULL COMMENT 'res文件Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目计划关联文档';

-- DDL for pms_project_scheme_milestone_node:
DROP TABLE IF EXISTS pms_project_scheme_milestone_node;
CREATE TABLE `pms_project_scheme_milestone_node`
(
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `logic_status`  int(11)     NOT NULL COMMENT '逻辑删除字段',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `org_id`        varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `modify_time`   datetime    NOT NULL COMMENT '修改时间',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '创建人',
    `status`        int(11)     NOT NULL COMMENT '状态',
    `template_id`   varchar(64)   DEFAULT NULL COMMENT '模版ID',
    `node_name`     varchar(64)   DEFAULT NULL COMMENT '节点名称',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `modify_id`     varchar(64) NOT NULL COMMENT '修改人',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `description`   varchar(255)  DEFAULT NULL COMMENT '描述说明',
    `sort`          int(11)       DEFAULT '0' COMMENT '排序字段',
    `node_chain`    varchar(1024) DEFAULT NULL COMMENT '父级链',
    `node_type`     varchar(255)  DEFAULT NULL COMMENT '计划类型',
    `rsp_dept_id`   varchar(255)  DEFAULT NULL COMMENT '责任代码',
    `duration_days` int(11)       DEFAULT NULL COMMENT '工期',
    `delay_days`    int(11)       DEFAULT NULL COMMENT '项目启动后n天开始',
    `process_flag`  bit(1)        DEFAULT NULL COMMENT '是否关联流程 1 是，0否',
    `number`        varchar(255)  DEFAULT NULL COMMENT '编码',
    `name`          varchar(255)  DEFAULT NULL COMMENT '名称',
    `parent_id`     varchar(255)  DEFAULT NULL COMMENT '父Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目计划里程碑节点';

-- DDL for pms_project_scheme_milestone_template:
DROP TABLE IF EXISTS pms_project_scheme_milestone_template;
CREATE TABLE `pms_project_scheme_milestone_template`
(
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `template_name` varchar(64) NOT NULL COMMENT '模版名称',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `modify_time`   datetime    NOT NULL COMMENT '修改时间',
    `org_id`        varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `status`        int(11)     NOT NULL COMMENT '状态',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `modify_id`     varchar(64) NOT NULL COMMENT '修改人',
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '创建人',
    `logic_status`  int(11)     NOT NULL COMMENT '逻辑删除字段',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `sort`          int(11)       DEFAULT '0' COMMENT '排序字段',
    `number`        varchar(255)  DEFAULT NULL COMMENT '编码',
    `name`          varchar(255)  DEFAULT NULL COMMENT '名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目计划里程碑模版';

-- DDL for pms_project_scheme_pre_post:
DROP TABLE IF EXISTS pms_project_scheme_pre_post;
CREATE TABLE `pms_project_scheme_pre_post`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `project_scheme_id` varchar(64)  DEFAULT NULL COMMENT '项目计划id',
    `project_id`        varchar(64)  DEFAULT NULL COMMENT '项目id',
    `type`              int(11)      DEFAULT NULL COMMENT '前后置类型',
    `pre_scheme_id`     varchar(64)  DEFAULT NULL COMMENT '前置计划Id',
    `post_scheme_id`    varchar(64)  DEFAULT NULL COMMENT '后置计划Id',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除状态',
    `creator_id`        varchar(64)  DEFAULT NULL COMMENT '创建者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `modify_time`       datetime     DEFAULT NULL COMMENT '修改时间',
    `org_id`            varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `platform_id`       varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `class_name`        varchar(100) DEFAULT NULL,
    `remark`            varchar(255) DEFAULT NULL COMMENT '备注',
    `owner_id`          varchar(64)  DEFAULT NULL,
    `status`            int(11)      DEFAULT NULL COMMENT '状态',
    `sort`              int(11)      DEFAULT NULL COMMENT '排序',
    `number`            varchar(255) DEFAULT NULL COMMENT '编码',
    `name`              varchar(255) DEFAULT NULL COMMENT '名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目计划前后置关系表';

-- DDL for pms_project_task_status:
DROP TABLE IF EXISTS pms_project_task_status;
CREATE TABLE `pms_project_task_status`
(
    `id`           varchar(64)  NOT NULL COMMENT 'ID',
    `type`         varchar(64)  DEFAULT NULL COMMENT '所属类型',
    `take_effect`  int(11)      DEFAULT NULL COMMENT '角色id',
    `project_id`   varchar(64)  DEFAULT NULL COMMENT '项目id',
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目角色用户表 项目关联的角色和用户';

-- DDL for pms_project_to_base_plan:
DROP TABLE IF EXISTS pms_project_to_base_plan;
CREATE TABLE `pms_project_to_base_plan`
(
    `modify_id`        varchar(64) NOT NULL COMMENT '修改人ID',
    `create_time`      datetime    NOT NULL COMMENT '创建时间',
    `org_id`           varchar(64)  DEFAULT NULL COMMENT '组织Id',
    `creator_id`       varchar(64) NOT NULL COMMENT '创建人Id',
    `modify_time`      datetime    NOT NULL COMMENT '修改时间',
    `platform_id`      varchar(64)  DEFAULT NULL COMMENT '平台Id',
    `logic_status`     int(11)      DEFAULT '1' COMMENT '是否删除',
    `project_id`       varchar(64)  DEFAULT NULL COMMENT '项目ID',
    `base_plan_id`     varchar(64)  DEFAULT NULL COMMENT '综合计划ID',
    `base_plan_number` varchar(64)  DEFAULT NULL COMMENT '综合计划编号',
    `project_number`   varchar(64)  DEFAULT NULL COMMENT '项目编号',
    `class_name`       varchar(64)  DEFAULT NULL,
    `id`               varchar(64) NOT NULL,
    `status`           int(11)      DEFAULT NULL,
    `remark`           varchar(512) DEFAULT NULL,
    `owner_id`         varchar(64)  DEFAULT NULL,
    `relation_type`    char(1)      DEFAULT NULL COMMENT '关联类型(0:主动,1:被动)',
    `routine`          bit(1)       DEFAULT b'0' COMMENT '是否例行 默认为否',
    `source_type`      varchar(64)  DEFAULT NULL COMMENT '来源类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目和综合计划的关系表（1;N）';

-- DDL for pms_project_weekly:
DROP TABLE IF EXISTS pms_project_weekly;
CREATE TABLE `pms_project_weekly`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`            varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`            varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`            int(11)        DEFAULT NULL COMMENT '状态',
    `logic_status`      int(11)        DEFAULT NULL COMMENT '逻辑删除字段',
    `week`              tinyint(4)     DEFAULT NULL COMMENT '所在年份第几周',
    `week_begin`        datetime    NOT NULL COMMENT '一周的开始时间',
    `week_end`          datetime    NOT NULL COMMENT '一周结束时间',
    `resp`              varchar(40) NOT NULL COMMENT '责任人',
    `evaluate`          varchar(800)   DEFAULT NULL COMMENT '评价',
    `score`             decimal(10, 0) DEFAULT NULL COMMENT '评分',
    `reviewed_by`       varchar(60)    DEFAULT NULL COMMENT '审核人',
    `carbon_copy_by`    varchar(400)   DEFAULT NULL COMMENT '抄送人多个时用英文逗号分隔',
    `content_summarize` varchar(600)   DEFAULT NULL COMMENT '内容总结',
    `evaluate_date`     datetime       DEFAULT NULL COMMENT '评价时间',
    `project_id`        varchar(64)    DEFAULT NULL COMMENT '项目Id',
    `overall_progress`  int(4)      NOT NULL COMMENT '整体进度',
    `summary`           varchar(800)   DEFAULT NULL COMMENT '汇报总结',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目周报表';

-- DDL for pms_project_weekly_content:
DROP TABLE IF EXISTS pms_project_weekly_content;
CREATE TABLE `pms_project_weekly_content`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime    NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) NOT NULL COMMENT '修改人',
    `remark`          varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`          varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`          int(11)        DEFAULT NULL COMMENT '状态',
    `logic_status`    int(11)     NOT NULL COMMENT '逻辑删除字段',
    `content`         varchar(600)   DEFAULT NULL COMMENT '工作内容',
    `task_time`       decimal(10, 0) DEFAULT NULL COMMENT '工时',
    `the_plan`        int(4)         DEFAULT NULL COMMENT '是否为计划内',
    `relationship`    varchar(200)   DEFAULT NULL COMMENT '关联关系',
    `weekly_id`       varchar(40)    DEFAULT NULL COMMENT '周报ID',
    `relation_type`   varchar(10)    DEFAULT NULL COMMENT '关联类型',
    `is_next`         int(1)         DEFAULT NULL COMMENT '是否为下周计划',
    `complete_status` int(1)         DEFAULT NULL COMMENT '计划是否已完成',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目周报内容表';
-- ----------------------------
-- Table structure for pms_question_management
-- ----------------------------
DROP TABLE IF EXISTS `pms_question_management`;
CREATE TABLE `pms_question_management`
(
    `id`                     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NOT NULL COMMENT 'id',
    `question_source`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '问题来源',
    `serious_level`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '验证程度',
    `predict_end_time`       datetime                                                      NULL DEFAULT NULL COMMENT '预计完成时间',
    `question_type`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '问题类型',
    `plan_id`                varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '关联任务Id',
    `exhibitor`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '提出人Id',
    `project_id`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '项目ID',
    `content`                text CHARACTER SET utf8 COLLATE utf8_general_ci               NULL COMMENT '内容',
    `proposed_time`          datetime                                                      NULL DEFAULT NULL COMMENT '提出时间',
    `schedule`               decimal(5, 2)                                                 NULL DEFAULT NULL COMMENT '进度',
    `recipient`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '接收人',
    `principal_id`           varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '负责人ID',
    `priority_level`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '优先级',
    `exhibitor_name`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '提出人名称',
    `recipient_name`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '接收人名称',
    `principal_name`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '负责人名称',
    `modify_id`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '修改人ID',
    `creator_id`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '创建人ID',
    `create_time`            datetime                                                      NOT NULL COMMENT '创建时间',
    `class_name`             varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL COMMENT '类名称',
    `remark`                 text CHARACTER SET utf8 COLLATE utf8_general_ci               NULL COMMENT '备注',
    `modify_time`            datetime                                                      NOT NULL COMMENT '修改时间',
    `owner_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '拥有者',
    `number`                 varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '编码',
    `name`                   varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci       NULL DEFAULT NULL COMMENT '名称',
    `sort`                   int(11)                                                       NULL DEFAULT NULL COMMENT '排序',
    `status`                 int(11)                                                       NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`           int(11)                                                       NOT NULL COMMENT '逻辑删除状态',
    `platform_id`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '组织ID',
    `document_id`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL,
    `data_source`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '数据来源',
    `data_source_id`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '数据来源id',
    `verify_id`              varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '验证ID',
    `product_number`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '产品编码',
    `material_number`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '物料编码',
    `production_orders`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生产订单',
    `product_number_sn`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编号（SN）',
    `stage`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '阶段',
    `process_link`           varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过程环节',
    `process_classifi`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '过程分类',
    `problem_phenomenon_one` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题现象一级分类',
    `problem_phenomenon_two` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题现象二级分类',
    `problem_phenomenon_th`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题现象三级分类',
    `problem_level`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题等级分类',
    `reasion_one`            varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一级原因分类',
    `reasion_two`            varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二级原因分类',
    `reasion_three`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三级原因分类',
    `reasion_remark`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '原因分析描述',
    `correct_classifi`       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纠正分类',
    `ecn_number`             varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联ECN编号',
    `correct_remark`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '纠正描述',
    `question_up_type`       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题升级类型',
    `is_di_technical_issues` bit(1)                                                        NULL DEFAULT NULL COMMENT '是否疑难技术问题',
    `is_assess`              bit(1)                                                        NULL DEFAULT NULL COMMENT '是否综合评估不再解决',
    `is_ecological_issues`   bit(1)                                                        NULL DEFAULT NULL COMMENT '是否生态问题',
    `is_quality_use_cases`   bit(1)                                                        NULL DEFAULT NULL COMMENT '是否典型质量案例',
    `one_case_to_another`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '举一反三',
    `corre_ac_description`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '纠正措施描述',
    `opinion_categories`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '意见类别',
    `review_points`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审要点',
    `is_technical_issues`    bit(1)                                                        NULL DEFAULT NULL COMMENT '是否技术问题',
    `is_typical_problems`    bit(1)                                                        NULL DEFAULT NULL COMMENT '是否典型问题',
    `op_classification`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '意见分类',
    `adoption_situation`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '采纳情况',
    `overall_description`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '整体改进描述',
    `is_active_relation`     bit(1)                                                        NULL DEFAULT NULL COMMENT '是否主动关联',
    `close_time`             datetime                                                      NULL DEFAULT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '问题管理'
  ROW_FORMAT = DYNAMIC;


-- DDL for pms_question_to_risk:
DROP TABLE IF EXISTS pms_question_to_risk;
CREATE TABLE `pms_question_to_risk`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='问题和风险关系';

-- DDL for pms_relation:
DROP TABLE IF EXISTS pms_relation;
CREATE TABLE `pms_relation`
(
    `id`          varchar(64) NOT NULL COMMENT 'id',
    `to_id`       varchar(64) NOT NULL COMMENT '副Id',
    `sort`        int(11) DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL COMMENT '修改人ID',
    `from_id`     varchar(64) NOT NULL COMMENT '主id',
    `creator_id`  varchar(64) NOT NULL COMMENT '创建者ID',
    `class_name`  varchar(64) NOT NULL COMMENT '类名称',
    `to_class`    varchar(64) NOT NULL COMMENT '副类名',
    `from_class`  varchar(64) NOT NULL COMMENT '主类名',
    `create_time` datetime    NOT NULL COMMENT '创建时间',
    `status`      int(11)     NOT NULL COMMENT '状态',
    `modify_time` datetime    NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='关系基类';

-- DDL for pms_revision_class:
DROP TABLE IF EXISTS pms_revision_class;
CREATE TABLE `pms_revision_class`
(
    `id`              varchar(64) NOT NULL COMMENT 'id',
    `rev_id`          varchar(64) DEFAULT NULL COMMENT '版本值',
    `previous_rev_id` varchar(64) DEFAULT NULL COMMENT '上一个版本',
    `next_rev_id`     varchar(64) DEFAULT NULL COMMENT '下一个版本',
    `initial_rev_id`  varchar(64) DEFAULT NULL COMMENT '初始版本',
    `rev_order`       int(11)     DEFAULT NULL COMMENT '版本顺序',
    `rev_key`         varchar(64) DEFAULT NULL COMMENT '版本KEY',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='版本';

-- DDL for pms_risk_management:
DROP TABLE IF EXISTS pms_risk_management;
CREATE TABLE `pms_risk_management`
(
    `id`                  varchar(64)  NOT NULL COMMENT 'id',
    `predict_end_time`    datetime     DEFAULT NULL COMMENT '预计完成时间',
    `coping_strategy`     varchar(64)  DEFAULT NULL COMMENT '应对策略',
    `plan_id`             varchar(64)  DEFAULT NULL COMMENT '任务ID/计划ID',
    `risk_type`           varchar(64)  DEFAULT NULL,
    `project_id`          varchar(64)  DEFAULT NULL,
    `solutions`           varchar(255) DEFAULT NULL COMMENT '应对措施',
    `risk_influence`      varchar(64)  DEFAULT NULL COMMENT '风险影响',
    `principal_id`        varchar(64)  DEFAULT NULL COMMENT '负责人ID',
    `risk_probability`    varchar(64)  DEFAULT NULL COMMENT '风险概率',
    `predict_start_time`  varchar(64)  DEFAULT NULL COMMENT '预期发生时间',
    `discern_person`      varchar(64)  DEFAULT NULL COMMENT '识别人',
    `discern_person_name` varchar(64)  DEFAULT NULL COMMENT '识别人名称',
    `principal_name`      varchar(64)  DEFAULT NULL COMMENT '负责人名称',
    `modify_id`           varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`          varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`         datetime     NOT NULL COMMENT '创建时间',
    `class_name`          varchar(128) NOT NULL COMMENT '类名称',
    `remark`              text COMMENT '备注',
    `modify_time`         datetime     NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`              varchar(128) DEFAULT NULL COMMENT '编码',
    `name`                varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`                int(11)      DEFAULT NULL COMMENT '排序',
    `status`              int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`        int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`         varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`              varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `document_id`         varchar(64)  DEFAULT NULL,
    `data_source`         varchar(64)  DEFAULT NULL COMMENT '数据来源',
    `data_source_id`      varchar(64)  DEFAULT NULL COMMENT '数据来源id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='危险管理';

-- DDL for pms_risk_plan:
DROP TABLE IF EXISTS pms_risk_plan;
CREATE TABLE `pms_risk_plan`
(
    `id`               varchar(64)  NOT NULL COMMENT 'id',
    `risk_probability` varchar(64)  DEFAULT NULL COMMENT '风险发生概率',
    `risk_influence`   varchar(64)  DEFAULT NULL COMMENT '影响程度',
    `risk_type`        varchar(64)  DEFAULT NULL COMMENT '风险类型',
    `solutions`        varchar(255) DEFAULT NULL COMMENT '应对措施',
    `project_id`       varchar(64)  NOT NULL COMMENT '项目ID',
    `modify_id`        varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`       varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`      datetime     NOT NULL COMMENT '创建时间',
    `class_name`       varchar(128) NOT NULL COMMENT '类名称',
    `remark`           text COMMENT '备注',
    `modify_time`      datetime     NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`           varchar(128) DEFAULT NULL COMMENT '编码',
    `name`             varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`             int(11)      DEFAULT NULL COMMENT '排序',
    `status`           int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`     int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`      varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`           varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `document_id`      varchar(64)  DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='风险预案';

-- DDL for pms_risk_question_relation:
DROP TABLE IF EXISTS pms_risk_question_relation;
CREATE TABLE `pms_risk_question_relation`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `risk_id`      varchar(64) NOT NULL COMMENT '风险id',
    `question_id`  varchar(64) NOT NULL COMMENT '问题id',
    `data_source`  varchar(64) NOT NULL COMMENT '数据来源',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='风险问题关联';

-- DDL for pms_stakeholder:
DROP TABLE IF EXISTS pms_stakeholder;
CREATE TABLE `pms_stakeholder`
(
    `id`           varchar(64)  NOT NULL COMMENT 'id',
    `address`      varchar(255) DEFAULT NULL COMMENT '地址',
    `contact_info` varchar(64)  DEFAULT NULL COMMENT '联系信息',
    `contact_type` varchar(64)  DEFAULT NULL COMMENT '联系方式',
    `project_id`   varchar(64)  NOT NULL,
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='干系人/不只是人';

-- DDL for pms_task_subject:
DROP TABLE IF EXISTS pms_task_subject;
CREATE TABLE `pms_task_subject`
(
    `id`           varchar(64)  NOT NULL COMMENT 'id',
    `project_id`   varchar(64)  NOT NULL COMMENT '项目ID',
    `take_effect`  int(11)      NOT NULL COMMENT '启用禁用  0   1',
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`  datetime     NOT NULL COMMENT '创建时间',
    `class_name`   varchar(128) NOT NULL COMMENT '类名称',
    `remark`       text COMMENT '备注',
    `modify_time`  datetime     NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`       varchar(128) DEFAULT NULL COMMENT '编码',
    `name`         varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`         int(11)      DEFAULT NULL COMMENT '排序',
    `status`       int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status` int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='任务科目';

-- DDL for pms_user_like_project:
DROP TABLE IF EXISTS pms_user_like_project;
CREATE TABLE `pms_user_like_project`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_id`   varchar(64)   DEFAULT NULL COMMENT '项目ID',
    `user_id`      varchar(64)   DEFAULT NULL COMMENT '用户ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='用户关注的项目记录表';

-- DDL for pms_warning_setting:
DROP TABLE IF EXISTS pms_warning_setting;
CREATE TABLE `pms_warning_setting`
(
    `id`               varchar(64)  NOT NULL COMMENT 'id',
    `frequency`        varchar(64)  DEFAULT NULL COMMENT '频率',
    `project_id`       varchar(64)  NOT NULL COMMENT '项目ID',
    `time`             varchar(64)  DEFAULT NULL COMMENT '提醒时间',
    `warning_way`      varchar(64)  DEFAULT NULL COMMENT '预警方式',
    `take_effect`      smallint(6)  DEFAULT NULL COMMENT '是否启用',
    `warning_type`     varchar(64)  NOT NULL COMMENT '预警类型',
    `day_num`          int(11)      DEFAULT NULL COMMENT '提醒天数',
    `dict_value_id`    varchar(64)  NOT NULL COMMENT '字典表的值id',
    `modify_id`        varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `creator_id`       varchar(64)  DEFAULT NULL COMMENT '创建人ID',
    `create_time`      datetime     NOT NULL COMMENT '创建时间',
    `class_name`       varchar(128) NOT NULL COMMENT '类名称',
    `remark`           text COMMENT '备注',
    `modify_time`      datetime     NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `number`           varchar(128) DEFAULT NULL COMMENT '编码',
    `name`             varchar(128) DEFAULT NULL COMMENT '名称',
    `sort`             int(11)      DEFAULT NULL COMMENT '排序',
    `status`           int(11)      NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `logic_status`     int(11)      NOT NULL COMMENT '逻辑删除状态',
    `platform_id`      varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`           varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `warning_category` varchar(64)  DEFAULT NULL COMMENT '提醒种类(day:提醒天数;percentage:提醒工期百分比)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='预警设置';

-- DDL for pms_warning_setting_message_recipient:
DROP TABLE IF EXISTS pms_warning_setting_message_recipient;
CREATE TABLE `pms_warning_setting_message_recipient`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `message_id`   varchar(64) NOT NULL COMMENT '消息id',
    `recipient_id` varchar(64) NOT NULL COMMENT '接收人id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目预警设置消息接收人';

-- DDL for pms_warning_setting_message_record:
DROP TABLE IF EXISTS pms_warning_setting_message_record;
CREATE TABLE `pms_warning_setting_message_record`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`        datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime    NOT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`             varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`             int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status`       int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_id`         varchar(64)   DEFAULT NULL COMMENT '项目id',
    `business_id`        varchar(64)   DEFAULT NULL COMMENT '业务id',
    `business_name`      varchar(200)  DEFAULT NULL COMMENT '业务名称',
    `warning_type`       varchar(64)   DEFAULT NULL COMMENT '预警类型',
    `warning_dict_id`    varchar(64)   DEFAULT NULL COMMENT '预警模版字典Id',
    `sender_id`          varchar(64)   DEFAULT NULL COMMENT '发送者id',
    `sender_time`        datetime      DEFAULT NULL COMMENT '发送时间',
    `message_content`    text COMMENT '消息内容',
    `actual_sender_time` datetime      DEFAULT NULL COMMENT '实际发送时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目预警设置消息记录';

-- DDL for pms_warning_to_role:
DROP TABLE IF EXISTS pms_warning_to_role;
CREATE TABLE `pms_warning_to_role`
(
    `id`          varchar(64) NOT NULL,
    `to_id`       varchar(64) NOT NULL,
    `sort`        int(11)     DEFAULT NULL,
    `modify_id`   varchar(64) NOT NULL,
    `from_id`     varchar(64) NOT NULL,
    `creator_id`  varchar(64) NOT NULL,
    `class_name`  varchar(64) DEFAULT NULL,
    `to_class`    varchar(64) DEFAULT NULL,
    `from_class`  varchar(64) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `status`      int(11)     NOT NULL,
    `modify_time` datetime    NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='预警和角色';

-- ----------------------------
-- Table structure for pms_work_hour_estimate
-- ----------------------------
DROP TABLE IF EXISTS `pms_work_hour_estimate`;
CREATE TABLE `pms_work_hour_estimate`
(
    `id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`  datetime                                                       NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime                                                       NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织Id',
    `status`       int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status` int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `number`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '单据编号',
    `member_id`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '成员id',
    `work_hour`    int(11)                                                        NOT NULL COMMENT '工时时长',
    `start_date`   datetime                                                       NOT NULL COMMENT '开始日期',
    `end_date`     datetime                                                       NOT NULL COMMENT '结束日期',
    `project_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '项目id',
    `member_name`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '成员名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '工时预估'
  ROW_FORMAT = DYNAMIC;

-- DDL for pms_work_hour_estimate_detail:
DROP TABLE IF EXISTS pms_work_hour_estimate_detail;
CREATE TABLE `pms_work_hour_estimate_detail`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `work_hour_id` varchar(64) NOT NULL COMMENT '工时id',
    `work_month`   varchar(20) NOT NULL COMMENT '月份',
    `work_hour`    int(11)     NOT NULL COMMENT '工时',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='工时预估明细';

-- DDL for pms_work_hour_fill:
DROP TABLE IF EXISTS pms_work_hour_fill;
CREATE TABLE `pms_work_hour_fill`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`       varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`         varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int(11)     NOT NULL COMMENT '状态',
    `logic_status`   int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_id`     varchar(64) NOT NULL COMMENT '项目id',
    `number`         varchar(64) NOT NULL COMMENT '单据编号',
    `type`           varchar(64)   DEFAULT NULL COMMENT '类型',
    `title`          varchar(64) NOT NULL COMMENT '标题',
    `work_hour_type` varchar(64)   DEFAULT NULL COMMENT '工时类型',
    `fill_role`      varchar(64)   DEFAULT NULL COMMENT '填报角色',
    `member_id`      varchar(64) NOT NULL COMMENT '成员id',
    `start_date`     datetime      DEFAULT NULL COMMENT '开始日期',
    `end_date`       datetime      DEFAULT NULL COMMENT '结束日期',
    `work_hour`      int(11)       DEFAULT NULL COMMENT '工时',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='工时填报';

-- DDL for pms_work_hour_fill_day:
DROP TABLE IF EXISTS pms_work_hour_fill_day;
CREATE TABLE `pms_work_hour_fill_day`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `fill_id`      varchar(64) NOT NULL COMMENT '工时填报id',
    `work_date`    varchar(64) NOT NULL COMMENT '工时日期',
    `work_hour`    int(11)     NOT NULL COMMENT '工时',
    `member_id`    varchar(64) NOT NULL COMMENT '成员id',
    `project_id`   varchar(64) NOT NULL COMMENT '项目id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='工时填报每天信息';

-- DDL for pms_work_hour_fill_detail:
DROP TABLE IF EXISTS pms_work_hour_fill_detail;
CREATE TABLE `pms_work_hour_fill_detail`
(
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`   datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`        int(11)     NOT NULL COMMENT '状态',
    `logic_status`  int(11)     NOT NULL COMMENT '逻辑删除字段',
    `fill_day_id`   varchar(64)   DEFAULT NULL COMMENT '工时填报天id',
    `work_date`     varchar(64) NOT NULL COMMENT '工时日期',
    `work_hour`     int(11)     NOT NULL COMMENT '工时',
    `project_place` varchar(64) NOT NULL COMMENT '项目地点',
    `relate_object` varchar(64)   DEFAULT NULL COMMENT '关联对象',
    `task_content`  varchar(500)  DEFAULT NULL COMMENT '任务内容',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='工时填报明细';

-- DDL for pms_year_investment_scheme:
DROP TABLE IF EXISTS pms_year_investment_scheme;
CREATE TABLE `pms_year_investment_scheme`
(
    `installation`       decimal(20, 2) DEFAULT NULL COMMENT '安装工程',
    `creator_id`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `remark`             varchar(1024)  DEFAULT NULL COMMENT '备注',
    `org_id`             varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `project_id`         varchar(64)    DEFAULT NULL COMMENT '项目Id',
    `number`             varchar(64)    DEFAULT NULL COMMENT '计划编号',
    `device`             decimal(20, 2) DEFAULT NULL COMMENT '设备投资',
    `architecture`       decimal(20, 2) DEFAULT NULL COMMENT '建筑工程',
    `last_year_do_desc`  varchar(1000)  DEFAULT NULL COMMENT 'Y-1年执行情况说明',
    `next_two_year`      decimal(20, 2) DEFAULT NULL COMMENT 'Y+2年投资计划',
    `year_process`       varchar(1000)  DEFAULT NULL COMMENT 'Y年形象进度',
    `modify_id`          varchar(64)    DEFAULT NULL COMMENT '修改人',
    `next_three_year`    decimal(20, 2) DEFAULT NULL COMMENT 'Y+3年投资计划',
    `next_one_year`      decimal(20, 2) DEFAULT NULL COMMENT 'Y+1年投资计划',
    `investment_id`      varchar(64)    DEFAULT NULL COMMENT '投资计划Id',
    `other`              decimal(20, 2) DEFAULT NULL COMMENT '其他费用',
    `modify_time`        datetime       DEFAULT NULL COMMENT '修改时间',
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `create_time`        datetime       DEFAULT NULL COMMENT '创建时间',
    `next_four_year`     decimal(10, 2) DEFAULT NULL COMMENT 'Y+4年投资计划',
    `name`               varchar(255)   DEFAULT NULL COMMENT '计划名称',
    `class_name`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `status`             int(11)        DEFAULT NULL COMMENT '状态',
    `logic_status`       int(11)        DEFAULT NULL COMMENT '逻辑删除字段',
    `next_five_year`     decimal(20, 2) DEFAULT NULL COMMENT 'Y+5年投资计划',
    `last_year_complete` decimal(20, 2) DEFAULT NULL COMMENT 'Y-1年投资计划预计完成',
    `platform_id`        varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `owner_id`           varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `close_flag`         bit(1)         DEFAULT NULL COMMENT '是否关闭投资计划',
    `year_name`          varchar(255)   DEFAULT NULL COMMENT '年份',
    `old_id`             varchar(255)   DEFAULT NULL COMMENT '调整前Id',
    `estimate_id`        varchar(255)   DEFAULT NULL COMMENT '概算Id',
    `total_do`           decimal(10, 2) DEFAULT NULL COMMENT '当年实际执行',
    `project_number`     varchar(64)    DEFAULT NULL COMMENT '项目编号',
    `serial_number`      int(64)        DEFAULT NULL COMMENT '项目编号-序号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='年度投资计划';

-- DDL for pms_year_investment_scheme_month_feedback:
DROP TABLE IF EXISTS pms_year_investment_scheme_month_feedback;
CREATE TABLE `pms_year_investment_scheme_month_feedback`
(
    `number`             varchar(30)    DEFAULT NULL COMMENT '计划编号',
    `owner_id`           varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `name`               varchar(50)    DEFAULT NULL COMMENT '名称',
    `modify_time`        datetime       DEFAULT NULL COMMENT '修改时间',
    `creator_id`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `class_name`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `next_process`       varchar(1024)  DEFAULT NULL COMMENT '下月进度计划',
    `project_id`         varchar(64)    DEFAULT NULL COMMENT '项目Id',
    `platform_id`        varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `year`               varchar(20)    DEFAULT NULL COMMENT '年份',
    `status`             int(11)        DEFAULT NULL COMMENT '状态',
    `delay_desc`         varchar(1000)  DEFAULT NULL COMMENT '项目总体进度滞后情况',
    `year_investment_id` varchar(64)    DEFAULT NULL COMMENT '年度投资计划Id',
    `month_process`      varchar(1000)  DEFAULT NULL COMMENT '本月进度执行情况说明',
    `month_do_status`    int(11)        DEFAULT NULL COMMENT '本月投资计划执行状态',
    `remark`             varchar(1024)  DEFAULT NULL COMMENT '备注',
    `org_id`             varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `reason`             varchar(1024)  DEFAULT NULL COMMENT '本月执行偏差原因及纠偏措施',
    `month`              varchar(20)    DEFAULT NULL COMMENT '月度',
    `logic_status`       int(11)        DEFAULT NULL COMMENT '逻辑删除字段',
    `m_practice_do`      decimal(20, 2) DEFAULT NULL COMMENT '1-M月实际执行',
    `modify_id`          varchar(64)    DEFAULT NULL COMMENT '修改人',
    `create_time`        datetime       DEFAULT NULL COMMENT '创建时间',
    `total_process`      varchar(1000)  DEFAULT NULL COMMENT '总体进度执行情况',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='月度投资计划反馈';

-- DDL for pmsx_bud_budget_month:
DROP TABLE IF EXISTS pmsx_bud_budget_month;
CREATE TABLE `pmsx_bud_budget_month`
(
    `id`                  varchar(64)                      NOT NULL COMMENT '主键',
    `class_name`          varchar(64)                               DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64)                      NOT NULL COMMENT '创建人',
    `modify_time`         datetime                         NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)                               DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime                         NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)                      NOT NULL COMMENT '修改人',
    `status`              int(11)                          NOT NULL COMMENT '状态',
    `logic_status`        int(11)                          NOT NULL COMMENT '逻辑删除字段',
    `budget_project_id`   varchar(64)                      NOT NULL COMMENT '项目预算表ID',
    `budget_project_name` varchar(128)                              DEFAULT NULL COMMENT '项目预算名',
    `year_expense`        decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '年度预算支出',
    `year_fact_expense`   decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '年度实际支出',
    `january_money`       decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '1月预算',
    `february_money`      decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '2月预算',
    `march_money`         decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '3月预算',
    `april_money`         decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '4月预算',
    `may_money`           decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '5月预算',
    `june_money`          decimal(16, 4) unsigned zerofill NOT NULL DEFAULT '000000000000.0000' COMMENT '6月预算',
    `july_money`          decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '7月预算',
    `august_money`        decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '8月预算',
    `september_money`     decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '9月预算',
    `october_money`       decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '10月预算',
    `november_money`      decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '11月预算',
    `december_money`      decimal(16, 4) unsigned          NOT NULL DEFAULT '0.0000' COMMENT '12月预算',
    `remark`              varchar(1024)                             DEFAULT NULL,
    `platform_id`         varchar(64)                               DEFAULT NULL,
    `org_id`              varchar(64)                               DEFAULT NULL,
    `sort`                int(11)                                   DEFAULT NULL,
    `number`              varchar(64)                               DEFAULT NULL,
    `name`                varchar(128)                              DEFAULT NULL,
    `project_id`          varchar(64)                      NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='费用预算月度年度表';

-- DDL for pmsx_bud_exponse_detail:
DROP TABLE IF EXISTS pmsx_bud_exponse_detail;
CREATE TABLE `pmsx_bud_exponse_detail`
(
    `id`                   varchar(64)             NOT NULL COMMENT '主键',
    `class_name`           varchar(64)                      DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)             NOT NULL COMMENT '创建人',
    `modify_time`          datetime                NOT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)                      DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime                NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)             NOT NULL COMMENT '修改人',
    `status`               int(11)                 NOT NULL COMMENT '状态',
    `logic_status`         int(11)                 NOT NULL COMMENT '逻辑删除字段',
    `sort`                 bigint(20)                       DEFAULT NULL COMMENT '排序',
    `number`               varchar(128)                     DEFAULT NULL COMMENT '编码',
    `cost_center_id`       varchar(64)                      DEFAULT NULL COMMENT '成本中心ID',
    `cost_center_name`     varchar(128)                     DEFAULT NULL COMMENT '成本中心名字',
    `expense_account_id`   varchar(64)                      DEFAULT NULL COMMENT '费用科目ID',
    `expense_account_name` varchar(128)                     DEFAULT NULL COMMENT '费用科目名字',
    `out_person_id`        varchar(64)                      DEFAULT NULL COMMENT '支出人ID',
    `out_person_name`      varchar(128)                     DEFAULT NULL COMMENT '支出人姓名',
    `out_money`            decimal(16, 4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '支出金额',
    `out_time`             datetime                NOT NULL COMMENT '支出时间',
    `description`          varchar(255)                     DEFAULT NULL COMMENT '描述',
    `tag`                  varchar(255)                     DEFAULT NULL COMMENT '附件URL',
    `budget_project_id`    varchar(64)                      DEFAULT NULL COMMENT '预算ID',
    `budget_project_name`  varchar(128)                     DEFAULT NULL COMMENT '预算名称',
    `remark`               varchar(1024)                    DEFAULT NULL,
    `platform_id`          varchar(64)                      DEFAULT NULL,
    `org_id`               varchar(64)                      DEFAULT NULL,
    `project_id`           varchar(64)             NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='支出详情表';

-- DDL for pmsx_bud_project_budget:
DROP TABLE IF EXISTS pmsx_bud_project_budget;
CREATE TABLE `pmsx_bud_project_budget`
(
    `id`                   varchar(64)             NOT NULL COMMENT '主键',
    `class_name`           varchar(64)                      DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)             NOT NULL COMMENT '创建人',
    `modify_time`          datetime                NOT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)                      DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime                NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)             NOT NULL COMMENT '修改人',
    `status`               int(11)                 NOT NULL COMMENT '状态',
    `logic_status`         int(11)                 NOT NULL COMMENT '逻辑删除字段',
    `sort`                 int(11)                          DEFAULT NULL COMMENT '排序',
    `number`               varchar(128)                     DEFAULT NULL COMMENT '编码',
    `name`                 varchar(128)                     DEFAULT NULL COMMENT '预算名称',
    `cost_center_id`       varchar(64)                      DEFAULT NULL COMMENT '成本中心ID',
    `cost_center_name`     varchar(128)                     DEFAULT NULL COMMENT '成本中心名字',
    `expense_account_id`   varchar(64)             NOT NULL COMMENT '费用科目ID',
    `expense_account_name` varchar(128)                     DEFAULT NULL COMMENT '费用科目名',
    `year`                 varchar(64)             NOT NULL COMMENT '年度',
    `year_expense`         decimal(16, 4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '年度预算总费用',
    `month_budget_id`      varchar(64)                      DEFAULT NULL COMMENT '费用预算月度表ID',
    `total_cost`           decimal(16, 4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '实际总成本',
    `price_difference`     decimal(16, 4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '差价',
    `is_out`               int(11) unsigned        NOT NULL DEFAULT '0' COMMENT '是否超出预算 0为未超预算，1为超出预算',
    `execution_schedule`   int(11)                          DEFAULT NULL COMMENT '执行进度',
    `project_id`           varchar(64)             NOT NULL,
    `remark`               varchar(1024)                    DEFAULT NULL,
    `platform_id`          varchar(64)                      DEFAULT NULL,
    `org_id`               varchar(64)                      DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目预算表';

-- DDL for pmsx_budget_annual:
DROP TABLE IF EXISTS pmsx_budget_annual;
CREATE TABLE `pmsx_budget_annual`
(
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)     DEFAULT NULL COMMENT '创建人',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`   datetime    NOT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)     DEFAULT NULL COMMENT '拥有者',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64) NOT NULL COMMENT '修改人',
    `remark`        text COMMENT '备注',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`        int(11)     NOT NULL COMMENT '状态',
    `logic_status`  int(11)     NOT NULL COMMENT '逻辑删除字段',
    `budget_number` varchar(64)     DEFAULT NULL COMMENT '预算编码对应POSID',
    `annual`        varchar(4)      DEFAULT NULL COMMENT '对应年度u',
    `practical`     decimal(65, 30) DEFAULT NULL COMMENT '实际预算对应（NDSJ）年的 实际',
    `promise`       decimal(65, 30) DEFAULT NULL COMMENT '年度承诺对应（NDCN）',
    `budget`        decimal(65, 30) DEFAULT NULL COMMENT 'budget       Decimal (15,2) 年度度预算对应（NDYS）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='预算年度表';

-- DDL for pmsx_budget_annual_monthly:
DROP TABLE IF EXISTS pmsx_budget_annual_monthly;
CREATE TABLE `pmsx_budget_annual_monthly`
(
    `id`                  varchar(64) NOT NULL COMMENT '主键',
    `class_name`          varchar(64)     DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`         datetime    NOT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)     DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime    NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64) NOT NULL COMMENT '修改人',
    `remark`              text COMMENT '备注',
    `platform_id`         varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`              int(11)     NOT NULL COMMENT '状态',
    `logic_status`        int(11)     NOT NULL COMMENT '逻辑删除字段',
    `budget_number`       varchar(64)     DEFAULT NULL COMMENT '预算编码对应POSID',
    `annual`              varchar(4)      DEFAULT NULL COMMENT '预算年度对应GJAHR',
    `january_promise`     decimal(65, 30) DEFAULT NULL COMMENT '1月承诺对应（Z01YDCN）',
    `january_budget`      decimal(65, 30) DEFAULT NULL COMMENT '1月预算(Z01YDYS)',
    `february_practical`  decimal(65, 30) DEFAULT NULL COMMENT '2月实际(Z02SJ ）',
    `february_promise`    decimal(65, 30) DEFAULT NULL COMMENT '2月承诺(Z02YDCN ）',
    `february_budget`     decimal(65, 30) DEFAULT NULL COMMENT '2月预算(Z02YDYS）',
    `march_promise`       decimal(65, 30) DEFAULT NULL COMMENT '3月承诺(Z03YDCN ）',
    `march_budget`        decimal(65, 30) DEFAULT NULL COMMENT '3月预算(Z03YDYS）',
    `april_practical`     decimal(65, 30) DEFAULT NULL COMMENT '4月实际(Z04SJ ）',
    `april_promise`       decimal(65, 30) DEFAULT NULL COMMENT '4月承诺(Z04YDCN ）',
    `april_budget`        decimal(65, 30) DEFAULT NULL COMMENT '4月预算(Z04YDYS）',
    `may_practical`       decimal(65, 30) DEFAULT NULL COMMENT '5月实际(Z05SJ ）',
    `may_promise`         decimal(65, 30) DEFAULT NULL COMMENT '5月承诺(Z05YDCN ）',
    `may_budget`          decimal(65, 30) DEFAULT NULL COMMENT '5月预算(Z05YDYS）',
    `june_practical`      decimal(65, 30) DEFAULT NULL COMMENT '6月实际(Z06SJ ）',
    `june_promise`        decimal(65, 30) DEFAULT NULL COMMENT '6月承诺(Z06YDCN ）',
    `june_budget`         decimal(65, 30) DEFAULT NULL COMMENT '6月预算(Z06YDYS）',
    `july_practical`      decimal(65, 30) DEFAULT NULL COMMENT '7月实际(Z07SJ ）',
    `july_promise`        decimal(65, 30) DEFAULT NULL COMMENT '7月承诺(Z07YDCN ）',
    `july_budget`         decimal(65, 30) DEFAULT NULL COMMENT '7月预算(Z07YDYS）',
    `september_practical` decimal(65, 30) DEFAULT NULL COMMENT '9月实际(Z09SJ ）',
    `august_practical`    decimal(65, 30) DEFAULT NULL COMMENT '8月实际(Z08SJ ）',
    `august_promise`      decimal(65, 30) DEFAULT NULL COMMENT '8月承诺(Z08YDCN ）',
    `august_budget`       decimal(65, 30) DEFAULT NULL COMMENT '8月预算(Z08YDYS）',
    `january_practical`   decimal(65, 30) DEFAULT NULL COMMENT '1月实际对应（Z01SJ ）',
    `march_practical`     decimal(65, 30) DEFAULT NULL COMMENT '3月实际(Z03SJ ）',
    `september_promise`   decimal(65, 30) DEFAULT NULL COMMENT '9月承诺(Z09YDCN ）',
    `september_budget`    decimal(65, 30) DEFAULT NULL COMMENT '9月预算(Z09YDYS）',
    `october_practical`   decimal(65, 30) DEFAULT NULL COMMENT '10月实际(Z10SJ ）',
    `october_promise`     decimal(65, 30) DEFAULT NULL COMMENT '10月承诺(Z10YDCN ）',
    `october_budget`      decimal(65, 30) DEFAULT NULL COMMENT '10月预算(Z10YDYS）',
    `november_practical`  decimal(65, 30) DEFAULT NULL COMMENT '11月实际(Z11SJ ）',
    `november_promise`    decimal(65, 30) DEFAULT NULL COMMENT '11月承诺(Z11YDCN ）',
    `november_budget`     decimal(65, 30) DEFAULT NULL COMMENT '11月预算(Z11YDYS）',
    `december_practical`  decimal(65, 30) DEFAULT NULL COMMENT '12月实际(Z12SJ ）',
    `december_promise`    decimal(65, 30) DEFAULT NULL COMMENT '12月承诺(Z12YDCN ）',
    `december_budget`     decimal(65, 30) DEFAULT NULL COMMENT '12月预算(Z12YDYS）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='预算年月度';

-- DDL for pmsx_budget_info:
DROP TABLE IF EXISTS pmsx_budget_info;
CREATE TABLE `pmsx_budget_info`
(
    `id`                           varchar(64) NOT NULL COMMENT '主键',
    `class_name`                   varchar(64)     DEFAULT NULL COMMENT '创建人',
    `creator_id`                   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`                  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`                     varchar(64)     DEFAULT NULL COMMENT '拥有者',
    `create_time`                  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`                    varchar(64) NOT NULL COMMENT '修改人',
    `remark`                       text COMMENT '备注',
    `platform_id`                  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                       int(11)     NOT NULL COMMENT '状态',
    `logic_status`                 int(11)     NOT NULL COMMENT '逻辑删除字段',
    `sum_promise`                  decimal(65, 30) DEFAULT NULL COMMENT '总体承诺对应（ZZTCN）',
    `taxation_expense_promise`     decimal(65, 30) DEFAULT NULL COMMENT '税金承诺对应（ Z_CNSJ）',
    `sum_practical`                decimal(65, 30) DEFAULT NULL COMMENT '总体实际执行对应（ZZTSJ）',
    `duty_person_name`             varchar(25)     DEFAULT NULL COMMENT 'wbs负责人姓名',
    `budget_number`                varchar(40)     DEFAULT NULL COMMENT '预算编码对应POSID',
    `project_manage`               varchar(12)     DEFAULT NULL COMMENT '项目经理',
    `project_general_leader`       varchar(12)     DEFAULT NULL COMMENT '项目总负责人',
    `project_general_leader_name`  varchar(40)     DEFAULT NULL COMMENT '项目负责人名称',
    `is_approval`                  varchar(2)      DEFAULT NULL COMMENT '是否立项',
    `approval_time`                varchar(6)      DEFAULT NULL COMMENT '计划立项时间',
    `source_type`                  varchar(6)      DEFAULT NULL COMMENT '项目来源分类',
    `project_no_desc`              varchar(50)     DEFAULT NULL COMMENT '项目码描述',
    `duty_person_number`           varchar(8)      DEFAULT NULL COMMENT 'wbs负责人编号',
    `duty_dept`                    varchar(8)      DEFAULT NULL COMMENT '责任部门',
    `duty_dept_name`               varchar(25)     DEFAULT NULL COMMENT '责任部门名称',
    `org_number`                   varchar(8)      DEFAULT NULL COMMENT '组织单位',
    `org_name`                     varchar(25)     DEFAULT NULL COMMENT '组织单位短文本',
    `accounting_subject`           varchar(10)     DEFAULT NULL COMMENT '会计科目',
    `cost_guide_rule`              varchar(15)     DEFAULT NULL COMMENT '成本导则',
    `five_expenses`                varchar(20)     DEFAULT NULL COMMENT '五项费用',
    `status_desc`                  varchar(255)    DEFAULT NULL COMMENT '对象状态描述',
    `dept`                         varchar(8)      DEFAULT NULL COMMENT '部门 对应orgeh_1',
    `profit_center`                varchar(10)     DEFAULT NULL COMMENT '利润中心',
    `capital_source`               varchar(2)      DEFAULT NULL COMMENT '资金来源',
    `approval_relevant_department` varchar(25)     DEFAULT NULL COMMENT '立项归口部门名称(orgtx_1)',
    `common_name`                  varchar(40)     DEFAULT NULL COMMENT '一般姓名',
    `ec_no`                        varchar(20)     DEFAULT NULL COMMENT 'ec号',
    `cost_guide_rule_desc`         varchar(40)     DEFAULT NULL COMMENT '描述 zcbdzms_1',
    `project_no`                   varchar(8)      DEFAULT NULL COMMENT '项目码',
    `project_manager_name`         varchar(40)     DEFAULT NULL COMMENT '项目经理名称',
    `tax_rate`                     varchar(2)      DEFAULT NULL COMMENT '税率%',
    `company_code`                 varchar(4)      DEFAULT NULL COMMENT '公司代码',
    `budget_name`                  varchar(40)     DEFAULT NULL COMMENT 'wbs名称',
    `annual`                       varchar(4)      DEFAULT NULL,
    `practical`                    decimal(65, 30) DEFAULT NULL,
    `promise`                      decimal(65, 30) DEFAULT NULL,
    `budget`                       decimal(65, 30) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='预算信息表';

-- DDL for pmsx_cost_center:
DROP TABLE IF EXISTS pmsx_cost_center;
CREATE TABLE `pmsx_cost_center`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `name`         varchar(64) NOT NULL COMMENT '成本中心名称',
    `number`       varchar(64) NOT NULL COMMENT '编码',
    `sort`         int(11)       DEFAULT NULL COMMENT '排序',
    `remake`       varchar(1024) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='成本中心类';

-- DDL for pmsx_deliverable_status_statistics:
DROP TABLE IF EXISTS pmsx_deliverable_status_statistics;
CREATE TABLE `pmsx_deliverable_status_statistics`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `now_day`           datetime     DEFAULT NULL COMMENT '统计时间',
    `date_str`          varchar(64)  DEFAULT NULL COMMENT '描述',
    `uk`                varchar(200) DEFAULT NULL COMMENT '唯一值',
    `type_id`           varchar(64)  DEFAULT NULL COMMENT '类型',
    `project_id`        varchar(64)  DEFAULT NULL COMMENT '项目id',
    `un_finished_count` int(11)      DEFAULT NULL COMMENT '未完成数量',
    `process_count`     int(11)      DEFAULT NULL COMMENT '流程中数量',
    `finished_count`    int(11)      DEFAULT NULL COMMENT '已完成数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交付物状态趋势统计表';

-- DDL for pmsx_demand_status_statistics:
DROP TABLE IF EXISTS pmsx_demand_status_statistics;
CREATE TABLE `pmsx_demand_status_statistics`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `now_day`        datetime     DEFAULT NULL COMMENT '统计时间',
    `date_str`       varchar(64)  DEFAULT NULL COMMENT '时间描述',
    `uk`             varchar(100) DEFAULT NULL COMMENT '唯一值',
    `type_id`        varchar(64)  DEFAULT NULL COMMENT '状态id',
    `project_id`     varchar(64)  DEFAULT NULL COMMENT '项目id',
    `no_start_count` int(11)      DEFAULT NULL COMMENT '未开始数量',
    `underway_count` int(11)      DEFAULT NULL COMMENT '进行中数量',
    `complete_count` int(11)      DEFAULT NULL COMMENT '已完成数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='需求状态趋势统计表';

-- DDL for pmsx_evaluation_detail:
DROP TABLE IF EXISTS pmsx_evaluation_detail;
CREATE TABLE `pmsx_evaluation_detail`
(
    `id`                     varchar(64) NOT NULL COMMENT 'id',
    `score`                  int(20)      DEFAULT NULL COMMENT '评分',
    `evaluation_detail_type` varchar(255) DEFAULT NULL COMMENT '数据字典所对应的编码',
    `evaluation_content`     text COMMENT '评价内容',
    `evaluation_id`          varchar(64) NOT NULL COMMENT '评价id',
    `class_name`             varchar(255) DEFAULT NULL COMMENT '类名',
    `creator_id`             varchar(64)  DEFAULT NULL COMMENT '创建者',
    `modify_time`            datetime     DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)  DEFAULT NULL COMMENT '修改人id',
    `status`                 int(11)      DEFAULT NULL COMMENT '状态',
    `logic_status`           int(11)      DEFAULT NULL COMMENT '逻辑删除',
    `sort`                   int(20)      DEFAULT NULL COMMENT '排序',
    `project_id`             varchar(64)  DEFAULT NULL COMMENT '项目ID',
    `remark`                 varchar(255) DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `number`                 varchar(128) DEFAULT NULL,
    `name`                   varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目评价详情';

-- DDL for pmsx_evaluation_project:
DROP TABLE IF EXISTS pmsx_evaluation_project;
CREATE TABLE `pmsx_evaluation_project`
(
    `id`                   varchar(64)  NOT NULL COMMENT 'id',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '类名',
    `number`               varchar(128) NOT NULL COMMENT '编码',
    `name`                 varchar(128) NOT NULL COMMENT '项目评价名称',
    `evaluation_type`      varchar(255) NOT NULL COMMENT '评价类型',
    `evaluation_person_id` varchar(64)  NOT NULL COMMENT '项目评价责任人id',
    `evaluation_time`      datetime     NOT NULL COMMENT '发起项目评价时间',
    `evaluation_object`    varchar(128)  DEFAULT NULL COMMENT '评价对象',
    `description`          varchar(1024) DEFAULT NULL COMMENT '描述',
    `project_manager_id`   varchar(64)   DEFAULT NULL COMMENT '项目经理id',
    `creator_id`           varchar(64)   DEFAULT NULL,
    `modify_time`          datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)   DEFAULT NULL COMMENT '修改人',
    `status`               int(11)       DEFAULT NULL,
    `logic_status`         int(11)       DEFAULT NULL COMMENT '逻辑删除',
    `sort`                 int(20)       DEFAULT NULL COMMENT '排序',
    `project_id`           varchar(64)   DEFAULT NULL COMMENT '项目ID',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64)   DEFAULT NULL COMMENT '组织ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目评价';

-- DDL for pmsx_expense_subject:
DROP TABLE IF EXISTS pmsx_expense_subject;
CREATE TABLE `pmsx_expense_subject`
(
    `id`           varchar(64) NOT NULL DEFAULT '0' COMMENT '主键',
    `class_name`   varchar(64)          DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)          DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime             DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)          DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime             DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)          DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024)        DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)          DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)          DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `name`         varchar(64) NOT NULL COMMENT '费用科目名称',
    `parent_id`    varchar(64) NOT NULL DEFAULT '0' COMMENT '父级目录',
    `number`       varchar(64) NOT NULL COMMENT '编码',
    `sort`         bigint(20)  NOT NULL COMMENT '排序值',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='费用科目类';

-- DDL for pmsx_goods_service_plan:
DROP TABLE IF EXISTS pmsx_goods_service_plan;
CREATE TABLE `pmsx_goods_service_plan`
(
    `id`                   varchar(64)  NOT NULL COMMENT 'id',
    `class_name`           varchar(64)    DEFAULT NULL COMMENT '类名',
    `number`               varchar(128) NOT NULL COMMENT '物资服务计划编号',
    `name`                 varchar(128)   DEFAULT NULL COMMENT '名称',
    `creator_id`           varchar(64)    DEFAULT NULL,
    `modify_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)    DEFAULT NULL COMMENT '修改人',
    `status`               int(11)        DEFAULT NULL,
    `logic_status`         int(11)        DEFAULT NULL COMMENT '逻辑删除',
    `sort`                 int(20)        DEFAULT NULL COMMENT '排序',
    `project_id`           varchar(64)    DEFAULT NULL COMMENT '项目ID',
    `remark`               varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64)    DEFAULT NULL COMMENT '组织ID',
    `description`          varchar(1024)  DEFAULT NULL COMMENT '描述',
    `type_code`            varchar(64)    DEFAULT NULL COMMENT '类型对应的字典编码',
    `goods_service_number` varchar(200)   DEFAULT NULL COMMENT '物资服务编码',
    `norms_model`          varchar(255)   DEFAULT NULL COMMENT '规格型号',
    `service_term`         int(20)        DEFAULT NULL COMMENT '服务期限',
    `unit_code`            varchar(255)   DEFAULT NULL COMMENT '计量单位对应数据字典',
    `demand_amount`        decimal(16, 2) DEFAULT NULL COMMENT '需求数量',
    `total_store_amount`   decimal(16, 2) DEFAULT NULL COMMENT '总入库数量',
    `demand_time`          datetime       DEFAULT NULL COMMENT '需求时间',
    `buy_plan_id`          varchar(64)    DEFAULT NULL COMMENT '采购计划编号',
    `demand_person_id`     varchar(64)    DEFAULT NULL COMMENT '需求人ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='物资/服务计划表';

-- DDL for pmsx_goods_service_store:
DROP TABLE IF EXISTS pmsx_goods_service_store;
CREATE TABLE `pmsx_goods_service_store`
(
    `id`                   varchar(64)    NOT NULL COMMENT 'id',
    `class_name`           varchar(64)             DEFAULT NULL COMMENT '类名',
    `number`               varchar(128)            DEFAULT NULL COMMENT '编码',
    `name`                 varchar(128)            DEFAULT NULL COMMENT '项目评价名称',
    `creator_id`           varchar(64)             DEFAULT NULL,
    `modify_time`          datetime                DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)             DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime                DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)             DEFAULT NULL COMMENT '修改人/添加人',
    `status`               int(11)                 DEFAULT NULL,
    `logic_status`         int(11)                 DEFAULT NULL COMMENT '逻辑删除',
    `sort`                 int(20)                 DEFAULT NULL COMMENT '排序',
    `project_id`           varchar(64)             DEFAULT NULL COMMENT '项目ID',
    `remark`               varchar(1024)           DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)             DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64)             DEFAULT NULL COMMENT '组织ID',
    `description`          varchar(1024)           DEFAULT NULL COMMENT '描述',
    `type_code`            varchar(64)             DEFAULT NULL COMMENT '类型对应的字典编码',
    `goods_service_number` varchar(200)            DEFAULT NULL COMMENT '物资服务编码',
    `norms_model`          varchar(255)            DEFAULT NULL COMMENT '规格型号',
    `unit_code`            varchar(255)            DEFAULT NULL COMMENT '计量单位对应数据字典',
    `total_store_amount`   decimal(16, 2) NOT NULL DEFAULT '0.00' COMMENT '总入库数量',
    `demand_time`          datetime                DEFAULT NULL COMMENT '需求时间',
    `store_time`           datetime                DEFAULT NULL COMMENT '入库日期',
    `store_amount`         decimal(16, 2)          DEFAULT NULL COMMENT '本次入库数量',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='物资/服务入库表';

-- DDL for pmsx_goods_status_statistics:
DROP TABLE IF EXISTS pmsx_goods_status_statistics;
CREATE TABLE `pmsx_goods_status_statistics`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `now_day`            datetime     DEFAULT NULL COMMENT '统计时间',
    `date_str`           varchar(64)  DEFAULT NULL COMMENT '描述',
    `uk`                 varchar(200) DEFAULT NULL COMMENT '唯一值',
    `type_id`            varchar(64)  DEFAULT NULL COMMENT '类型',
    `project_id`         varchar(64)  DEFAULT NULL COMMENT '项目id',
    `no_audit_count`     int(11)      DEFAULT NULL COMMENT '未审核数量',
    `under_review_count` int(11)      DEFAULT NULL COMMENT '审核中数量',
    `reviewed_count`     int(11)      DEFAULT NULL COMMENT '已审核数量',
    `store_count`        int(11)      DEFAULT NULL COMMENT '已入库数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='物资状态趋势统计表';

-- DDL for pmsx_goods_store_record:
DROP TABLE IF EXISTS pmsx_goods_store_record;
CREATE TABLE `pmsx_goods_store_record`
(
    `id`                     varchar(64) NOT NULL COMMENT 'id',
    `class_name`             varchar(64)    DEFAULT NULL COMMENT '类名',
    `number`                 varchar(128)   DEFAULT NULL COMMENT '编码',
    `name`                   varchar(128)   DEFAULT NULL COMMENT '项目评价名称',
    `creator_id`             varchar(64)    DEFAULT NULL,
    `modify_time`            datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)    DEFAULT NULL COMMENT '修改人',
    `status`                 int(11)        DEFAULT NULL,
    `logic_status`           int(11)        DEFAULT NULL COMMENT '逻辑删除',
    `sort`                   int(20)        DEFAULT NULL COMMENT '排序',
    `project_id`             varchar(64)    DEFAULT NULL COMMENT '项目ID',
    `remark`                 varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)    DEFAULT NULL COMMENT '组织ID',
    `description`            varchar(1024)  DEFAULT NULL COMMENT '描述',
    `goods_service_store_id` varchar(64)    DEFAULT NULL COMMENT '物资服务入库ID',
    `store_time`             datetime       DEFAULT NULL COMMENT '入库时间',
    `goods_service_number`   varchar(255)   DEFAULT NULL COMMENT '物资服务编码',
    `store_amount`           decimal(16, 2) DEFAULT NULL COMMENT '本次入库数量',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='物资/服务入库记录表';

-- DDL for pmsx_idea_form:
DROP TABLE IF EXISTS pmsx_idea_form;
CREATE TABLE `pmsx_idea_form`
(
    `form_type`        varchar(64)   DEFAULT NULL COMMENT '单据类型',
    `publish_dept_id`  varchar(64)   DEFAULT NULL COMMENT '发布部门',
    `review_dept_ids`  varchar(1024) DEFAULT NULL,
    `reply_time`       datetime      DEFAULT NULL COMMENT '回复时间',
    `man_user`         varchar(128)  DEFAULT NULL COMMENT '主办人',
    `cooperation_user` varchar(64)   DEFAULT NULL COMMENT '协办',
    `third_verify`     varchar(64)   DEFAULT NULL COMMENT '第三方检查备案',
    `specialty_code`   varchar(64)   DEFAULT NULL COMMENT '专业代码',
    `desc`             varchar(1024) DEFAULT NULL COMMENT '回复意见描述',
    `id`               varchar(64) NOT NULL COMMENT '主键',
    `class_name`       varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`       varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`      datetime    NOT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`      datetime    NOT NULL COMMENT '创建时间',
    `modify_id`        varchar(64) NOT NULL COMMENT '修改人',
    `remark`           varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`      varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`           varchar(64) NOT NULL COMMENT '业务组织Id',
    `interface_id`     varchar(64) NOT NULL COMMENT '传递单/接口ID',
    `status`           int(11)     NOT NULL COMMENT '状态',
    `logic_status`     int(11)     NOT NULL COMMENT '逻辑删除字段',
    `reply_suggest`    varchar(64) NOT NULL COMMENT '回复意见',
    `number`           varchar(64) NOT NULL COMMENT '编号',
    `project_id`       varchar(64)   DEFAULT NULL COMMENT '项目id',
    `data_id`          varchar(64)   DEFAULT NULL COMMENT '数据ID',
    `data_class_name`  varchar(64)   DEFAULT NULL COMMENT '数据classname',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='意见单';

-- DDL for pmsx_idea_form_to_idea_form:
DROP TABLE IF EXISTS pmsx_idea_form_to_idea_form;
CREATE TABLE `pmsx_idea_form_to_idea_form`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `source_id`    varchar(64)   DEFAULT NULL COMMENT '来源id',
    `target_id`    varchar(64)   DEFAULT NULL COMMENT '目标ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='意见单和意见单的关系';

-- DDL for pmsx_if_to_parameter_to_ins:
DROP TABLE IF EXISTS pmsx_if_to_parameter_to_ins;
CREATE TABLE `pmsx_if_to_parameter_to_ins`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `if_id`        varchar(64) NOT NULL COMMENT '意见单id',
    `parameter_id` varchar(64) NOT NULL COMMENT '参数ID',
    `ins_id`       varchar(64)   DEFAULT NULL COMMENT '参数实列ID',
    `copy_type`    varchar(64)   DEFAULT NULL COMMENT '拷贝类型',
    `model_id`     varchar(64)   DEFAULT NULL COMMENT '模板ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='意见单和参数和参数实列的关系';

-- DDL for pmsx_im_to_parameter:
DROP TABLE IF EXISTS pmsx_im_to_parameter;
CREATE TABLE `pmsx_im_to_parameter`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `im_id`        varchar(64) NOT NULL COMMENT '接口ID',
    `param_id`     varchar(64) NOT NULL COMMENT '参数ID',
    `model_id`     varchar(64)   DEFAULT NULL COMMENT '模板ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口和参数的关系';

-- DDL for pmsx_interface_management:
DROP TABLE IF EXISTS pmsx_interface_management;
CREATE TABLE `pmsx_interface_management`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `remark`            varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`            int(11)     NOT NULL COMMENT '状态',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除字段',
    `publish_org_name`  varchar(64)   DEFAULT NULL COMMENT '发布单位',
    `receive_org_name`  varchar(64)   DEFAULT NULL COMMENT '接受单位',
    `type`              varchar(64)   DEFAULT NULL COMMENT '接口类型',
    `form_type`         varchar(64)   DEFAULT NULL COMMENT '单据类型',
    `number`            varchar(64)   DEFAULT NULL COMMENT '流水号',
    `publish_dept_id`   varchar(64)   DEFAULT NULL COMMENT '发布部门',
    `review_dept_ids`   varchar(1080) DEFAULT NULL COMMENT '接受部门',
    `reply_time`        datetime      DEFAULT NULL COMMENT '回复时间',
    `man_user`          varchar(128)  DEFAULT NULL COMMENT '主办人',
    `cooperation_users` varchar(1080) DEFAULT NULL COMMENT '协办',
    `third_verify`      varchar(64)   DEFAULT NULL COMMENT '第三方检查备案',
    `specialty_code`    varchar(64)   DEFAULT NULL COMMENT '专业代码',
    `desc`              varchar(1024) DEFAULT NULL COMMENT '接口描述',
    `project_id`        varchar(64)   DEFAULT NULL COMMENT '所属项目ID',
    `interface_state`   varchar(10)   DEFAULT NULL COMMENT '接口状态',
    `data_id`           varchar(64)   DEFAULT NULL COMMENT '数据ID',
    `data_class_name`   varchar(64)   DEFAULT NULL COMMENT '数据classname',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口管理';

-- DDL for pmsx_new_project_budget:
DROP TABLE IF EXISTS pmsx_new_project_budget;
CREATE TABLE `pmsx_new_project_budget`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64)     DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime    NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)     DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) NOT NULL COMMENT '修改人',
    `remark`          text COMMENT '备注',
    `platform_id`     varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`          int(11)     NOT NULL COMMENT '状态',
    `logic_status`    int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_number`  varchar(64) NOT NULL COMMENT '项目编号',
    `budget_number`   varchar(64)     DEFAULT NULL COMMENT '预算编号',
    `apply_for_money` decimal(65, 30) DEFAULT NULL COMMENT '申请金额',
    `project_id`      varchar(64)     DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目预算表';

-- DDL for pmsx_new_project_to_base_plan:
DROP TABLE IF EXISTS pmsx_new_project_to_base_plan;
CREATE TABLE `pmsx_new_project_to_base_plan`
(
    `modify_id`        varchar(64) NOT NULL COMMENT '修改人ID',
    `create_time`      datetime    NOT NULL COMMENT '创建时间',
    `org_id`           varchar(64) DEFAULT NULL COMMENT '组织Id',
    `creator_id`       varchar(64) NOT NULL COMMENT '创建人Id',
    `modify_time`      datetime    NOT NULL COMMENT '修改时间',
    `platform_id`      varchar(64) DEFAULT NULL COMMENT '平台Id',
    `logic_status`     int(11)     DEFAULT NULL COMMENT '是否删除',
    `project_id`       varchar(64) DEFAULT NULL COMMENT '项目ID',
    `base_plan_id`     varchar(64) DEFAULT NULL COMMENT '综合计划ID',
    `base_plan_number` varchar(64) DEFAULT NULL COMMENT '综合计划编号',
    `project_number`   varchar(64) DEFAULT NULL COMMENT '项目编号',
    `class_name`       varchar(64) DEFAULT NULL,
    `id`               varchar(64) NOT NULL,
    `status`           int(11)     DEFAULT NULL,
    `remark`           text,
    `owner_id`         varchar(64) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目和综合计划的关系表（1;N）';

-- DDL for pmsx_plan_status_statistics:
DROP TABLE IF EXISTS pmsx_plan_status_statistics;
CREATE TABLE `pmsx_plan_status_statistics`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `now_day`            datetime     DEFAULT NULL COMMENT '统计时间',
    `date_str`           varchar(64)  DEFAULT NULL COMMENT '时间展示',
    `uk`                 varchar(200) DEFAULT NULL COMMENT '唯一值',
    `type_id`            varchar(64)  DEFAULT NULL COMMENT '类型',
    `project_id`         varchar(64)  DEFAULT NULL COMMENT '项目id',
    `wait_release_count` int(11)      DEFAULT NULL COMMENT '待发布数量',
    `release_count`      int(11)      DEFAULT NULL COMMENT '已发布数量',
    `complete_count`     int(11)      DEFAULT NULL COMMENT '已完成数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目内计划状态趋势统计表';

-- DDL for pmsx_plan_to_im:
DROP TABLE IF EXISTS pmsx_plan_to_im;
CREATE TABLE `pmsx_plan_to_im`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `to_id`        varchar(64)   DEFAULT NULL COMMENT '副Id',
    `from_id`      varchar(64)   DEFAULT NULL COMMENT '主id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='计划和接口的关联关系';

-- DDL for pmsx_plan_to_param:
DROP TABLE IF EXISTS pmsx_plan_to_param;
CREATE TABLE `pmsx_plan_to_param`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `from_id`      varchar(64)   DEFAULT NULL COMMENT '主id',
    `to_id`        varchar(64)   DEFAULT NULL COMMENT '副Id',
    `model_id`     varchar(64)   DEFAULT NULL COMMENT '模板id',
    `ins_id`       varchar(64)   DEFAULT NULL COMMENT '实列ID',
    `copy_type`    varchar(64)   DEFAULT NULL COMMENT '拷贝类型',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='计划和参数的关联关系';

-- DDL for pmsx_project_achievement:
DROP TABLE IF EXISTS pmsx_project_achievement;
CREATE TABLE `pmsx_project_achievement`
(
    `id`               varchar(64) NOT NULL COMMENT '主键',
    `class_name`       varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`       varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`         varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`        varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`           varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`      varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`           varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`           int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status`     int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `sort`             int(11)       DEFAULT NULL COMMENT '排序',
    `milestone_id`     varchar(64)   DEFAULT NULL COMMENT '里程碑Id',
    `name`             varchar(64)   DEFAULT NULL COMMENT '名称',
    `secrecy_level`    varchar(100)  DEFAULT NULL COMMENT '密级',
    `hierarchy_level`  varchar(100)  DEFAULT NULL COMMENT '层级',
    `res_person`       varchar(64)   DEFAULT NULL COMMENT '责任人',
    `res_dept`         varchar(64)   DEFAULT NULL COMMENT '责任部门',
    `res_office`       varchar(64)   DEFAULT NULL COMMENT '责任科室',
    `plan_commit_time` datetime      DEFAULT NULL COMMENT '计划提交时间',
    `project_id`       varchar(255)  DEFAULT NULL COMMENT '项目Id',
    `floder_id`        varchar(255)  DEFAULT NULL COMMENT '成果类别Id',
    `number`           varchar(255)  DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目成果表';

-- DDL for pmsx_project_achievement_floder:
DROP TABLE IF EXISTS pmsx_project_achievement_floder;
CREATE TABLE `pmsx_project_achievement_floder`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status` int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `sort`         int(11)       DEFAULT NULL COMMENT '排序',
    `name`         varchar(64)   DEFAULT NULL COMMENT '文件夹名称',
    `project_id`   varchar(64)   DEFAULT NULL COMMENT '项目id',
    `approval_id`  varchar(64)   DEFAULT NULL COMMENT '立项id',
    `number`       varchar(255)  DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目成果文件夹表';

-- DDL for pmsx_project_approval:
DROP TABLE IF EXISTS pmsx_project_approval;
CREATE TABLE `pmsx_project_approval`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`     datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`       varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`          varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`          varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`          int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status`    int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `sort`            int(11)       DEFAULT NULL COMMENT '排序',
    `project_id`      varchar(64)   DEFAULT NULL COMMENT '项目id',
    `approval_reason` text COMMENT '立项理由',
    `project_number`  varchar(64)   DEFAULT NULL COMMENT '项目编号',
    `number`          varchar(64)   DEFAULT NULL COMMENT '立项编码',
    `name`            varchar(64)   DEFAULT NULL COMMENT '名称',
    `start_time`      datetime      DEFAULT NULL COMMENT '发起日期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目立项表';

-- DDL for pmsx_project_approval_milestone:
DROP TABLE IF EXISTS pmsx_project_approval_milestone;
CREATE TABLE `pmsx_project_approval_milestone`
(
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`    varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`   datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`   datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`     varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`        varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`        int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status`  int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `sort`          int(11)       DEFAULT NULL COMMENT '排序',
    `task_level`    varchar(64)   DEFAULT NULL COMMENT '任务层级',
    `secrecy_level` varchar(64)   DEFAULT NULL COMMENT '密级',
    `begin_time`    datetime      DEFAULT NULL COMMENT '计划开始时间',
    `end_time`      datetime      DEFAULT NULL COMMENT '计划结束时间',
    `circumstance`  int(11)       DEFAULT NULL COMMENT '计划状况',
    `scheme_status` int(11)       DEFAULT NULL COMMENT '计划状态',
    `res_person`    varchar(64)   DEFAULT NULL COMMENT '负责人',
    `res_office`    varchar(64)   DEFAULT NULL COMMENT '责任科室',
    `res_dept`      varchar(64)   DEFAULT NULL COMMENT '责任部门',
    `project_id`    varchar(64)   DEFAULT NULL COMMENT '项目id',
    `approval_id`   varchar(64)   DEFAULT NULL COMMENT '项目立项id',
    `milestone_id`  varchar(64)   DEFAULT NULL COMMENT '项目里程碑id',
    `name`          varchar(255)  DEFAULT NULL COMMENT '里程碑名称',
    `number`        varchar(255)  DEFAULT NULL COMMENT '编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目立项里程碑';

-- DDL for pmsx_project_data_refresh_record:
DROP TABLE IF EXISTS pmsx_project_data_refresh_record;
CREATE TABLE `pmsx_project_data_refresh_record`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime    NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64) NOT NULL COMMENT '修改人',
    `remark`         text COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int(11)     NOT NULL COMMENT '状态',
    `logic_status`   int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_id`     varchar(64) NOT NULL COMMENT '项目ID',
    `data_type`      varchar(20) NOT NULL COMMENT '数据类型',
    `project_number` varchar(64) NOT NULL COMMENT '项目编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目数据刷新纪录';

-- DDL for pmsx_project_funds_received:
DROP TABLE IF EXISTS pmsx_project_funds_received;
CREATE TABLE `pmsx_project_funds_received`
(
    `id`                  varchar(64) NOT NULL COMMENT '主键',
    `class_name`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`          varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`              varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`              varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`              int(11)        DEFAULT NULL COMMENT '状态',
    `logic_status`        int(11)        DEFAULT NULL COMMENT '逻辑删除字段',
    `project_id`          varchar(64)    DEFAULT NULL COMMENT '项目id',
    `contract_number`     varchar(64)    DEFAULT NULL COMMENT '合同编码',
    `collection_point`    varchar(64)    DEFAULT NULL COMMENT '合同收款节点',
    `funds_received_date` datetime       DEFAULT NULL COMMENT '实收日期',
    `funds_received`      decimal(10, 0) DEFAULT NULL COMMENT '实收金额',
    `sale_date`           datetime       DEFAULT NULL COMMENT '销售日期',
    `invoice_number`      varchar(64)    DEFAULT NULL COMMENT '发票号码',
    `invoice_money`       decimal(10, 0) DEFAULT NULL COMMENT '发票金额',
    `receivable_id`       varchar(64)    DEFAULT NULL COMMENT '应收Id',
    `number`              varchar(64)    DEFAULT NULL COMMENT '实收编码',
    `name`                varchar(64)    DEFAULT NULL COMMENT '客户名称',
    `sort`                int(11)        DEFAULT NULL COMMENT '排序',
    `payment_term`        varchar(64)    DEFAULT NULL COMMENT '收款方式',
    `stakeholder_id`      varchar(64)    DEFAULT NULL COMMENT '客户名称id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目实收表';

-- DDL for pmsx_project_internal_association:
DROP TABLE IF EXISTS pmsx_project_internal_association;
CREATE TABLE `pmsx_project_internal_association`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status` int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `name`         varchar(100)  DEFAULT NULL COMMENT '名称',
    `number`       varchar(100)  DEFAULT NULL COMMENT '编号',
    `sort`         int(11)       DEFAULT NULL COMMENT '排序',
    `project_id`   varchar(64)   DEFAULT NULL COMMENT '项目id',
    `project_name` varchar(100)  DEFAULT NULL COMMENT '项目名称',
    `type`         varchar(50)   DEFAULT NULL COMMENT '内部类型',
    `inner_id`     varchar(64)   DEFAULT NULL COMMENT '内部数据id',
    `inner_name`   varchar(100)  DEFAULT NULL COMMENT '内部名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目内部关联表';

-- DDL for pmsx_project_receivable:
DROP TABLE IF EXISTS pmsx_project_receivable;
CREATE TABLE `pmsx_project_receivable`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64)    DEFAULT NULL COMMENT '创建人',
    `modify_time`        datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64)    DEFAULT NULL COMMENT '平台ID',
    `org_id`             varchar(64)    DEFAULT NULL COMMENT '业务组织Id',
    `status`             int(11)        DEFAULT NULL COMMENT '状态',
    `logic_status`       int(11)        DEFAULT NULL COMMENT '逻辑删除字段',
    `name`               varchar(64)    DEFAULT NULL COMMENT '客户名称',
    `number`             varchar(64)    DEFAULT NULL COMMENT '应收编码',
    `sort`               int(11)        DEFAULT NULL COMMENT '排序',
    `contract_number`    varchar(64)    DEFAULT NULL COMMENT '合同编码',
    `collection_point`   varchar(64)    DEFAULT NULL COMMENT '合同收款节点',
    `receivable_date`    datetime       DEFAULT NULL COMMENT '应收日期',
    `amount_receivable`  decimal(10, 0) DEFAULT NULL COMMENT '应收金额',
    `funds_received`     decimal(10, 0) DEFAULT NULL COMMENT '实收金额',
    `sale_sate`          datetime       DEFAULT NULL COMMENT '销售日期',
    `no_amount_received` decimal(10, 0) DEFAULT NULL COMMENT '未收金额',
    `project_id`         varchar(64)    DEFAULT NULL COMMENT '项目id',
    `stakeholder_id`     varchar(64)    DEFAULT NULL COMMENT '客户名称id',
    `contract_id`        varchar(64)    DEFAULT NULL COMMENT '合同id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目应收表';

-- DDL for pmsx_project_scheme_milestone_node_pre_post:
DROP TABLE IF EXISTS pmsx_project_scheme_milestone_node_pre_post;
CREATE TABLE `pmsx_project_scheme_milestone_node_pre_post`
(
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除字段',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `type`              int(11)     NOT NULL COMMENT '前后置类型',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `status`            int(11)     NOT NULL COMMENT '状态',
    `class_name`        varchar(64)                     DEFAULT NULL COMMENT '创建人',
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `project_scheme_id` varchar(64) NOT NULL COMMENT '项目计划id',
    `org_id`            varchar(64) NOT NULL COMMENT '业务组织Id',
    `template_id`       varchar(64) NOT NULL COMMENT '模板id',
    `owner_id`          varchar(64)                     DEFAULT NULL COMMENT '拥有者',
    `remark`            varchar(1024)                   DEFAULT NULL COMMENT '备注',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `sort`              int(11)                         DEFAULT NULL COMMENT '排序',
    `number`            varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '编码',
    `name`              varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '名称',
    `pre_scheme_id`     varchar(64) CHARACTER SET utf8  DEFAULT NULL COMMENT '前置计划Id',
    `post_scheme_id`    varchar(64) CHARACTER SET utf8  DEFAULT NULL COMMENT '后置计划Id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目计划模板前后置关系';

-- DDL for pmsx_project_set_up:
DROP TABLE IF EXISTS pmsx_project_set_up;
CREATE TABLE `pmsx_project_set_up`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `name`         varchar(255)   DEFAULT NULL COMMENT '名称',
    `project_id`   varchar(64)    DEFAULT NULL COMMENT '项目Id',
    `key`          varchar(255)   DEFAULT NULL COMMENT '配置key',
    `value`        varchar(255)   DEFAULT NULL COMMENT '配置value',
    `number`       varbinary(255) DEFAULT NULL COMMENT '编码',
    `sort`         int(11)        DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='项目设置';

-- DDL for pmsx_project_supplies_management:
DROP TABLE IF EXISTS pmsx_project_supplies_management;
CREATE TABLE `pmsx_project_supplies_management`
(
    `id`                      varchar(64) NOT NULL COMMENT '主键',
    `class_name`              varchar(64)     DEFAULT NULL COMMENT '创建人',
    `creator_id`              varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`             datetime    NOT NULL COMMENT '修改时间',
    `owner_id`                varchar(64)     DEFAULT NULL COMMENT '拥有者',
    `create_time`             datetime    NOT NULL COMMENT '创建时间',
    `modify_id`               varchar(64) NOT NULL COMMENT '修改人',
    `remark`                  text COMMENT '备注',
    `platform_id`             varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                  varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                  int(11)     NOT NULL COMMENT '状态',
    `logic_status`            int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_number`          varchar(64)     DEFAULT NULL COMMENT '项目编号',
    `project_id`              varchar(64)     DEFAULT NULL COMMENT '项目ID',
    `number`                  varchar(64)     DEFAULT NULL COMMENT '物料编号',
    `line_number`             varchar(64)     DEFAULT NULL COMMENT '计划行号',
    `phase`                   varchar(64)     DEFAULT NULL COMMENT '阶段',
    `phase_status`            varchar(64)     DEFAULT NULL COMMENT '阶段状态',
    `supplies_number`         varchar(64)     DEFAULT NULL COMMENT '物料/服务编号',
    `supplies_desc`           varchar(128)    DEFAULT NULL COMMENT '物料/服务描述',
    `supplies_name`           varchar(64)     DEFAULT NULL COMMENT '物料服务名称',
    `specifications_models`   varchar(64)     DEFAULT NULL COMMENT '规格型号',
    `demand_num`              decimal(25, 3)  DEFAULT NULL COMMENT '需求数量',
    `unit_of_measure_demand`  varchar(64)     DEFAULT NULL COMMENT '（需求数量）计量单位',
    `demand_person`           varchar(64)     DEFAULT NULL COMMENT '需求人员',
    `demand_type`             varchar(64)     DEFAULT NULL COMMENT '需求类型',
    `qa_level`                varchar(64)     DEFAULT NULL COMMENT 'QA等级',
    `nucleus_safety_level`    varchar(64)     DEFAULT NULL COMMENT '核酸安全级别',
    `backup_level`            varchar(64)     DEFAULT NULL COMMENT '备份级别',
    `contract_aog_date`       datetime        DEFAULT NULL COMMENT '合同到货日期',
    `purchase_engineer`       varchar(64)     DEFAULT NULL COMMENT '采购工程师',
    `storage`                 varchar(5)      DEFAULT NULL COMMENT '是否入库',
    `storage_num`             decimal(65, 30) DEFAULT NULL COMMENT '入库数量',
    `demand_date`             datetime        DEFAULT NULL COMMENT '需求日期',
    `unit_of_measure_storage` varchar(255)    DEFAULT NULL COMMENT '（入库数量）计量单位',
    `plan_type`               varchar(255)    DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='项目物料表';

-- DDL for pmsx_purchase_status_statistics:
DROP TABLE IF EXISTS pmsx_purchase_status_statistics;
CREATE TABLE `pmsx_purchase_status_statistics`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `now_day`            datetime     DEFAULT NULL COMMENT '统计时间',
    `date_str`           varchar(64)  DEFAULT NULL COMMENT '描述',
    `uk`                 varchar(200) DEFAULT NULL COMMENT '唯一值',
    `type_id`            varchar(64)  DEFAULT NULL COMMENT '类型id',
    `project_id`         varchar(64)  DEFAULT NULL COMMENT '项目id',
    `no_audit_count`     int(11)      DEFAULT NULL COMMENT '未审核数量',
    `under_review_count` int(11)      DEFAULT NULL COMMENT '审核中数量',
    `reviewed_count`     int(11)      DEFAULT NULL COMMENT '已审核数量',
    `close_count`        int(11)      DEFAULT NULL COMMENT '已关闭数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购状态趋势统计表';

-- DDL for pmsx_question_status_statistics:
DROP TABLE IF EXISTS pmsx_question_status_statistics;
CREATE TABLE `pmsx_question_status_statistics`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `now_day`           datetime     DEFAULT NULL COMMENT '统计时间',
    `date_str`          varchar(64)  DEFAULT NULL COMMENT '描述',
    `uk`                varchar(200) DEFAULT NULL COMMENT '唯一值',
    `type_id`           varchar(64)  DEFAULT NULL COMMENT '类型',
    `project_id`        varchar(64)  DEFAULT NULL COMMENT '项目id',
    `un_finished_count` int(11)      DEFAULT NULL COMMENT '未完成数量',
    `finished_count`    int(11)      DEFAULT NULL COMMENT '已完成数量',
    `close_count`       int(11)      DEFAULT NULL COMMENT '已关闭数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='问题状态趋势统计表';

-- DDL for pmsx_risk_status_statistics:
DROP TABLE IF EXISTS pmsx_risk_status_statistics;
CREATE TABLE `pmsx_risk_status_statistics`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `now_day`           datetime     DEFAULT NULL COMMENT '统计时间',
    `date_str`          varchar(64)  DEFAULT NULL COMMENT '描述',
    `uk`                varchar(200) DEFAULT NULL COMMENT '唯一值',
    `type_id`           varchar(64)  DEFAULT NULL COMMENT '类型id',
    `project_id`        varchar(64)  DEFAULT NULL COMMENT '项目id',
    `un_finished_count` int(11)      DEFAULT NULL COMMENT '未完成数量',
    `process_count`     int(11)      DEFAULT NULL COMMENT '流程中数量',
    `finished_count`    int(11)      DEFAULT NULL COMMENT '已完成数量',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='风险状态趋势统计表';

-- DDL for pmsx_scientific_research_demand_declare:
DROP TABLE IF EXISTS pmsx_scientific_research_demand_declare;
CREATE TABLE `pmsx_scientific_research_demand_declare`
(
    `id`                 varchar(64) NOT NULL COMMENT '主键',
    `class_name`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`         varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`        datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`           varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`        datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`          varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`             varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`        varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`             varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`             int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status`       int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `name`               varchar(100)  DEFAULT NULL COMMENT '名称',
    `number`             varchar(100)  DEFAULT NULL COMMENT '编号',
    `clue_id`            varchar(64)   DEFAULT NULL COMMENT '线索id',
    `priority`           varchar(10)   DEFAULT NULL COMMENT '优先级',
    `res_dept`           varchar(64)   DEFAULT NULL COMMENT '责任部门',
    `res_person`         varchar(64)   DEFAULT NULL COMMENT '责任人',
    `declare_reason`     varchar(500)  DEFAULT NULL COMMENT '申报理由',
    `begin_time`         datetime      DEFAULT NULL COMMENT '开始时间',
    `end_time`           datetime      DEFAULT NULL COMMENT '期望结束时间',
    `declare_background` varchar(500)  DEFAULT NULL COMMENT '申报背景摘要',
    `declare_target`     varchar(500)  DEFAULT NULL COMMENT '申报目标',
    `declare_technology` varchar(500)  DEFAULT NULL COMMENT '申报技术摘要',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='科研需求申报';

-- DDL for pmsx_scientific_research_demand_declare_file_info:
DROP TABLE IF EXISTS pmsx_scientific_research_demand_declare_file_info;
CREATE TABLE `pmsx_scientific_research_demand_declare_file_info`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`       varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`       int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status` int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `file_data_id` varchar(64)   DEFAULT NULL COMMENT '文件id',
    `declare_id`   varchar(64)   DEFAULT NULL COMMENT '科研需求申报id',
    `type`         varchar(64)   DEFAULT NULL COMMENT '类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='科研需求申报文件信息';

-- DDL for pmsx_supplies_data_refresh_record:
DROP TABLE IF EXISTS pmsx_supplies_data_refresh_record;
CREATE TABLE `pmsx_supplies_data_refresh_record`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime    NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64) NOT NULL COMMENT '修改人',
    `remark`         text COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int(11)     NOT NULL COMMENT '状态',
    `logic_status`   int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_number` varchar(64) DEFAULT NULL COMMENT '项目编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='物料数据刷新纪录表';

CREATE TABLE `pmsx_project_collection`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`     varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`    datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`       varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`    datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`      varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`         varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`    varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`         varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`         int(11)       DEFAULT NULL COMMENT '状态',
    `logic_status`   int(11)       DEFAULT NULL COMMENT '逻辑删除字段',
    `name`           varchar(200)  DEFAULT NULL COMMENT '项目集名称',
    `related_person` varchar(1024) DEFAULT NULL COMMENT '项目可见人员',
    `res_person`     varchar(64)   DEFAULT NULL COMMENT '项目责任人',
    `res_dept`       varchar(64)   DEFAULT NULL COMMENT '责任部门',
    `number`         varchar(100)  DEFAULT NULL COMMENT '编码',
    `sort`           int(11)       DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目集';


CREATE TABLE `pmsx_project_collection_to_project`
(
    `id`          varchar(64) NOT NULL COMMENT '主键',
    `class_name`  varchar(64)  DEFAULT NULL COMMENT '创建人',
    `creator_id`  varchar(64)  DEFAULT NULL COMMENT '创建人',
    `modify_time` datetime     DEFAULT NULL COMMENT '修改时间',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id`   varchar(64)  DEFAULT NULL COMMENT '修改人',
    `status`      int(11)      DEFAULT NULL COMMENT '状态',
    `from_id`     varchar(64)  DEFAULT NULL COMMENT '来源id',
    `from_class`  varchar(100) DEFAULT NULL COMMENT '来源类名',
    `to_id`       varchar(64)  DEFAULT NULL COMMENT '目标id',
    `to_class`    varchar(100) DEFAULT NULL COMMENT '目标类名',
    `sort`        int(11)      DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目集关联项目';



create table pmsx_project_privilege_subject
(
    id           varchar(64)  not null comment 'id'
        primary key,
    subject_type varchar(32)  null comment '主题类型',
    subject_id   varchar(64)  null comment '主题类型',
    rule_id      varchar(64)  null comment '规则id',
    page_id      varchar(64)  null,
    class_name   varchar(128) not null comment '类名称',
    status       int          not null comment '状态',
    owner_id     varchar(64)  null comment '拥有者',
    creator_id   varchar(64)  null comment '拥有者',
    create_time  datetime     null comment '创建时间',
    modify_id    varchar(64)  null comment '拥有者',
    modify_time  datetime     null comment '修改时间',
    remark       varchar(512) null comment '备注',
    platform_id  varchar(64)  null comment '平台ID',
    org_id       varchar(64)  null comment '组织ID',
    unique_key   varchar(64)  null comment '唯一key',
    logic_status int          null comment '逻辑删除状态',
    project_id   varchar(64)  null comment '项目ID'
) comment '项目的权限主题';


create table if not exists pmsx_project_privilege_resource
(
    id
                         varchar(64)  not null comment 'id'
        primary key,
    resource_type        varchar(64)  not null comment '资源类型',
    resource_id          varchar(64)  not null comment '资源ID',
    privilege_subject_id varchar(64)  not null comment '权限主题id',
    class_name           varchar(128) not null comment '类名称',
    status               int          not null comment '状态',
    owner_id             varchar(64)  null comment '拥有者',
    creator_id           varchar(64)  null comment '拥有者',
    create_time          datetime     null comment '创建时间',
    modify_id            varchar(64)  null comment '拥有者',
    modify_time          datetime     null comment '修改时间',
    remark               varchar(512) null comment '备注',
    platform_id          varchar(64)  null comment '平台ID',
    org_id               varchar(64)  null comment '组织ID',
    unique_key           varchar(64)  null comment '唯一key',
    logic_status         int          null comment '逻辑删除状态'
)
    comment '项目的权限资源';


create table if not exists pmsx_performance_indicator
(
    id
                   varchar(64)   not null comment '主键'
        primary key,
    class_name     varchar(64)   null comment '创建人',
    creator_id     varchar(64)   null comment '创建人',
    modify_time    datetime      not null comment '修改时间',
    owner_id       varchar(64)   null comment '拥有者',
    create_time    datetime      not null comment '创建时间',
    modify_id      varchar(64)   null comment '修改人',
    remark         varchar(1024) null comment '备注',
    platform_id    varchar(64)   not null comment '平台ID',
    org_id         varchar(64)   not null comment '业务组织Id',
    status         int           not null comment '状态',
    logic_status   int           not null comment '逻辑删除字段',
    performance_id varchar(64)   null comment '绩效ID',
    indicator_id   varchar(64)   not null comment '指标ID，如果是自定义的就没有',
    weight         decimal(10,
                       2)        null comment '该指标权重占比',
    indicator_name varchar(255)  not null comment '指标名称',
    score_standard varchar(512)  null comment '评分标准',
    score          decimal(10,
                       2)        null comment '分数'
)
    comment '项目绩效和指标关联';


create table if not exists pmsx_project_performance
(
    id
                 varchar(64)   not null comment '主键'
        primary key,
    class_name   varchar(64)   null comment '创建人',
    creator_id   varchar(64)   not null comment '创建人',
    modify_time  datetime      not null comment '修改时间',
    owner_id     varchar(64)   null comment '拥有者',
    create_time  datetime      not null comment '创建时间',
    modify_id    varchar(64)   not null comment '修改人',
    remark       varchar(1024) null comment '备注',
    platform_id  varchar(64)   not null comment '平台ID',
    org_id       varchar(64)   not null comment '业务组织Id',
    status       int           not null comment '状态',
    logic_status int           not null comment '逻辑删除字段',
    name         varchar(255)  null comment '名称',
    project_id   varchar(64)   null comment '项目ID',
    type_id      varchar(255)  not null comment '考核类型ID（字典配置）',
    year         varchar(4)    null comment '考核年份',
    `period`     varchar(64)   null comment '考核时间，可以是月份、季度、半年度标识，对于月度考核，可以直接存储月份（如表示1月）；对于季度考核，可以使用Q表示第一至第四季度；对于半年度考核，可以使用',
    user_id      varchar(64)   null comment '用户ID',
    total_score  decimal(10,
                     2)        null comment '总分',
    score_date   datetime      null comment '评分时间'
)
    comment '项目绩效与指标关联';



create table if not exists pmsx_indicator_library
(
    id
                   varchar(64)   not null comment '主键'
        primary key,
    class_name     varchar(64)   null comment '创建人',
    creator_id     varchar(64)   null comment '创建人',
    modify_time    datetime      not null comment '修改时间',
    owner_id       varchar(64)   null comment '拥有者',
    create_time    datetime      not null comment '创建时间',
    modify_id      varchar(64)   null comment '修改人',
    remark         varchar(1024) null comment '备注',
    platform_id    varchar(64)   not null comment '平台ID',
    org_id         varchar(64)   not null comment '业务组织Id',
    status         int           not null comment '状态',
    logic_status   int           not null comment '逻辑删除字段',
    score_standard varchar(512)  not null comment '绩效评分标准',
    weight         decimal(10,
                       2)        not null comment '该指标默认权重',
    name           varchar(255)  not null comment '绩效指标名称'
)
    comment '绩效管理指标库';

create table if not exists pmsx_performance_template_to_indicator
(
    id
                   varchar(64)   not null comment '主键'
        primary key,
    class_name     varchar(64)   null comment '创建人',
    creator_id     varchar(64)   null comment '创建人',
    modify_time    datetime      not null comment '修改时间',
    owner_id       varchar(64)   null comment '拥有者',
    create_time    datetime      not null comment '创建时间',
    modify_id      varchar(64)   not null comment '修改人',
    remark         varchar(1024) null comment '备注',
    platform_id    varchar(64)   null comment '平台ID',
    org_id         varchar(64)   not null comment '业务组织Id',
    status         int           not null comment '状态',
    logic_status   int           not null comment '逻辑删除字段',
    indicator_id   varchar(64)   not null comment '指标ID',
    template_id    varchar(64)   not null comment '绩效模版ID',
    indicator_name varchar(128)  not null comment '指标名称',
    weight         decimal(10,
                       2)        null comment '指标权重',
    score_standard varchar(512)  not null comment '指标评分标准',
    sort           int           null
)
    comment '绩效模版和指标关联';


create table if not exists pmsx_performance_template
(
    id
                 varchar(64)   not null comment '主键'
        primary key,
    class_name   varchar(64)   null comment '创建人',
    creator_id   varchar(64)   null comment '创建人',
    modify_time  datetime      not null comment '修改时间',
    owner_id     varchar(64)   null comment '拥有者',
    create_time  datetime      not null comment '创建时间',
    modify_id    varchar(64)   null comment '修改人',
    remark       varchar(1024) null comment '备注',
    platform_id  varchar(64)   null comment '平台ID',
    org_id       varchar(64)   not null comment '业务组织Id',
    status       int           not null comment '状态',
    logic_status int           not null comment '逻辑删除字段',
    name         varchar(255)  null comment '名称',
    sort         int           null,
    code         varchar(128)  null comment '编码'
)
    comment '项目绩效模版';



create table if not exists pmsx_project_plan_type_to_project_plan_attribute
(
    id           varchar(64)   not null comment '主键' primary key,
    class_name   varchar(64)   null comment '创建人',
    creator_id   varchar(64)   not null comment '创建人',
    modify_time  datetime      not null comment '修改时间',
    owner_id     varchar(64)   null comment '拥有者',
    create_time  datetime      not null comment '创建时间',
    modify_id    varchar(64)   not null comment '修改人',
    platform_id  varchar(64)   not null comment '平台ID',
    org_id       varchar(64)   not null comment '业务组织Id',
    logic_status int           not null comment '逻辑删除字段',
    from_id      varchar(64)   null comment '来源ID',
    to_id        varchar(64)   null comment 'to ID',
    to_class     varchar(255)  not null comment 'toClass',
    from_class   varchar(255)  not null comment 'fromClass',
    sort         int           null comment 'sort',
    remark       varchar(1024) null,
    status       int           null
)
    comment '项目计划类型与项目计划类型属性的关系';


create table if not exists pmsx_project_plan_type_attribute_value
(
    id
                 varchar(64)   not null comment '主键'
        primary key,
    class_name   varchar(64)   null comment '创建人',
    creator_id   varchar(64)   not null comment '创建人',
    modify_time  datetime      not null comment '修改时间',
    owner_id     varchar(64)   null comment '拥有者',
    create_time  datetime      not null comment '创建时间',
    modify_id    varchar(64)   not null comment '修改人',
    remark       varchar(1024) null comment '备注',
    platform_id  varchar(64)   not null comment '平台ID',
    org_id       varchar(64)   not null comment '业务组织Id',
    status       int           not null comment '状态',
    logic_status int           not null comment '逻辑删除字段',
    image_id     varchar(128)  not null comment 'imageId',
    number       varchar(128)  not null comment '编码',
    type_id      varchar(64)   not null comment 'typeId',
    name         varchar(128)  not null comment '名称',
    sort         int           not null comment '排序',
    attribute_id varchar(64)   not null comment 'attributeId',
    value        varchar(255)  not null comment '属性值',
    code         varchar(64)   null comment 'code'
)
    comment '项目类型属性值';


create table if not exists pmsx_project_plan_type_attribute
(
    id           varchar(64)   not null comment '主键' primary key,
    class_name   varchar(64)   null comment '创建人',
    creator_id   varchar(64)   not null comment '创建人',
    modify_time  datetime      not null comment '修改时间',
    owner_id     varchar(64)   null comment '拥有者',
    create_time  datetime      not null comment '创建时间',
    modify_id    varchar(64)   not null comment '修改人',
    remark       varchar(1024) null comment '备注',
    platform_id  varchar(64)   not null comment '平台ID',
    org_id       varchar(64)   not null comment '业务组织Id',
    status       int           not null comment '状态',
    logic_status int           not null comment '逻辑删除字段',
    number       varchar(64)   not null comment '编码',
    options      varchar(255)  null comment '选项值',
    name         varchar(128)  null comment '名称',
    `require`    int           not null comment '是否必填',
    code         varchar(64)   null comment '编码'
)
    comment '项目计划类型属性';

CREATE TABLE IF NOT EXISTS pmsx_project_plan_type
(
    id           VARCHAR(64)   NOT NULL COMMENT '主键' PRIMARY KEY,
    class_name   VARCHAR(64)   NULL COMMENT '创建人',
    creator_id   VARCHAR(64)   NOT NULL COMMENT '创建人',
    modify_time  DATETIME      NOT NULL COMMENT '修改时间',
    owner_id     VARCHAR(64)   NULL COMMENT '拥有者',
    create_time  DATETIME      NOT NULL COMMENT '创建时间',
    modify_id    VARCHAR(64)   NOT NULL COMMENT '修改人',
    remark       VARCHAR(1024) NOT NULL COMMENT '备注',
    platform_id  VARCHAR(64)   NOT NULL COMMENT '平台ID',
    org_id       VARCHAR(64)   NOT NULL COMMENT '业务组织Id',
    STATUS       INT           NOT NULL COMMENT '状态',
    logic_status INT           NOT NULL COMMENT '逻辑删除字段',
    icon         VARCHAR(128)  NOT NULL COMMENT '图标',
    image_id     VARCHAR(128)  NULL COMMENT 'imageId',
    number       VARCHAR(128)  NOT NULL COMMENT '编码',
    NAME         VARCHAR(128)  NOT NULL COMMENT '名称',
    parent_id    VARCHAR(64)   NOT NULL COMMENT '父级ID'
) COMMENT '项目计划类型管理';



CREATE TABLE `pmsx_project_place`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11)     NOT NULL COMMENT '状态',
    `logic_status` int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_id`   varchar(64)   DEFAULT NULL COMMENT '项目数据主键',
    `sort`         int(1)        DEFAULT NULL,
    `number`       varchar(255)  DEFAULT NULL,
    `name`         varchar(255)  DEFAULT NULL,
    `area_id`      varchar(255)  DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目地点';


DROP TABLE IF EXISTS pms_project_scheme_feedback;
CREATE TABLE `pms_project_scheme_feedback`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `logic_status`      int(11)     NOT NULL COMMENT '逻辑删除字段',
    `project_scheme_id` varchar(64) DEFAULT NULL COMMENT '项目计划id',
    `is_execute`        bit(1)      DEFAULT b'0' COMMENT '是否执行',
    `next_execute_time` datetime    DEFAULT NULL COMMENT '下一次执行时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目计划反馈日志';

DROP TABLE IF EXISTS pms_project_scheme_fallback;
CREATE TABLE `pms_project_scheme_fallback`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64)  DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `remark`            varchar(255) DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `org_id`            varchar(64)  DEFAULT NULL COMMENT '业务组织Id',
    `status`            int(11)      DEFAULT NULL COMMENT '状态',
    `logic_status`      int(11)      DEFAULT NULL COMMENT '逻辑删除字段',
    `project_scheme_id` varchar(64)  DEFAULT NULL COMMENT '项目计划id',
    `reason`            varchar(512) DEFAULT NULL COMMENT '退回原因',
    PRIMARY KEY (`id`),
    KEY `project_scheme_id_index` (`project_scheme_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目计划回退';


-- ----------------------------
-- Table structure for pms_project_human_resource_setting
-- ----------------------------
DROP TABLE IF EXISTS `pms_project_human_resource_setting`;
CREATE TABLE `pms_project_human_resource_setting`
(
    `id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '主键',
    `class_name`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `modify_time`  datetime(0)                                                    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime(0)                                                    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '修改人',
    `remark`       varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '业务组织Id',
    `status`       int(11)                                                        NOT NULL COMMENT '状态',
    `logic_status` int(11)                                                        NOT NULL COMMENT '逻辑删除字段',
    `data_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据ID',
    `data_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '数据类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '人力资源权限设置'
  ROW_FORMAT = Dynamic;