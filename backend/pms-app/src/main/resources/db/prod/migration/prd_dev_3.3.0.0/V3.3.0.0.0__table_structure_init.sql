-- ----------------------------
-- Table structure for pmsx_review
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_review`;
CREATE TABLE `pmsx_review`
(
    `content`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容备注',
    `project_id`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目ID',
    `meeting_no`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会议纪要号',
    `question_file_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题整改报告文件号',
    `review_file_no`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审报告文件号',
    `plm_no`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审文件PLM编号',
    `review_address`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会议地点',
    `review_time`      datetime NULL DEFAULT NULL COMMENT '会议召开时间',
    `examine_state`    tinyint(4) NULL DEFAULT NULL COMMENT '文档齐套检查',
    `deliver_id`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联交付物',
    `phase_legacy`     tinyint(4) NULL DEFAULT NULL COMMENT '阶段遗留问题已关闭',
    `request_state`    tinyint(4) NULL DEFAULT NULL COMMENT '满足产品开发流程的要求',
    `manage_user`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目管理专员',
    `name`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审名称',
    `plan_id`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联任务计划',
    `review_type`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审类型',
    `id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`      datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`      datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`           varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`           int(11) NOT NULL DEFAULT 1 COMMENT '状态',
    `logic_status`     int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
    `number`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目评审' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_review_essentials
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_review_essentials`;
CREATE TABLE `pmsx_review_essentials`
(
    `content`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评审要点内容',
    `review_phase`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审阶段',
    `essentials_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '要点类型',
    `id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`     datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`     datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`          varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`          int(11) NOT NULL DEFAULT 1 COMMENT '状态',
    `logic_status`    int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
    `number`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评审要点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_review_library
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_review_library`;
CREATE TABLE `pmsx_review_library`
(
    `maintain_dept` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '维护部门',
    `experts`       varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审专家',
    `name`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审要点库名称',
    `id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`   datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`   datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`        int(11) NOT NULL DEFAULT 1 COMMENT '状态',
    `logic_status`  int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
    `number`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评审要点库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_review_member
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_review_member`;
CREATE TABLE `pmsx_review_member`
(
    `scale`         tinyint(4) NULL DEFAULT 0 COMMENT '级别',
    `user_id`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '成员',
    `type`          int(11) NULL DEFAULT 1 COMMENT '类型',
    `id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`   datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`   datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`        int(11) NOT NULL DEFAULT 1 COMMENT '状态',
    `logic_status`  int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
    `number`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评审组成员' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pmsx_review_opinion
-- ----------------------------
DROP TABLE IF EXISTS `pmsx_review_opinion`;
CREATE TABLE `pmsx_review_opinion`
(
    `review_essentials_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审要点',
    `question_id`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联问题',
    `presented_user`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提出人',
    `opinion`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '意见',
    `type`                 int(11) NULL DEFAULT 1 COMMENT '意见类型',
    `id`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`          datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`          datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`               int(11) NOT NULL DEFAULT 1 COMMENT '状态',
    `logic_status`         int(11) NOT NULL DEFAULT 1 COMMENT '逻辑删除字段',
    `number`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
    `essentials`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '要点',
    `description`          varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评审意见' ROW_FORMAT = Dynamic;



CREATE TABLE `pas_income_contract`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`          datetime    NOT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime    NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64) NOT NULL COMMENT '修改人',
    `remark`               varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`               int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`         int(11) NOT NULL COMMENT '逻辑删除字段',
    `sale_dept`            varchar(256)   DEFAULT NULL COMMENT '销售部门',
    `account_manager`      varchar(256)   DEFAULT NULL COMMENT '客户经理',
    `client_code`          varchar(256)   DEFAULT NULL COMMENT '客户代码',
    `final_user`           varchar(256)   DEFAULT NULL COMMENT '最终用户',
    `opportunity_number`   varchar(256)   DEFAULT NULL COMMENT '商机编码',
    `project_number`       varchar(256)   DEFAULT NULL COMMENT '项目编码',
    `project_name`         varchar(256)   DEFAULT NULL COMMENT '项目名称',
    `type`                 varchar(256)   DEFAULT NULL COMMENT '合同类型',
    `order_type`           varchar(256)   DEFAULT NULL COMMENT '订单类型',
    `product_line`         varchar(256)   DEFAULT NULL COMMENT '产品线',
    `clerk`                varchar(256)   DEFAULT NULL COMMENT '商务员',
    `amount`               decimal(11, 2) DEFAULT NULL COMMENT '金额',
    `terms`                bit(1)         DEFAULT NULL COMMENT '是否有调制条款',
    `product_operate_type` varchar(256)   DEFAULT NULL COMMENT '产品运营分类',
    `project_context`      varchar(512)   DEFAULT NULL COMMENT '项目背景',
    `dept_accept_time`     datetime       DEFAULT NULL COMMENT '部门受理日期',
    `number`               varchar(128)   DEFAULT NULL COMMENT '合同编码',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同管理(超越)';

CREATE TABLE `pas_income_contract_product`
(
    `id`                      varchar(64) NOT NULL COMMENT '主键',
    `class_name`              varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`              varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`             datetime    NOT NULL COMMENT '修改时间',
    `owner_id`                varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`             datetime    NOT NULL COMMENT '创建时间',
    `modify_id`               varchar(64) NOT NULL COMMENT '修改人',
    `remark`                  varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`             varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                  varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                  int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`            int(11) NOT NULL COMMENT '逻辑删除字段',
    `model`                   varchar(64)    DEFAULT NULL COMMENT '产品型号',
    `product_second_classify` varchar(64)    DEFAULT NULL COMMENT '产品二级分类',
    `contr_product_number`    varchar(64)    DEFAULT NULL COMMENT '合同签订产品编码',
    `product_number`          varchar(64)    DEFAULT NULL COMMENT '基准目录产品编码',
    `num`                     decimal(11, 2) DEFAULT NULL COMMENT '数量',
    `price`                   decimal(11, 2) DEFAULT NULL COMMENT '单价',
    `amount`                  decimal(11, 2) DEFAULT NULL COMMENT '产品金额',
    `qualified_scope`         bit(1)         DEFAULT NULL COMMENT '是否在承制资格范围内',
    `request_deliver_time`    datetime       DEFAULT NULL COMMENT '要求交付日期',
    `contract_id`             varchar(64) NOT NULL COMMENT '合同id',
    PRIMARY KEY (`id`),
    KEY                       `index_contract` (`contract_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同产品';

CREATE TABLE `pms_project_approval_income`
(
    `id`                     varchar(64)    NOT NULL COMMENT '主键',
    `class_name`             varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64)    NOT NULL COMMENT '创建人',
    `modify_time`            datetime       NOT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime       NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)    NOT NULL COMMENT '修改人',
    `remark`                 varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)    NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)    NOT NULL COMMENT '业务组织Id',
    `status`                 int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`           int(11) NOT NULL COMMENT '逻辑删除字段',
    `product_id`             varchar(64)    NOT NULL COMMENT '产品id',
    `product_name`           varchar(255)  DEFAULT NULL COMMENT '产品名称',
    `expected_contract_year` datetime       NOT NULL COMMENT '预期合同年份',
    `fabricate_hour`         decimal(11, 2) NOT NULL COMMENT '制造工时',
    `expected_sale_number`   int(11) NOT NULL COMMENT '预期销售数量',
    `expected_income`        decimal(11, 2) NOT NULL COMMENT '预期收益',
    `project_approval_id`    varchar(64)    NOT NULL COMMENT '项目立项id',
    `product_number`         varchar(128)  DEFAULT NULL COMMENT '产品编码',
    PRIMARY KEY (`id`),
    KEY                      `index_approval_id` (`project_approval_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收益策划';

CREATE TABLE `pms_project_income`
(
    `id`                     varchar(64)    NOT NULL COMMENT '主键',
    `class_name`             varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`             varchar(64)    NOT NULL COMMENT '创建人',
    `modify_time`            datetime       NOT NULL COMMENT '修改时间',
    `owner_id`               varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`            datetime       NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64)    NOT NULL COMMENT '修改人',
    `remark`                 varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64)    NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64)    NOT NULL COMMENT '业务组织Id',
    `status`                 int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`           int(11) NOT NULL COMMENT '逻辑删除字段',
    `project_id`             varchar(64)    NOT NULL COMMENT '项目id',
    `product_id`             varchar(64)    NOT NULL COMMENT '产品id',
    `product_name`           varchar(255)  DEFAULT NULL COMMENT '产品名称',
    `product_number`         varchar(128)  DEFAULT NULL COMMENT '产品编码',
    `sale_over`              bit(1)         NOT NULL COMMENT '销售是否结束',
    `expected_contract_year` datetime       NOT NULL COMMENT '预期合同年份',
    `expected_product_price` decimal(11, 2) NOT NULL COMMENT '原预期产品单价',
    `orig_expected_outcome`  decimal(11, 2) NOT NULL COMMENT '原预期总产出',
    `expected_outcomes`      decimal(11, 2) NOT NULL COMMENT '现预期总产出',
    `approval_income_id`     varchar(64)    NOT NULL COMMENT '收益策划id',
    PRIMARY KEY (`id`),
    KEY                      `index_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收益管理';


CREATE TABLE `pms_question_library`
(
    `logic_status`           int(11) NOT NULL,
    `principal_name`         varchar(64)    DEFAULT NULL,
    `creator_id`             varchar(64)    DEFAULT NULL,
    `org_id`                 varchar(64)    DEFAULT NULL,
    `recipient_name`         varchar(64)    DEFAULT NULL,
    `modify_id`              varchar(64)    DEFAULT NULL,
    `plan_id`                varchar(64)    DEFAULT NULL,
    `sort`                   int(11) DEFAULT NULL COMMENT '排序',
    `recipient`              varchar(64)    DEFAULT NULL COMMENT '接收人',
    `priority_level`         varchar(64)    DEFAULT NULL,
    `question_source`        varchar(64)    DEFAULT NULL,
    `exhibitor`              varchar(64)    DEFAULT NULL COMMENT '提出人Id',
    `schedule`               decimal(10, 0) DEFAULT NULL COMMENT '进度',
    `id`                     varchar(64)  NOT NULL COMMENT 'id',
    `project_id`             varchar(64)    DEFAULT NULL,
    `exhibitor_name`         varchar(64)    DEFAULT NULL,
    `number`                 varchar(128)   DEFAULT NULL COMMENT '编码',
    `document_id`            varchar(64)    DEFAULT NULL,
    `serious_level`          varchar(64)    DEFAULT NULL,
    `predict_end_time`       datetime       DEFAULT NULL,
    `content`                varchar(3000)  DEFAULT NULL COMMENT '内容',
    `name`                   varchar(128)   DEFAULT NULL COMMENT '名称',
    `modify_time`            datetime     NOT NULL,
    `remark`                 varchar(512)   DEFAULT NULL COMMENT '备注',
    `question_type`          varchar(64)    DEFAULT NULL COMMENT '问题类型',
    `proposed_time`          datetime       DEFAULT NULL,
    `owner_id`               varchar(64)    DEFAULT NULL,
    `principal_id`           varchar(64)    DEFAULT NULL,
    `create_time`            datetime     NOT NULL,
    `status`                 int(11) NOT NULL COMMENT '状态 -1删除 0禁用 1启用',
    `platform_id`            varchar(64)    DEFAULT NULL,
    `class_name`             varchar(128) NOT NULL,
    `data_source`            varchar(64)    DEFAULT NULL COMMENT '数据来源',
    `question_id`            varchar(64)    DEFAULT NULL COMMENT '问题ID',
    `product_number`         varchar(64)    DEFAULT NULL COMMENT '产品编码',
    `material_number`        varchar(64)    DEFAULT NULL COMMENT '物料编码',
    `production_orders`      varchar(128)   DEFAULT NULL COMMENT '生产订单',
    `product_number_sn`      varchar(128)   DEFAULT NULL COMMENT '产品编号（SN）',
    `stage`                  varchar(64)    DEFAULT NULL COMMENT '阶段',
    `process_link`           varchar(128)   DEFAULT NULL COMMENT '过程环节',
    `process_classifi`       varchar(64)    DEFAULT NULL COMMENT '过程分类',
    `problem_phenomenon_one` varchar(128)   DEFAULT NULL COMMENT '问题现象一级分类',
    `problem_phenomenon_two` varchar(128)   DEFAULT NULL COMMENT '问题现象二级分类',
    `problem_phenomenon_th`  varchar(128)   DEFAULT NULL COMMENT '问题现象三级分类',
    `problem_level`          varchar(128)   DEFAULT NULL COMMENT '问题等级分类',
    `reasion_one`            varchar(128)   DEFAULT NULL COMMENT '一级原因分类',
    `reasion_two`            varchar(128)   DEFAULT NULL COMMENT '二级原因分类',
    `reasion_three`          varchar(128)   DEFAULT NULL COMMENT '三级原因分类',
    `reasion_remark`         text COMMENT '原因分析描述',
    `correct_classifi`       varchar(128)   DEFAULT NULL COMMENT '纠正分类',
    `ecn_number`             varchar(128)   DEFAULT NULL COMMENT '关联ECN编号',
    `correct_remark`         text COMMENT '纠正描述',
    `question_up_type`       varchar(128)   DEFAULT NULL COMMENT '问题升级类型',
    `is_di_technical_issues` bit(1)         DEFAULT NULL COMMENT '是否疑难技术问题',
    `is_assess`              bit(1)         DEFAULT NULL COMMENT '是否综合评估不再解决',
    `is_ecological_issues`   bit(1)         DEFAULT NULL COMMENT '是否生态问题',
    `is_quality_use_cases`   bit(1)         DEFAULT NULL COMMENT '是否典型质量案例',
    `one_case_to_another`    varchar(128)   DEFAULT NULL COMMENT '举一反三',
    `corre_ac_description`   text COMMENT '纠正措施描述',
    `opinion_categories`     varchar(128)   DEFAULT NULL COMMENT '意见类别',
    `review_points`          varchar(128)   DEFAULT NULL COMMENT '评审要点',
    `is_technical_issues`    bit(1)         DEFAULT NULL COMMENT '是否技术问题',
    `is_typical_problems`    bit(1)         DEFAULT NULL COMMENT '是否典型问题',
    `op_classification`      varchar(128)   DEFAULT NULL COMMENT '意见分类',
    `adoption_situation`     varchar(64)    DEFAULT NULL COMMENT '采纳情况',
    `overall_description`    text COMMENT '整体改进描述',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题库';
