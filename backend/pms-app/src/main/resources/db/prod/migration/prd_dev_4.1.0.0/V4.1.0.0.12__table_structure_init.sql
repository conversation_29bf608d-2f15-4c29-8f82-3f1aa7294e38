
ALTER TABLE `pmsx_train_center`
    change COLUMN `contrac_person`  contact_person_ids varchar(500) DEFAULT NULL COMMENT '中心培训联络人id拼接';

ALTER TABLE `pmsx_train_center`
    add COLUMN `contact_person_names`  varchar(500) DEFAULT NULL COMMENT '中心培训联络人名称' ;



CREATE TABLE `pmsx_train_contact` (
                                      `id` varchar(64) NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                      `modify_time` datetime NOT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                      `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
                                      `dept_id` varchar(64) DEFAULT NULL COMMENT '中心ID或部门ID',
                                      `contact_person_ids` varchar(500) DEFAULT NULL COMMENT '联系人id拼接',
                                      `dept_name` varchar(200) DEFAULT NULL COMMENT '中心名称或者部门名称',
                                      `contact_person_names` varchar(240) DEFAULT NULL COMMENT '联系人名称拼接',
                                      `dept_code` varchar(64) DEFAULT NULL COMMENT '部门code或者中心code',
                                      `contact_type` varchar(64) DEFAULT NULL COMMENT '联系人类型',
                                      `base_code` varchar(64) DEFAULT NULL COMMENT '基地编码',
                                      `base_name` varchar(128) DEFAULT NULL COMMENT '基地名称',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='培训联络人信息表';


