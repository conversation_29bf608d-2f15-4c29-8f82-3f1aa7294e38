--  安质环信息编码字段
alter table pmsx_safety_quality_env add COLUMN dept_code  VARCHAR(64) COMMENT '检查人所在部门编号';
alter table pmsx_safety_quality_env add COLUMN rsp_dept_code  VARCHAR(64) COMMENT '直接责任部门code';
alter table pmsx_safety_quality_env add COLUMN rsp_center_code  VARCHAR(64) COMMENT '责任中心code';

alter table `ncf_form_contract_info` modify `is_fill_onetime_acceptance` bit(10);
alter table `ncf_form_purchase_request` modify `file_name` text;
alter table `ncf_form_purchase_request` modify `purchase_content` text;
alter table `ncf_form_contract_pay_milestone` modify `price_total_fixed` bit(64);
alter table `pmsx_job_manage` add COLUMN `bus_status` VARCHAR(10) DEFAULT '121' COMMENT '作业状态';
alter table `pms_req_detail` modify `context` longtext;
alter table `pms_related_transaction_form` modify `form_status` varchar(256);

alter table `pmsx_req_clarification_record` modify `origin_quote_start_time` DATETIME  null DEFAULT NULL;
alter table `pmsx_req_clarification_record` modify `new_quote_dl_time` DATETIME  null DEFAULT NULL;
alter table `pms_related_transaction_form` modify `work_title` varchar(1024);
ALTER TABLE `pms_non_contract_proc` MODIFY `expense_info` LONGTEXT;
ALTER TABLE `pms_non_contract_proc` MODIFY `supplier_info` LONGTEXT;
alter table `pms_market_contract` modify `name` varchar(64) default null;
alter table `pms_market_contract` modify `contract_type` varchar(64) default null;
alter table `pms_ncf_form_purch_order` modify `used_time` varchar(32);