delete from sys_code_segment where id in ('s3rd1825866569689673729','s3rd1825866569689673730');


INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1825866569689673731', '年', '1', '9hi11820634282397937664', '', 'DATE_YYYY', '1', '', '0', 1, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-20 20:04:48', 'user00000000000000000100000000000000', '2024-08-20 20:04:48', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);


INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1825866569689673732', '年', '1', '9hi11820634400077524992', '', 'DATE_YYYY', '1', '', '0', 1, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-20 20:04:48', 'user00000000000000000100000000000000', '2024-08-20 20:04:48', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);