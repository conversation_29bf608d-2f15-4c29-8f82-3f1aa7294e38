ALTER TABLE `ncf_form_contract_info`
    ADD COLUMN `warning_money` varchar(255) NULL COMMENT '合同预警金额' AFTER `is_fill_onetime_acceptance`,
ADD COLUMN `warning_day` varchar(255) NULL COMMENT '合同预警日期' AFTER `warning_money`;

INSERT INTO `xxl_job_info` (`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (100, 5, '采购模块-定时修改合同字段', '2024-06-29 23:03:14', '2024-06-29 23:03:14', 'orion', '', 'CRON', '0 0 2 * * ?', 'DO_NOTHING', 'FIRST', 'ContractUpdateDataJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-06-29 23:03:14', '', 0, 0, 0);
INSERT INTO `xxl_job_info` (`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (99, 5, '采购模块-定时修改集采订单订单待支付', '2024-06-29 23:01:16', '2024-06-29 23:01:16', 'orion', '', 'CRON', '0 0 2 * * ?', 'DO_NOTHING', 'FIRST', 'OrderUpdateDataXxlJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-06-29 23:01:16', '', 0, 0, 0);
