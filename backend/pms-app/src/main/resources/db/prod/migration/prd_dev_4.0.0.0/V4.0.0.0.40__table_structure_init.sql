--
INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (102, 5, '大修计划状态变更-生产服务', '2024-07-25 11:43:00', '2024-07-25 11:43:00', 'orion', '', 'CRON', '0 55 2 * * ?', 'DO_NOTHING', 'FIRST', 'majorRepairPlanChangeStatus', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', '大修计划状态变更-生产服务', '2024-07-25 11:43:00', '', 0, 0, 0);

INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (103, 5, '人员岗位培训落地验证-生产服务', '2024-07-25 11:43:00', '2024-07-25 11:43:00', 'orion', '', 'CRON', '0 40 2 * * ?', 'DO_NOTHING', 'FIRST', 'personJobPostAuthorizeVerifyEffect', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', '人员岗位培训落地验证-生产服务', '2024-07-25 11:43:00', '', 0, 0, 0);

-- 新增状态策略
INSERT INTO `pmi_data_status`(`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks18055gg19045197127680', 'DataStatus', 'txf71804517545829916672', NULL, '4', '已失效', 121, NULL, NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-07-25 11:43:00', '314j1787363372854079488', '2024-07-25 11:43:00', 1, 4450, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);