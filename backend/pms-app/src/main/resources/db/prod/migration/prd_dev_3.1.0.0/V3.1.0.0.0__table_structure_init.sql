DROP TABLE IF EXISTS pms_project_life_cycle_phase;
CREATE TABLE `pms_project_life_cycle_phase`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64)  DEFAULT NULL COMMENT '创建人',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`            int(11) NOT NULL COMMENT '状态',
    `logic_status`      int(11) NOT NULL COMMENT '逻辑删除字段',
    `project_id`        varchar(64)  DEFAULT NULL COMMENT '项目id',
    `project_scheme_id` varchar(64)  DEFAULT NULL COMMENT '项目计划id（里程碑id）',
    `template_id`       varchar(64)  DEFAULT NULL COMMENT '全生命周期模板id',
    `phase_description` text COMMENT '阶段描述',
    `sort`              int(11) NOT NULL COMMENT '排序',
    `remark`            varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全生命周期阶段';

DROP TABLE IF EXISTS pms_project_life_cycle_template;
CREATE TABLE `pms_project_life_cycle_template`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)  DEFAULT NULL COMMENT '创建人',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime    NOT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)  DEFAULT NULL COMMENT '拥有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64) NOT NULL COMMENT '修改人',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`       int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `name`         varchar(255) DEFAULT NULL COMMENT '名称',
    `content`      text COMMENT '内容',
    `file_num`     int(11) NOT NULL COMMENT '文件数',
    `remark`       varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全生命周期说明模板';