-- 新增容器
insert into `pmi_container` (id, name, page_id, code, label, class_name, status, owner_id, creator_id, create_time, modify_id, modify_time, platform_id, unique_key, logic_status, class_id) VALUES(UUID(), '门户首页', 'xaxe2ef1846a066a471596d8912743952597', 'MAIN_HOME_container_01', '门户首页', 'Container', 1, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', now(), 'user00000000000000000100000000000000', now(), 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', UUID(), 1, null);
-- 新增页面
INSERT INTO `pmi_page`(`id`, `name`, `component`, `component_name`, `path`, `code`, `page_type`, `config_id`, `micro_web_name`, `sort`, `is_system`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `menu_id`, `default_page`) VALUES ('f2tr1803020919814656000', '大修工作包详情', '/pms/dailyWork/pages/pages/workPackageDetails', 'PMSDailyWorkPackageDetails', '/dailyWork/package/:id', 'PMSDailyWorkPackageDetails', '0', NULL, 'pms', NULL, NULL, 'Page', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-06-18 19:04:20', '314j1000000000000000000', '2024-07-08 16:08:16', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'menu1801065414909108224', 0);
