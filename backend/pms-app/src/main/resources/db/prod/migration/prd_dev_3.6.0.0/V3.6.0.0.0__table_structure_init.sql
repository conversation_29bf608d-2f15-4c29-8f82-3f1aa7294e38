
CREATE TABLE `pms_document_model_library_dir`
(
    `parent_id`    varchar(256)  DEFAULT NULL COMMENT '父级id',
    `name`         varchar(256)  DEFAULT NULL COMMENT '文件名',
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `class_name`   varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`   varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`     varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`       varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`  varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`       varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`       int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`       varchar(64)   DEFAULT NULL COMMENT '编码',
    `sort`         int(11) unsigned zerofill DEFAULT NULL,
    `chain`        varchar(64)   DEFAULT NULL,
    `description`  text COMMENT '描述',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档模板库文件夹';


CREATE TABLE `pms_document_model_library`
(
    `initial_rev_id`    varchar(256)  DEFAULT NULL COMMENT '初始版本',
    `rev_id`            varchar(256)  DEFAULT NULL COMMENT '版本值',
    `previous_rev_id`   varchar(256)  DEFAULT NULL COMMENT '上一个版本',
    `rev_key`           varchar(256)  DEFAULT NULL COMMENT '版本KEY',
    `rev_order`         int(11) DEFAULT NULL COMMENT '版本顺序',
    `next_rev_id`       varchar(256)  DEFAULT NULL COMMENT '下一个版本',
    `use_scope`         text COMMENT '应用范围',
    `is_use_all_object` bit(2)        DEFAULT NULL COMMENT '是否应用所有对象',
    `name`              varchar(256)  DEFAULT NULL COMMENT '文档模板名称',
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`          varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`            varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`            int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`      int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`            varchar(64)   DEFAULT NULL COMMENT '编码',
    `main_table_id`     varchar(64)   DEFAULT NULL COMMENT '主表ID',
    `is_main_rev`       bit(2)        DEFAULT NULL COMMENT '主版本',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档模板库';



CREATE TABLE `pms_document_decomposition`
(
    `parent_id`     varchar(256)  DEFAULT NULL COMMENT '父级id',
    `task_id`       varchar(256)  DEFAULT NULL COMMENT '关联任务',
    `name`          varchar(256)  DEFAULT NULL COMMENT '条目标题',
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`   datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`        int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`  int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`        varchar(64)   DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64)   DEFAULT NULL COMMENT '主表ID',
    `content`       text COMMENT '条目编制内容',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档分解';



CREATE TABLE `pms_task_decomposition`
(
    `rsp_dept`                 varchar(256)  DEFAULT NULL COMMENT '责任部门',
    `rsp_user`                 varchar(256)  DEFAULT NULL COMMENT '责任人',
    `work_time`                int(11) DEFAULT NULL COMMENT '工期',
    `process_instances`        varchar(256)  DEFAULT NULL COMMENT '处理实例',
    `process_object`           varchar(256)  DEFAULT NULL COMMENT '处理对象',
    `name`                     varchar(256)  DEFAULT NULL COMMENT ' 任务名称',
    `id`                       varchar(64) NOT NULL COMMENT '主键',
    `class_name`               varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`               varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`              datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`                 varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`              datetime    NOT NULL COMMENT '创建时间',
    `modify_id`                varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                   varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`              varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                   varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`                   int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`             int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`                   varchar(64)   DEFAULT NULL COMMENT '编码',
    `main_table_id`            varchar(64)   DEFAULT NULL COMMENT '主表ID',
    `parent_id`                varchar(256)  DEFAULT NULL COMMENT '父级id',
    `documentDecomposition_id` varchar(64)   DEFAULT NULL COMMENT '文档分解ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务分解';



CREATE TABLE `pms_task_decomposition_pre_post`
(
    `id`                    varchar(64) NOT NULL COMMENT '主键',
    `class_name`            varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`            varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`           datetime    NOT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`           datetime    NOT NULL COMMENT '创建时间',
    `modify_id`             varchar(64) NOT NULL COMMENT '修改人',
    `remark`                varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                int(11) NOT NULL COMMENT '状态',
    `logic_status`          int(11) NOT NULL COMMENT '逻辑删除字段',
    `pre_task_id`           varchar(64)   DEFAULT NULL COMMENT '前置计划Id',
    `post_task_id`          varchar(64)   DEFAULT NULL COMMENT '后置计划Id',
    `task_decomposition_id` varchar(64) NOT NULL COMMENT '任务id',
    `type`                  varchar(10) NOT NULL COMMENT '前后置类型',
    `template_id`           varchar(64)   DEFAULT NULL COMMENT '模板id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务分解前后置关系';



CREATE TABLE `pms_product_plan`
(
    `project_approval_id` varchar(256)  DEFAULT NULL COMMENT '项目立项ID',
    `description`         text COMMENT '技术指标描述',
    `name`                varchar(256)  DEFAULT NULL COMMENT '技术指标名称',
    `id`                  varchar(64) NOT NULL COMMENT '主键',
    `class_name`          varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`          varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`         datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`            varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`         datetime    NOT NULL COMMENT '创建时间',
    `modify_id`           varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`              varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`         varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`              varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`              int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`        int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`              varchar(64)   DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品策划';


ALTER TABLE pmsx_project_approval
    add `type` varchar(255) DEFAULT NULL COMMENT '立项类型',
add  `source` varchar(255) DEFAULT NULL COMMENT '来源',
add  `estimate_start_time` datetime DEFAULT NULL COMMENT '预估项目开始时间',
add  `estimate_end_time` datetime DEFAULT NULL COMMENT '预估项目结束时间',
add  `require_review_id` varchar(64) DEFAULT NULL COMMENT '需求评审单',
add  `rsp_user` varchar(64) DEFAULT NULL COMMENT '负责人';


CREATE TABLE `pmsx_collaborative_compilation_task`
(
    `id`                  varchar(64)  NOT NULL COMMENT '主键',
    `sort`                int(11) DEFAULT NULL COMMENT '排序',
    `name`                varchar(255) NOT NULL COMMENT '名称',
    `approval_id`         varchar(64)  NOT NULL COMMENT '立项id',
    `level`               int(11) DEFAULT NULL COMMENT '层级',
    `parent_id`           varchar(64)   DEFAULT NULL COMMENT '父id',
    `status`              int(11) DEFAULT NULL COMMENT '状态',
    `rsp_dept`            varchar(64)   DEFAULT NULL COMMENT '责任部门',
    `rsp_user`            varchar(64)   DEFAULT NULL COMMENT '责任人',
    `begin_time`          datetime      DEFAULT NULL COMMENT '计划开始时间',
    `end_time`            datetime      DEFAULT NULL COMMENT '计划结束时间',
    `circumstance`        int(11) DEFAULT NULL COMMENT '计划情况',
    `actual_end_time`     datetime      DEFAULT NULL COMMENT '实际结束时间',
    `actual_begin_time`   datetime      DEFAULT NULL COMMENT '实际开始时间',
    `platform_id`         varchar(64)   DEFAULT NULL COMMENT '平台Id',
    `org_id`              varchar(64)   DEFAULT NULL COMMENT '组织Id',
    `logic_status`        int(11) NOT NULL COMMENT '逻辑删除状态',
    `creator_id`          varchar(64)   DEFAULT NULL COMMENT '创建者',
    `create_time`         datetime      DEFAULT NULL,
    `modify_id`           varchar(64)   DEFAULT NULL COMMENT '修改人ID',
    `modify_time`         datetime      DEFAULT NULL,
    `parent_chain`        varchar(1024) DEFAULT NULL COMMENT '父级链',
    `remark`              varchar(255)  DEFAULT NULL COMMENT '备注',
    `class_name`          varchar(100)  DEFAULT NULL COMMENT '类名',
    `owner_id`            varchar(64)   DEFAULT NULL,
    `task_desc`           varchar(1024) DEFAULT NULL COMMENT '任务描述',
    `top_sort`            int(11) DEFAULT NULL COMMENT '置顶序号（0：取消置顶）',
    `execute_desc`        varchar(100)  DEFAULT NULL COMMENT '执行情况说明',
    `issue_time`          datetime(3) DEFAULT NULL COMMENT '下发计划时间',
    `duration_days`       int(11) DEFAULT NULL COMMENT '计划工期',
    `number`              varchar(255)  DEFAULT NULL COMMENT '编码',
    `issued_user`         varchar(64)   DEFAULT NULL COMMENT '下达人',
    `reason_confirmation` varchar(1024) DEFAULT NULL COMMENT '确认理由',
    `last_status`         int(11) DEFAULT NULL COMMENT '上一个状态',
    `content`             text COMMENT '任务内容',
    `process_instances`   varchar(255)  DEFAULT NULL COMMENT '处理实例',
    `process_object`      varchar(255)  DEFAULT NULL COMMENT '处理对象',
    `reason_transfer`     text COMMENT '转办理由',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='协同编制任务表';



CREATE TABLE `pmsx_collaborative_compilation_document`
(
    `parent_id`     varchar(256)  DEFAULT NULL COMMENT '父级id',
    `task_id`       varchar(256)  DEFAULT NULL COMMENT '关联任务',
    `name`          varchar(256)  DEFAULT NULL COMMENT '条目标题',
    `id`            varchar(64) NOT NULL COMMENT '主键',
    `class_name`    varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`    varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`   datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`      varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `modify_id`     varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`        varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`   varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`        varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`        int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`  int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`        varchar(100)  DEFAULT NULL COMMENT '编码',
    `approval_id`   varchar(64)   DEFAULT NULL COMMENT '立项Id',
    `content`       text COMMENT '条目内容',
    `write_require` text COMMENT '编写要求',
    `sort`          int(10) DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协同编制文档分解';



CREATE TABLE `pmsx_approval_task_pre_post`
(
    `id`           varchar(64) NOT NULL COMMENT '主键',
    `approval_id`  varchar(64)  DEFAULT NULL COMMENT '立项id',
    `task_id`      varchar(64)  DEFAULT NULL COMMENT '任务id',
    `type`         int(11) DEFAULT NULL COMMENT '前后置类型',
    `pre_task_id`  varchar(64)  DEFAULT NULL COMMENT '前置计划Id',
    `post_task_id` varchar(64)  DEFAULT NULL COMMENT '后置计划Id',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除状态',
    `creator_id`   varchar(64)  DEFAULT NULL COMMENT '创建者',
    `create_time`  datetime    NOT NULL COMMENT '创建时间',
    `modify_id`    varchar(64)  DEFAULT NULL COMMENT '修改人ID',
    `modify_time`  datetime     DEFAULT NULL COMMENT '修改时间',
    `org_id`       varchar(64)  DEFAULT NULL COMMENT '组织ID',
    `platform_id`  varchar(64)  DEFAULT NULL COMMENT '平台ID',
    `class_name`   varchar(100) DEFAULT NULL,
    `remark`       varchar(255) DEFAULT NULL COMMENT '备注',
    `owner_id`     varchar(64)  DEFAULT NULL,
    `status`       int(11) DEFAULT NULL COMMENT '状态',
    `sort`         int(11) DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='项目立项任务前后置关系表';


CREATE TABLE `pmsx_require_review_form`
(
    `id`                    varchar(64) NOT NULL COMMENT '主键',
    `class_name`            varchar(64)    DEFAULT NULL COMMENT '创建人',
    `creator_id`            varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`           datetime    NOT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)    DEFAULT NULL COMMENT '拥有者',
    `create_time`           datetime    NOT NULL COMMENT '创建时间',
    `modify_id`             varchar(64) NOT NULL COMMENT '修改人',
    `remark`                varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`                int(11) NOT NULL COMMENT '状态',
    `logic_status`          int(11) NOT NULL COMMENT '逻辑删除字段',
    `require_review_logo`   varchar(100)   DEFAULT NULL COMMENT '需求评审标识',
    `army_arms`             varchar(100)   DEFAULT NULL COMMENT '军兵种',
    `application_scenarios` varchar(100)   DEFAULT NULL COMMENT '应用场景',
    `is_case_hardened`      varchar(100)   DEFAULT NULL COMMENT '是否定型',
    `id_examine`            varchar(100)   DEFAULT NULL COMMENT '是否军检',
    `product_line`          varchar(100)   DEFAULT NULL COMMENT '产品线',
    `total_sign_amount`     decimal(10, 0) DEFAULT NULL COMMENT '公司预计签订金额-共计',
    `sign_amount`           decimal(10, 0) DEFAULT NULL COMMENT '预计签订金额',
    `demand_capacity`       text COMMENT '市场需求容量',
    `competition_situation` varchar(255)   DEFAULT NULL COMMENT '产品竞争情况',
    `lead_sign`             varchar(255)   DEFAULT NULL COMMENT '商机标识',
    `lead_name`             varchar(255)   DEFAULT NULL COMMENT '商机名称',
    `sign_client`           varchar(255)   DEFAULT NULL COMMENT '签单客户',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='需求评审单';


DROP TABLE IF EXISTS `pmsx_project_condition`;
CREATE TABLE `pmsx_project_condition`
(
    `id`                   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `class_name`           varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `modify_time`          datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`               text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
    `platform_id`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务组织Id',
    `status`               int(11) NULL DEFAULT NULL COMMENT '状态',
    `logic_status`         int(11) NULL DEFAULT NULL COMMENT '逻辑删除字段',
    `project_name`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目名称',
    `project_number`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
    `undertaking_center`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '承接中心',
    `cust_company`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户（电厂）',
    `detailed_description` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '详细描述',
    `CC_mnumber`           bigint(20) NULL DEFAULT NULL COMMENT 'CCM设备/隐患消除(个)',
    `save_time`            bigint(20) NULL DEFAULT NULL COMMENT '大修工期节约(H)',
    `m_sv_reduce`          bigint(20) NULL DEFAULT NULL COMMENT '集体剂量降低(man.mSv)',
    `project_cost`         decimal(10, 0) NULL DEFAULT NULL COMMENT '项目成本(万)',
    `project_revenue`      decimal(10, 0) NULL DEFAULT NULL COMMENT '项目营收',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '项目状态' ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for ncf_form_ncf_form_supplier_review
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_ncf_form_supplier_review`;
CREATE TABLE `ncf_form_ncf_form_supplier_review`
(
    `approval_completio_time` datetime NULL DEFAULT NULL COMMENT '审批完成时间',
    `contract_id`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商编码',
    `contract_name`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
    `application_number`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请编号',
    `project_category`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目类别',
    `project_name`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称/采购任务名称',
    `procure_number`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购包号',
    `applicant_type`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请类型',
    `applicant`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
    `declaring_company`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请公司',
    `state`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
    `reason`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原因',
    `process_step`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程环节',
    `id`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`             datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`             datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                  varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`                  int(11) NOT NULL COMMENT '状态',
    `logic_status`            int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资审供应商信息表' ROW_FORMAT = Dynamic;



CREATE TABLE `pms_ncf_purch_index`
(
    `remarks`              varchar(256)  DEFAULT NULL COMMENT '备注说明',
    `chain_ratio`          varchar(256)  DEFAULT NULL COMMENT '环比',
    `year_basis`           varchar(256)  DEFAULT NULL COMMENT '同比',
    `goal`                 varchar(256)  DEFAULT NULL COMMENT '目标',
    `current_month`        varchar(256)  DEFAULT NULL COMMENT '本月',
    `last_month`           varchar(256)  DEFAULT NULL COMMENT '上月',
    `index_name`           varchar(256)  DEFAULT NULL COMMENT '指标名称',
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`           varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`          datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`          datetime    NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`               int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`         int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`               varchar(64)   DEFAULT NULL COMMENT '编码',
    `index_year`           varchar(256)  DEFAULT NULL COMMENT '年份',
    `index_month`          varchar(256)  DEFAULT NULL COMMENT '月份',
    `indicator_ownership`  varchar(256)  DEFAULT NULL COMMENT '指标所属领域',
    `index_classification` varchar(256)  DEFAULT NULL COMMENT '指标分类',
    `indicator_state`      varchar(256)  DEFAULT NULL COMMENT '指标状态',
    `index_order`          int(4) DEFAULT NULL COMMENT '序号',
    `up_to_this_month`     varchar(255)  DEFAULT NULL COMMENT '截至到本月',
    `up_to_last_month`     varchar(255)  DEFAULT NULL COMMENT '截至到上月',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购供应指标';

CREATE TABLE `ncf_form_supplier_review`
(
    `contract_id`              varchar(256)  DEFAULT NULL COMMENT '供应商编码',
    `contract_name`            varchar(256)  DEFAULT NULL COMMENT '供应商名称',
    `application_number`       varchar(256)  DEFAULT NULL COMMENT '申请编号',
    `project_category`         varchar(256)  DEFAULT NULL COMMENT '项目类别',
    `project_name`             varchar(256)  DEFAULT NULL COMMENT '项目名称/采购任务名称',
    `procure_number`           varchar(256)  DEFAULT NULL COMMENT '采购包号',
    `applicant_type`           varchar(256)  DEFAULT NULL COMMENT '申请类型',
    `applicant`                varchar(256)  DEFAULT NULL COMMENT '申请人',
    `declaring_company`        varchar(256)  DEFAULT NULL COMMENT '申请公司',
    `state`                    varchar(256)  DEFAULT NULL COMMENT '状态',
    `reason`                   varchar(256)  DEFAULT NULL COMMENT '原因',
    `process_step`             varchar(256)  DEFAULT NULL COMMENT '流程环节',
    `id`                       varchar(64) NOT NULL COMMENT '主键',
    `class_name`               varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`               varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`              datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`                 varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`              datetime    NOT NULL COMMENT '创建时间',
    `modify_id`                varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                   varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`              varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                   varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`                   int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`             int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`                   varchar(64)   DEFAULT NULL COMMENT '编码',
    `approval_completion_time` datetime      DEFAULT NULL COMMENT '审批完成时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资审供应商信息表';


alter table pms_contract_milestone
    add `expect_accept_date` datetime DEFAULT NULL COMMENT '预计验收日期';
alter table pms_market_contract_milestone_reschedule CHANGE `old_plan_accept_date` `old_expect_accept_date` datetime;
alter table pms_market_contract_milestone_reschedule CHANGE `new_plan_accept_date` `new_expect_accept_date` datetime;



DROP TABLE IF EXISTS pms_non_contract_proc;
CREATE TABLE `pms_non_contract_proc`
(
    `work_topic`            varchar(256)  DEFAULT NULL COMMENT '工作主题',
    `process_name`          varchar(256)  DEFAULT NULL COMMENT '流程名称',
    `initiation_time`       datetime      DEFAULT NULL COMMENT '发起时间',
    `initiator`             varchar(256)  DEFAULT NULL COMMENT '发起人',
    `claimant`              varchar(256)  DEFAULT NULL COMMENT '报销人',
    `apply_company_code`    varchar(256)  DEFAULT NULL COMMENT '申请公司编码',
    `apply_company_name`    varchar(256)  DEFAULT NULL COMMENT '申请公司名称',
    `apply_dept`            varchar(256)  DEFAULT NULL COMMENT '申请部门',
    `expense_company_code`  varchar(256)  DEFAULT NULL COMMENT '费用归属公司编码',
    `xpense_company_name`   varchar(256)  DEFAULT NULL COMMENT '费用归属公司名称',
    `reimbursed_amount`     varchar(256)  DEFAULT NULL COMMENT '报销金额',
    `req_payment_time`      datetime      DEFAULT NULL COMMENT '要求付款时间',
    `currency`              varchar(256)  DEFAULT NULL COMMENT '币种',
    `in_rmb`                varchar(256)  DEFAULT NULL COMMENT '折合人民币',
    `apply_reason`          varchar(256)  DEFAULT NULL COMMENT '申请原因',
    `expense_info`          varchar(256)  DEFAULT NULL COMMENT '费用信息',
    `supplier_info`         varchar(256)  DEFAULT NULL COMMENT '供应商信息',
    `is_internal_tx`        bit(1)        DEFAULT NULL COMMENT '是否内部交易',
    `internal_tx_number`    varchar(256)  DEFAULT NULL COMMENT '内部交易号',
    `process_status`        varchar(256)  DEFAULT NULL COMMENT '流程状态',
    `payment_way`           varchar(256)  DEFAULT NULL COMMENT '支付方式',
    `project_category`      varchar(256)  DEFAULT NULL COMMENT '立项类别',
    `project_code`          varchar(256)  DEFAULT NULL COMMENT '立项号',
    `project_name`          varchar(256)  DEFAULT NULL COMMENT '立项名称',
    `bk_dept`               varchar(256)  DEFAULT NULL COMMENT '归口部门',
    `payment_amount`        varchar(256)  DEFAULT NULL COMMENT '支付金额',
    `transaction_amount`    varchar(256)  DEFAULT NULL COMMENT '交易金额',
    `actual_payment_amount` varchar(256)  DEFAULT NULL COMMENT '实际支付金额',
    `apply_number`          varchar(256)  DEFAULT NULL COMMENT '申请笔数',
    `reimbursement_amount`  varchar(256)  DEFAULT NULL COMMENT '报销金额',
    `id`                    varchar(64) NOT NULL COMMENT '主键',
    `class_name`            varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`            varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`           datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`              varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`           datetime    NOT NULL COMMENT '创建时间',
    `modify_id`             varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`                int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`          int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`                varchar(64)   DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='无合同采购表';

-- ----------------------------
-- Table structure for ncf_form_purchase_request
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_purchase_request`;
CREATE TABLE `ncf_form_purchase_request`
(
    `id`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `code`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购申请单编码',
    `name`                 text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '申请单名称',
    `creator_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `project_end_time`     datetime NULL DEFAULT NULL COMMENT '采购立项完成时间',
    `project_code`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购立项号',
    `file_name`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名称',
    `state`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请单状态',
    `type`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请单类型',
    `source`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请单来源',
    `money`                decimal(20, 2) NULL DEFAULT NULL COMMENT '采购申请金额（元）',
    `rate`                 decimal(20, 2) NULL DEFAULT NULL COMMENT '汇率',
    `estimated_begin_time` datetime NULL DEFAULT NULL COMMENT '预计开工时间',
    `warranty_level`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质保等级',
    `applicant_dept`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请部门',
    `applicant_user`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
    `currency`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '币种',
    `with_safety`          bit(1) NULL DEFAULT NULL COMMENT '与现场安全相关',
    `purchase_plan_code`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购计划号',
    `bk_dept`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归口部门',
    `bk_manage`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归口管理',
    `suggest_purchase_way` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '建议采购方式',
    `purchase_content`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '采购内容',
    `rec_sup_list`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推荐供应商名单',
    `rec_pt_sup_list`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推荐潜在供应商名单',
    `is_frame_contrac`     bit(1) NULL DEFAULT NULL COMMENT '是否有匹配的框架合同',
    `modify_time`          datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`          datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`               int(11) NOT NULL COMMENT '状态',
    `logic_status`         int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `contract_id`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '框架合同编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购申请主表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS ncf_form_purchase_request_attachment;
CREATE TABLE `ncf_form_purchase_request_attachment`
(
    `id`                   varchar(64) NOT NULL COMMENT '主键',
    `class_name`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`           varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`          datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`             varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`          datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`            varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`               varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`          varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`               varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`               int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`         int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `code`                 varchar(64)   DEFAULT NULL COMMENT '采购申请编号',
    `node`                 varchar(64)   DEFAULT NULL COMMENT '节点',
    `attachment_name`      varchar(255)  DEFAULT NULL COMMENT '文件名',
    `classification_level` varchar(64)   DEFAULT NULL COMMENT '文件密级',
    `secrecy_term`         varchar(64)   DEFAULT NULL COMMENT '保密期限',
    `document_type`        varchar(64)   DEFAULT NULL COMMENT '文档类型',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购申请附件';

-- ----------------------------
-- Table structure for ncf_form_purchase_request_detail
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_purchase_request_detail`;
CREATE TABLE `ncf_form_purchase_request_detail`
(
    `project_id`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行项目id',
    `internal_order`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内部订单',
    `item`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料',
    `item_group`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料组',
    `general_ledger_subject` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '总账科目',
    `asset`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资产',
    `required_quantity`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '需求数量',
    `unit`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
    `delivery_time`          datetime NULL DEFAULT NULL COMMENT '交货时间',
    `unit_price`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单价',
    `total_price`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '总价',
    `local_currency_amount`  decimal(20, 2) NULL DEFAULT NULL COMMENT '本位币金额',
    `cost_center`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '成本中心',
    `project_id_name`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目编号/名称',
    `wbs_id`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'WBS编号',
    `id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`            datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`                 int(11) NOT NULL COMMENT '状态',
    `logic_status`           int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购申请行项目表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS pms_ncf_form_purch_order;
CREATE TABLE `pms_ncf_form_purch_order`
(
    `order_number`                     varchar(256)   DEFAULT NULL COMMENT '订单编号',
    `contract_name`                    varchar(256)   DEFAULT NULL COMMENT '供应商名称',
    `return_amount`                    decimal(10, 2) DEFAULT NULL COMMENT '退货金额',
    `enterprise_name`                  varchar(256)   DEFAULT NULL COMMENT '企业名称',
    `order_placer`                     varchar(255)   DEFAULT NULL COMMENT '下单人',
    `order_time`                       datetime       DEFAULT NULL COMMENT '下单时间',
    `time_of_delivery`                 datetime       DEFAULT NULL COMMENT '订单最后一次交货时间',
    `time_of_last_receipt`             datetime       DEFAULT NULL COMMENT '订单最后一次确认收货时间',
    `used_time`                        datetime       DEFAULT NULL COMMENT '发货耗时',
    `reconciliation_application_time`  datetime       DEFAULT NULL COMMENT '对账申请时间',
    `reconciliation_confirmation_time` datetime       DEFAULT NULL COMMENT '对账确认时间',
    `application_for_invoicing_time`   datetime       DEFAULT NULL COMMENT '申请开票时间',
    `invoicing_time`                   datetime       DEFAULT NULL COMMENT '开票时间',
    `paid_time`                        datetime       DEFAULT NULL COMMENT '支付时间',
    `po_order_number`                  varchar(256)   DEFAULT NULL COMMENT 'PO订单号',
    `department`                       varchar(256)   DEFAULT NULL COMMENT '部门',
    `contract_id`                      varchar(256)   DEFAULT NULL COMMENT '供应商编码',
    `payment_manager`                  varchar(256)   DEFAULT NULL COMMENT '支付负责人',
    `acceptance_method`                varchar(256)   DEFAULT NULL COMMENT '验收方式',
    `order_total_amount`               decimal(10, 2) DEFAULT NULL COMMENT '订单总金额（含税含费）',
    `purch_req_doc_code`               varchar(256)   DEFAULT NULL COMMENT '采购申请号',
    `pr_project_id`                    varchar(256)   DEFAULT NULL COMMENT 'PR行项目',
    `order_state`                      varchar(256)   DEFAULT NULL COMMENT '订单状态',
    `commodity_background_category`    varchar(256)   DEFAULT NULL COMMENT '商品后台类目',
    `item_coding`                      varchar(256)   DEFAULT NULL COMMENT '单品编码',
    `item_name`                        varchar(256)   DEFAULT NULL COMMENT '单品名称',
    `e_commerce_order_number`          varchar(256)   DEFAULT NULL COMMENT '电商订单编号',
    `suborder_number`                  varchar(256)   DEFAULT NULL COMMENT '子订单号',
    `order_amount`                     decimal(10, 2) DEFAULT NULL COMMENT '订单金额',
    `amount_payable`                   decimal(10, 2) DEFAULT NULL COMMENT '应付金额',
    `settlement_status`                varchar(256)   DEFAULT NULL COMMENT '结算状态',
    `order_confirmation_time`          datetime       DEFAULT NULL COMMENT '订单确认时间',
    `order_approval_time`              datetime       DEFAULT NULL COMMENT '订单审批时间',
    `pr_company_name`                  varchar(256)   DEFAULT NULL COMMENT 'PR公司名称',
    `commerce_channel_order_number`    varchar(256)   DEFAULT NULL COMMENT '电商渠道订单号',
    `reconciler`                       varchar(256)   DEFAULT NULL COMMENT '对账人',
    `consignee`                        varchar(256)   DEFAULT NULL COMMENT '收货负责人',
    `receipt_reviewer`                 varchar(256)   DEFAULT NULL COMMENT '收货审核人',
    `settlement_method`                varchar(256)   DEFAULT NULL COMMENT '结算方式',
    `request_delivery_date`            datetime       DEFAULT NULL COMMENT '要求到货日期',
    `order_phone_number`               varchar(256)   DEFAULT NULL COMMENT '下单人电话',
    `id`                               varchar(64) NOT NULL COMMENT '主键',
    `class_name`                       varchar(64)    DEFAULT NULL COMMENT '类名',
    `creator_id`                       varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`                      datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`                         varchar(64)    DEFAULT NULL COMMENT '数据所有者',
    `create_time`                      datetime    NOT NULL COMMENT '创建时间',
    `modify_id`                        varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`                           varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`                      varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                           varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`                           int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`                     int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`                           varchar(64)    DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商城集采订单';

DROP TABLE IF EXISTS pms_ncf_purch_project_implementation;
CREATE TABLE `pms_ncf_purch_project_implementation`
(
    `process_name`                   varchar(256)   DEFAULT NULL COMMENT '流程名称',
    `promoter`                       varchar(256)   DEFAULT NULL COMMENT '发起人',
    `initiation_time`                datetime       DEFAULT NULL COMMENT '发起时间',
    `purch_req_ecp_code`             varchar(256)   DEFAULT NULL COMMENT '采购立项申请号',
    `purch_req_doc_code`             varchar(256)   DEFAULT NULL COMMENT '采购申请号',
    `applicant`                      varchar(256)   DEFAULT NULL COMMENT '申请人',
    `apply_department`               varchar(256)   DEFAULT NULL COMMENT '需求部门',
    `project_name`                   varchar(256)   DEFAULT NULL COMMENT '项目名称',
    `purch_req_end_time`             datetime       DEFAULT NULL COMMENT '采购申请完成时间',
    `purch_req_amount`               decimal(10, 2) DEFAULT NULL COMMENT '采购立项申请金额',
    `biz_respons`                    varchar(256)   DEFAULT NULL COMMENT '商务人员',
    `tech_respons`                   varchar(256)   DEFAULT NULL COMMENT '技术人员',
    `financial_staff`                varchar(256)   DEFAULT NULL COMMENT '财务人员',
    `others`                         varchar(256)   DEFAULT NULL COMMENT '其他人员',
    `is_collection_purch`            bit(1)         DEFAULT NULL COMMENT '是否属于应集采范围',
    `expected_contract_signing_time` varchar(256)   DEFAULT NULL COMMENT '期望合同签订时间',
    `purch_plan_number`              varchar(256)   DEFAULT NULL COMMENT '采购计划需求编号',
    `contract_type`                  varchar(256)   DEFAULT NULL COMMENT '合同类型',
    `contract_state`                 varchar(256)   DEFAULT NULL COMMENT '合同状态',
    `purch_type`                     varchar(256)   DEFAULT NULL COMMENT '采购类型',
    `purch_method`                   varchar(256)   DEFAULT NULL COMMENT '采购方式',
    `next_step_work_arrangement`     varchar(256)   DEFAULT NULL COMMENT '下一步工作安排',
    `concerns`                       varchar(256)   DEFAULT NULL COMMENT '关注事项',
    `used_time`                      varchar(256)   DEFAULT NULL COMMENT '已经耗时',
    `first_distribution`             datetime       DEFAULT NULL COMMENT '一级分发',
    `secondary_distribution`         datetime       DEFAULT NULL COMMENT '二级分发',
    `accept_confirmation`            datetime       DEFAULT NULL COMMENT '接受确认',
    `purch_start`                    datetime       DEFAULT NULL COMMENT '采购启动发起',
    `purch_start_approval`           datetime       DEFAULT NULL COMMENT '采购启动审批',
    `inq_issuance`                   datetime       DEFAULT NULL COMMENT '询价签发',
    `quote_end`                      datetime       DEFAULT NULL COMMENT '报价截止时间',
    `open_quote`                     datetime       DEFAULT NULL COMMENT '开启报价时间',
    `review_time`                    datetime       DEFAULT NULL COMMENT '评审时间',
    `review_out_time`                datetime       DEFAULT NULL COMMENT '公示发布时间',
    `upm_approval_in_progress`       datetime       DEFAULT NULL COMMENT 'UPM审批中',
    `upm_approval_complete`          datetime       DEFAULT NULL COMMENT 'UPM审批完成',
    `send_sap`                       datetime       DEFAULT NULL COMMENT '发送SAP',
    `id`                             varchar(64) NOT NULL COMMENT '主键',
    `class_name`                     varchar(64)    DEFAULT NULL COMMENT '类名',
    `creator_id`                     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`                    datetime       DEFAULT NULL COMMENT '修改时间',
    `owner_id`                       varchar(64)    DEFAULT NULL COMMENT '数据所有者',
    `create_time`                    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`                      varchar(64)    DEFAULT NULL COMMENT '修改人',
    `remark`                         varchar(1024)  DEFAULT NULL COMMENT '备注',
    `platform_id`                    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                         varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`                         int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status`                   int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number`                         varchar(64)    DEFAULT NULL COMMENT '编码',
    `contract_number`                varchar(64)    DEFAULT NULL COMMENT '合同编号',
    `contract_name`                  varchar(255)   DEFAULT NULL COMMENT '合同名称',
    `is_public_launch`               varchar(255)   DEFAULT NULL COMMENT '是否发布启动公示',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购项目实施表';

-- ----------------------------
-- Table structure for ncf_form_contract_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_info`;
CREATE TABLE `ncf_form_contract_info`
(
    `contract_number`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
    `is_fream`                   bit(10) NULL DEFAULT NULL COMMENT '是否框架合同',
    `purchase_applicant`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购申请号',
    `name`                       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
    `status_name`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同执行状态',
    `fream_residue_amount`       decimal(20, 2) NULL DEFAULT NULL COMMENT '框架合同剩余金额',
    `type`                       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同类型',
    `procurement_order_number`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购订单号',
    `project_code`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购立项号',
    `procurement_amount`         decimal(20, 2) NULL DEFAULT NULL COMMENT '采购立项金额',
    `supplier_name`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商',
    `factory_name`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工厂',
    `business_rsp_user`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务负责人',
    `change_money`               decimal(20, 2) NULL DEFAULT NULL COMMENT '变更金额',
    `change_percent`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更比例',
    `pay_money`                  decimal(20, 2) NULL DEFAULT NULL COMMENT '支付金额',
    `is_contract_terminate`      bit(10) NULL DEFAULT NULL COMMENT '是否合同终止',
    `claim_amount`               decimal(20, 2) NULL DEFAULT NULL COMMENT '索赔金额',
    `claim_percent`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '索赔比例',
    `terminate_amount`           decimal(20, 2) NULL DEFAULT NULL COMMENT '终止金额',
    `terminate_percent`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '终止比例',
    `fream_begin_time`           datetime NULL DEFAULT NULL COMMENT '框架开始时间',
    `fream_end_time`             datetime NULL DEFAULT NULL COMMENT '框架结束时间',
    `fream_used_amount`          decimal(20, 2) NULL DEFAULT NULL COMMENT '框架合同已使用金额',
    `is_fream_period`            bit(10) NULL DEFAULT NULL COMMENT '是否框架有效期内',
    `pay_percent`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付比例',
    `procurement_way`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购方式',
    `procurement_cycle`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购周期',
    `amount_saved`               decimal(20, 2) NULL DEFAULT NULL COMMENT '节约金额',
    `business_activity_type`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务活动类型',
    `end_procurement_way`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最终采购方式',
    `business_file_type`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商务文件类型',
    `estimated_start_time`       datetime NULL DEFAULT NULL COMMENT '预计合同开始日期',
    `estimated_end_time`         datetime NULL DEFAULT NULL COMMENT '预计合同结束日期',
    `pay_way`                    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款方式',
    `execution_status_name`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同履约状态',
    `object_type`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标的类别',
    `type_percent`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类别占比（%）',
    `approved_price`             decimal(20, 2) NULL DEFAULT NULL COMMENT '审批价格（RMB）',
    `final_price`                decimal(20, 2) NULL DEFAULT NULL COMMENT '最终价格（原币）',
    `actual_start_time`          datetime NULL DEFAULT NULL COMMENT '实际合同开始日期',
    `actual_end_time`            datetime NULL DEFAULT NULL COMMENT '实际合同结束日期',
    `estimated_delivery_time`    datetime NULL DEFAULT NULL COMMENT '预计合同交付日期',
    `acceptance_results`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '验收结果',
    `actual_acceptance_times`    datetime NULL DEFAULT NULL COMMENT '实际验收日期',
    `is_public_launch`           bit(10) NULL DEFAULT NULL COMMENT '是否发布启动公示',
    `id`                         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`                datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`                datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                     varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`                     int(11) NOT NULL COMMENT '状态',
    `logic_status`               int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `is_performance_bond`        bit(10) NULL DEFAULT NULL COMMENT '是否办理履约保证金',
    `margin_payment_method`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保证金支付方式',
    `security_deposit`           decimal(20, 2) NULL DEFAULT NULL COMMENT '保证金',
    `ia_calculation`             bit(10) NULL DEFAULT NULL COMMENT '是否参与计算	',
    `project_end_time`           datetime NULL DEFAULT NULL COMMENT '采购立项审批完成时间',
    `recommend_end_time`         datetime NULL DEFAULT NULL COMMENT '合同推荐审批完成时间',
    `is_biz_recommend`           bit(10) NULL DEFAULT NULL COMMENT '商务是否推荐供应商',
    `contract_name`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
    `is_calculation`             bit(10) NULL DEFAULT NULL COMMENT '是否参与计算',
    `price_model`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '价格模式',
    `subdivision`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部处',
    `is_fill_onetime_acceptance` bit(1) NULL DEFAULT NULL COMMENT '是否填写一次验收合格',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同主表信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_contract_change
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_contract_change`;
CREATE TABLE `ncf_form_contract_change`
(
    `change_id`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更编号',
    `change_title`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更标题',
    `change_type`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更类型',
    `change_request_date`         datetime NULL DEFAULT NULL COMMENT '变更申请日期',
    `this_change_amount`          decimal(20, 2) NULL DEFAULT NULL COMMENT '本次变更金额',
    `cumulative_change_amount`    decimal(20, 2) NULL DEFAULT NULL COMMENT '累计变更金额',
    `cumulative_change_rate`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '累计变更比率',
    `contact_amount_after_change` decimal(20, 2) NULL DEFAULT NULL COMMENT '变更后合同承诺总价（总目标值）',
    `id`                          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`                 datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`                 datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                      varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`                      int(11) NOT NULL COMMENT '状态',
    `logic_status`                int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `main_table_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表ID',
    `contract_name`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
    `contract_number`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同变更信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ncf_form_supplier_info
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_supplier_info`;
CREATE TABLE `ncf_form_supplier_info`
(
    `supplier_number`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商编码',
    `name`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
    `account`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商账号',
    `find_pass_eamil`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '找回密码邮箱',
    `sim_name`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商简称',
    `e_name`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商英文名称',
    `reg_country_region`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注册国家和地区',
    `province`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
    `city`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
    `county`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区/县',
    `reg_address`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注册地址',
    `ems_number`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮政编码',
    `url`                    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网址',
    `landline_phone`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '固定电话',
    `extension`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分机',
    `fax`                    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '传真',
    `organization_type`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组织类型',
    `legalrep`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法人代表',
    `company_nature`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业性质',
    `zgh_child`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中广核集团参股或控股公司',
    `capital_currency`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注册资金币种',
    `registered_capital`     decimal(20, 2) NULL DEFAULT NULL COMMENT '注册资本（万）',
    `parent_org`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上级主管单位',
    `major_shareholder`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要控股公司',
    `products_services`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可提供产品/服务文字描述',
    `company_overview`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司简介',
    `business_license_num`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业执照注册号/统一社会信用代码',
    `business_license_start` datetime NULL DEFAULT NULL COMMENT '营业执照有效期起',
    `business_license_end`   datetime NULL DEFAULT NULL COMMENT '营业执照有效期至',
    `operation_scope`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经营范围',
    `recommend_supplier`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否推荐供应商',
    `sector_name`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '板块名称',
    `supplier_level`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商级别',
    `qual_validity`          datetime NULL DEFAULT NULL COMMENT '资审有效期',
    `procurement_cat`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购品类',
    `proc_cat_code`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购品类编码',
    `delivery_scope_desc`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供货范围文本描述',
    `supplier_type`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商分类',
    `id`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`            datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                 varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`                 int(11) NOT NULL COMMENT '状态',
    `logic_status`           int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `supplier_category`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商类别',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商管理' ROW_FORMAT = Dynamic;



CREATE TABLE `ncf_form_supplier_restricted_record`
(
    `supplier_code`            varchar(256)  DEFAULT NULL COMMENT '供应商编码',
    `supplier_name`            varchar(256)  DEFAULT NULL COMMENT '供应商名称',
    `serial_number`            varchar(256)  DEFAULT NULL COMMENT '序号',
    `contract_number`          varchar(256)  DEFAULT NULL COMMENT '合同编号',
    `contract_name`            varchar(256)  DEFAULT NULL COMMENT '合同名称',
    `application_number`       varchar(256)  DEFAULT NULL COMMENT '申请编号',
    `restricted_type`          varchar(256)  DEFAULT NULL COMMENT '受限类型',
    `rectification_status`     varchar(256)  DEFAULT NULL COMMENT '整改状态',
    `blacklist_type`           varchar(256)  DEFAULT NULL COMMENT '黑名单类型',
    `project_name`             varchar(256)  DEFAULT NULL COMMENT '项目名称',
    `declaration_date`         datetime      DEFAULT NULL COMMENT '申报日期',
    `applicant`                varchar(256)  DEFAULT NULL COMMENT '申请人',
    `declaring_company`        varchar(256)  DEFAULT NULL COMMENT '申报公司',
    `content_description`      varchar(256)  DEFAULT NULL COMMENT '内容描述',
    `approval_completion_time` varchar(256)  DEFAULT NULL COMMENT '审批完成时间',
    `whether_thawed`           varchar(256)  DEFAULT NULL COMMENT '是否解冻',
    `restricted_scope`         varchar(256)  DEFAULT NULL COMMENT '受限范围',
    `group_sends_sap`          varchar(256)  DEFAULT NULL COMMENT '集团发送SAP',
    `id`                       varchar(64) NOT NULL COMMENT '主键',
    `class_name`               varchar(64)   DEFAULT NULL COMMENT '类名',
    `creator_id`               varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`              datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`                 varchar(64)   DEFAULT NULL COMMENT '数据所有者',
    `create_time`              datetime    NOT NULL COMMENT '创建时间',
    `modify_id`                varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`                   varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`              varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`                   varchar(64) NOT NULL COMMENT '业务组织ID',
    `status`                   int(11) NOT NULL COMMENT '状态',
    `logic_status`             int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                   varchar(64)   DEFAULT NULL COMMENT '编码',
    `main_table_id`            varchar(64)   DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='受限事件记录';

-- ----------------------------
-- Table structure for ncf_form_non_contract_proc
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_non_contract_proc`;
CREATE TABLE `ncf_form_non_contract_proc`
(
    `work_topic`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `process_name`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `initiation_time`       datetime NULL DEFAULT NULL COMMENT '发起时间',
    `initiator`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发起人',
    `claimant`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报销人',
    `apply_company_code`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请公司编码',
    `apply_company_name`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请公司名称',
    `apply_dept`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请部门',
    `expense_company_code`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '费用归属公司编码',
    `xpense_company_name`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '费用归属公司名称',
    `reimbursed_amount`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报销金额',
    `req_payment_time`      datetime NULL DEFAULT NULL COMMENT '要求付款时间',
    `currency`              varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '币种',
    `in_rmb`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '折合人民币',
    `apply_reason`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请原因',
    `expense_info`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '费用信息',
    `supplier_info`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商信息',
    `is_internal_tx`        bit(10) NULL DEFAULT NULL COMMENT '是否内部交易',
    `internal_tx_number`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内部交易号',
    `process_status`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程状态',
    `payment_way`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式',
    `project_category`      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项类别',
    `project_code`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项号',
    `project_name`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '立项名称',
    `bk_dept`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归口部门',
    `payment_amount`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付金额',
    `transaction_amount`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易金额',
    `actual_payment_amount` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实际支付金额',
    `apply_number`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请笔数',
    `reimbursement_amount`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报销金额',
    `id`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`           datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`           datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`                int(11) NOT NULL COMMENT '状态',
    `logic_status`          int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '无合同采购表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ncf_form_ncf_form_purch_order
-- ----------------------------
DROP TABLE IF EXISTS `ncf_form_ncf_form_purch_order`;
CREATE TABLE `ncf_form_ncf_form_purch_order`
(
    `order_number`                     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
    `contract_name`                    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
    `return_amount`                    decimal(10, 0) NULL DEFAULT NULL COMMENT '退货金额',
    `enterprise_name`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业名称',
    `order_placer`                     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `order_time`                       datetime NULL DEFAULT NULL COMMENT '下单时间',
    `time_of_delivery`                 datetime NULL DEFAULT NULL COMMENT '订单最后一次交货时间',
    `time_of_last_receipt`             datetime NULL DEFAULT NULL COMMENT '订单最后一次确认收货时间',
    `used_time`                        datetime NULL DEFAULT NULL COMMENT '发货耗时',
    `reconciliation_application_time`  datetime NULL DEFAULT NULL COMMENT '对账申请时间',
    `reconciliation_confirmation_time` datetime NULL DEFAULT NULL COMMENT '对账确认时间',
    `application_for_invoicing_time`   datetime NULL DEFAULT NULL COMMENT '申请开票时间',
    `invoicing_time`                   datetime NULL DEFAULT NULL COMMENT '开票时间',
    `paid_time`                        datetime NULL DEFAULT NULL COMMENT '支付时间',
    `po_order_umber`                   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'PO订单号',
    `department`                       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
    `contract_id`                      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商编码',
    `payment_manager`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付负责人',
    `acceptance_method`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '验收方式',
    `order_total_amount`               decimal(10, 0) NULL DEFAULT NULL COMMENT '订单总金额（含税含费）',
    `purch_req_doc_code`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购申请号',
    `pr_project_id`                    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'PR行项目',
    `order_state`                      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单状态',
    `commodity_background_category`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品后台类目',
    `item_coding`                      varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单品编码',
    `item_name`                        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单品名称',
    `e_commerce_order_number`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电商订单编号',
    `suborder_number`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子订单号',
    `order_amount`                     decimal(10, 0) NULL DEFAULT NULL COMMENT '订单金额',
    `amount_payable`                   decimal(10, 0) NULL DEFAULT NULL COMMENT '应付金额',
    `settlement_status`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算状态',
    `order_confirmation_time`          datetime NULL DEFAULT NULL COMMENT '订单确认时间',
    `order_approval_time`              datetime NULL DEFAULT NULL COMMENT '订单审批时间',
    `pr_company_name`                  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'PR公司名称',
    `commerce_channel_order_number`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电商渠道订单号',
    `reconciler`                       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对账人',
    `consignee`                        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货负责人',
    `receipt_reviewer`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货审核人',
    `settlement_method`                varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算方式',
    `request_delivery_date`            datetime NULL DEFAULT NULL COMMENT '要求到货日期',
    `order_phone_number`               varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下单人电话',
    `id`                               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `class_name`                       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
    `creator_id`                       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `modify_time`                      datetime NULL DEFAULT NULL COMMENT '修改时间',
    `owner_id`                         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据所有者',
    `create_time`                      datetime                                                     NOT NULL COMMENT '创建时间',
    `modify_id`                        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `remark`                           varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `platform_id`                      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台ID',
    `org_id`                           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务组织ID',
    `status`                           int(11) NOT NULL COMMENT '状态',
    `logic_status`                     int(11) NOT NULL COMMENT '逻辑删除字段',
    `number`                           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商城集采订单' ROW_FORMAT = Dynamic;
