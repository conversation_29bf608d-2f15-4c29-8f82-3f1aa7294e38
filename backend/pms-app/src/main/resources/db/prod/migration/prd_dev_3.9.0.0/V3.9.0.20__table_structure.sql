ALTER TABLE pmsx_project_initiation ADD project_person_id varchar(64) NULL COMMENT '项目责任人ID';
ALTER TABLE pmsx_project_initiation ADD project_assume_center_id varchar(64) NULL COMMENT '承担中心id';


DROP TABLE IF EXISTS `pmsx_req_clarification_record`;
CREATE TABLE `pmsx_req_clarification_record`  (
                                                  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                                  `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                                  `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                                  `modify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                                  `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                                  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                  `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                  `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                  `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台ID',
                                                  `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务组织Id',
                                                  `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                                  `logic_status` int(11) NULL DEFAULT NULL COMMENT '逻辑删除字段',
                                                  `clarification_theme` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '澄清主题',
                                                  `clarification_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '澄清类型',
                                                  `clarification_lot` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '澄清批次',
                                                  `clarification_context` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '澄清内容',
                                                  `is_update_time` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否更新报价时间',
                                                  `requirement_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '需求编号',
                                                  `release_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
                                                  `check_time` datetime NULL DEFAULT NULL COMMENT '查看时间',
                                                  `check_status` char(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '查看状态',
                                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '需求澄清记录' ROW_FORMAT = Dynamic;



INSERT INTO dme_class (`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl1805135860073684992', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_req_clarification_record', 'ReqClarificationRecord', 'w75p', NULL, 'pmsx', 'reqclarificationrecord', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '需求澄清记录', '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);

INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073600', 'gtjl1805135860073684992', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL, 1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073601', 'gtjl1805135860073684992', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073602', 'gtjl1805135860073684992', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073603', 'gtjl1805135860073684992', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073604', 'gtjl1805135860073684992', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073605', 'gtjl1805135860073684992', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073606', 'gtjl1805135860073684992', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073607', 'gtjl1805135860073684992', 'remark', 'Varchar', 1024, NULL, 'remark', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073608', 'gtjl1805135860073684992', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073609', 'gtjl1805135860073684992', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073610', 'gtjl1805135860073684992', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805135860082073611', 'gtjl1805135860073684992', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:08:22', '314j1000000000000000000', '2024-06-24 15:08:22', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805136570764943360', 'gtjl1805135860073684992', 'clarificationTheme', 'Varchar', 64, NULL, 'clarificationtheme', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:11:11', '314j1000000000000000000', '2024-06-24 15:11:11', '澄清主题', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805136729808756736', 'gtjl1805135860073684992', 'clarificationType', 'Varchar', 64, NULL, 'clarificationtype', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:11:49', '314j1000000000000000000', '2024-06-24 15:11:49', '澄清类型', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805136895123054592', 'gtjl1805135860073684992', 'clarificationLot', 'Varchar', 64, NULL, 'clarificationlot', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:12:28', '314j1000000000000000000', '2024-06-24 15:12:28', '澄清批次', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805137370593550336', 'gtjl1805135860073684992', 'clarificationContext', 'Varchar', 4096, NULL, 'clarificationcontext', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:14:22', '314j1000000000000000000', '2024-06-24 15:14:22', '澄清内容', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805137527431159808', 'gtjl1805135860073684992', 'IsUpdateTime', 'Varchar', 64, NULL, 'isupdatetime', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:14:59', '314j1000000000000000000', '2024-06-24 15:14:59', '是否更新报价时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805137750542966784', 'gtjl1805135860073684992', 'requirementId', 'Varchar', 64, NULL, 'requirementid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:15:52', '314j1000000000000000000', '2024-06-24 15:15:52', '需求编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805138025383124992', 'gtjl1805135860073684992', 'ReleaseTime', 'DateTime', 64, NULL, 'releasetime', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:16:58', '314j1000000000000000000', '2024-06-24 15:16:58', '发布时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805138193201422336', 'gtjl1805135860073684992', 'checkTime', 'DateTime', 64, NULL, 'checktime', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:17:38', '314j1000000000000000000', '2024-06-24 15:17:38', '查看时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO dme_class_attribute (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1805138313594724352', 'gtjl1805135860073684992', 'checkStatus', 'Char', 64, NULL, 'checkstatus', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-24 15:18:06', '314j1000000000000000000', '2024-06-24 15:18:06', '查看状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
