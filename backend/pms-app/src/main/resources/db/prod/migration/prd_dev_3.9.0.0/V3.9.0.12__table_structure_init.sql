--  sql
alter table pms_job_post_requirement add column certificate_id varchar(64)  COMMENT '证书ID';



alter TABLE  pms_job_post_library MODIFY   authorization_time  int (4)  COMMENT '授权时间月份';


alter TABLE pmsx_person_job_post_authorize   add column source_id varchar(64)  COMMENT '岗位授权ID';
alter TABLE pmsx_person_job_post_Equ         add column source_id varchar(64)  COMMENT '岗位授权ID';

alter TABLE pmsx_person_train_equ_record     add column source_id varchar(64)  COMMENT '培训ID';
alter TABLE pmsx_person_train_info_record    add column source_id varchar(64)  COMMENT '培训ID';

alter TABLE pmsx_person_train_equ_record    add column form_train_number varchar(64)  COMMENT '被等效的培训编号';



alter TABLE pmsx_person_mange add column contact_user_code VARCHAR(64) COMMENT '接口人编号';
alter TABLE pmsx_person_mange MODIFY contact_dept VARCHAR(64) COMMENT '接口部门id';
alter TABLE pmsx_person_mange add column contact_dept_code VARCHAR(64) COMMENT '接口部门编号';
alter TABLE pmsx_person_mange MODIFY contact_office VARCHAR(64) COMMENT '接口科室id';
alter TABLE pmsx_person_mange add column contact_office_code VARCHAR(64)  COMMENT'接口科室编号';
alter TABLE pmsx_person_mange add column job_taboos_name VARCHAR(128)  COMMENT '职业禁忌症名称';
alter TABLE pmsx_person_mange MODIFY design_ctrl_zone_op bit(1)  COMMENT '涉及控制区作业: 是/否 true/false';
alter TABLE pmsx_person_mange MODIFY work_res_person bit(1)  COMMENT '工作负责人：是/否 true/false';
alter TABLE pmsx_person_mange MODIFY preparation_engineer bit(1)  COMMENT '准备工程师：是/否 true/false';
alter TABLE pmsx_person_mange MODIFY qc_str  bit(1)  COMMENT 'QC：是/否 true/false';
alter TABLE pmsx_person_mange MODIFY fu_ti_saf_off  bit(1)  COMMENT '专职安全员：是/否 true/false';
alter TABLE pmsx_person_mange MODIFY pa_ti_saf_off  bit(1)  COMMENT '兼职安全员：是/否 true/false';

alter TABLE pmsx_person_mange MODIFY newcomer  bit(1)  COMMENT '新人：是/否 true/false';
alter TABLE pmsx_person_mange add column newcomer_match_person_code  VARCHAR(64)  COMMENT '新人对口人编号';
alter TABLE pmsx_person_mange MODIFY is_base_permanent  bit(1)  COMMENT '是否常驻基地：是/否 true/false';



alter Table pmsx_person_manage_ledger add COLUMN contact_dept_code VARCHAR(64) COMMENT '接口部门编号';
alter Table pmsx_person_manage_ledger add COLUMN contact_dept_name VARCHAR(64) COMMENT '接口部门名称';
alter Table pmsx_person_manage_ledger add COLUMN contact_office_code VARCHAR(64) COMMENT '接口科室编号';
alter Table pmsx_person_manage_ledger add COLUMN contact_office_name VARCHAR(64) COMMENT '接口科室编号';

alter Table pmsx_person_manage_ledger add COLUMN contact_user_name VARCHAR(64) COMMENT '接口人名称';
alter Table pmsx_person_manage_ledger add COLUMN contact_user_code VARCHAR(64) COMMENT '接口人编号';
alter Table pmsx_person_manage_ledger add COLUMN work_type_name VARCHAR(64) COMMENT '大修/日常';


-- alter Table pmsx_person_manage_ledger add COLUMN base_place_project_name VARCHAR(64) COMMENT '接口部门名称';

alter Table pmsx_person_manage_ledger add COLUMN base_place_project_name VARCHAR(128) COMMENT '基地内承担的主要项目：项目名称';
alter Table pmsx_person_manage_ledger add COLUMN job_taboos_name VARCHAR(128) COMMENT '职业禁忌症名称';
alter Table pmsx_person_manage_ledger add COLUMN newcomer_match_person_name VARCHAR(128) COMMENT '新人对口人名称';
alter Table pmsx_person_manage_ledger add COLUMN newcomer_match_person_code VARCHAR(64) COMMENT '新人对口人编号';


alter Table pmsx_person_manage_ledger add COLUMN design_ctrl_zone_op bit(1) COMMENT '涉及控制区作业: 是/否 true/false';

alter TABLE pmsx_person_manage_ledger MODIFY work_res_person bit(1)  COMMENT '工作负责人：是/否 true/false';
alter TABLE pmsx_person_manage_ledger MODIFY preparation_engineer bit(1)  COMMENT '准备工程师：是/否 true/false';
alter TABLE pmsx_person_manage_ledger MODIFY qc_str  bit(1)  COMMENT 'QC：是/否 true/false';
alter TABLE pmsx_person_manage_ledger MODIFY fu_ti_saf_off  bit(1)  COMMENT '专职安全员：是/否 true/false';
alter TABLE pmsx_person_manage_ledger MODIFY pa_ti_saf_off  bit(1)  COMMENT '兼职安全员：是/否 true/false';
alter TABLE pmsx_person_manage_ledger MODIFY is_base_permanent  bit(1)  COMMENT '是否常驻基地：是/否 true/false';
alter TABLE pmsx_person_manage_ledger MODIFY newcomer  bit(1)  COMMENT '新人：是/否 true/false';