<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.IncomePlanExecutionTrackMapper">

    <select id="getDatas" resultType="com.chinasie.orion.domain.vo.IncomePlanExecutionTrackVO">
		SELECT se.* from (
		SELECT
		d.id,
		p.work_topics AS workTopics,
		DATE_FORMAT( d.estimate_invoice_date, '%Y-%m' ) AS estimateInvoiceMonth,
		d.expertise_center AS expertiseCenter,
		d.expertise_station AS expertiseStation,
		c.`status` AS contractStatus,
		c.contract_type AS contractType,
		d.contract_number AS contractNumber,
		d.number AS incomePlanDataNumber,
		d.billing_company AS billingCompany,
		d.party_A_dept_id AS partyADeptId,
		d.contract_id AS contractId,
		d.contract_name AS contractName,
		d.milestone_id AS milestoneId,
		d.milestone_name AS milestoneName,
		d.project_number AS projectNumber,
		d.project_id AS projectId,
		d.project_rsp_user_id AS projectRspUserId,
		d.lock_status AS lockStatus,
		m.plan_accept_date AS planAcceptDate,
		m.actual_accept_date AS actualAcceptDate,
		c.contract_amt as contractAmt,
		d.milestone_amt AS milestoneAmt,
		d.tax_rate AS taxRate,
		d.industry,
		CASE
		WHEN m.actual_accept_date != null THEN
		'完成验收' ELSE '未完成验收'
		END AS milestoneStatus,
		d.income_confirm_type AS incomeConfirmType,
		d.estimate_invoice_date AS estimateInvoiceDate,
		d.inv_amt as invAmt,
		d.cancel_inv_amt as cancelInvAmt,
		d.write_off_amt as writeOffAmt,
		d.advance_pay_income_amt as advancePayIncomeAmt,
		d.estimate_amt as estimateAmt,
		d.risk_link as riskLink,
		d.risk_type as riskType,
		d.other_notes as otherNotes,
		m.contract_number as associationContractNumber,
		d.status as incomePlanDataStatus,
		d.internal_external as internalExternal,
		CASE
		WHEN m.actual_accept_date != null THEN
		m.actual_accept_date ELSE m.expect_accept_date
		END AS acceptDate,
		CASE
		WHEN m.actual_accept_date != null THEN
		m.actual_milestone_amt ELSE m.except_acceptance_amt
		END AS acceptAmt,
		d.income_plan_amt AS incomePlanAmt,
		m.tech_rsp_user AS techRspUser,
		m.bus_rsp_user AS busRspUser,
		ROUND(pvn.confirm_revenue_amount,2) AS recognitionRevenueAmt,
		ROUND(pvn.reverse_amount,2) AS writeOffFisAmt,
		ROUND((d.income_plan_amt-pvn.confirm_revenue_amount+pvn.reverse_amount),2) as noRecognitionRevenueAmt,
		pvn.post_date as postDate,
		pvn.voucher_num as certificateNumber,
		rpn.six_num as sixCode,
		CASE
		WHEN d.status = 121 THEN rpn.process_theme_upm
		WHEN d.status in (120,160) THEN rpn.process_theme_fis
		ELSE "" END
		AS processTopics,
		CASE
		WHEN d.status = 121 THEN rpn.current_stage_upm
		WHEN d.status in (120,160) THEN rpn.current_stage_fis
		ELSE "" END
		AS currentSession,
		CASE
		WHEN d.status = 121 THEN rpn.current_stage_executor_upm
		WHEN d.status in (120,160) THEN rpn.current_stage_executor_fis
		ELSE "" END
		AS currentLinkExecutor,
		m.milestone_amt as planAcceptAmt,
		c.client_project_nature as powerProjectPlant,
		ifNull(ci.cus_name,d.party_A_dept_id_name) as partyADeptIdName,
		ci.group_info as partASecondDept,
		ci.tax_id_code as taxIdCode
		FROM
		pmsx_income_plan p
		JOIN pmsx_income_plan_data d ON p.id = d.income_plan_id
		LEFT JOIN pms_market_contract c ON d.contract_id = c.id
		LEFT JOIN pms_contract_milestone m ON d.milestone_id = m.id
		left join
		(select revenue_plan_num , DATE_FORMAT( max(posting_date), '%Y-%m' ) posting_date, max(posting_date) as post_date,
		sum(COALESCE (confirm_revenue_amount,0))+ max(reverse_amount) confirm_revenue_amount,
		max(reverse_amount) reverse_amount,
		GROUP_CONCAT(voucher_num ORDER BY voucher_num SEPARATOR '、') voucher_num
		from pmsx_voucher_num
		where logic_status = 1
		group by revenue_plan_num ) pvn on pvn.revenue_plan_num = d.`number` and pvn.posting_date = p.work_topics
		left join pmsx_revenue_plan_num rpn on rpn.revenue_plan_num = d.`number`
		LEFT JOIN pms_customer_info ci ON ci.id = d.party_A_dept_id
		<if test="param.expertiseCenterName != null">
			LEFT JOIN pmi_dept cen ON cen.id = d.expertise_center
		</if>
		<if test="param.expertiseStationName != null">
			LEFT JOIN pmi_dept sta ON sta.id = d.expertise_station
		</if>
		<if test="param.projectRspUserName != null">
			LEFT JOIN pmi_user u ON u.id = d.project_rsp_user_id
		</if>
		WHERE d.logic_status = 1
		AND ( d.number IS NOT NULL and d.number != "未生成编号" )
		and p.income_plan_type = d.data_version
		<if test="param.incomePlanDataNumber != null ">
			AND d.number like CONCAT('%', #{param.incomePlanDataNumber}, '%')
		</if>
		<if test="param.expertiseCenterName != null">
			AND cen.name like CONCAT('%', #{param.expertiseCenterName}, '%')
		</if>
		<if test="param.expertiseStationName != null">
			AND sta.name like  CONCAT('%', #{param.expertiseStationName}, '%')
		</if>
		<if test="param.projectRspUserName != null">
			and u. name like CONCAT('%', #{param.projectRspUserName}, '%')
		</if>

		<if test="param.workTopics != null">
			and p.work_topics = #{param.workTopics}
		</if>

		<if test="param.status != null">
			and d.status = #{param.status}
		</if>

		<if test="param.incomePlanDataStatus != null and param.incomePlanDataStatus !=101">
			and d.status = #{param.incomePlanDataStatus}
		</if>
		<if test="param.incomePlanDataStatus != null and param.incomePlanDataStatus == 101">
			and d.status in (111,110,130)
		</if>

		<if test="param.contractName != null ">
			AND d.contract_name like CONCAT('%', #{param.contractName}, '%')
		</if>

		<if test="param.internalExternal != null ">
			AND d.internal_external =  #{param.internalExternal}
		</if>

		<if test="param.incomeConfirmType != null ">
			AND d.income_confirm_type =   #{param.incomeConfirmType}
		</if>

		<if test="param.sixCode != null ">
			AND rpn.six_num like CONCAT('%', #{param.sixCode}, '%')
		</if>
		<if test="param.certificateNumber != null ">
			AND pvn.voucher_num like CONCAT('%', #{param.certificateNumber}, '%')
		</if>

		<if test="param.isPermission == false ">
			and (d.project_rsp_user_id = #{param.userId}
			<if test="param.centers.size >0" >
				or d.expertise_center in
				<foreach collection="param.centers" item="center" open="(" close=")" separator=",">
					#{center}
				</foreach>
			</if>
			<if test="param.stations.size >0" >
				or d.expertise_station in
				<foreach collection="param.stations" item="station" open="(" close=")" separator=",">
					#{station}
				</foreach>
			</if>
			)
		</if>
		) se where
		1=1
		<if test="param.currentSession != null ">
			AND se.currentSession like CONCAT('%', #{param.currentSession}, '%')
		</if>
	</select>


    <select id="getExportDatas" resultType="com.chinasie.orion.domain.vo.IncomePlanExecutionTrackVO">
		SELECT se.* from (
		SELECT
		d.id,
		p.work_topics AS workTopics,
		DATE_FORMAT( d.estimate_invoice_date, '%Y-%m' ) AS estimateInvoiceMonth,
		d.expertise_center AS expertiseCenter,
		d.expertise_station AS expertiseStation,
		c.`status` AS contractStatus,
		c.contract_type AS contractType,
		d.contract_number AS contractNumber,
		d.number AS incomePlanDataNumber,
		d.billing_company AS billingCompany,
		d.party_A_dept_id AS partyADeptId,
		d.contract_id AS contractId,
		d.contract_name AS contractName,
		d.milestone_id AS milestoneId,
		d.milestone_name AS milestoneName,
		d.project_number AS projectNumber,
		d.project_id AS projectId,
		d.project_rsp_user_id AS projectRspUserId,
		d.lock_status AS lockStatus,
		m.plan_accept_date AS planAcceptDate,
		m.actual_accept_date AS actualAcceptDate,
		c.contract_amt as contractAmt,
		d.milestone_amt AS milestoneAmt,
		d.tax_rate AS taxRate,
		CASE
		WHEN m.actual_accept_date != null THEN
		'完成验收' ELSE '未完成验收'
		END AS milestoneStatus,
		d.income_confirm_type AS incomeConfirmType,
		d.estimate_invoice_date  AS estimateInvoiceDate,
		d.inv_amt as invAmt,
		d.cancel_inv_amt as cancelInvAmt,
		d.write_off_amt as writeOffAmt,
		d.advance_pay_income_amt as advancePayIncomeAmt,
		d.estimate_amt as estimateAmt,
		d.risk_link as riskLink,
		d.risk_type as riskType,
		d.other_notes as otherNotes,
		m.contract_number as associationContractNumber,
		d.status as incomePlanDataStatus,
		d.internal_external as  internalExternal,
		d.industry,
		CASE
		WHEN m.actual_accept_date != null THEN
		m.actual_accept_date ELSE m.expect_accept_date
		END AS acceptDate,
		CASE
		WHEN m.actual_accept_date != null THEN
		m.actual_milestone_amt ELSE m.except_acceptance_amt
		END AS acceptAmt,
		d.income_plan_amt AS incomePlanAmt,
		m.tech_rsp_user AS techRspUser,
		m.bus_rsp_user AS busRspUser,
		ROUND(pvn.confirm_revenue_amount,2) AS recognitionRevenueAmt,
		ROUND(pvn.reverse_amount,2) AS writeOffFisAmt,
		ROUND((d.income_plan_amt-pvn.confirm_revenue_amount+pvn.reverse_amount),2) as noRecognitionRevenueAmt,
		pvn.post_date as postDate,
		pvn.voucher_num as certificateNumber,
		rpn.six_num as sixCode,
		CASE
		WHEN d.status = 121 THEN rpn.process_theme_upm
		WHEN d.status in (120,160) THEN rpn.process_theme_fis
		ELSE "" END
		AS processTopics,
		CASE
		WHEN d.status = 121 THEN rpn.current_stage_upm
		WHEN d.status  in (120,160) THEN rpn.current_stage_fis
		ELSE "" END
		AS currentSession,
		CASE
		WHEN d.status = 121 THEN rpn.current_stage_executor_upm
		WHEN d.status  in (120,160) THEN rpn.current_stage_executor_fis
		ELSE "" END
		AS currentLinkExecutor,
		m.milestone_amt as planAcceptAmt,
		c.client_project_nature as powerProjectPlant,
		ifNull(ci.cus_name,d.party_A_dept_id_name) as  partyADeptIdName,
		ci.group_info as partASecondDept,
		ci.tax_id_code as taxIdCode
		FROM
		pmsx_income_plan p
		JOIN pmsx_income_plan_data d ON p.id = d.income_plan_id
		LEFT JOIN pms_market_contract c ON d.contract_id = c.id
		LEFT JOIN pms_contract_milestone m ON d.milestone_id = m.id
		left join
		(select revenue_plan_num , DATE_FORMAT( max(posting_date), '%Y-%m' ) posting_date, max(posting_date) as post_date,
		sum(COALESCE (confirm_revenue_amount,0))+ max(reverse_amount) confirm_revenue_amount,
		max(reverse_amount) reverse_amount,
		GROUP_CONCAT(voucher_num ORDER BY voucher_num SEPARATOR '、') voucher_num
		from pmsx_voucher_num
		where logic_status = 1
		group by revenue_plan_num ) pvn on pvn.revenue_plan_num = d.`number` and pvn.posting_date = p.work_topics
		left join pmsx_revenue_plan_num rpn on rpn.revenue_plan_num = d.`number`
		LEFT JOIN pms_customer_info ci ON ci.id  = d.party_A_dept_id
		<if test="param.expertiseCenterName != null">
			LEFT JOIN pmi_dept cen ON cen.id  = d.expertise_center
		</if>
		<if test="param.expertiseStationName != null">
			LEFT JOIN pmi_dept sta ON sta.id  = d.expertise_station
		</if>
		<if test="param.projectRspUserName != null">
			LEFT JOIN pmi_user u ON u.id  = d.project_rsp_user_id
		</if>
		WHERE
		 d.logic_status = 1
		AND ( d.number IS NOT NULL and d.number != "未生成编号" )
		and p.income_plan_type = d.data_version
	<if test="param.ids != null and param.ids.size() > 0">
			AND d.id IN
			<foreach item="id" collection="param.ids" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="param.ids = null ">
			<if test="param.incomePlanDataNumber != null ">
				AND d.number like CONCAT('%', #{param.incomePlanDataNumber}, '%')
			</if>
			<if test="param.contractName != null ">
				AND d.contract_name like CONCAT('%', #{param.contractName}, '%')
			</if>

			<if test="param.expertiseCenterName != null">
				AND cen.name like CONCAT('%', #{param.expertiseCenterName}, '%')
			</if>
			<if test="param.expertiseStationName != null">
				AND sta.name like  CONCAT('%', #{param.expertiseStationName}, '%')
			</if>
			<if test="param.projectRspUserName != null">
				and u. name like CONCAT('%', #{param.projectRspUserName}, '%')
			</if>

			<if test="param.workTopics != null">
				and p.work_topics = #{param.workTopics}
			</if>

			<if test="param.status != null">
				and d.status = #{param.status}
			</if>

			<if test="param.internalExternal != null ">
				AND d.internal_external =  #{param.internalExternal}
			</if>

			<if test="param.incomeConfirmType != null ">
				AND d.income_confirm_type like  CONCAT('%', #{param.incomeConfirmType}, '%')
			</if>
			<if test="param.incomePlanDataStatus != null and param.incomePlanDataStatus = 101">
				and d.status in (111,110,130)
			</if>
			<if test="param.incomePlanDataStatus != null and param.incomePlanDataStatus !=101">
				and d.status = #{param.incomePlanDataStatus}
			</if>

			<if test="param.sixCode != null ">
				AND rpn.six_num like CONCAT('%', #{param.sixCode}, '%')
			</if>
			<if test="param.certificateNumber != null ">
				AND pvn.voucher_num like CONCAT('%', #{param.certificateNumber}, '%')
			</if>
			<if test="param.isPermission == false ">
				and (d.project_rsp_user_id = #{param.userId}
				<if test="param.centers.size >0" >
					or d.expertise_center in
					<foreach collection="param.centers" item="center" open="(" close=")" separator=",">
						#{center}
					</foreach>
				</if>
				<if test="param.stations.size >0" >
					or d.expertise_station in
					<foreach collection="param.stations" item="station" open="(" close=")" separator=",">
						#{station}
					</foreach>
				</if>
				)
			</if>

		</if>
		) se where
		1=1
		<if test="param.ids == null ">
		<if test="param.currentSession != null ">
			AND se.currentSession like CONCAT('%', #{param.currentSession}, '%')
		</if>
		</if>
    </select>
</mapper>