<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.MajorRepairOrgMapper">


    <select id="listByRepairRoundAndParenId" resultType="com.chinasie.orion.domain.vo.MajorRepairOrgSimpleVO">
        select id,name,code,level,type,parent_id as parentId,level_type as levelType,rsp_user_id as rspUserId
             ,rsp_user_name as rspUserName,rsp_user_code as rspUserCode,begin_time as beginTime,end_time as endTime
             ,actual_begin_time as actualBeginTime,actual_end_time as actualEndTime,chain_path as chainPath
             ,repair_round as repairRound,sort,class_name ,chain_path as chainPath from pmsx_major_repair_org
        where  logic_status = 1
        <if test="repairRound != null and repairRound != ''">
            and repair_round = #{repairRound}
        </if>
        <if test="parentId != null and parentId != ''">
            and (parent_id = #{parentId} or id = #{parentId})
        </if>
    </select>


    <select id="listSimpleByOrgId" resultType="com.chinasie.orion.domain.dto.MajorRepairOrgJobSimpleDTO">
        select jb.id,pj.begin_time as beginTime,pj.end_time as endTime,pj.actual_begin_time as actualBeginTime,pj.actual_end_time as actualEndTime
        from pmsx_relation_org_to_job jb
            left join  pmsx_job_manage pj on pj.number = jb.job_number
        where jb.logic_status = 1 and jb.repair_org_id = #{repairOrgId}
        union
        select pm.id,pm.begin_time as beginTime,pm.end_time as endTime,pm.actual_begin_time
        as actualBeginTime,pm.actual_end_time as actualEndTime from pmsx_major_repair_org pm
        where pm.logic_status = 1 and pm.parent_id = #{repairOrgId}

    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">
        select max(sort) from pmsx_major_repair_org where parent_id = #{parentId}
    </select>
</mapper>
