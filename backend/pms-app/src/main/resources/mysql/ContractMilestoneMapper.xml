<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ContractMilestoneMapper">
    <update id="setPlanAcceptDateToNull">
        update pms_contract_milestone set plan_accept_date = NULL where id = #{id}
    </update>
    <update id="setExpectAcceptDateToNull">
        update pms_contract_milestone set expect_accept_date = NULL where id = #{id}
    </update>


    <select id="selectListContract" resultType="com.chinasie.orion.domain.entity.ContractMilestone">
(select  *  from pms_contract_milestone
where contract_id =#{contractId} and milestone_name like concat('%',#{milestoneName},'%')  and logic_status=1)
union
(select  * from pms_contract_milestone
where parent_id in(select  id  from pms_contract_milestone where contract_id =#{contractId} and milestone_name like concat('%',#{milestoneName},'%') and logic_status=1));

    </select>
</mapper>
