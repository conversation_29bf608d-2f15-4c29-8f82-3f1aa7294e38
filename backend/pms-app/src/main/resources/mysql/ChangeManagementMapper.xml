<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ChangeManagementRepository">

    <resultMap id="indexData" type="com.chinasie.orion.search.common.domain.IndexData">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="fetchIndexData" resultMap="indexData">
        SELECT m.id, m.create_time
        FROM pms_change_management m
        LEFT OUTER JOIN search_index_record r ON r.data_id = m.id
        WHERE <![CDATA[m.create_time >= #{lastIndexTime}]]> AND r.data_id IS NULL
        ORDER BY m.create_time
        LIMIT #{limitSize}
    </select>


</mapper>
