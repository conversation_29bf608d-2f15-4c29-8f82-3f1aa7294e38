<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ProjectSchemeRepository">

    <resultMap id="indexData" type="com.chinasie.orion.search.common.domain.IndexData">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="fetchIndexData" resultMap="indexData">
        SELECT m.id, m.create_time
        FROM pms_project_scheme m
        LEFT OUTER JOIN search_index_record r ON r.data_id = m.id
        WHERE <![CDATA[m.create_time >= #{lastIndexTime}]]> AND r.data_id IS NULL
        ORDER BY m.create_time
        LIMIT #{limitSize}
    </select>

    <select id="selectListByProjectId" resultType="com.chinasie.orion.domain.dto.ProjectSchemeSimpleDTO">
        SELECT
            `id`,
            `sort`,
            `name`,
            `icon`,
            `project_id`,
            `project_number`,
            `level`,
            `parent_id`,
            `node_type`,
            `type`,
            `status`,
            `rsp_sub_dept`,
            `rsp_user`,
            `begin_time`,
            `end_time`,
            `circumstance`,
            `actual_end_time`,
            `actual_begin_time`,
            `top_sort`,
            `duration_days`,
            `is_work_job`,
            `enforce_type`,
            `enforce_base_place`,
            `enforce_scope`,
            `work_content`,
            `repair_round`,
            `is_carry_materials`,
            `plan_active`,
            `rsp_user_code`,
            `participant_users`,
            `remark`,
            `platform_id`,
            `creator_id`,
            `org_id`,
            `scheme_number`,
            `parent_chain`,
            `is_work`,
            `urgency`,
            contract_milestone_id,
            issue_time
        FROM
            pms_project_scheme
        WHERE
            project_id = #{projectId}
          and logic_status=1

    </select>
    <select id="selectListMemberByRepairRound" resultType="java.lang.String">
        select user_id from pmsx_major_repair_plan_member where major_repair_turn = #{repairRound} and logic_status = 1
    </select>
</mapper>
