<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ProjectRepository">

    <resultMap id="indexData" type="com.chinasie.orion.search.common.domain.IndexData">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="fetchIndexData" resultMap="indexData">
        SELECT m.id, m.create_time
        FROM pms_project m
        LEFT OUTER JOIN search_index_record r ON r.data_id = m.id
        WHERE <![CDATA[m.create_time >= #{lastIndexTime}]]> AND r.data_id IS NULL
        ORDER BY m.create_time
        LIMIT #{limitSize}
    </select>
    <select id="getProjectBasicInfoList" resultType="com.chinasie.orion.domain.vo.ProjectSimpleBasicVO">
        select p1.number as projectNumber,p1.name as projectName,t1.number as contractNumber,t1.name as contractName
        from pms_project AS p1
                 LEFT JOIN pmsx_project_initiation AS t2 ON p1.number = t2.project_number and t2.logic_status = 1
                 LEFT JOIN pms_market_contract AS t1  ON t2.contract_numbers = t1.number and t1.logic_status = 1
        where p1.logic_status = 1
    </select>


</mapper>
