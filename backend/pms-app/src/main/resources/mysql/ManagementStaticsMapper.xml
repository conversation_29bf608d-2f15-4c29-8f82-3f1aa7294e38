<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.management.repository.ManagementStaticsMapper">


    <select id="selectMilestoneCompletion" resultType="java.math.BigDecimal">
        SELECT
        SUM( pmcma.acceptance_ratio * milestone.milestone_amt / 100 ) / 10000
        FROM
        pms_market_contract_milestone_acceptance pmcma
        INNER JOIN pms_contract_milestone milestone ON milestone.id = pmcma.milestone_id
        INNER JOIN pms_market_contract contract ON contract.id = milestone.contract_id
        -- INNER JOIN pms_requirement_mangement pr ON contract.requirement_id = pr.id
        INNER JOIN pms_customer_info pc ON contract.cust_person_id = pc.id

        WHERE
        contract.`status` = '130' AND
        pmcma.actual_accept_date BETWEEN #{startTime}
        AND #{endTime}
        <if test="deptId != null and deptId != ''">
            AND contract.tech_rsp_dept = #{deptId}
        </if>
        <if test="type != null and type != ''">
            AND pc.ywsrlx = #{type}
        </if>
    </select>
    <select id="selectMilestonePlan" resultType="java.math.BigDecimal">
        SELECT
        SUM(milestone.milestone_amt) /10000
        FROM
        pms_contract_milestone milestone
        INNER JOIN pms_market_contract contract ON milestone.contract_id = contract.id
        -- INNER JOIN pms_requirement_mangement req ON req.id = contract.requirement_id
        INNER JOIN pms_customer_info pc ON contract.cust_person_id = pc.id
        WHERE
        contract.`status` = '130' AND
        milestone.plan_accept_date BETWEEN #{startTime}
        AND #{endTime}
        <if test="deptId != null and deptId != ''">
            AND contract.tech_rsp_dept = #{deptId}
        </if>
        <if test="type != null and type != ''">
            AND pc.ywsrlx = #{type}
        </if>
    </select>
    <select id="selectContractNum" resultType="java.lang.Integer">
        SELECT
        count( pmc.id )
        FROM
        pms_market_contract pmc
        -- INNER JOIN pms_requirement_mangement pr ON pmc.requirement_id = pr.id
        INNER JOIN pms_customer_info pc ON pmc.cust_person_id = pc.id
        WHERE
            pmc.logic_status = 1
        -- AND pr.logic_status = 1
        AND pc.logic_status = 1
        AND pmc.create_time BETWEEN #{startTime} AND #{endTime}
        AND pmc.`status`  IN('121','130','160','140')
        <if test="deptId != null and deptId != ''">
            AND pmc.tech_rsp_dept = #{deptId}
        </if>
        <if test="type != null and type != ''">
            AND pc.ywsrlx = #{type}
        </if>
    </select>

    <select id="fulfuilContractNum" resultType="java.lang.Integer">
        SELECT
        count( pmc.id )
        FROM
        pms_market_contract pmc
        INNER JOIN pms_market_contract_sign pmcs ON pmc.id = pmcs.contract_id
        WHERE
        pmc.logic_status = 1
        AND pmcs.effect_date BETWEEN #{startTime} AND #{endTime}
        AND pmc.`status`  IN('130')
        <if test="deptId != null and deptId != ''">
            AND pmc.tech_rsp_dept = #{deptId}
        </if>
<!--        <if test="type != null and type != ''">-->
<!--            AND pc.ywsrlx = #{type}-->
<!--        </if>-->
    </select>
    <select id="requirementTotal" resultType="java.lang.Integer">

    </select>

    <!-- 统计已发出的报价单，按报价单是否有系统报价发出时间判断 -->
    <select id="sendQuoteNum" resultType="java.lang.Integer">
        SELECT
            count( pq.id ) as number
        FROM
            pmsx_quotation_management pq
                INNER JOIN pms_requirement_mangement pr ON pq.requirement_id = pr.id
                INNER JOIN pms_customer_info pc ON pr.cust_person = pc.id
        WHERE
            pq.issue_time BETWEEN #{startTime} AND #{endTime}
          AND pq.logic_status = 1 and pq.send_out_time is not null
        <if test="deptId != null and deptId != ''">
            AND pr.req_ownership = #{deptId}
        </if>

        <if test="type != null and type != ''">
            AND pc.ywsrlx = #{type}
        </if>

    </select>

    <!-- 统计已发出的报价单，报价金额，按报价单是否有系统报价发出时间判断 -->
    <select id="sendQuoteAmount" resultType="com.chinasie.orion.management.domain.entity.QuotationManagement">
        SELECT
        pq.quote_amt
        FROM
        pmsx_quotation_management pq
        INNER JOIN pms_requirement_mangement pr ON pq.requirement_id = pr.id
        INNER JOIN pms_customer_info pc ON pr.cust_person = pc.id
        WHERE
        pq.issue_time BETWEEN #{startTime} AND #{endTime}
        AND pq.logic_status = 1 and pq.send_out_time is not null
        <if test="deptId != null and deptId != ''">
            AND pr.req_ownership = #{deptId}
        </if>

        <if test="type != null and type != ''">
            AND pc.ywsrlx = #{type}
        </if>

    </select>


    <select id="biddingQuotNum" resultType="java.lang.Integer">
        SELECT
        count( pq.id ) as number
        FROM
        pmsx_quotation_management pq
        INNER JOIN pms_requirement_mangement pr ON pq.requirement_id = pr.id
        INNER JOIN pms_customer_info pc ON pr.cust_person = pc.id
        WHERE
        pq.issue_time BETWEEN #{startTime} AND #{endTime}   AND pq.`status` IN ('1') AND pq.logic_status = 1
        <if test="deptId != null and deptId != ''">
            AND pr.req_ownership = #{deptId}
        </if>

        <if test="type != null and type != ''">
            AND pc.ywsrlx = #{type}
        </if>
    </select>

    <select id="findQuotations" resultType="com.chinasie.orion.management.domain.dto.ContractSignStatisticsDTO">
        SELECT
            qo.id AS id,
            qo.quote_amt AS quoteAmt,
            qo.requirement_id AS requirementId,
            qo.modify_time AS modifyTime,
            cus.ywsrlx AS ywsrlx
        FROM
            pmsx_quotation_management qo
                LEFT JOIN
            pms_market_contract mc ON qo.id = mc.quote_id AND mc.logic_status = 1
                LEFT JOIN
            pms_requirement_mangement re ON qo.requirement_id = re.id
                LEFT JOIN
            pms_customer_info cus ON re.cust_person = cus.id
        WHERE
            qo.status = 1
          AND mc.quote_id IS NULL;
    </select>



    <select id="fulfuilTpContractNum" resultType="java.lang.Integer">
        SELECT
        count( pmc.id )
        FROM
        pms_market_contract pmc
        INNER JOIN pms_market_contract_sign pmcs ON pmc.id = pmcs.contract_id
        WHERE
        pmc.logic_status = 1
        AND pmcs.effect_date BETWEEN #{startTime} AND #{endTime}
        AND pmc.`status`  IN('130')
        AND pmc.contract_type = 'totalPriceContract'
        <if test="deptId != null and deptId != ''">
            AND pmc.tech_rsp_dept = #{deptId}
        </if>
    </select>

    <select id="fulfuilframeContractNum" resultType="java.lang.Integer">
        SELECT
        count( pmc.id )
        FROM
        pms_market_contract pmc
        INNER JOIN pms_market_contract_sign pmcs ON pmc.id = pmcs.contract_id
        WHERE
        pmc.logic_status = 1
        AND pmcs.effect_date BETWEEN #{startTime} AND #{endTime}
        AND pmc.`status`  IN('130')
        AND pmc.contract_type = 'frameContract'
        <if test="deptId != null and deptId != ''">
            AND pmc.tech_rsp_dept = #{deptId}
        </if>
    </select>

    <select id="fulfuilCompositeContractNum" resultType="java.lang.Integer">
        SELECT
        count( pmc.id )
        FROM
        pms_market_contract pmc
        INNER JOIN pms_market_contract_sign pmcs ON pmc.id = pmcs.contract_id
        WHERE
        pmc.logic_status = 1
        AND pmcs.effect_date BETWEEN #{startTime} AND #{endTime}
        AND pmc.`status`  IN('130')
        AND pmc.contract_type = 'compositeContract'
        <if test="deptId != null and deptId != ''">
            AND pmc.tech_rsp_dept = #{deptId}
        </if>
    </select>
    <select id="actualAccept" resultType="com.chinasie.orion.management.domain.vo.MilestoneLineChartQueryVO">
        SELECT
        SUM( pcm.milestone_amt * pmcma.acceptance_ratio / 100 ) / 10000 AS acceptAmount,
        MONTH ( pmcma.actual_accept_date ) AS saleMonth
        FROM
        pms_contract_milestone pcm
        INNER JOIN pms_market_contract_milestone_acceptance pmcma ON pcm.id = pmcma.milestone_id
        WHERE
        YEAR ( pmcma.actual_accept_date )  = #{filterYear} AND pmcma.logic_status = 1 AND pcm.logic_status = 1
        <if test="deptId != null and deptId != ''">
            AND pcm.undert_dept = #{deptId}
        </if>
        GROUP BY
            saleMonth
        ORDER BY
            saleMonth;

    </select>
    <select id="planAccept" resultType="com.chinasie.orion.management.domain.vo.MilestoneLineChartQueryVO">
        SELECT
        SUM( pcm.milestone_amt) /10000 as planAmount,
        MONTH ( pcm.plan_accept_date ) AS saleMonth
        FROM
        pms_contract_milestone pcm
        WHERE
        YEAR ( pcm.plan_accept_date ) = #{filterYear}
        <if test="deptId != null and deptId != ''">
            AND pcm.undert_dept = #{deptId}
        </if>
        GROUP BY
        saleMonth
        ORDER BY
        saleMonth;
    </select>
    <select id="getExceptionReportNum" resultType="java.lang.Integer">
        SELECT
            count(cme.id)
        FROM
            pms_market_contract_milestone_exception cme
                INNER JOIN pms_contract_milestone pcm ON pcm.id = cme.milestone_id
        WHERE
            pcm.`status` = '110' AND pcm.logic_status = 1 AND cme.logic_status = 1
    </select>
    <select id="selectPlannedRevenue" resultType="java.math.BigDecimal">
        SELECT
        SUM(milestone.milestone_amt) /10000
        FROM
        pms_contract_milestone milestone
        INNER JOIN pms_market_contract contract ON milestone.contract_id = contract.id
        INNER JOIN pms_requirement_mangement req ON req.id = contract.requirement_id
        INNER JOIN pms_customer_info pc ON req.cust_person = pc.id
        WHERE
        contract.`status` = '130' AND
        pcm.plan_accept_date BETWEEN #{startTime}
        AND #{endTime}
        <if test="deptId != null and deptId != ''">
            AND pmc.tech_rsp_dept = #{deptId}
        </if>
    </select>
    <select id="selectAcceptedRevenue" resultType="java.math.BigDecimal">
        SELECT
        SUM( pmcma.acceptance_ratio * pcm.milestone_amt / 100 ) /10000
        FROM
        pms_market_contract_milestone_acceptance pmcma
        INNER JOIN pms_contract_milestone pcm ON pcm.id = pmcma.milestone_id
        WHERE
        pmcma.actual_accept_date BETWEEN #{startTime}
        AND #{endTime}
        <if test="deptId != null and deptId != ''">
            AND pmc.tech_rsp_dept = #{deptId}
        </if>
    </select>


    <select id="selectStatistic" parameterType="map" resultType="com.chinasie.orion.domain.entity.Scheme">
        SELECT id,status,end_time
        FROM plan_scheme
        WHERE rsp_user = #{id} And logic_status=1 And org_id=#{orgId}
        And status IN (105, 106, 107, 108, 109, 111, 112, 101, 100, 160, 104)
    </select>


</mapper>
