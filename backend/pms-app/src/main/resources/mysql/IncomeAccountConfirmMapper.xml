<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.IncomeAccountConfirmMapper">
    <select id="getStatistics" resultType="com.chinasie.orion.domain.vo.IncomeAccountConfirmStatisticsVO">
SELECT
    m.*,
    pipd.contractId,
    pipd.milestoneId,
    pipd.milestoneName,
    pipd.incomeVerifyType,
    pipd.projectNumber
FROM (
    SELECT
        ac.voucher_num as certificateSerialNumber,
        ac.voucher_type as voucherType,
        ac.income_plan_num as incomePlanNum,
        MIN(ac.voucher_date) AS documentDate ,
        MAX(ac.con_text) AS conText,
        MAX(ac.confirm_status) as confirmStatus,
        SUM(
            CASE
                WHEN SUBSTRING(ac.subject, 1, 4) = '6001' THEN -ac.local_currency_amt
                ELSE 0
            END
        ) AS amtNoTax,
        SUM(
            CASE
                WHEN SUBSTRING(ac.subject, 1, 4) = '2221' THEN -ac.local_currency_amt
                ELSE 0
            END
        ) AS tax,
        SUM(
        CASE
        WHEN SUBSTRING(ac.subject, 1, 4) = '2205' and voucher_type = 'invoice_advance_payment' THEN ac.local_currency_amt
        WHEN SUBSTRING(ac.subject, 1, 4) = '2205' and voucher_type != 'invoice_advance_payment' THEN -ac.local_currency_amt
        ELSE 0
        END
        ) AS amtTax
    FROM
        pmsx_income_account_confirm ac
        <where>
        <if test="voucherNums != null">
            AND ac.voucher_num in
            <foreach item="voucherNum" index="index" collection="voucherNums"
                     open="(" separator="," close=")">
                #{voucherNum}
            </foreach>
        </if>
        </where>
        GROUP BY
        ac.voucher_num,
        ac.voucher_type,
        ac.income_plan_num
        ) m
        LEFT JOIN
        (
        SELECT
        d.contract_id as contractId,
        d.milestone_id as milestoneId,
        d.milestone_name as milestoneName,
        d.income_confirm_type as incomeVerifyType,
        d.project_number as projectNumber,
        d.number
        FROM
        pmsx_income_plan p
        JOIN pmsx_income_plan_data d ON p.id = d.income_plan_id
        AND p.logic_status = 1
        AND d.logic_status = 1
        JOIN ( SELECT max( repeat_count ) repeat_count, number FROM pmsx_income_plan_data WHERE logic_status = 1
        and number is not null
        GROUP BY number ) v ON d.number = v.number
        AND d.repeat_count = v.repeat_count
        WHERE
        d.data_version = p.income_plan_type
        <if test="voucherNums != null">
            AND d.number in
            <foreach item="voucherNum" index="index" collection="voucherNums"
                     open="(" separator="," close=")">
                #{voucherNum}
            </foreach>
        </if>
        ) pipd
        ON
        m.incomePlanNum = pipd.number;
    </select>
</mapper>