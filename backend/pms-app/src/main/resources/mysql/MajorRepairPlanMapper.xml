<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.MajorRepairPlanMapper">

    <select id="countJobManageByRepairPlanId" resultType="com.chinasie.orion.domain.vo.CountVO">
        SELECT
            p.id as id,count(j.repair_round) as count
        FROM
            pmsx_major_repair_plan p
                LEFT JOIN pmsx_job_manage j ON p.repair_round = j.repair_round
        where p.id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY p.id
    </select>
    <select id="countProjectPlanByRepairPlanId" resultType="com.chinasie.orion.domain.vo.CountVO">
        SELECT
            p.id,
            count(s.repair_round) as count
        FROM
            pmsx_major_repair_plan p
                LEFT JOIN pms_project_scheme s ON p.repair_round = s.repair_round
        where p.id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY p.id
    </select>
</mapper>