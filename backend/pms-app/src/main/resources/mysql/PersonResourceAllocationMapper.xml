<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.PersonResourceAllocationMapper">

    <select id="queruSpecialtyandTeam" resultType="com.chinasie.orion.domain.vo.resourceAllocation.ResourceAllocationSpecialtyVO">
        SELECT
            pmro.id AS id,
            pmro.repair_round AS repairRound,
            pmro.chain_path AS chainPath,
            pmro.name,
            pmro.code,
            pmro.parent_id AS parentId,
            pmro.level,
            pmro.level_type AS levelType
        FROM
            pmsx_major_repair_org pmro
        WHERE
            pmro.logic_status = 1
          and (pmro.repair_round != ''
                or pmro.repair_round is not null)
          AND pmro.repair_round = #{repairRound}
    </select>

    <!--
        查询个人基本信息及其资源分配情况
        逻辑说明：
        1. 本查询从多个表中联接获取数据，包括人员基本信息、人员管理信息、人员与维修组织关系信息、大修组织信息及大修计划信息
        2. 使用distinct关键字确保查询结果的唯一性
        3. 通过条件判断来筛选有效的大修组织、人员及其关系信息
        4. 根据是否为永久人员，选择不同的日期格式化逻辑
        5. 提供了多个条件过滤选项，包括时间范围、关键字、维修轮次等，以满足不同的查询需求
    -->
    <select id="getPersonInfo" resultType="com.chinasie.orion.domain.vo.resourceAllocation.ResourceAllocationInfoVO">
        SELECT distinct
        protop.person_id as id,
        protop.id as relationId,
        protop.repair_org_id as teamId,
        protop.code as number,
        protop.name as name,
        pmro.name as teamName,
        pmro.repair_round as repairRound,
        protop.repair_round,
        DATE_FORMAT(protop.plan_begin_time, '%Y-%m-%d') as inDate,
        DATE_FORMAT(protop.plan_end_time, '%Y-%m-%d') as outDate,
        IF(protop.is_base_permanent is null, 0, protop.is_base_permanent) as isBasePermanent
        FROM pmsx_relation_org_to_person protop
        JOIN pmsx_major_repair_org pmro
        ON pmro.id = protop.repair_org_id
        JOIN pmsx_major_repair_plan pmrp
        ON pmrp.repair_round = pmro.repair_round
        WHERE
        pmro.logic_status = 1
        AND protop.logic_status = 1
        AND protop.code is not NULL
        <!-- 根据开始时间和结束时间过滤，确保大修计划时间与给定时间范围有交集 -->
        <if test="beginTime != null and endTime != null">
            and ((pmrp.end_time >= #{beginTime}
                  and pmrp.begin_time &lt;= #{beginTime})
                or (pmrp.begin_time &lt;= #{endTime}
                     and pmrp.end_time >= #{endTime}))
        </if>
        <!-- 根据关键字过滤，支持用户代码和全名的模糊匹配 -->
        <if test="keyword != null and keyword != ''">
            AND (protop.code like CONCAT('%', #{keyword}, '%')
            OR protop.name like CONCAT('%', #{keyword}, '%'))
        </if>
        <!-- 根据维修轮次过滤 -->
        <if test="repairRound != null and repairRound != ''">
            AND protop.repair_round = #{repairRound}
        </if>
        <!-- 排除特定维修轮次的人员，并确保日期信息完整 -->
        <if test="noRepairRound != null and noRepairRound != ''">
            AND protop.base_code != #{noRepairRound}
            and protop.plan_begin_time is not null and protop.plan_end_time is not null
        </if>
        <!-- 根据用户代码列表过滤 -->
        <if test="numbers != null and numbers.size() > 0">
            and protop.code in
            <foreach item="item" index="index" collection="numbers" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 按维修轮次升序排序 -->
        order by protop.repair_round asc
    </select>

    <!--
        查询资产信息

        此查询语句用于获取特定条件下的资产分配信息 涉及多个表的连接查询 包括材料管理表、材料与维修组织关系表、主要维修组织表和主要维修计划表
        查询结果映射为 ResourceAllocationInfoVO 对象
    -->
    <select id="getAssetsInfo" resultType="com.chinasie.orion.domain.vo.resourceAllocation.ResourceAllocationInfoVO">
        SELECT distinct
        protm.repair_org_id as teamId,
        pmro.name as teamName,
        pmm.id as id,
        pmm.number as number,
        pmm.asset_name as name,
        pmm.id as relationId,
        pmro.repair_round as repairRound,
        pmm.in_date as inDate,
        pmm.out_date as outDate
        FROM pmsx_material_manage pmm
        join pmsx_relation_org_to_material protm
        on pmm.id = protm.material_id
        join pmsx_major_repair_org pmro
        on pmro.id = protm.repair_org_id
        JOIN pmsx_major_repair_plan pmrp
        ON pmrp.repair_round = pmro.repair_round
        WHERE  pmm.logic_status = 1
        and protm.logic_status = 1
        and pmro.logic_status = 1
        <!-- 如果开始时间和结束时间都非空，则添加时间条件 -->
        <if test="beginTime != null and endTime != null">
            and ((pmrp.end_time >= #{beginTime}
            and pmrp.begin_time &lt;= #{beginTime})
            or (pmrp.begin_time &lt;= #{endTime}
            and pmrp.end_time >= #{endTime}))
        </if>
        <!-- 如果关键字非空，则添加关键字搜索条件 -->
        <if test="keyword != null and keyword!= ''">
            and (pmm.number like CONCAT ('%', #{keyword}, '%')
            or pmm.asset_name like CONCAT('%',#{keyword},'%'))
        </if>
        <!-- 如果维修轮次非空，则添加维修轮次条件 -->
        <if test="repairRound != null and repairRound != ''">
            and pmro.repair_round = #{repairRound}
        </if>
        <!-- 如果非维修轮次非空，则添加非维修轮次条件 -->
        <if test="noRepairRound != null and noRepairRound != ''">
            and pmm.base_code != #{noRepairRound}
            and pmm.in_date is not null and pmm.out_date is not null
        </if>
        <!-- 如果资产编号列表非空，则添加资产编号条件 -->
        <if test="numbers != null and numbers.size() > 0">
            and pmm.number in
            <foreach item="item" index="index" collection="numbers" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="setPersonTimeWithTrue"  parameterType="java.util.List">
        UPDATE pmsx_relation_org_to_person
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plan_begin_time=case" suffix="end,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.relationId} THEN #{item.realStartDate}
                </foreach>
            </trim>
            <trim prefix="plan_end_time=case" suffix="end,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.relationId} THEN #{item.realEndDate}
                </foreach>
            </trim>
        </trim>
        <where>
            <foreach collection="list" separator="or" item="item">
                id = #{item.relationId}
            </foreach>
        </where>
    </update>

    <update id="setPersonTimeWithFalse"  parameterType="java.util.List">
        UPDATE pmsx_person_mange
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="in_date=case" suffix="end,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.realStartDate}
                </foreach>
            </trim>
            <trim prefix="out_date=case" suffix="end,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.realEndDate}
                </foreach>
            </trim>
        </trim>
        <where>
            <foreach collection="list" separator="or" item="item">
                id = #{item.id}
            </foreach>
        </where>
    </update>

    <update id="setAssetTime"  parameterType="java.util.List">
        UPDATE pmsx_material_manage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="in_date=case" suffix="end,">
                <foreach collection="list" item="item">
                    WHEN id =#{item.id}   THEN #{item.realStartDate}
                </foreach>
            </trim>
            <trim prefix="out_date=case" suffix="end,">
                <foreach collection="list" item="item">
                    WHEN id =#{item.id}   THEN #{item.realEndDate}
                </foreach>
            </trim>
        </trim>
        <where>
            <foreach collection="list" separator="or" item="item">
                id =#{item.id}
            </foreach>
        </where>
    </update>

</mapper>