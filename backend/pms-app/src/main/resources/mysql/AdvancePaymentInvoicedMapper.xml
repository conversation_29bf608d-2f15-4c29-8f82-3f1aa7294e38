<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.AdvancePaymentInvoicedMapper">
    <select id="getTotal" resultType="com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO">
    select ifnull( sum(amt_no_tax),0) as amtNoTax,ifnull(sum(tax),0) as Tax,ifnull(sum(amt_tax),0) as amtTax
    from pmsx_advance_payment_invoiced
    where
    contract_id = #{contractId} and logic_status =1
    </select>

    <select id="getMilestoneTotal" resultType="com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO">
        select ifnull( sum(amt_no_tax),0) as amtNoTax,ifnull(sum(tax),0) as Tax,ifnull(sum(amt_tax),0) as amtTax,milestone_id
        from pmsx_advance_payment_invoiced
        where
        milestone_id IN
        <foreach item='id' collection='milestoneIds' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        and logic_status =1
        group by milestone_id
    </select>

</mapper>