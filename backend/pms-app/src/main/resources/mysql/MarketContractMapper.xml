<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.MarketContractMapper">

    <select id="findByProjectNumber" resultType="com.chinasie.orion.domain.vo.MarketContractApiVO">
        SELECT
        t1.id,
        t1.class_name,
        t1.creator_id,
        t1.modify_time,
        t1.owner_id,
        t1.create_time,
        t1.modify_id,
        t1.remark,
        t1.platform_id,
        t1.org_id,
        t1.`status`,
        t1.logic_status,
        t1.number,
        t1.`name`,
        t1.contract_type,
        t1.quote_id,
        t1.quote_number,
        t1.fream_contract,
        t1.tech_rsp_user,
        t1.tech_rsp_dept,
        t1.currency,
        t1.tak_effect_date,
        t1.end_date,
        t1.rel_trans_appr,
        t1.trans_appr_id,
        t1.trans_appr_number,
        t1.content,
        t1.is_quality_period,
        t1.quality_end_date,
        t1.is_quality_amt,
        t1.quality_amt,
        t1.quality_level,
        t1.sign_time,
        t1.begin_time,
        t1.end_time,
        t1.end_type,
        t1.sign_remark,
        t1.commerce_rsp_user,
        t1.close_date,
        t1.close_user_id,
        t1.is_purchase,
        t1.frame_contract_id,
        t1.frame_contract_amt,
        t1.close_type,
        t1.requirement_id,
        t1.contract_sign_user_id,
        t1.contract_sign_user_name,
        t1.cust_person_id,
        t2.project_number,
        t3.milestone_amt,
        t3.cost_bus_type
        FROM
        pms_market_contract AS t1
        LEFT JOIN pmsx_project_initiation AS t2 ON t2.contract_numbers = t1.number
        left join pms_contract_milestone t3 on t3.contract_id = t1.id AND t3.`status` in(110,130) and t3.logic_status = 1
        WHERE
        t2.project_number in
        <foreach collection="projectNumbers" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND t1.logic_status = 1
        AND t2.logic_status = 1
--         and t1.frame_contract_id is null
    </select>

    <select id="findByFrameContractIds" resultType="com.chinasie.orion.domain.vo.MarketContractApiVO">
        SELECT
        t1.id,
        t1.class_name,
        t1.creator_id,
        t1.modify_time,
        t1.owner_id,
        t1.create_time,
        t1.modify_id,
        t1.remark,
        t1.platform_id,
        t1.org_id,
        t1.`status`,
        t1.logic_status,
        t1.number,
        t1.`name`,
        t1.contract_type,
        t1.quote_id,
        t1.quote_number,
        t1.fream_contract,
        t1.tech_rsp_user,
        t1.tech_rsp_dept,
        t1.currency,
        t1.tak_effect_date,
        t1.end_date,
        t1.rel_trans_appr,
        t1.trans_appr_id,
        t1.trans_appr_number,
        t1.content,
        t1.is_quality_period,
        t1.quality_end_date,
        t1.is_quality_amt,
        t1.quality_amt,
        t1.quality_level,
        t1.sign_time,
        t1.begin_time,
        t1.end_time,
        t1.end_type,
        t1.sign_remark,
        t1.commerce_rsp_user,
        t1.close_date,
        t1.close_user_id,
        t1.is_purchase,
        t1.frame_contract_id,
        t1.frame_contract_amt,
        t1.close_type,
        t1.requirement_id,
        t1.contract_sign_user_id,
        t1.contract_sign_user_name,
        t1.cust_person_id,
        t3.milestone_amt,
        t3.cost_bus_type
        FROM
        pms_market_contract AS t1
        left join pms_contract_milestone t3 on t3.contract_id = t1.id AND t3.`status` in(110,130) and t3.logic_status = 1
        WHERE
        t1.frame_contract_id in
        <foreach collection="contractIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND t1.logic_status = 1
    </select>

    <select id="getDeptByCodeAndOrgId" resultType="com.chinasie.orion.domain.dto.MarketContractDivisionLeaderDTO">
        SELECT id AS deptId, type
        FROM pmi_dept
        WHERE dept_code = #{deptCode} AND org_id = #{orgId}
    </select>


</mapper>
