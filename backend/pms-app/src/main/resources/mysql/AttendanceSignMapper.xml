<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.AttendanceSignMapper">


    <select id="attendanceSignStatistics" resultType="com.chinasie.orion.domain.entity.AttendanceSignQuarterStatistics">
        SELECT
            a.job_grade,
            count(*) userCount,
            sum( attandance_rate ),
            SUM( a.attandance_rate * b.unit_price ) unit_price
        FROM
            pmsx_attendance_sign a
                LEFT JOIN pmsx_contract_cost_type b ON a.job_grade = b.cost_name and a.contract_no = b.contract_number
        AND b.cost_type_number = 'positionCost'
                AND b.logic_status = 1
        WHERE
            a.logic_status = 1
            and a.attandance_year = #{year}
            and a.contract_no = #{contractNo}
            and a.org_code = #{orgCode}
            and a.attandance_quarter = #{attandanceQuarter}
        GROUP BY
            a.job_grade
    </select>


    <select id="attendanceSignUserStatistics" resultType="com.chinasie.orion.domain.entity.AttendanceSignUserQuarterStatistics">
        SELECT
            a.user_code,
            GROUP_CONCAT(DISTINCT a.user_name SEPARATOR ', ') user_name,
            GROUP_CONCAT(DISTINCT a.org_name SEPARATOR ', ') org_name,
            sum( attandance_rate ) attandance_rate,
            SUM( a.attandance_rate * b.unit_price ) unit_price,
            GROUP_CONCAT(DISTINCT job_grade SEPARATOR ', ') job_grade
        FROM
            pmsx_attendance_sign a
                LEFT JOIN pmsx_contract_cost_type b ON a.job_grade = b.cost_name and a.contract_no = b.contract_number
                AND b.cost_type_number = 'positionCost'
                AND b.logic_status = 1
        WHERE
            a.logic_status = 1
          and a.attandance_year = #{year}
          and a.contract_no = #{contractNo}
          and a.org_code = #{orgCode}
          and a.attandance_quarter = #{attandanceQuarter}
        GROUP BY
            a.user_code
    </select>


    <select id="attendanceSignList" resultType="com.chinasie.orion.domain.entity.AttendanceSignUserSatisfationEvaluation">
        SELECT
            DISTINCT
            a.org_code,
            a.org_name,
            a.dept_code,
            a.dept_name,
            a.user_code,
            a.user_name,
            a.supplier_no,
            a.supplier_name,
            a.contract_no,
            a.contract_name,
            a.job_grade
        FROM
            pmsx_attendance_sign a
        WHERE
            a.logic_status = 1
          and a.org_code is not null
          and a.dept_code is not null
          and a.attandance_year = #{year}
          and a.contract_no = #{contractNo}
    </select>

</mapper>
