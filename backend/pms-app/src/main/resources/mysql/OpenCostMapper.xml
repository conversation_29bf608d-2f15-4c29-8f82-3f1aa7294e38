<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.OpenCostMapper">



    <select id="openCostUserStatistics" resultType="com.chinasie.orion.domain.entity.OpenCostUserStatistics">
        SELECT
            a.user_code,
            GROUP_CONCAT(DISTINCT a.user_name SEPARATOR ', ') user_name,
            GROUP_CONCAT(DISTINCT a.org_name SEPARATOR ', ') org_name,
            SUM(IF(pay_type_no = 'logisticsFee',pay_amt,0) ) logisticsAmt,
            SUM(IF(pay_type_no = 'physicalExaminationFee',pay_amt,0) ) physicalExaminationAmt,
            SUM(IF(pay_type_no = 'laborFee',pay_amt,0) ) laborAmt,
            SUM(IF(pay_type_no = 'restaurantManagementFee',pay_amt,0) ) restaurantManagementAmt,
            SUM(IF(pay_type_no = 'otherFee',pay_amt,0) ) otherAmt
        FROM pmsx_open_cost a
        WHERE
            a.logic_status = 1
          and a.data_year = #{year}
          and a.contract_no = #{contractNo}
          and a.org_code = #{orgCode}
          and a.data_quarter = #{quarter}
        GROUP BY
            a.user_code
    </select>

</mapper>
