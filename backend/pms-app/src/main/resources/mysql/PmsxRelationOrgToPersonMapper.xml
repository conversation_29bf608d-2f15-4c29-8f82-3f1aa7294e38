<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.PmsxRelationOrgToPersonMapper">

    <update id="updateRelationOrgToPerson">
        UPDATE pmsx_relation_org_to_person SET
            <foreach collection="List" item="item" separator=",">
                plan_begin_time = #{item.planBeginTime},
                plan_end_time = #{item.planEndTime},
            </foreach>
        WHERE org_id = #{orgId} AND person_id = #{personId}
    </update>

</mapper>