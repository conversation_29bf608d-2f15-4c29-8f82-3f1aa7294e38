<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.RepairPlanMapper">

    <select id="queryRepairPlanByRepairRound" resultType="com.chinasie.orion.domain.vo.resourceAllocation.RepairPlanVO">
            SELECT
                repair_round as repairRound,
                name as repairName,
                begin_time as beginTime,
                end_time as endTime,
                base_code as baseCode,
                base_name as baseName
            FROM pmsx_major_repair_plan
            WHERE logic_status = 1
                AND repair_round = #{repairRound}
    </select>

    <select id="queryRepairPlan" resultType="com.chinasie.orion.domain.vo.resourceAllocation.RepairPlanVO">
            SELECT
                repair_round as repairRound,
                name as repairName,
                begin_time as beginTime,
                end_time as endTime,
                base_code as baseCode,
                base_name as baseName
            FROM pmsx_major_repair_plan
            WHERE logic_status = 1
                and begin_time >= now()
                <if test="repairRound!= null and repairRound!= ''">
                    AND repair_round LIKE CONCAT ('%', #{repairRound}, '%')
                </if>
    </select>

</mapper>