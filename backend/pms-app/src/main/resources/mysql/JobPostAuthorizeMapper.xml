<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.JobPostAuthorizeMapper">



    <select id="getJobPersonNumMap" resultType="com.chinasie.orion.domain.dto.CountJobDTO">
        select  person_manage_id as `key` ,count(person_manage_id) as  `value`
        from  pmsx_job_post_authorize
        <where>
            logic_status = 1
            <if test=" null != personIdList and  personIdList.size()>0">
                and  person_manage_id in
                <foreach collection="personIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by person_manage_id
    </select>

    <select id="getUserJobInfoList" resultType="com.chinasie.orion.domain.dto.source.PersonSourceDTO">
        select p.id as relationId ,j.id as jobId,p.person_code as userCode,COALESCE(NULLIF(b.full_name, ''), pb.name) AS fullName
             ,j.repair_round as repairRound,j.`name` as jobName,p.person_manage_id as personId
             ,p.plan_begin_date as taskBeginDate,p.plan_end_date as taskEndDate
             ,j.begin_time as jobBeginDate,j.end_time as jobEndDate
        from pmsx_job_post_authorize p
         JOIN pmsx_job_manage j ON p.job_id = j.id  and j.logic_status=1
         JOIN pmsx_basic_user b ON p.person_code = b.user_code  and b.logic_status=1
         JOIN pmi_user pb on pb.code=p.person_code and pb.logic_status=1
        where j.repair_round  =#{repairRound} and p.logic_status =1
        <if test="null != keyword and keyword != ''">
            and (b.full_name like concat('%',#{keyword},'%')
                     or p.person_code like concat('%',#{keyword},'%')
                     or j.name like concat('%',#{keyword},'%')
                     or pb.name like concat('%',#{keyword},'%')
                     or pb.code like concat('%',#{keyword},'%'))
        </if>
        <if test="null != year and year != ''">
            and <![CDATA[YEAR(j.begin_time) <= #{year}]]> and <![CDATA[YEAR(j.end_time) >= #{year}]]>
        </if>
<!--        <if test="null != quarter and quarter != ''">-->
<!--            and <![CDATA[QUARTER(j.begin_time) <= #{quarter}]]> and <![CDATA[QUARTER(j.end_time) >= #{quarter}]]>-->
<!--        </if>-->
    </select>

    <select id="peronOverlapSourceList" resultType="com.chinasie.orion.domain.dto.source.PersonSourceDTO">
        select p.id as relationId ,j.id as jobId,p.person_code as userCode, COALESCE(NULLIF(b.full_name, ''), pb.name) AS fullName
        ,j.repair_round as repairRound,j.`name` as jobName,p.person_manage_id as personId
        ,p.plan_begin_date as taskBeginDate,p.plan_end_date as taskEndDate
        ,j.begin_time as jobBeginDate,j.end_time as jobEndDate
        from pmsx_job_post_authorize p
        LEFT JOIN pmsx_job_manage j on  p.job_id = j.id and j.logic_status=1
        LEFT JOIN pmsx_basic_user b on p.person_code=b.user_code and b.logic_status=1
        LEFT JOIN pmi_user pb on pb.code=p.person_code and pb.logic_status=1
<!--       暂时不需要控制 大修是否完成（按理说应该需要控制大修已完成的不需要对比）  join -->
        where  p.logic_status =1
        <if test="null != keyword and keyword != ''">
            and (b.full_name like concat('%',#{keyword},'%')
                     or b.user_code like concat('%',#{keyword},'%')
                     or j.name like concat('%',#{keyword},'%')
                     or pb.name like concat('%',#{keyword},'%')
                     or pb.code like concat('%',#{keyword},'%'))
        </if>
        <if test="null != year and year != ''">
            and <![CDATA[YEAR(j.begin_time) <= #{year}]]> and <![CDATA[YEAR(j.end_time) >= #{year}]]>
        </if>
        <!--        j.repair_round =#{repairRound} and -->
        <if test=" null != userCodeList and  userCodeList.size()>0" >
            and  p.person_code in
            <foreach collection="userCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listByJobIdList"  resultType="java.lang.String">
        select job_id from pmsx_job_post_authorize  pj
        INNER JOIN pmsx_person_mange  p on pj.person_manage_id  = p.id  and p.logic_status=1
        where p.logic_status =1 and p.newcomer=1 and pj.job_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="listByPersonId" resultType="com.chinasie.orion.domain.dto.job.JobPersonNewcomerDTO">
        select pj.job_id as jobId,p.newcomer from pmsx_job_post_authorize  pj
                               left  JOIN pmsx_person_mange  p on pj.person_manage_id  = p.id  and p.logic_status=1
        where pj.logic_status =1 and pj.person_manage_id =#{personId}
    </select>
    <select id="listByJobIdAndNotPersonId" resultType="java.lang.String">
        select job_id from pmsx_job_post_authorize  pj
                               INNER JOIN pmsx_person_mange  p on pj.person_manage_id  = p.id  and p.logic_status=1
        where pj.logic_status =1 and p.newcomer=1 and pj.job_id in
        <foreach collection="jobIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and pj.person_manage_id != #{personId}
    </select>



</mapper>
