<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.RelationOrgToMaterialMapper">
    <update id="deleteRelation">
        UPDATE pmsx_relation_org_to_material
        SET logic_status = -1
        WHERE (material_id, repair_org_id) IN
        <foreach collection="param" item="item" separator="), (" open="((" close="))">
            #{item.materialId}, #{item.repairOrgId}
        </foreach>
    </update>

    <update id="removeNoRelation">
        UPDATE pmsx_material_manage a
        LEFT JOIN pmsx_relation_org_to_material b ON a.id = b.material_id
        set a.logic_status = -1
        WHERE
        a.logic_status = 1
        AND a.STATUS IN (
        0,
        2)
        AND b.id is not null
        AND a.id in
        <foreach collection="param" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
</mapper>