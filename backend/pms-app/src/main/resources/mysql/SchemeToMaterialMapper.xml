<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.SchemeToMaterialMapper">


    <select id="countByMaterial" resultType="com.chinasie.orion.domain.dto.material.MaterialManageBO">
        select material_id as 'materialId',count(id) as 'count' from pmsx_scheme_to_material
            where material_id in
            <foreach item="item" index="index" collection="materialIds" open="(" separator="," close=")">
                #{item}
            </foreach>
              and class_name = 'SchemeToMaterial'
              and logic_status = 1
        GROUP BY material_id
    </select>
    <select id="getListByRepairRoundAndKeyword" resultType="com.chinasie.orion.domain.entity.SchemeToMaterial">
        select * from pmsx_scheme_to_material where repair_round=#{repairRound} and logic_status=1
        <if test="null != keyword and keyword != ''">
            and ( material_name like concat('%',#{keyword},'%') or material_number like concat('%',#{keyword},'%'))
        </if>
    </select>
    <select id="pageCount" resultType="java.lang.Long">
        select count(distinct pst.material_id) from pmsx_scheme_to_material pst
        inner join pms_project_scheme ps on ps.id = pst.plan_scheme_id
        inner join  pms_project pp on ps.project_id = pp.id and pp.logic_status=1
        where pst.repair_round =#{repairRound} and ps.logic_status=1 and pst.logic_status=1
        <if test="null != keyword and keyword != ''">
            and (pst.material_number like concat('%',#{keyword},'%') or pst.product_code like concat('%',#{keyword},'%')
            or pst.material_name like concat('%',#{keyword},'%')
            or pp.name like concat('%',#{keyword},'%') or pp.number like concat('%',#{keyword},'%')
            or ps.name like concat('%',#{keyword},'%'))
        </if>

    </select>
    <!--
     condition.select(SchemeToMaterial::getMaterialName,SchemeToMaterial::getMaterialId
                ,SchemeToMaterial::getMaterialNumber,SchemeToMaterial::getBaseCode,
                SchemeToMaterial::getToolStatus,SchemeToMaterial::getMaintenanceCycle,SchemeToMaterial::getProductCode,
                SchemeToMaterial::getAssetType,SchemeToMaterial::getRepairRound);
     -->
    <select id="pageToList" resultType="com.chinasie.orion.domain.entity.SchemeToMaterial">
        select  distinct b.material_id,b.material_name,b.material_number,b.base_code,b.tool_status,
        b.maintenance_cycle,b.product_code,b.asset_type,b.repair_round  from (
        select  pst.material_id,pst.material_name,pst.material_number,pst.base_code,pst.tool_status,
        pst.maintenance_cycle,pst.product_code,pst.asset_type,pst.repair_round from pmsx_scheme_to_material pst
        inner join pms_project_scheme ps on ps.id = pst.plan_scheme_id
        inner join  pms_project pp on ps.project_id = pp.id and pp.logic_status=1
        where pst.repair_round =#{repairRound} and ps.logic_status=1 and pst.logic_status=1
        <if test="null != keyword and keyword != ''">
            and (pst.material_number like concat('%',#{keyword},'%') or pst.product_code like concat('%',#{keyword},'%')
                 or pst.material_name like concat('%',#{keyword},'%')
                 or pp.name like concat('%',#{keyword},'%') or pp.number like concat('%',#{keyword},'%')
                 or ps.name like concat('%',#{keyword},'%'))
        </if>
        order by pp.name desc ,ps.name desc,pst.create_time desc
                      ) b
        limit #{current},#{pageSize}
    </select>
    <select id="listByMaterialIdList" resultType="com.chinasie.orion.domain.vo.SchemeToMaterialVO">
        select pst.material_id,ps.name as planSchemeName,pp.name as projectName,pst.plan_scheme_id as planSchemeId,pp.id as projectId from pmsx_scheme_to_material pst
        inner join pms_project_scheme ps on ps.id = pst.plan_scheme_id
        inner join  pms_project pp on ps.project_id = pp.id and pp.logic_status=1
        where ps.repair_round =#{repairRound} and ps.logic_status=1 and pst.logic_status=1 and pst.material_id in
        <foreach item="item" index="index" collection="materialIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>