<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.PersonMangeMapper">

    <update id="removeNew">
    UPDATE pmsx_relation_org_to_person
        SET logic_status = -1
        WHERE (person_id, repair_org_id) IN
        <foreach collection="param" item="item" separator="), (" open="((" close="))">
            #{item.personId}, #{item.repairOrgId}
        </foreach>
    </update>

    <update id="removeNoRelation">
        UPDATE pmsx_person_mange a
        LEFT JOIN pmsx_relation_org_to_person b ON a.id = b.person_id and b.logic_status = 1
        set a.logic_status = -1
        WHERE
        a.logic_status = 1
        AND a.STATUS IN (
        0,
        2)
        AND b.id is not null
        AND a.id in
        <foreach collection="personIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>


    <select id="queryListByParam" resultType="com.chinasie.orion.domain.entity.PersonMange">
       select  `id`, `class_name`,`org_id`, `logic_status`, `number`, `base_name`, `work_type`,
            `act_in_date`, `act_out_date`, `leave_reason`,
            `leave_remark`, `in_date`, `out_date`,
            `base_code`, `base_place_project`, `repair_round`,  `newcomer`,
            `is_base_permanent`, `design_ctrl_zone_op`, `contact_user_code`,
            `contact_dept_code`, `contact_office_code`, `job_taboos_name`,
            `newcomer_match_person_code`, `contact_office_name`, `contact_user_name`,
            `contact_dept_name`,`status`, `base_place_project_name`, `is_join_year_major_repair`, `is_height_measure_person`
            from  pmsx_person_mange
    <where>
        <if test="userCodeList != null and userCodeList.size() > 0">
            and `number` in
            <foreach collection="userCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="baseCode != null and baseCode != ''">
            and `base_code` = #{baseCode}
        </if>
    </where>


    </select>


    <select id="queryListByIdList" resultType="com.chinasie.orion.domain.entity.PersonMange">
        select  `id`, `class_name`,`org_id`, `logic_status`, `number`, `base_name`, `work_type`,
        `act_in_date`, `act_out_date`, `leave_reason`,`in_days`,
        `leave_remark`, `in_date`, `out_date`,
        `base_code`, `base_place_project`, `repair_round`,  `newcomer`,
        `is_base_permanent`, `design_ctrl_zone_op`, `contact_user_code`,
        `contact_dept_code`, `contact_office_code`, `job_taboos_name`,
        `newcomer_match_person_code`, `contact_office_name`, `contact_user_name`,
        `contact_dept_name`,`status`, `base_place_project_name`, `is_join_year_major_repair`, `is_height_measure_person`
        from  pmsx_person_mange
        <where>
            <if test="idList != null and idList.size() > 0">
                and `id` in
                <foreach collection="idList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>


    </select>
    <select id="getPersonManage" resultType="com.chinasie.orion.domain.entity.PersonMange">
        select  `id`, `class_name`, `remark`, `platform_id`
             , `org_id`, `status`, `logic_status`, `number`, `base_name`, `contact_dept`, `contact_office`, `contact_user`
             , `enter_mode`, `work_type`, `act_in_date`, `act_out_date`, `leave_reason`, `leave_remark`, `in_date`, `out_date`
             , `base_code`, `base_place_project`, `repair_round`, `height_str`, `weight_str`, `job_taboos`, `chemical_toxin_use_job`
             , `work_res_person`, `preparation_engineer`, `qc_str`, `qc_work_year`, `fu_ti_saf_off`, `pa_ti_saf_off`, `spe_task_cert_sit`
             , `participate_or_not`, `newcomer`, `newcomer_type`, `newcomer_match_person`, `authorization_status`, `is_base_permanent`, `design_ctrl_zone_op`
             , `contact_user_code`, `contact_dept_code`, `contact_office_code`, `job_taboos_name`, `newcomer_match_person_code`, `contact_office_name`
             , `contact_user_name`, `contact_dept_name`, `base_place_project_name`, `is_join_year_major_repair`, `is_height_measure_person`, `main_work_center`
             , `is_job`, `is_finish_out_handover`, `is_again_in` from pmsx_person_mange where `id`=#{id}

    </select>
    <select id="getPersonManageInAndOutDateList" resultType="com.chinasie.orion.domain.dto.source.PersonInfoDTO">
        <!--  获取 人员管理 类型标识为1 -->
        select p.id ,p.number,p.in_date,p.out_date,'1' type,status as inOrOut,base_code,	CONCAT(p.id, '_', COALESCE(p.in_date, 'in'), '_', COALESCE(p.out_date, 'out')) AS 'unique_key'
        from pmsx_person_mange p
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        union all
        <!--  获取 人员管理 类型标识为0 -->
        select ml.person_manage_id as id,ml.number,ml.in_date,ml.out_date,'0' type,
        case  ml.type
        when 'input' then 1
        when 'out' then 2
        else ''
        end as inOrOut,
        base_code,
        CONCAT(ml.person_manage_id ,'_',COALESCE(ml.in_date, 'in'),'_',COALESCE(ml.out_date, 'in')) as 'unique_key'
        from pmsx_person_manage_ledger ml
        where in_date is not null and out_date is not  null  and ml.person_manage_id is not null and ml.person_manage_id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="getPersonManageNotIn" resultType="com.chinasie.orion.domain.entity.PersonMange">
        select  `id`, `class_name`, `remark`, `platform_id`
             , `org_id`, `status`, `logic_status`, `number`, `base_name`, `contact_dept`, `contact_office`, `contact_user`
             , `enter_mode`, `work_type`, `act_in_date`, `act_out_date`, `leave_reason`, `leave_remark`, `in_date`, `out_date`
             , `base_code`, `base_place_project`, `repair_round`, `height_str`, `weight_str`, `job_taboos`, `chemical_toxin_use_job`
             , `work_res_person`, `preparation_engineer`, `qc_str`, `qc_work_year`, `fu_ti_saf_off`, `pa_ti_saf_off`, `spe_task_cert_sit`
             , `participate_or_not`, `newcomer`, `newcomer_type`, `newcomer_match_person`, `authorization_status`, `is_base_permanent`, `design_ctrl_zone_op`
             , `contact_user_code`, `contact_dept_code`, `contact_office_code`, `job_taboos_name`, `newcomer_match_person_code`, `contact_office_name`
             , `contact_user_name`, `contact_dept_name`, `base_place_project_name`, `is_join_year_major_repair`, `is_height_measure_person`, `main_work_center`
             , `is_job`, `is_finish_out_handover`, `is_again_in` from pmsx_person_mange
        WHERE
            logic_status = 1
          AND act_in_date is not null
          AND act_out_date is not null
          AND (NOW() BETWEEN act_in_date and act_out_date OR DATE(NOW()) = DATE(act_in_date))
          AND status = 0
    </select>

    <select id="getPersonManageTreeData" resultType="com.chinasie.orion.domain.vo.PersonTmpVO">
        SELECT DISTINCT pm.id,pm.newcomer,pm.in_date as 'planInDate',pm.out_date as 'planOutDate',
                        pm.in_date as 'inDate',pm.out_date as 'outDate',pm.number,
                        pm.act_in_date as 'actInDate',pm.act_out_date as 'actOutDate',ud.sex,
                        pm.is_base_permanent as 'isBasePermanent',pm.newcomer_match_person as 'newcomerMatchPerson',
                        pm.newcomer_match_person_code as 'newcomerMatchPersonCode',pm.in_days as 'inDays',
                        pm.status,pm.leave_reason as 'leaveReason',pm.is_finish_out_handover as 'isFinishOutHandover',
                        pm.is_again_in as 'isAgainIn',pbu.id as 'basicUserId',pm.base_code as 'baseCode'
        FROM pmsx_relation_org_to_person rop
        INNER JOIN pmsx_person_mange pm ON rop.person_id = pm.id
        INNER JOIN pmi_user ud ON ud.code = pm.number
        INNER JOIN pmsx_basic_user pbu ON pbu.user_code = ud.code
        WHERE rop.repair_org_id IN
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
          AND pm.logic_status = 1
        <if test="keyword != null and keyword != ''">
            AND (pm.number LIKE CONCAT('%', #{keyword}, '%')
            OR ud.name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>

    <select id="getJobPostNames" resultType="com.chinasie.orion.domain.entity.PersonMange">
        select
            ppm.`number` ,
            ppjpa.job_post_name as 'jobPostName'
        from
            pmsx_person_mange ppm
                inner join pmsx_person_job_post_authorize ppjpa on ppm.`number` = ppjpa.user_code
        where ppjpa.status = 1 and ppjpa.authorize_status = 130 and (ppm.base_code = #{baseCode} or ppm.base_code = 'SNPI')
            and ppm.number in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getPersonManageTreeDataStrategy" resultType="com.chinasie.orion.domain.vo.PersonTmpVO">
        select
        distinct pm.id as 'personId',
        pm.newcomer,
        pm.in_date as 'planInDate',
        pm.out_date as 'planOutDate',
        pm.number,
        pm.act_in_date as 'actInDate',
        pm.act_out_date as 'actOutDate',
        ud.sex,
        ud.name as 'userName',
        pm.is_base_permanent as 'isBasePermanent',
        pm.newcomer_match_person as 'newcomerMatchPerson',
        pm.newcomer_match_person_code as 'newcomerMatchPersonCode',
        pm.in_days as 'inDays',
        pm.status,
        pm.leave_reason as 'leaveReason',
        pm.is_finish_out_handover as 'isFinishOutHandover',
        pm.is_again_in as 'isAgainIn',
        pbu.id as 'basicUserId',
        pm.base_code as 'baseCode',
        rop.repair_org_id as 'repairOrgId',
        pmro.rsp_user_id as 'rspUserId',
        pmro.chain_path as 'chainPath',
        pmro.parent_id as 'parentId',
        pm.id as 'id',
        rop.id as 'relationId'
        from
        pmsx_person_mange pm
        inner join pmsx_relation_org_to_person rop on
        rop.person_id = pm.id and rop.logic_status = 1
        inner join pmi_user ud on
        ud.code = pm.number
        inner join pmsx_basic_user pbu on
        pbu.user_code = ud.code
        inner join pmsx_major_repair_org pmro on pmro.id = rop.repair_org_id
        WHERE rop.repair_org_id IN
        <foreach item="id" index="index" collection="orgIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND pm.logic_status = 1
        <if test="sql != null and sql != ''">AND ${sql}</if>
        <if test="keyword != null and keyword != ''">
            AND (pm.number LIKE CONCAT('%', #{keyword}, '%')
            OR ud.name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>


</mapper>
