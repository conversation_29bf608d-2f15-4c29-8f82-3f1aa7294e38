<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.CostShareMapper">
    <select id="getTotal" resultType="com.chinasie.orion.domain.entity.ProjectFullSizeReport">
SELECT
	company_id,
	wbs_expertise_center,
	business_classification,
	project_id,
	ROUND(SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END )/10000,0) AS 'operatingIncome',
	ROUND(SUM( CASE WHEN cost_type = '0002' THEN amount ELSE 0 END )/10000,0) AS 'directPurchaseCost',
	ROUND(SUM( CASE WHEN cost_type = '0003' THEN amount ELSE 0 END )/10000,0) AS 'directTravelCost',
	ROUND(SUM( CASE WHEN cost_type = '0004' THEN amount ELSE 0 END )/10000,0) AS 'laborCost',
	ROUND(SUM( CASE WHEN cost_type = '0005' THEN amount ELSE 0 END )/10000,0) AS 'technicalConfiguration',
	ROUND((SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END ) - SUM( CASE WHEN cost_type IN ( '0002', '0003', '0004', '0005' ) THEN amount ELSE 0 END ))/10000,0) AS 'projectDirectCostGross',
	ROUND((SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END ) - SUM( CASE WHEN cost_type IN ( '0002', '0003', '0004', '0005' ) THEN amount ELSE 0 END ))/SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END )*100,2) as 'projectDirectCostGrossMargin',
	ROUND(SUM( CASE WHEN cost_type = '0006' THEN amount ELSE 0 END )/10000,0) AS 'dailyAdministrativeExpenses',
	ROUND(SUM( CASE WHEN cost_type = '0007' THEN amount ELSE 0 END )/10000,0) AS 'softwareUsageFee',
	ROUND(SUM( CASE WHEN cost_type = '0008' THEN amount ELSE 0 END )/10000,0) AS 'taxeSurcharge',
	ROUND((SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END ) - SUM( CASE WHEN cost_type IN ( '0002', '0003', '0004', '0005','0006','0007','0008') THEN amount ELSE 0 END ))/10000,0) AS 'projectGrossProfit',
	ROUND((SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END ) - SUM( CASE WHEN cost_type IN ( '0002', '0003', '0004', '0005','0006','0007','0008') THEN amount ELSE 0 END ))/SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END )*100,2) as 'projectGrossMargin',

	ROUND(SUM( CASE WHEN cost_type = '0009' THEN amount ELSE 0 END )/10000,0) AS 'managementFee',
	ROUND((SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END ) - SUM( CASE WHEN cost_type IN ( '0002', '0003', '0004', '0005','0006','0007','0008','0009') THEN amount ELSE 0 END ))/10000,0) AS 'projectProfit',
	ROUND((SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END ) - SUM( CASE WHEN cost_type IN ( '0002', '0003', '0004', '0005','0006','0007','0008','0009') THEN amount ELSE 0 END ))/SUM( CASE WHEN cost_type = '0001' THEN amount ELSE 0 END )*100,2) as 'projectProfitMargin'
FROM
	pmsx_cost_share
	where year = #{year} and logic_status = 1
GROUP BY
	company_id,
	wbs_expertise_center,
	business_classification,
	project_id
    </select>

</mapper>