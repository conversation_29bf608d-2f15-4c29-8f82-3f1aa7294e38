<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.InTransactionPreReconciliationMapper">

    <select id="getDatas" resultType="com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO">
SELECT
    d.id,
	p.work_topics_name AS workTopicsName,
	p.work_topics workTopics,
	d.expertise_center AS expertiseCenter,
	d.expertise_station AS expertiseStation,
	d.number AS incomePlanDataNumber,
	d.billing_company AS billingCompany,
	d.party_A_dept_id AS partyADeptId,
	d.contract_id AS contractId,
	d.contract_number AS contractNumber,
	d.contract_name AS contractName,
	d.milestone_id AS milestoneId,
	d.milestone_name AS milestoneName,
	m.plan_accept_date AS planAcceptDate,
	d.milestone_amt AS milestoneAmt,
	d.tax_rate AS taxRate,
	d.industry,
CASE
		WHEN d.income_confirm_type != 'advance_payment_invoice' THEN
		COALESCE(d.estimate_amt,0) + COALESCE(d.inv_amt,0) + COALESCE(d.cancel_inv_amt,0) + COALESCE(d.write_off_amt,0) + COALESCE(d.advance_pay_income_amt,0) ELSE 0
	END AS includedTaxAmt,
	d.income_plan_amt AS incomePlanAmt,
	c.contract_type AS contractType,
	c.`status` AS contractStatus,
	m.tech_rsp_user AS techRspUser,
	m.bus_rsp_user AS busRspUser,
	c.client_project_nature as powerProjectPlant,
	d.party_A_contract_number as orderNumber,
	ifNull(ci.cus_name,d.party_A_dept_id_name) as  partyADeptIdName,
	ci.group_info as partASecondDept
FROM
	pmsx_income_plan p
	LEFT JOIN pmsx_income_plan_data d ON p.id = d.income_plan_id
	LEFT JOIN pms_market_contract c ON d.contract_id = c.id
	LEFT JOIN pms_contract_milestone m ON d.milestone_id = m.id
	LEFT JOIN pms_customer_info ci ON ci.id  = d.party_A_dept_id
		<if test="param.expertiseCenterName != null">
	    LEFT JOIN pmi_dept cen ON cen.id  = d.expertise_center
		</if>
		<if test="param.expertiseStationName != null">
		LEFT JOIN pmi_dept sta ON sta.id  = d.expertise_station
		</if>
WHERE
     d.logic_status = 1
	AND ( d.number IS NOT NULL and d.number != "未生成编号" )
	AND d.internal_external = "group_wide"
	and p.income_plan_type = d.data_version
		<if test="param.incomePlanDataNumber != null ">
			AND d.number like CONCAT('%', #{param.incomePlanDataNumber}, '%')
		</if>
		<if test="param.partASecondDept != null ">
			AND ci.group_info like CONCAT('%', #{param.partASecondDept}, '%')
		</if>
		<if test="param.partyADeptIdName != null ">
			AND d.party_A_dept_id_name like CONCAT('%', #{param.partyADeptIdName}, '%')
		</if>
		<if test="param.orderNumber != null ">
			AND d.party_A_contract_number like CONCAT('%', #{param.orderNumber}, '%')
		</if>
		<if test="param.contractName != null ">
			AND d.contract_name like CONCAT('%', #{param.contractName}, '%')
		</if>
		<if test="param.expertiseCenterName != null">
			AND cen.name like CONCAT('%', #{param.expertiseCenterName}, '%')
		</if>
		<if test="param.expertiseStationName != null">
			AND sta.name like CONCAT('%', #{param.expertiseStationName}, '%')
		</if>
		<if test="param.isPermission == false ">
		    and (d.project_rsp_user_id = #{param.userId}
		    <if test="param.centers.size >0" >
				or d.expertise_center in
				<foreach collection="param.centers" item="center" open="(" close=")" separator=",">
					#{center}
				</foreach>
			</if>
			<if test="param.stations.size >0" >
				or d.expertise_station in
				<foreach collection="param.stations" item="station" open="(" close=")" separator=",">
					#{station}
				</foreach>
			</if>
		    )
		</if>

	</select>


	<select id="getExportDatas" resultType="com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO">
SELECT
	p.work_topics_name AS workTopicsName,
	p.work_topics workTopics,
	d.expertise_center AS expertiseCenter,
	d.expertise_station AS expertiseStation,
	d.number AS incomePlanDataNumber,
	d.billing_company AS billingCompany,
	d.party_A_dept_id AS partyADeptId,
	d.contract_id AS contractId,
	d.contract_number AS contractNumber,
	d.contract_name AS contractName,
	d.milestone_id AS milestoneId,
	d.milestone_name AS milestoneName,
	m.plan_accept_date AS planAcceptDate,
	d.milestone_amt AS milestoneAmt,
	d.tax_rate AS taxRate,
	d.industry,
CASE
		WHEN d.income_confirm_type != 'advance_payment_invoice' THEN
		COALESCE(d.estimate_amt,0) + COALESCE(d.inv_amt,0) + COALESCE(d.cancel_inv_amt,0) + COALESCE(d.write_off_amt,0) + COALESCE(d.advance_pay_income_amt,0) ELSE 0
	END AS includedTaxAmt,
	d.income_plan_amt AS incomePlanAmt,
	c.contract_type AS contractType,
	c.`status` AS contractStatus,
	m.tech_rsp_user AS techRspUser,
	m.bus_rsp_user AS busRspUser,
	c.client_project_nature as powerProjectPlant,
		d.party_A_contract_number as orderNumber,
		ifNull(ci.cus_name,d.party_A_dept_id_name) as  partyADeptIdName,
		ci.group_info as partASecondDept
FROM
	pmsx_income_plan p
	LEFT JOIN pmsx_income_plan_data d ON p.id = d.income_plan_id
	LEFT JOIN pms_market_contract c ON d.contract_id = c.id
	LEFT JOIN pms_contract_milestone m ON d.milestone_id = m.id
		LEFT JOIN pms_customer_info ci ON ci.id  = d.party_A_dept_id
		<if test="param.expertiseCenterName != null">
			LEFT JOIN pmi_dept cen ON cen.id  = d.expertise_center
		</if>
		<if test="param.expertiseStationName != null">
			LEFT JOIN pmi_dept sta ON sta.id  = d.expertise_station
		</if>
WHERE
    d.logic_status = 1
	AND ( d.number IS NOT NULL and d.number != "未生成编号" )
	AND d.internal_external = "group_wide"
	and p.income_plan_type = d.data_version
		<if test="param.ids != null and param.ids.size() > 0">
			AND d.id IN
			<foreach item="id" collection="param.ids" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="param.ids = null ">
			<if test="param.incomePlanDataNumber != null ">
				AND d.number like CONCAT('%', #{param.incomePlanDataNumber}, '%')
			</if>
			<if test="param.partASecondDept != null ">
				AND ci.group_info like CONCAT('%', #{param.partASecondDept}, '%')
			</if>
			<if test="param.partyADeptIdName != null ">
				AND d.party_A_dept_id_name like CONCAT('%', #{param.partyADeptIdName}, '%')
			</if>
			<if test="param.orderNumber != null ">
				AND d.party_A_contract_number like CONCAT('%', #{param.orderNumber}, '%')
			</if>
			<if test="param.contractName != null ">
				AND d.contract_name like CONCAT('%', #{param.contractName}, '%')
			</if>
			<if test="param.expertiseCenterName != null">
				AND cen.name like CONCAT('%', #{param.expertiseCenterName}, '%')
			</if>
			<if test="param.expertiseStationName != null">
				AND sta.name like CONCAT('%', #{param.expertiseStationName}, '%')
			</if>
			<if test="param.isPermission == false ">
				and (d.project_rsp_user_id = #{param.userId}
				<if test="param.centers.size >0" >
					or d.expertise_center in
					<foreach collection="param.centers" item="center" open="(" close=")" separator=",">
						#{center}
					</foreach>
				</if>
				<if test="param.stations.size >0" >
					or d.expertise_station in
					<foreach collection="param.stations" item="station" open="(" close=")" separator=",">
						#{station}
					</foreach>
				</if>
				)
			</if>
		</if>

	</select>
</mapper>