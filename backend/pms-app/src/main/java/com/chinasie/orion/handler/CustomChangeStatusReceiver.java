package com.chinasie.orion.handler;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;
import com.chinasie.orion.constant.RelationClassNameConstant;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.InterfaceStatusUpdateService;
import com.chinasie.orion.service.StatusUpdateService;
import com.chinasie.orion.util.json.JsonUtils;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 * 书签数据
 *
 * @author: czz
 * @date: 2021/07/5/15:17
 * @description:
 */
@Component
@Deprecated
public class CustomChangeStatusReceiver extends AbstractChangeStatusReceiver {

    private final Logger logger = LoggerFactory.getLogger(CustomChangeStatusReceiver.class);

    @Resource
    private StatusUpdateService statusUpdateService;


    @Resource
    private InterfaceStatusUpdateService interfaceStatusUpdateService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    private static final List<String> MODEL_CLASS_NAME = new ArrayList<>() {{
        add(RelationClassNameConstant.DELIVERABLE);
        add(RelationClassNameConstant.RISK_MANAGEMENT);
        add(RelationClassNameConstant.DEMAND_MANAGEMENT);
        add(RelationClassNameConstant.QUESTION_MANAGEMENT);
        add(RelationClassNameConstant.PLAN);
    }};

    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        logger.info("状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (MODEL_CLASS_NAME.contains(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }else{
                    if(Objects.equals(RelationClassNameConstant.INTERFACE_MANAGEMENT,classVO.getClassName())||Objects.equals(RelationClassNameConstant.IDEA_FORM,classVO.getClassName())){
                        ThreadUtil.execAsync(() -> {
                            try {
                                consumerCreateMessageInterface(msg);
                            } catch (Exception e) {
                                processError(msg, channel, message, e);
                            }
                        });
                    }
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        logger.error("状态更改消息消费:msg，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "topic-change-status-queue-pms", durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status.exchange}", type = ExchangeTypes.FANOUT)
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {
        boolean result = statusUpdateService.businessChange(message);
        logger.info("项目管理状态变更成功-参数:{}-结果:{}", JsonUtils.obj2String(message), result);

    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessageInterface(ChangeStatusMessageDTO message) throws Exception {
        boolean result = interfaceStatusUpdateService.businessChange(message);
        logger.info("项目管理状态变更成功-参数:{}-结果:{}", JsonUtils.obj2String(message), result);

    }
}
