package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.CommonFileUploadDTO;
import com.chinasie.orion.domain.dto.MarketContractFileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;

import java.util.List;
import java.util.Map;

public interface CommonService {
    /**
     *  新增附件
     *
     * * @param commonFileUploadDTO
     */
    Boolean createFile(CommonFileUploadDTO commonFileUploadDTO)throws Exception;

    /**
     *  查询附件
     *
     * * @param dataId
     */
    List<FileVO> queryFile(String dataId)throws Exception;


    /**
     *  删除附件
     *
     * * @param fileId
     */
    Boolean deleteFile(List<String> fileIds)throws Exception;
}
