package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.dto.CommonFileUploadDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.service.CommonService;
import com.chinasie.orion.service.ProjectCollectionStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private FileApiService fileApi;

    @Override
    public Boolean createFile(CommonFileUploadDTO commonFileUploadDTO) throws Exception {
        List<FileDTO> fileList = commonFileUploadDTO.getFileList();
        if (CollectionUtils.isEmpty(fileList)) {
            return false;
        }
        fileList.forEach(item -> {
            item.setDataId(commonFileUploadDTO.getDataId());
        });
        fileApi.batchSaveFile(fileList);
        return true;
    }

    @Override
    public List<FileVO> queryFile(String dataId) throws Exception {
        List<FileVO> fileVOList = fileApi.getFilesByDataId(dataId);
        return fileVOList;
    }

    @Override
    public Boolean deleteFile(List<String> fileIds) throws Exception {
        return fileApi.deleteByIds(fileIds);
    }
}
