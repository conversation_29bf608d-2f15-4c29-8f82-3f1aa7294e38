package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.ProjectBudget;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;

import java.util.List;

public interface ProjectOverviewNewService {
    /**
     * 获取风险统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectRiskCountVO getRiskCount(List<String> projectId) throws Exception;

    /**
     * 获取问题统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectProblemCountVO getProblemCount(List<String> projectId) throws Exception;

    /**
     * 获取需求统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectDemandCountVO getDemandCount(List<String> projectId) throws Exception;

    /**
     * 获取项目预警信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<WarningSettingMessageRecordVO> getwarningSettingMessageRecords(List<String> projectId) throws Exception;

    /**
     * 获取营收统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectRevenueTotalVO getProjectRevenueTotalVO(List<String> projectId) throws Exception;

    /**
     * 获取预算统计数据
     *
     * @param projectId
     * @param type
     * @param year
     * @param id
     * @return
     * @throws Exception
     */
    ProjectBudgetTotalVO getBudgetTotalVO(List<String> projectIds, String type, String year, String id) throws Exception;

    /**
     * 获取计划统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectPlanCountVO getPlanCount(List<String> projectId) throws Exception;

    /**
     * 获取按月统计的计划数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectPlanCountVO> getPlanMonth(List<String> projectId) throws Exception;

    /**
     * 获取物资统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    GoodsServiceCountVO getGoodsServiceCount(List<String> projectId) throws Exception;

    /**
     * 获取项目详情信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectInfoVO getProjectInfo(String projectId) throws Exception;

    /**
     * 获取项目里程碑信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectMilestoneViewVO> getProjectMilestoneViewList(String projectId) throws Exception;

    GoodsServiceSituationVO getGoodsServiceSituation(String projectId) throws Exception;

    GoodsServiceSituationVO getGoodsPlanSituation(String projectId) throws Exception;

    List<ProjectBudgetTotalVO> getBudgetVOs(List<String> projectIds) throws Exception;

    /**
     * 获取项目工时信息
     *
     * @param
     * @return
     * @throws Exception
     */
    ProjectWorkHourVO getProjectWorkHourInfo(List<String> projectIds) throws Exception;
}
