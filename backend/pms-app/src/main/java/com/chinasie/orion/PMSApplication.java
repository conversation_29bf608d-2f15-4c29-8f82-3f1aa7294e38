package com.chinasie.orion;

import com.chinasie.orion.util.LogUtil;
import com.mzt.logapi.starter.annotation.EnableLogRecord;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableDiscoveryClient
@EnableFeignClients
@EnableCaching
@EnableScheduling
@SpringBootApplication
@EnableLogRecord(tenant = "com.chinasie.orion")
@MapperScan(basePackages = {"com.chinasie.orion.*.repository"})
@MapperScan(basePackages = {"com.chinasie.orion.repository"})
@MapperScan(basePackages = {"com.chinasie.orion.*.mapper"})
@MapperScan(basePackages = {"com.chinasie.orion.number.api.repository"})
@MapperScan(basePackages = {"com.chinasie.orion.file.api.repository"})
@MapperScan(basePackages = {"com.chinasie.orion.base.api.repository"})
@MapperScan(basePackages = {"com.chinasie.orion.permission.core.mapper"})
//@MapperScan(basePackages = {"com.chinasie.orion.base.api"})
public class PMSApplication {
    public static void main(String[] args) {
        try {
            SpringApplication.run(PMSApplication.class, args);
        } catch (Exception e) {
            LogUtil.error("项目启动失败", e);
        }

    }
}
