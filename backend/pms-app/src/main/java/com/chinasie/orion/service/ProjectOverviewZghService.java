package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeForZghDTO;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeForZghVO;
import com.chinasie.orion.domain.vo.AcceptanceFormVO;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.ProjectConditionVO;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;
import com.chinasie.orion.domain.vo.projectOverviewZgh.*;
import com.chinasie.orion.management.domain.vo.ContractInfoVO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestVO;
import com.chinasie.orion.management.domain.vo.NcfPurchProjectImplementationVO;

import java.util.List;

public interface ProjectOverviewZghService {


    /**
     * 项目基础信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectBaseVO getProjectBase(String projectId) throws Exception;


    /**
     * 收入实时概况
     *
     * @param projectId
     * @param costBusType
     * @return
     */
    ProjectIncomeVO getProjectIncome(String projectId, String costBusType);


    /**
     * 支出实时概况
     *
     * @param projectId
     * @return
     */
    ProjectCostVO getProjectCost(String projectId);


    /**
     * 项目毛利
     *
     * @param projectId
     * @return
     */
    ProjectGrossProfitVO getProjectGrossProfit(String projectId);


    /**
     * 设备/软件使用费，日常行政管理费用，前台项目部成本分摊，项目毛利，内部委托成本，税费
     *
     * @param projectId
     * @return
     */
    ProjectOtherCostVO getProjectOtherCost(String projectId);


    /**
     * 项目里程碑
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectMilestoneVO> getProjectMilestones(String projectId) throws Exception;


    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     */
    List<ProjectLifeVO> getProjectLife(String projectId) throws Exception;


    /**
     * 项目全生命周期监控——采购申请
     *
     * @param projectId
     * @return
     */
    List<NcfFormpurchaseRequestVO> getProjectLifeOfPurchaseRequest(String projectId);

    /**
     * 项目全生命周期监控——采购启动
     *
     * @param projectId
     * @return
     */
    List<NcfPurchProjectImplementationVO> getProjectLifeOfImplementation(String projectId);


    /**
     * ZGH修改节点的内容、附件等属性.
     *
     * @param projectId
     * @param lifeCycleNode
     * @return
     */
    String updateProjectLifeCycleNodeForZGh(String projectId, ProjectLifeCycleNodeForZghDTO lifeCycleNode) throws Exception;

    /**
     * ZGH获取节点的内容、附件等属性.
     *
     * @param projectId
     * @param nodeId
     * @return
     */
    ProjectLifeCycleNodeForZghVO getProjectLifeCycleNodeForZGh(String projectId, String nodeId) throws Exception;

    /**
     * 项目全生命周期监控——合同执行
     *
     * @param projectId
     * @return
     */
    List<ContractInfoVO> getProjectLifeOfContract(String projectId);


    /**
     * 项目全生命周期监控——里程碑交付
     *
     * @param projectId
     * @return
     */
    List<ContractMilestoneVO> getProjectLifeOfMilestoneDelivery(String projectId);


    /**
     * 项目全生命周期监控——验收结题
     *
     * @param projectId
     * @return
     */
    List<AcceptanceFormVO> getProjectLifeOfAcceptance(String projectId);

    /**
     * 项目全生命周期监控——项目后评价
     *
     * @param projectId
     * @return
     */
    List<ProjectConditionVO> getPostEvaluation(String projectId);


    /**
     * 计划状态,计划执行异常
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectPlanStatusAndOverdueCountVO getProjectPlanCount(String projectId) throws Exception;


    /**
     * 项目成员计划数量
     *
     * @param projectId
     * @return
     */
    ProjectPlanMemberCountVO getProjectPlanMemberCount(String projectId) throws Exception;


    /**
     * 异常计划明细信息
     *
     * @param projectId
     * @return
     */
    ProjectPlanOverdueCountVO getProjectPlanOverdueCount(String projectId, Integer status, Integer dateType) throws Exception;


    /**
     * 项目预算概览
     *
     * @param projectId
     * @param type
     * @param year
     * @param budgetTypeId
     * @return
     */
    ProjectBudgetOverviewVO getProjectBudgetOverview(String projectId, Integer type, String year, String budgetTypeId);

    /**
     * 项目营收
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectRevenueOverviewVO getProjectRevenueOverview(String projectId) throws Exception;


    /**
     * 获取风险统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectRiskCountVO getRiskCount(String projectId) throws Exception;

    /**
     * 获取问题统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectProblemCountVO getProblemCount(String projectId) throws Exception;

    /**
     * 获取需求统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectDemandCountVO getDemandCount(String projectId) throws Exception;


    /**
     * 项目质量
     *
     * @param projectId
     * @return
     */
    ProjectQualityCountVO getQualityCount(String projectId);


    /**
     * 项目物资
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    GoodsServiceCountVO getGoodsServiceCount(String projectId) throws Exception;


    /**
     * 获取项目工时信息
     *
     * @param
     * @return
     * @throws Exception
     */
    ProjectWorkHourVO getProjectWorkHour(String projectIds) throws Exception;


    /**
     * 获取项目预警信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<WarningSettingMessageRecordVO> getWarningRecords(String projectId) throws Exception;


    /**
     * 项目知识文档库
     *
     * @param projectId
     * @return
     */
    List<ProjectDocumentCountVO> getDocumentCount(String projectId, Integer step) throws Exception;



}
