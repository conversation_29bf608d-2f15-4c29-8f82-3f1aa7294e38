package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeForZghDTO;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeForZghVO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceForm;
import com.chinasie.orion.domain.entity.production.JobManage;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;
import com.chinasie.orion.domain.vo.projectOverviewZgh.*;
import com.chinasie.orion.domain.vo.projectOverviewZgh.ProjectIncomeVO;
import com.chinasie.orion.domain.vo.statics.BatchProjectUserVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.file.api.service.LyraFileApiService;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.ContractInfoVO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestVO;
import com.chinasie.orion.management.domain.vo.NcfPurchProjectImplementationVO;
import com.chinasie.orion.management.service.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.GoodsServicePlanMapper;
import com.chinasie.orion.repository.ProjectContractRepository;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectOverviewZghServiceImpl implements ProjectOverviewZghService {

    public static final String DICT_PROJECT_TYPE = "pms_project_type";

    @Autowired
    private ProjectService projectService;

    @Resource
    private DictBo dictBo;

    @Resource
    private UserBo userBo;

    @Resource
    private ProjectRoleUserService projectRoleUserService;

    @Resource
    private ProjectSchemeService projectSchemeService;

    @Resource
    private FileApiService fileApiService;


    @Resource
    private ProjectPayService projectPayService;

    @Resource
    private MaterialManageService materialManageService;

    @Resource
    private ProjectInitiationService projectInitiationService;

    @Autowired
    private ProjectInitiationWBSService projectInitiationWBSService;

    @Resource
    private MarketContractService marketContractService;

    @Resource
    private PasFeignService pasFeignService;

    @Resource
    private NcfFormpurchaseRequestService ncfFormpurchaseRequestService;

    @Resource
    private NcfFormpurchaseRequestDetailService ncfFormpurchaseRequestDetailService;

    @Resource
    private NcfPurchProjectImplementationService ncfPurchProjectImplementationService;

    @Resource
    private ContractInfoService contractInfoService;

    @Resource
    private NonContractProcService nonContractProcService;

    @Resource
    private QuotationManagementService quotationManagementService;

    @Autowired
    private RequirementMangementService requirementMangementService;

    @Resource
    private ContractMilestoneService contractMilestoneService;

    @Resource
    private BudgetManagementService budgetManagementService;

    @Resource
    private BudgetExpendFormService budgetExpendFormService;

    @Autowired
    private RiskManagementService riskManagementService;

    @Autowired
    private QuestionManagementService questionManagementService;

    @Autowired

    private DemandManagementService demandManagementService;

    @Autowired
    private WarningSettingMessageRecordService warningSettingMessageRecordService;

    @Autowired
    private ProjectFundsReceivedService projectFundsReceivedService;

    @Autowired
    private ExpenseSubjectService expenseSubjectService;

    @Autowired
    private WorkHourEstimateService workHourEstimateService;

    @Autowired
    private WorkHourFillService workHourFillService;

    @Resource
    private ProjectContractRepository projectContractRepository;

    @Resource
    private UserBaseApiService userBaseApiService;

    @Resource
    private DeptBaseApiService deptBaseApiService;


    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;


    @Autowired
    private ProjectRoleService projectRoleService;


    @Autowired
    private ProjectLifeCycleNodeZghService projectLifeCycleNodeZghService;

    @Autowired
    private LyraFileApiService lyraFileApiService;

    @Autowired
    private AcceptanceFormService acceptanceFormService;

    @Autowired
    private ProjectConditionService projectConditionService;
    @Autowired
    private EvaluationProjectService evaluationProjectService;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Resource
    private QuestionToRiskService questionToRiskService;


    /**
     * 获取项目详情信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public ProjectBaseVO getProjectBase(String projectId) throws Exception {
        ProjectBaseVO projectBaseVO = new ProjectBaseVO();
        Project project = projectService.getById(projectId);
        if (Objects.isNull(project)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该项目不存在，或者已删除。");
        }
        projectBaseVO.setId(project.getId());

        //项目经理
        if (StringUtils.isNotBlank(project.getResPerson())) {
            UserVO userVO = userBo.getUserById(project.getResPerson());
            if (userVO != null) {
                projectBaseVO.setPm(userVO.getName());
            }
        }


        //项目周期
        projectBaseVO.setProjectStartTime(project.getProjectStartTime());
        projectBaseVO.setProjectEndTime(project.getProjectEndTime());

        //项目类型
        Map<String, String> projectTypeDictValueMap = dictBo.getDictValue(DICT_PROJECT_TYPE);
        projectBaseVO.setTypeName(projectTypeDictValueMap.get(project.getProjectType()));

        // 异步查询项目进度
        CompletableFuture<Void> projectProgressFuture = CompletableFuture.runAsync(() -> {
            try {
                LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
                String select = "count(*) as planTotal," +
                        "ifnull(sum( CASE WHEN `status` = " + 111 + " THEN 1 ELSE 0 END ),0) AS completeCount ";
                lambdaQueryWrapperX.select(select);
                lambdaQueryWrapperX.eqIfPresent(ProjectScheme::getProjectId, projectId);
                Map<String, Object> map = projectSchemeService.getMap(lambdaQueryWrapperX);
                int total = Integer.parseInt(map.get("planTotal").toString());
                int completeCount = Integer.parseInt(map.get("completeCount").toString());
                if (total == 0) {
                    projectBaseVO.setSchedule((double) 0);
                } else {
                    projectBaseVO.setSchedule((double) completeCount / total);
                }
            } catch (Exception ex) {
                log.error("查询项目进度异常", ex);
            }

        });

        // 异步查询项目成员
        CompletableFuture<Void> projectMembersFuture = CompletableFuture.runAsync(() -> {
            try {
                LambdaQueryWrapper<ProjectRoleUser> roleUserLambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectRoleUser.class);
                roleUserLambdaQueryWrapper.eq(ProjectRoleUser::getProjectId, projectId);
                long count = projectRoleUserService.count(roleUserLambdaQueryWrapper);
                projectBaseVO.setMemberCount(count);
            } catch (Exception ex) {
                log.error("查询项目成员异常", ex);
            }
        });

        // 异步查询设备物料
        CompletableFuture<Void> equipmentMaterialsFuture = CompletableFuture.runAsync(() -> {
            try {
                MPJLambdaWrapper<MaterialManage> materialManageMPJLambdaWrapper = new LambdaQueryWrapperX<>(MaterialManage.class)
                        .rightJoin(com.chinasie.orion.domain.entity.production.JobManage.class, on -> on.eq(MaterialManage::getJobNo, com.chinasie.orion.domain.entity.production.JobManage::getNumber))
                        .rightJoin(ProjectScheme.class, on -> on.eq(ProjectScheme::getId, JobManage::getPlanSchemeId).eq(ProjectScheme::getProjectId, projectId));

                long count = materialManageService.count(materialManageMPJLambdaWrapper);

                projectBaseVO.setMatterCount(count);
            } catch (Exception ex) {
                log.error("查询设备物料异常", ex);
            }
        });

        // 异步查询作业总数
        CompletableFuture<Void> totalTasksFuture = CompletableFuture.runAsync(() -> {
            try {
                long count = projectSchemeService.count(new LambdaQueryWrapperX<>(ProjectScheme.class)
                        .eq(ProjectScheme::getProjectId, projectId)
                        .eq(ProjectScheme::getIsWorkJob, true)
                );
                projectBaseVO.setJobCount(count);
            } catch (Exception ex) {
                log.error("查询作业总数异常", ex);
            }
        });

        // 等待所有查询完成
        CompletableFuture.allOf(
                projectProgressFuture,
                projectMembersFuture,
                equipmentMaterialsFuture,
                totalTasksFuture
        ).get();
        return projectBaseVO;
    }


    @Override
    public ProjectIncomeVO getProjectIncome(String projectId, String costBusType) {
        ProjectIncomeVO result = new ProjectIncomeVO();
        Project project = projectService.getById(projectId);
        String projectType = project.getProjectType();
        List<String> constBusTypes = Lists.newArrayList();
        Map<String, String> marketContractNameMap = new HashMap<>();
        LambdaQueryWrapperX<ContractMilestone> queryWrapper =  new LambdaQueryWrapperX<>(ContractMilestone.class);
        if(!ProjectTypeEnum.PROJECT_TYPE_ZZ.getValue().equals(projectType)){
            LambdaQueryWrapperX<ProjectInitiation> lambdaQueryWrapperX =  new LambdaQueryWrapperX();
            lambdaQueryWrapperX.select("t.*,GROUP_CONCAT( t1.wbs_element ) as wbsElement");
            lambdaQueryWrapperX.leftJoin(ProjectInitiationWBS.class,ProjectInitiationWBS::getProjectNumber,ProjectInitiation::getProjectNumber);
            lambdaQueryWrapperX.groupBy(ProjectInitiation::getId);
            lambdaQueryWrapperX.eq(ProjectInitiation::getProjectNumber, project.getNumber());
            List<ProjectInitiation> projectInitiations = projectInitiationService.list(lambdaQueryWrapperX);
            if (CollectionUtils.isEmpty(projectInitiations)) {
                return result;
            }
//        List<String> wbsElementList = projectInitiations.stream().filter(o -> StrUtil.isNotBlank(o.getWbsElement()))
//                .map(o -> Arrays.asList(o.getWbsElement().split(","))).flatMap(Collection::stream).distinct().collect(Collectors.toList());
//        if(CollectionUtil.isEmpty(wbsElementList)){
//            return result;
//        }
            LambdaQueryWrapperX<ProjectInitiationWBS> wBSLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectInitiationWBS.class);
            wBSLambdaQueryWrapperX.eq(ProjectInitiationWBS :: getProjectNumber, project.getNumber());
            wBSLambdaQueryWrapperX.select(ProjectInitiationWBS::getId, ProjectInitiationWBS::getBusiness);
            List<ProjectInitiationWBS> projectInitiationWBS =  projectInitiationWBSService.list(wBSLambdaQueryWrapperX);

            List<String> marketContractNumbers = projectInitiations.stream().filter(o -> StrUtil.isNotBlank(o.getContractNumbers())).map(o -> Arrays.asList(o.getContractNumbers().split(","))).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(marketContractNumbers)) {
                return result;
            }

            List<MarketContract> marketContracts = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class).in(MarketContract::getNumber, marketContractNumbers));
            if (CollectionUtils.isEmpty(marketContracts)) {
                return result;
            }
            marketContractNameMap = marketContracts.stream().collect(Collectors.toMap(MarketContract::getId, MarketContract::getName));
            List<String> marketContractsIds = marketContracts.stream().map(MarketContract::getId).collect(Collectors.toList());
            //查询子合同
            List<MarketContract> childContracts = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class).in(MarketContract::getFrameContractId, marketContractsIds));
            if(!CollectionUtils.isEmpty(childContracts)){
                List<String> childContractIds = childContracts.stream().map(MarketContract::getId).collect(Collectors.toList());
                marketContractsIds.addAll(childContractIds);
                marketContractNameMap.putAll(childContracts.stream().collect(Collectors.toMap(MarketContract::getId, MarketContract::getName)));
            }


            queryWrapper.in(ContractMilestone::getContractId, marketContractsIds);

            if(!CollectionUtils.isEmpty(projectInitiationWBS)){
                List<String> businesses = projectInitiationWBS.stream().filter(item -> StringUtils.isNotBlank(item.getBusiness())).map(ProjectInitiationWBS :: getBusiness).distinct().collect(Collectors.toList());
                constBusTypes.addAll(businesses);
            }
        }
        else{
            List<MarketContract> marketContracts = marketContractService.list();
            marketContractNameMap = marketContracts.stream().collect(Collectors.toMap(MarketContract::getId, MarketContract::getName));
            constBusTypes.add("ZZ");
        }
        queryWrapper.in(ContractMilestone::getStatus, Arrays.asList(110,130));
        if(StringUtils.isNotEmpty(costBusType)){
            queryWrapper.eq(ContractMilestone::getCostBusType, costBusType);
        }


//        //特殊处理 先根据前缀找到完整成本业务分类
//        List<DictValueVO> dictValues = dictRedisHelper.getDictListByCode("cos_business_type");
//        List<String> dictValueList = dictValues.stream().map(DictValueVO::getNumber).collect(Collectors.toList());
//        for(String wbsElement : wbsElementList){
//            String[] wbsElementArray = wbsElement.split("\\.");
//            if(wbsElementArray.length > 1){
//                for(String dict:dictValueList){
//                    if(dict.toLowerCase().startsWith(wbsElementArray[1].toLowerCase())){
//                        constBusTypes.add(dict);
//                        break;
//                    }
//                }
//            }
//        }
        if(CollectionUtil.isNotEmpty(constBusTypes)){
            queryWrapper.in(ContractMilestone::getCostBusType, constBusTypes);
        }else{
            return result;
        }
        List<ContractMilestone> contractMilestones = contractMilestoneService.list(queryWrapper);
        if (CollectionUtils.isEmpty(contractMilestones)) {
            return result;
        }
        BigDecimal planIncome = contractMilestones.stream().map(ContractMilestone::getMilestoneAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setPlanIncome(planIncome);
        BigDecimal practiceIncome = contractMilestones.stream().map(ContractMilestone::getActualMilestoneAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setPracticeIncome(practiceIncome);
        result.setIncome(practiceIncome);
        List<DictValueVO> cosDict = dictRedisHelper.getDictListByCode(DictConts.COS_BUSINESS_TYPE);
        Map<String, DictValueVO> cosDictMap = cosDict.stream().collect(Collectors.toMap(DictValueVO::getValue, Function.identity()));
        final Map<String, String> marketContractNameMapTemp = marketContractNameMap;
        List<ProjectIncomeVO.ProjectIncomeItemVO> incomes = new ArrayList<>() {{
            contractMilestones.forEach(o -> {
                o.setCostBusTypeName(cosDictMap.getOrDefault(o.getCostBusType(), new DictValueVO()).getDescription());

                add(new ProjectIncomeVO.ProjectIncomeItemVO(o.getContractId(),
                        marketContractNameMapTemp.get(o.getContractId()),
                        o.getMilestoneName(),
                        o.getMilestoneAmt(),
                        o.getPlanAcceptDate(),
                        o.getActualMilestoneAmt(),
                        (o.getMilestoneAmt()== null?new BigDecimal(0):o.getMilestoneAmt()).subtract(Objects.nonNull(o.getActualMilestoneAmt()) ? o.getActualMilestoneAmt() : BigDecimal.ZERO),
                        o.getTotalAcceptRate(),
                        o.getCostBusType(),
                        o.getCostBusTypeName()
                ));
            });
        }};
        result.setIncomes(incomes);
        return result;
    }

    @Override
    public ProjectCostVO getProjectCost(String projectId) {
        ProjectCostVO result = new ProjectCostVO();
        result.setCosts(new ArrayList<>());
        List<ProjectPay> projectPays = projectPayService.list(new LambdaQueryWrapperX<>(ProjectPay.class).eq(ProjectPay::getProjectId, projectId));
        if (CollectionUtils.isEmpty(projectPays)) {
            return result;
        }
        BigDecimal planCost = projectPays.stream().map(ProjectPay::getPlanAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setPlanIncome(planCost);
        BigDecimal actualCost = projectPays.stream().map(ProjectPay::getActualAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setPracticeIncome(actualCost);
        result.setCost(actualCost);
        projectPays.forEach(pp -> {

            /**
             项目支出表逻辑更新
             2.结余金额=计划金额-承诺金额-实际金额
             3.进度=实际金额/计划金额 ×%
             */
            if (Objects.isNull(pp.getPlanAmount())) {
                pp.setPlanAmount(BigDecimal.ZERO);
            }
            if (Objects.isNull(pp.getPromiseAmount())) {
                pp.setPromiseAmount(BigDecimal.ZERO);
            }
            if (Objects.isNull(pp.getActualAmount())) {
                pp.setActualAmount(BigDecimal.ZERO);
            }
            BigDecimal progress;
            if (pp.getPlanAmount().compareTo(BigDecimal.ZERO) == 0) {
                if (pp.getActualAmount().compareTo(BigDecimal.ZERO) == 0) {
                    progress = BigDecimal.ZERO;
                } else {
                    progress = BigDecimal.valueOf(1);
                }

            } else {
                progress = pp.getActualAmount().divide(pp.getPlanAmount(), 4, RoundingMode.HALF_UP);
            }

            result.getCosts().add(new ProjectCostVO.ProjectCostItemVO(pp.getId(), pp.getSubjectId(),
                    pp.getSubjectName(),
                    pp.getPlanAmount(),
                    pp.getPromiseAmount(),
                    pp.getShopMallPurchaseAmount(),
                    pp.getNormalPurchaseAmount(),
                    pp.getActualAmount(),
                    pp.getPlanAmount().subtract(pp.getPromiseAmount()).subtract(pp.getActualAmount()),
                    progress.multiply(BigDecimal.valueOf(100))));
        });
        return result;
    }

    @Override
    public ProjectGrossProfitVO getProjectGrossProfit(String projectId) {
        ProjectGrossProfitVO result = new ProjectGrossProfitVO();

        ProjectIncomeVO projectIncome = getProjectIncome(projectId, null);
        projectIncome = Objects.isNull(projectIncome) ? new ProjectIncomeVO() : projectIncome;

        ProjectCostVO projectCost = getProjectCost(projectId);
        projectCost = Objects.isNull(projectCost) ? new ProjectCostVO() : projectCost;

        // 对计划收入和成本、实际收入和成本进行非空和除零检查
        BigDecimal planIncome = Objects.isNull(projectIncome.getPlanIncome()) ? BigDecimal.ZERO : projectIncome.getPlanIncome();
        BigDecimal planCost = Objects.isNull(projectCost.getPlanIncome()) ? BigDecimal.ZERO : projectCost.getPlanIncome();


        BigDecimal practiceIncome = Objects.isNull(projectIncome.getPracticeIncome()) ? BigDecimal.ZERO : projectIncome.getPracticeIncome();
        BigDecimal practiceCost = Objects.isNull(projectCost.getPracticeIncome()) ? BigDecimal.ZERO : projectCost.getPracticeIncome();


        // 项目毛利（计划）=项目收入（计划）-所有的成本（计划）
        BigDecimal planProfit = planIncome.subtract(planCost);
        result.setPlan(planProfit);

        // 项目毛利（实际）=项目收入（实际）-所有的成本（实际）
        BigDecimal actualProfit = practiceIncome.subtract(practiceCost);
        result.setPractice(actualProfit);

        // 项目毛利润（计划）=项目毛利（计划）/项目收入（计划）

        BigDecimal planProfitRate;
        if (planIncome.compareTo(BigDecimal.ZERO) == 0) {
            if (planProfit.compareTo(BigDecimal.ZERO) == 0) {
                planProfitRate = BigDecimal.ZERO;
            } else {
                planProfitRate = BigDecimal.valueOf(1);
            }

        } else {
            planProfitRate = planProfit.divide(planIncome, 4, RoundingMode.HALF_UP);
        }
        result.setPlanRate(planProfitRate.multiply(BigDecimal.valueOf(100)));
        // 项目毛利润（实际）=项目毛利（实际）/项目收入（实际）
        BigDecimal actualProfitRate;
        if (practiceIncome.compareTo(BigDecimal.ZERO) == 0) {
            if (actualProfit.compareTo(BigDecimal.ZERO) == 0) {
                actualProfitRate = BigDecimal.ZERO;
            } else {
                actualProfitRate = BigDecimal.valueOf(1);
            }

        } else {
            actualProfitRate = actualProfit.divide(practiceIncome, 4, RoundingMode.HALF_UP);
        }

        result.setPracticeRate(actualProfitRate.multiply(BigDecimal.valueOf(100)));

        return result;
    }

    @Override
    public ProjectOtherCostVO getProjectOtherCost(String projectId) {
        // TODO 暂不实现
        return new ProjectOtherCostVO();
    }

    @Override
    public List<ProjectLifeVO> getProjectLife(String projectId) throws Exception {
        Map<String, String> idStatusMap = new HashMap<>();
        Map<String, String> idDataIdMap = new HashMap<>();
        try {
            //查询项目信息
            Project project = projectService.getById(projectId);
            if (Objects.isNull(project)) {
                throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "项目信息不存在");
            }
            //项目关联项目立项
            ProjectInitiation projectInitiation = projectInitiationService.getOne(new LambdaQueryWrapperX<>(ProjectInitiation.class).eq(ProjectInitiation::getProjectNumber, project.getNumber()));
            if (!Objects.isNull(projectInitiation)) {
                idDataIdMap.put("1-5", projectInitiation.getId());
                idDataIdMap.put("1-6", projectInitiation.getId());
                if (Objects.equals(projectInitiation.getProjectLabel(), "1")) {
                    idStatusMap.put("1-5", ProjectLifeVO.NodeState.FINISHED.name());
                    idStatusMap.put("1-6", ProjectLifeVO.NodeState.NOT_START.name());

                    //项目立项关联合同
                    String contractNumbers = projectInitiation.getContractNumbers();
                    List<MarketContract> marketContractS = new ArrayList<>();
                    if (StrUtil.isNotBlank(contractNumbers)) {
                        List<String> contractNumberList = Arrays.asList(contractNumbers.split(","));
                        //调用合同接口
                        marketContractS = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class)
                                .in(MarketContract::getNumber, contractNumberList));
                    }
                    //合同关联报价
                    List<QuotationManagement> quotationManagements = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(marketContractS)) {
                        idDataIdMap.put("1-4", marketContractS.get(0).getId());
                        idDataIdMap.put("4-1", marketContractS.get(0).getId());
                        idDataIdMap.put("3-2-1", marketContractS.get(0).getId());
                        idDataIdMap.put("3-2-2", marketContractS.get(0).getId());
                        idStatusMap.put("1-4", ProjectLifeVO.NodeState.FINISHED.name());
                        List<String> quoteIds = marketContractS.stream().map(MarketContract::getQuoteId).distinct().collect(Collectors.toList());
                        quotationManagements = quotationManagementService.listByIds(quoteIds);


                        //合同里程碑完成
                        List<String> marketContractsIds = marketContractS.stream().map(MarketContract::getId).collect(Collectors.toList());
                        List<ContractMilestone> contractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class).in(ContractMilestone::getContractId, marketContractsIds));
                        if (!CollectionUtils.isEmpty(contractMilestones) && contractMilestones.stream().allMatch(contractMilestone -> Objects.equals(contractMilestone.getStatus(), MarketContractMilestoneStatusEnum.COMPLATED.getStatus()))) {
                            idStatusMap.put("4-1", ProjectLifeVO.NodeState.FINISHED.name());
                        } else {
                            idStatusMap.put("4-1", ProjectLifeVO.NodeState.UNDERWAY.name());
                        }

                    }

                    List<RequirementMangement> requirementManagements = new ArrayList<>();
                    //报价关联需求
                    if (!CollectionUtils.isEmpty(quotationManagements)) {
                        idDataIdMap.put("1-3", quotationManagements.get(0).getId());
                        idStatusMap.put("1-3", ProjectLifeVO.NodeState.FINISHED.name());


                        idDataIdMap.put("1-2", quotationManagements.get(0).getId());
                        idStatusMap.put("1-2", ProjectLifeVO.NodeState.FINISHED.name());

                        List<String> requirementIds = quotationManagements.stream().map(QuotationManagement::getRequirementId).distinct().collect(Collectors.toList());
                        requirementManagements = requirementMangementService.listByIds(requirementIds);
                    }
                    if (!CollectionUtils.isEmpty(requirementManagements)) {
                        idDataIdMap.put("1-1", requirementManagements.get(0).getId());
                        idStatusMap.put("1-1", ProjectLifeVO.NodeState.FINISHED.name());


                        idDataIdMap.put("0-1", requirementManagements.get(0).getId());
                        idStatusMap.put("0-1", ProjectLifeVO.NodeState.FINISHED.name());
                    }
                }
                else if (Objects.equals(projectInitiation.getProjectLabel(), "2")) {
                    idStatusMap.put("1-5", ProjectLifeVO.NodeState.NOT_START.name());
                    idStatusMap.put("1-6", ProjectLifeVO.NodeState.FINISHED.name());

                    //项目立项关联经营管理模块线索
                    String clueNumbers = projectInitiation.getClueNumbers();
                    List<Map<String, Object>> clues = new ArrayList<>();
                    if (StrUtil.isNotBlank(clueNumbers)) {
                        List<String> clueList = Arrays.asList(clueNumbers.split(","));
                        //feign调用pas接口
                        ResponseDTO<List<Map<String, Object>>> responseDTO = pasFeignService.listByClueIds(clueList);
                        if (Objects.nonNull(responseDTO) && Objects.nonNull(responseDTO.getResult())) {
                            clues = responseDTO.getResult();
                        }
                    }
                    if (!CollectionUtils.isEmpty(clues)) {
                        idDataIdMap.put("0-2", (String) clues.get(0).get("id"));
                        idStatusMap.put("0-2", ProjectLifeVO.NodeState.FINISHED.name());
                    }
                }
//                return new ProjectLifeVO().setNodeStatus(idStatusMap, idDataIdMap);
            }

            idStatusMap.put("1", ProjectLifeVO.NodeState.FINISHED.name());
            idStatusMap.put("2", ProjectLifeVO.NodeState.FINISHED.name());


            //项目立项关联采购立项
            List<NcfFormpurchaseRequestDetail> purchaseRequestDetails = ncfFormpurchaseRequestDetailService.list(new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class)
                    .like(NcfFormpurchaseRequestDetail::getWbsId, project.getNumber()));
            //采购立项关联采购实施
            List<NcfPurchProjectImplementation> purchaseImplementations = new ArrayList<>();
            if (!CollectionUtils.isEmpty(purchaseRequestDetails)) {

                List<String> requestCodes = purchaseRequestDetails.stream().map(NcfFormpurchaseRequestDetail::getProjectCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                List<NcfFormpurchaseRequest> purchaseRequests = ncfFormpurchaseRequestService.list(new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class).in(NcfFormpurchaseRequest::getCode, requestCodes));


                idDataIdMap.put("2-1", purchaseRequests.get(0).getId());
                idStatusMap.put("2-1", ProjectLifeVO.NodeState.FINISHED.name());
                if (!CollectionUtils.isEmpty(purchaseRequests)) {
                    List<String> purchaseRequestCodes = purchaseRequests.stream().map(NcfFormpurchaseRequest::getCode).collect(Collectors.toList());
                    purchaseImplementations = ncfPurchProjectImplementationService.list(new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class).in(NcfPurchProjectImplementation::getPurchReqEcpCode, purchaseRequestCodes));
                }
            }

            //采购实施关联采购合同
            List<ContractInfo> purchaseContractInfos = new ArrayList<>();
            if (!CollectionUtils.isEmpty(purchaseImplementations)) {

                idDataIdMap.put("2-2", purchaseImplementations.get(0).getId());
                idStatusMap.put("2-2", ProjectLifeVO.NodeState.FINISHED.name());

                idDataIdMap.put("2-3", purchaseImplementations.get(0).getId());
                idStatusMap.put("2-3", ProjectLifeVO.NodeState.FINISHED.name());


                List<String> purchaseContractNumbers = purchaseImplementations.stream().map(NcfPurchProjectImplementation::getPurchReqEcpCode).distinct().collect(Collectors.toList());
                purchaseContractInfos = contractInfoService.list(new LambdaQueryWrapperX<>(ContractInfo.class).in(ContractInfo::getPurchaseApplicant, purchaseContractNumbers));

                if (!CollectionUtils.isEmpty(purchaseContractInfos)) {
                    idDataIdMap.put("2-4", purchaseContractInfos.get(0).getId());
                    idStatusMap.put("2-4", ProjectLifeVO.NodeState.FINISHED.name());

                    idDataIdMap.put("2-6", purchaseContractInfos.get(0).getId());
                    boolean isContractIng = purchaseContractInfos.stream().anyMatch(o -> "履行中".equals(o.getExecutionStatusName()));
                    if (isContractIng) {
                        idStatusMap.put("2-6", ProjectLifeVO.NodeState.FINISHED.name());
                        idStatusMap.put("2-7", ProjectLifeVO.NodeState.FINISHED.name());

                    }

                    idStatusMap.put("3", ProjectLifeVO.NodeState.FINISHED.name());
                }


            }

            //项目关联无合同采购
            List<NonContractProc> nonContractProcs = nonContractProcService.list(new LambdaQueryWrapperX<>(NonContractProc.class).eq(NonContractProc::getProjectCode, project.getNumber()));
            if (!CollectionUtils.isEmpty(nonContractProcs)) {
                idDataIdMap.put("2-5", nonContractProcs.get(0).getId());
                idStatusMap.put("2-5", ProjectLifeVO.NodeState.FINISHED.name());

                idDataIdMap.put("2-6", nonContractProcs.get(0).getId());
                idStatusMap.put("2-6", ProjectLifeVO.NodeState.FINISHED.name());

                idStatusMap.put("2-7", ProjectLifeVO.NodeState.FINISHED.name());

                idStatusMap.put("3", ProjectLifeVO.NodeState.FINISHED.name());

            }

            //项目关联计划
            List<ProjectScheme> projectSchemes = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class).eq(ProjectScheme::getProjectId, projectId));
            if (!CollectionUtils.isEmpty(projectSchemes)) {

                idStatusMap.put("3-1", ProjectLifeVO.NodeState.FINISHED.name());

                idDataIdMap.put("3-2", projectSchemes.get(0).getId());

                boolean isExecuting = projectSchemes.stream().anyMatch(o -> Status.EXECUTING.getCode().equals(o.getStatus()) || Status.COMPLETED.getCode().equals(o.getStatus()));
                if (isExecuting) {
                    idStatusMap.put("3-2", ProjectLifeVO.NodeState.FINISHED.name());
                }

                idStatusMap.put("3-4", ProjectLifeVO.NodeState.UNDERWAY.name());


                idStatusMap.put("3-2-2", ProjectLifeVO.NodeState.UNDERWAY.name());

            }

            //查询项目完工数据
            if (Objects.equals(project.getStatus(), 140)) {
                idDataIdMap.put("3-3", "todo");
                idStatusMap.put("3-3", ProjectLifeVO.NodeState.FINISHED.name());

                idStatusMap.put("3-4", ProjectLifeVO.NodeState.FINISHED.name());
                idStatusMap.put("4", ProjectLifeVO.NodeState.FINISHED.name());


                idStatusMap.put("3-1", ProjectLifeVO.NodeState.FINISHED.name());

                idDataIdMap.put("3-2", projectSchemes.get(0).getId());
                idStatusMap.put("3-2", ProjectLifeVO.NodeState.FINISHED.name());

                idStatusMap.put("3-4", ProjectLifeVO.NodeState.FINISHED.name());


                idStatusMap.put("3-2-2", ProjectLifeVO.NodeState.FINISHED.name());

                //验收结题
                List<AcceptanceForm> acceptanceForms = acceptanceFormService.list(new LambdaQueryWrapperX<>(AcceptanceForm.class).in(AcceptanceForm::getProjectId, projectId));

                if (!CollectionUtils.isEmpty(acceptanceForms) && acceptanceForms.stream().allMatch(acceptanceForm -> Objects.equals(acceptanceForm.getStatus(), AcceptanceFormStatusEnum.CHECK_AND_ACCEPTED.getStatus()))) {
                    idStatusMap.put("4-2", ProjectLifeVO.NodeState.FINISHED.name());
                } else {
                    idStatusMap.put("4-2", ProjectLifeVO.NodeState.UNDERWAY.name());
                }


            }
            //项目后评价
            List<ProjectCondition> projectConditions = projectConditionService.list(new LambdaQueryWrapperX<>(ProjectCondition.class).eq(ProjectCondition::getProjectNumber, project.getNumber()));
            if (!CollectionUtils.isEmpty(projectConditions)) {
                idDataIdMap.put("4-3", projectConditions.get(0).getId());
                idStatusMap.put("4-3", ProjectLifeVO.NodeState.FINISHED.name());
            }


        } catch (Exception ex) {
            log.error("数据处理异常", ex);
        }

        return new ProjectLifeVO().setNodeStatus(idStatusMap, idDataIdMap);
    }


    @Override
    public List<NcfFormpurchaseRequestVO> getProjectLifeOfPurchaseRequest(String projectId) {
        //查询项目信息
        Project project = projectService.getById(projectId);
        if (Objects.isNull(project)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "项目信息不存在");
        }
        //项目关联项目立项
        ProjectInitiation projectInitiation = projectInitiationService.getOne(new LambdaQueryWrapperX<>(ProjectInitiation.class).eq(ProjectInitiation::getProjectNumber, project.getNumber()));
        if (Objects.isNull(projectInitiation)) {
            return new ArrayList<>();
        }

        //项目立项关联采购立项
        List<NcfFormpurchaseRequestDetail> purchaseRequestDetails = ncfFormpurchaseRequestDetailService.list(new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class)
                .like(NcfFormpurchaseRequestDetail::getWbsId, project.getNumber()));
        if (CollectionUtils.isEmpty(purchaseRequestDetails)) {
            return new ArrayList<>();
        }

        List<String> requestCodes = purchaseRequestDetails.stream().map(NcfFormpurchaseRequestDetail::getProjectCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<NcfFormpurchaseRequest> purchaseRequests = ncfFormpurchaseRequestService.list(new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class).in(NcfFormpurchaseRequest::getCode, requestCodes));
        if (CollectionUtils.isEmpty(purchaseRequests)) {
            return new ArrayList<>();
        }
        List<NcfFormpurchaseRequestVO> result = BeanCopyUtils.convertListTo(purchaseRequests, NcfFormpurchaseRequestVO::new);

        return result;
    }

    @Override
    public List<NcfPurchProjectImplementationVO> getProjectLifeOfImplementation(String projectId) {
        List<NcfFormpurchaseRequestVO> purchaseRequests = getProjectLifeOfPurchaseRequest(projectId);
        if (CollectionUtils.isEmpty(purchaseRequests)) {
            return new ArrayList<>();
        }
        List<String> purchaseRequestCodes = purchaseRequests.stream().map(NcfFormpurchaseRequestVO::getCode).collect(Collectors.toList());
        List<NcfPurchProjectImplementation> purchaseImplementations = ncfPurchProjectImplementationService.list(new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class).in(NcfPurchProjectImplementation::getPurchReqEcpCode, purchaseRequestCodes));
        List<NcfPurchProjectImplementationVO> result = BeanCopyUtils.convertListTo(purchaseImplementations, NcfPurchProjectImplementationVO::new);
        return result;
    }

    @Override
    public List<ContractInfoVO> getProjectLifeOfContract(String projectId) {
        List<NcfPurchProjectImplementationVO> projectLifeOfImplementation = getProjectLifeOfImplementation(projectId);
        if (CollectionUtils.isEmpty(projectLifeOfImplementation)) {
            return new ArrayList<>();
        }
        List<String> purchaseContractNumbers = projectLifeOfImplementation.stream().map(NcfPurchProjectImplementationVO::getPurchReqEcpCode).distinct().collect(Collectors.toList());
        List<ContractInfo> purchaseContractInfos = contractInfoService.list(new LambdaQueryWrapperX<>(ContractInfo.class).in(ContractInfo::getPurchaseApplicant, purchaseContractNumbers));
        List<ContractInfoVO> result = BeanCopyUtils.convertListTo(purchaseContractInfos, ContractInfoVO::new);
        return result;
    }


    @Override
    public List<ContractMilestoneVO> getProjectLifeOfMilestoneDelivery(String projectId) {
        //查询项目信息
        Project project = projectService.getById(projectId);
        if (Objects.isNull(project)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "项目信息不存在");
        }
        //项目关联项目立项
        ProjectInitiation projectInitiation = projectInitiationService.getOne(new LambdaQueryWrapperX<>(ProjectInitiation.class).eq(ProjectInitiation::getProjectNumber, project.getNumber()));
        if (Objects.isNull(projectInitiation)) {
            return new ArrayList<>();
        }

        //项目立项关联合同
        String contractNumbers = projectInitiation.getContractNumbers();
        List<MarketContract> marketContractS = new ArrayList<>();
        if (StrUtil.isNotBlank(contractNumbers)) {
            List<String> contractNumberList = Arrays.asList(contractNumbers.split(","));
            //调用合同接口
            marketContractS = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class)
                    .in(MarketContract::getNumber, contractNumberList));
        }
        //合同关联报价
        if (!CollectionUtils.isEmpty(marketContractS)) {
            //合同里程碑完成
            List<String> marketContractsIds = marketContractS.stream().map(MarketContract::getId).collect(Collectors.toList());
            List<ContractMilestone> contractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class).in(ContractMilestone::getContractId, marketContractsIds));
            List<ContractMilestoneVO> result = BeanCopyUtils.convertListTo(contractMilestones, ContractMilestoneVO::new);
            return result;
        }

        return new ArrayList<>();
    }

    @Override
    public List<AcceptanceFormVO> getProjectLifeOfAcceptance(String projectId) {
        //验收解题
        List<AcceptanceForm> acceptanceForms = acceptanceFormService.list(new LambdaQueryWrapperX<>(AcceptanceForm.class).in(AcceptanceForm::getProjectId, projectId));

        List<AcceptanceFormVO> result = BeanCopyUtils.convertListTo(acceptanceForms, AcceptanceFormVO::new);

        return result;

    }

    @Override
    public List<ProjectConditionVO> getPostEvaluation(String projectId) {
        //查询项目信息
        Project project = projectService.getById(projectId);
        if (Objects.isNull(project)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "项目信息不存在");
        }
        List<ProjectCondition> projectConditions = projectConditionService.list(new LambdaQueryWrapperX<>(ProjectCondition.class).eq(ProjectCondition::getProjectNumber, project.getNumber()));
        List<ProjectConditionVO> result = BeanCopyUtils.convertListTo(projectConditions, ProjectConditionVO::new);

        return result;
    }

    @Override
    public String updateProjectLifeCycleNodeForZGh(String projectId, ProjectLifeCycleNodeForZghDTO lifeCycleNode) throws Exception {
        if (StrUtil.isBlank(lifeCycleNode.getNodeId())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "节点Id不存在");
        }
        ProjectLifeCycleNodeZgh projectLifeCycleNodeZgh = projectLifeCycleNodeZghService.getOne(new LambdaQueryWrapperX<>(ProjectLifeCycleNodeZgh.class)
                .eq(ProjectLifeCycleNodeZgh::getProjectId, projectId)
                .eq(ProjectLifeCycleNodeZgh::getNodeId, lifeCycleNode.getNodeId())
        );
        String rsp;
        if (Objects.nonNull(projectLifeCycleNodeZgh)) {
            projectLifeCycleNodeZgh.setContent(lifeCycleNode.getContent());
            projectLifeCycleNodeZgh.setProjectId(projectId);
            projectLifeCycleNodeZgh.setAttachmentFlag(CollectionUtils.isEmpty(lifeCycleNode.getAttachments()));
            projectLifeCycleNodeZgh.setNodeId(lifeCycleNode.getNodeId());
            projectLifeCycleNodeZghService.updateById(projectLifeCycleNodeZgh);
            rsp = projectLifeCycleNodeZgh.getId();
        } else {
            ProjectLifeCycleNodeZgh projectLifeCycleNode = new ProjectLifeCycleNodeZgh();
            projectLifeCycleNode.setContent(lifeCycleNode.getContent());
            projectLifeCycleNode.setProjectId(projectId);
            projectLifeCycleNode.setAttachmentFlag(CollectionUtils.isEmpty(lifeCycleNode.getAttachments()));
            projectLifeCycleNode.setNodeId(lifeCycleNode.getNodeId());

            projectLifeCycleNodeZghService.save(projectLifeCycleNode);
            rsp = projectLifeCycleNode.getId();

        }


        //处理附件
        List<FileDTO> attachments = lifeCycleNode.getAttachments();
        lyraFileApiService.deleteByDataIds(Collections.singletonList(rsp));
        if (!CollectionUtils.isEmpty(attachments)) {
            attachments.forEach(f -> {
                f.setId(null);
                f.setDataId(rsp);
                f.setDataType(ProjectLifeCycleNodeZghService.default_attachment_data_type);
            });
            lyraFileApiService.createBatch(attachments);
        }
        return rsp;
    }

    @Override
    public ProjectLifeCycleNodeForZghVO getProjectLifeCycleNodeForZGh(String projectId, String nodeId) throws Exception {

        ProjectLifeCycleNodeZgh projectLifeCycleNodeZgh = projectLifeCycleNodeZghService.getOne(new LambdaQueryWrapperX<>(ProjectLifeCycleNodeZgh.class)
                .eq(ProjectLifeCycleNodeZgh::getProjectId, projectId)
                .eq(ProjectLifeCycleNodeZgh::getNodeId, nodeId)
        );
        if (Objects.isNull(projectLifeCycleNodeZgh)) {
            return new ProjectLifeCycleNodeForZghVO();
        }

        ProjectLifeCycleNodeForZghVO result = BeanCopyUtils.convertTo(projectLifeCycleNodeZgh, ProjectLifeCycleNodeForZghVO::new);


        List<FileTreeVO> fileVOS = lyraFileApiService.getFilesByDataIdAndDataType(projectLifeCycleNodeZgh.getId(), ProjectLifeCycleNodeZghService.default_attachment_data_type);
        fileVOS.forEach(fileTreeVO -> fileTreeVO.setChildren(null));
        if (CollectionUtils.isEmpty(fileVOS)) {
            result.setAttachments(new ArrayList<>());
        } else {
            result.setAttachments(fileVOS);
        }

        return result;
    }

    /**
     * 获取计划统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectPlanStatusAndOverdueCountVO getProjectPlanCount(String projectId) throws Exception {
        /**
         * 待发布  ：未开始
         * 已完成：已完成
         * 其余对应：进行中
         */

        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        String select = "count(*) as planTotal," +
                "ifnull(sum( CASE WHEN `status` = " + 101 + " THEN 1 ELSE 0 END ),0) AS noStartCount ," +
                "ifnull(sum( CASE WHEN `status` in (120,130,140,110,121,150) THEN 1 ELSE 0 END ),0) AS underwayCount ," +
                "ifnull(sum( CASE WHEN `status` in (111,160) THEN 1 ELSE 0 END ),0) AS completeCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100012 + " or `circumstance` = " + 100013 + " or `circumstance` = " + 100016 + " THEN 1 ELSE 0 END ),0) AS planAbnormalTotal ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100012 + "  THEN 1 ELSE 0 END ),0) AS nearExpiredCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100013 + " and `status` in(130,140)  THEN 1 ELSE 0 END ),0) AS overdueCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100016 + " and `status` in (111) THEN 1 ELSE 0 END ),0) AS overdueCompleteCount ";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.eqIfPresent(ProjectScheme::getProjectId, projectId);
        Map<String, Object> map = projectSchemeService.getMap(lambdaQueryWrapperX);
        ProjectPlanStatusAndOverdueCountVO planCountVO = new ProjectPlanStatusAndOverdueCountVO();

        ProjectPlanStatusAndOverdueCountVO.PlanStatusCountVO planStatusCount = new ProjectPlanStatusAndOverdueCountVO.PlanStatusCountVO();

        planStatusCount.setTotal(Integer.parseInt(map.get("planTotal").toString()));
        planStatusCount.setTodo(Integer.parseInt(map.get("noStartCount").toString()));
        planStatusCount.setDoing(Integer.parseInt(map.get("underwayCount").toString()));
        planStatusCount.setDone(Integer.parseInt(map.get("completeCount").toString()));
        planCountVO.setPlanStatusCount(planStatusCount);


        ProjectPlanStatusAndOverdueCountVO.PlanOverdueCountVO planOverdueCount = new ProjectPlanStatusAndOverdueCountVO.PlanOverdueCountVO();
        planOverdueCount.setTotal(Integer.parseInt(map.get("planAbnormalTotal").toString()));
        planOverdueCount.setOverdue(Integer.parseInt(map.get("overdueCount").toString()));
        planOverdueCount.setDoing(Integer.parseInt(map.get("nearExpiredCount").toString()));
        planOverdueCount.setOverdueDone(Integer.parseInt(map.get("overdueCompleteCount").toString()));

        planCountVO.setPlanOverdueCount(planOverdueCount);


        return planCountVO;
    }

    @Override
    public ProjectPlanMemberCountVO getProjectPlanMemberCount(String projectId) throws Exception {
        ProjectPlanMemberCountVO result = new ProjectPlanMemberCountVO();
        List<BatchProjectUserVO> projectUserBatch = projectRoleService.getProjectUserBatch(Collections.singletonList(projectId));
        if (CollectionUtils.isEmpty(projectUserBatch)) {
            return result;
        }
        BatchProjectUserVO batchProjectUserVO = projectUserBatch.get(0);
        List<UserVO> userVOS = batchProjectUserVO.getUserVOS();
        if (CollectionUtils.isEmpty(userVOS)) {
            return result;
        }

        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        String select = "rsp_user AS memberId," +
                "count( rsp_user ) AS total," +
                "SUM( CASE WHEN `status` in (120,130,140,110,121,150) THEN 1 ELSE 0 END ) AS doing," +
                "SUM( CASE WHEN `circumstance` = 100013 AND `status`  IN ( 130,140 ) THEN 1 ELSE 0 END ) AS overdue," +
                "SUM( CASE WHEN STATUS IN ( 111, 160 ) THEN 1 ELSE 0 END ) AS done ";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.eqIfPresent(ProjectScheme::getProjectId, projectId);
        lambdaQueryWrapperX.groupBy(ProjectScheme::getRspUser);
        lambdaQueryWrapperX.isNotNull(ProjectScheme::getRspUser);


        List<Map<String, Object>> maps = projectSchemeService.listMaps(lambdaQueryWrapperX);
        Map<String, Map<String, Object>> memberIdMap = maps.stream().collect(Collectors.toMap(o -> String.valueOf(o.get("memberId")), o -> o));

        List<ProjectPlanMemberCountVO.ProjectPlanMemberCountItemVO> items = new ArrayList<>();
        for (UserVO userVO : userVOS) {
            Map<String, Object> objectMap = memberIdMap.getOrDefault(userVO.getId(), new HashMap<>());

            int doing = Integer.parseInt(objectMap.getOrDefault("doing", "0").toString());
            int overdue = Integer.parseInt(objectMap.getOrDefault("overdue", "0").toString());
            int done = Integer.parseInt(objectMap.getOrDefault("done", "0").toString());
            items.add(new ProjectPlanMemberCountVO.ProjectPlanMemberCountItemVO(
                            userVO.getId(),
                            userVO.getName(),
                            doing,
                            done,
                            overdue,
                            doing + overdue + done
                    )
            );
        }

        items.sort(Comparator.comparing(ProjectPlanMemberCountVO.ProjectPlanMemberCountItemVO::getTotal));
        items.removeIf(o -> o.getTotal() == 0);
        result.setItems(items);
        Integer doing = items.stream().mapToInt(ProjectPlanMemberCountVO.ProjectPlanMemberCountItemVO::getDoing).sum();
        result.setDoing(doing);
        Integer overdue = items.stream().mapToInt(ProjectPlanMemberCountVO.ProjectPlanMemberCountItemVO::getOverdue).sum();
        result.setOverdue(overdue);
        Integer done = items.stream().mapToInt(ProjectPlanMemberCountVO.ProjectPlanMemberCountItemVO::getDone).sum();
        result.setDone(done);
        return result;
    }

    @Override
    public ProjectPlanOverdueCountVO getProjectPlanOverdueCount(String projectId, Integer status, Integer dateType) throws Exception {
        if (Objects.isNull(status)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "计划状态参数不能为空");
        }


        ProjectPlanOverdueCountVO result = new ProjectPlanOverdueCountVO();
        /**
         *   @ApiImplicitParam(value = "status", name = "状态：1 逾期预警; 2 临期；3 逾期完成", required = true),
         *   @ApiImplicitParam(value = "date", name = "任务类型:1 当天任务；2本周任务；3本月任务；4全部", required = true),
         */
        LambdaQueryWrapperX<ProjectScheme> countCondition = new LambdaQueryWrapperX<>(ProjectScheme.class);
        String select = "ifnull(sum( CASE WHEN `circumstance` = " + 100012 + "  THEN 1 ELSE 0 END ),0) AS nearExpiredCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100013 + " and `status` in (130,140)  THEN 1 ELSE 0 END ),0) AS overdueCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100013 + " and `status` in (111) THEN 1 ELSE 0 END ),0) AS overdueCompleteCount ";
        countCondition.select(select);
        countCondition.eqIfPresent(ProjectScheme::getProjectId, projectId);
        Map<String, Object> countMap = projectSchemeService.getMap(countCondition);

        result.setOverdue(Integer.parseInt(countMap.get("overdueCount").toString()));
        result.setDoing(Integer.parseInt(countMap.get("nearExpiredCount").toString()));
        result.setOverdueDone(Integer.parseInt(countMap.get("overdueCompleteCount").toString()));
        //风险数量
        result.setRiskCount((int) riskManagementService.count(new LambdaQueryWrapper<RiskManagement>()
                .eq(RiskManagement::getProjectId, projectId)
                .eq(RiskManagement::getLogicStatus,1)));
        //问题数量
        result.setQuestionCount((int) questionManagementService.count(new LambdaQueryWrapper<QuestionManagement>()
                .eq(QuestionManagement::getProjectId, projectId)
                .eq(QuestionManagement::getLogicStatus,1)));

        if(status == 1 || status == 2 || status == 3){
            if (Objects.isNull(dateType)) {
                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "计划范围类型参数不能为空");
            }
            LambdaQueryWrapperX<ProjectScheme> itemCondition = new LambdaQueryWrapperX<>(ProjectScheme.class);
            itemCondition.eqIfPresent(ProjectScheme::getProjectId, projectId);
            if (status == 1) {
                itemCondition.eq(ProjectScheme::getCircumstance, 100013).in(ProjectScheme::getStatus, 130, 140);
            } else if (status == 2) {
                itemCondition.eq(ProjectScheme::getCircumstance, 100012);
            } else {
                itemCondition.eq(ProjectScheme::getCircumstance, 100013).eq(ProjectScheme::getStatus, 111);
            }
            if (dateType == 1) {
                itemCondition.between(ProjectScheme::getStartTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()));
            } else if (dateType == 2) {
                itemCondition.between(ProjectScheme::getStartTime, DateUtil.beginOfWeek(new Date()), DateUtil.endOfWeek(new Date()));
            } else if (dateType == 3) {
                itemCondition.between(ProjectScheme::getStartTime, DateUtil.beginOfMonth(new Date()), DateUtil.endOfMonth(new Date()));
            }
            List<ProjectScheme> projectSchemes = projectSchemeService.list(itemCondition);
            List<ProjectSchemeVO> projectSchemeVOS = BeanCopyUtils.convertListTo(projectSchemes, ProjectSchemeVO::new);
            if (!CollectionUtils.isEmpty(projectSchemeVOS)) {

                List<String> rspUserIds = projectSchemeVOS.stream().map(ProjectSchemeVO::getRspUser).filter(Objects::nonNull).collect(Collectors.toList());
                List<SimpleUserVO> userByIds = userBaseApiService.getUserByIds(rspUserIds);
                Map<String, String> userNameMap = userByIds.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName));


                List<String> rspDeptIds = projectSchemeVOS.stream().map(ProjectSchemeVO::getRspSubDept).filter(Objects::nonNull).collect(Collectors.toList());
                List<DeptVO> deptByIds = deptBaseApiService.getDeptByIds(rspDeptIds);
                Map<String, String> deptNameMap = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));


                projectSchemeVOS.forEach(ps -> {
                    if (StrUtil.isNotBlank(ps.getRspUser())) {
                        ps.setRspUserName(userNameMap.getOrDefault(ps.getRspUser(), ""));

                    }
                    if (StrUtil.isNotBlank(ps.getRspSubDept())) {
                        ps.setRspSubDeptName(deptNameMap.getOrDefault(ps.getRspSubDept(), ""));
                    }
                    if (Objects.nonNull(ps.getEndTime())) {
                        ps.setOverdueDays(DateUtil.betweenDay(ps.getEndTime(), new Date(), false));
                    }

                    if (Objects.nonNull(ps.getCircumstance())) {
                        ps.setCircumstanceName(Status.codeMapping(ps.getCircumstance()));
                    }

                    ps.setChildren(null);
                });
            }
            result.setProjectSchemeVOS(projectSchemeVOS);
        }else if(status == 4){
            List<RiskManagement> list = riskManagementService.list(new LambdaQueryWrapper<RiskManagement>()
                    .eq(RiskManagement::getProjectId, projectId)
                    .eq(RiskManagement::getLogicStatus, 1));
            List<RiskManagementVO> riskManagementVOList = BeanCopyUtils.convertListTo(list, RiskManagementVO::new);
            Set<String> userIdList = new HashSet<>();
            for (RiskManagement questionManagementDTO : list) {
                String principalId = questionManagementDTO.getPrincipalId();
                if (org.springframework.util.StringUtils.hasText(principalId)) {
                    userIdList.add(principalId);
                }
            }
            Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(new ArrayList<>(userIdList));
            Map<String, String> riskProbabilityValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PROBABILITIES);
            Map<String, String> riskInfluenceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_INFLUENCES);
            Map<String, String> copingStrategyValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_COPING_STRATEGIES);
            List<QuestionToRisk> questionToRisks = questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class)
                    .in(QuestionToRisk::getFromId, riskManagementVOList.stream().map(RiskManagementVO::getId).collect(Collectors.toList())));
            Map<String,List<QuestionToRisk>> questionToRiskMap = questionToRisks.stream().collect(Collectors.groupingBy(QuestionToRisk::getFromId));
            riskManagementVOList.forEach(o -> {
                String principalId = o.getPrincipalId();
                if (org.springframework.util.StringUtils.hasText(principalId)) {
                    UserVO userVO = userMapByUserIds.get(principalId);
                    o.setPrincipalName(userVO == null ? "" : userVO.getName());
                    o.setRiskProbabilityName(riskProbabilityValueToDesMap.get(o.getRiskProbability()));
                    o.setRiskInfluenceName(riskInfluenceValueToDesMap.get(o.getRiskInfluence()));
                    o.setCopingStrategyName(copingStrategyValueToDesMap.get(o.getCopingStrategy()));
                }
                if(!CollectionUtils.isEmpty(questionToRiskMap.get(o.getId()))){
                    o.setIsToQuestion(Boolean.TRUE);
                }else {
                    o.setIsToQuestion(Boolean.FALSE);
                }
            });
            result.setRiskManagementVos(riskManagementVOList);
        }else if(status == 5){
           List<QuestionManagement> list = questionManagementService.list(new LambdaQueryWrapper<QuestionManagement>()
                .eq(QuestionManagement::getProjectId, projectId)
                .eq(QuestionManagement::getLogicStatus, 1));
            List<QuestionManagementVO> questionManagementVOS = BeanCopyUtils.convertListTo(list, QuestionManagementVO::new);
            questionManagementService.setContent(questionManagementVOS);
            result.setQuestionManagementVOs(questionManagementVOS);
        }

        return result;
    }

    /**
     * 获取预算统计数据
     *
     * @param projectId
     * @param type
     * @param year
     * @param budgetTypeId
     * @return
     */
    @Override
    public ProjectBudgetOverviewVO getProjectBudgetOverview(String projectId, Integer type, String year, String budgetTypeId) {
        if (Objects.isNull(type)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "类型参数不能为空");
        }
        ProjectBudgetOverviewVO result = new ProjectBudgetOverviewVO();
        List<BudgetManagement> budgetManagements = budgetManagementService.list(new LambdaQueryWrapperX<>(BudgetManagement.class)
                .eq(BudgetManagement::getProjectId, projectId)
        );
        if (CollectionUtils.isEmpty(budgetManagements)) {
            return result;
        }

        Map<String, List<BudgetManagement>> budgetManagementgroupMap = budgetManagements.stream().collect(Collectors.groupingBy(BudgetManagement::getExpenseSubjectNumber));


        List<BudgetExpendForm> budgetExpendForms = budgetExpendFormService.list(new LambdaQueryWrapperX<>(BudgetExpendForm.class)
                .eq(BudgetExpendForm::getProjectId, projectId)
        );

        Map<String, List<BudgetExpendForm>> budgetExpendFormgroupMap = budgetExpendForms.stream().collect(Collectors.groupingBy(BudgetExpendForm::getExpenseAccountNumber));

        List<String> expenseSubjectNumbers = new ArrayList<>();
        expenseSubjectNumbers.addAll(budgetManagementgroupMap.keySet());
        expenseSubjectNumbers.addAll(budgetExpendFormgroupMap.keySet());
        List<ExpenseSubject> expenseSubjects = expenseSubjectService.list(new LambdaQueryWrapperX<>(ExpenseSubject.class).select(ExpenseSubject::getNumber, ExpenseSubject::getName).in(ExpenseSubject::getNumber, expenseSubjectNumbers));

        Map<String, String> expenseSubjectNumberNameMap = expenseSubjects.stream().collect(Collectors.toMap(ExpenseSubject::getNumber, ExpenseSubject::getName));


        if (type == 1) {
            BigDecimal budgetMoney = budgetManagements.stream().map(BudgetManagement::getBudgetMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            result.setBudgetMoney(budgetMoney);
            BigDecimal expendMoney = budgetExpendForms.stream().map(BudgetExpendForm::getExpendMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            result.setExpendMoney(expendMoney);
            result.setResidueMoney(budgetMoney.subtract(expendMoney));

            List<ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO> items = new ArrayList<>();
            budgetManagementgroupMap.forEach((k, v) -> {
                ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO tmp = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
                tmp.setName(expenseSubjectNumberNameMap.get(k));
                BigDecimal budgetMoneyItem = v.stream().map(BudgetManagement::getBudgetMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                tmp.setBudgetMoney(budgetMoneyItem);
                items.add(tmp);
            });


            result.setItems(items);
        }
        if (type == 2) {

            List<ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO> items = new ArrayList<>();

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO january = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            january.setName("1月");
            BigDecimal januaryMoney = budgetManagements.stream().map(BudgetManagement::getJanuaryMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            january.setBudgetMoney(januaryMoney);
            items.add(january);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO february = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            february.setName("2月");
            BigDecimal februaryMoney = budgetManagements.stream().map(BudgetManagement::getFebruaryMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            february.setBudgetMoney(februaryMoney);
            items.add(february);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO march = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            march.setName("3月");
            BigDecimal marchMoney = budgetManagements.stream().map(BudgetManagement::getMarchMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            march.setBudgetMoney(marchMoney);
            items.add(march);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO april = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            april.setName("4月");
            BigDecimal aprilMoney = budgetManagements.stream().map(BudgetManagement::getAprilMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            april.setBudgetMoney(aprilMoney);
            items.add(april);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO may = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            may.setName("5月");
            BigDecimal mayMoney = budgetManagements.stream().map(BudgetManagement::getMayMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            may.setBudgetMoney(mayMoney);
            items.add(may);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO june = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            june.setName("6月");
            BigDecimal juneMoney = budgetManagements.stream().map(BudgetManagement::getJuneMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            june.setBudgetMoney(juneMoney);
            items.add(june);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO july = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            july.setName("7月");
            BigDecimal julyMoney = budgetManagements.stream().map(BudgetManagement::getJulyMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            july.setBudgetMoney(julyMoney);
            items.add(july);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO august = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            august.setName("8月");
            BigDecimal augustMoney = budgetManagements.stream().map(BudgetManagement::getAugustMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            august.setBudgetMoney(augustMoney);
            items.add(august);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO september = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            september.setName("9月");
            BigDecimal septemberMoney = budgetManagements.stream().map(BudgetManagement::getSeptemberMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            september.setBudgetMoney(septemberMoney);
            items.add(september);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO october = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            october.setName("10月");
            BigDecimal octoberMoney = budgetManagements.stream().map(BudgetManagement::getOctoberMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            october.setBudgetMoney(octoberMoney);
            items.add(october);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO november = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            november.setName("11月");
            BigDecimal novemberMoney = budgetManagements.stream().map(BudgetManagement::getNovemberMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            november.setBudgetMoney(novemberMoney);
            items.add(november);

            ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO december = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
            december.setName("12月");
            BigDecimal decemberMoney = budgetManagements.stream().map(BudgetManagement::getDecemberMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            december.setBudgetMoney(decemberMoney);
            items.add(december);
            result.setItems(items);
        }
        if (type == 3) {
            BigDecimal budgetMoney = budgetManagements.stream().map(BudgetManagement::getBudgetMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal expendMoney = budgetExpendForms.stream().map(BudgetExpendForm::getExpendMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO> items = new ArrayList<>();
            budgetManagementgroupMap.forEach((k, v) -> {
                ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO tmp = new ProjectBudgetOverviewVO.ProjectBudgetOverviewItemVO();
                tmp.setName(expenseSubjectNumberNameMap.get(k));
                BigDecimal budgetMoneyItem = v.stream().map(BudgetManagement::getBudgetMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                tmp.setBudgetMoney(budgetMoneyItem);
                List<BudgetExpendForm> budgetExpendFormsTmp = budgetExpendFormgroupMap.get(k);
                if (Objects.nonNull(budgetExpendFormsTmp)) {
                    BigDecimal spendMoney = budgetExpendFormsTmp.stream().map(BudgetExpendForm::getExpendMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    tmp.setSpendMoney(spendMoney);
                }


                items.add(tmp);
            });

            result.setItems(items);

            result.setSpendMoney(expendMoney);
            result.setOverspendMoney(expendMoney.subtract(budgetMoney).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : expendMoney.subtract(budgetMoney));
        }

        return result;
    }


    /**
     * 获取营收统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectRevenueOverviewVO getProjectRevenueOverview(String projectId) throws Exception {
        ProjectRevenueOverviewVO projectRevenueTotalVO = new ProjectRevenueOverviewVO();
        LambdaQueryWrapperX<ProjectContract> projectContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectContract.class);
        projectContractLambdaQueryWrapperX.eq(ProjectContract::getProjectId, projectId);
        projectContractLambdaQueryWrapperX.eq(ProjectContract::getContractCategory, "saleContract");
        projectContractLambdaQueryWrapperX.in(ProjectContract::getStatus, 130, 107);
        List<ProjectContract> list = projectContractRepository.selectList(projectContractLambdaQueryWrapperX);

        if (CollectionUtil.isEmpty(list)) {
            return projectRevenueTotalVO;
        }
        List<String> numbers = list.stream().map(ProjectContract::getNumber).collect(Collectors.toList());
        LambdaQueryWrapperX<ProjectFundsReceived> fundsReceivedLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectFundsReceived.class);
        fundsReceivedLambdaQueryWrapperX.in(ProjectFundsReceived::getContractNumber, numbers);

        List<ProjectFundsReceived> list1 = projectFundsReceivedService.list(fundsReceivedLambdaQueryWrapperX);
        Map<String, BigDecimal> map = getMap(list1);
        BigDecimal targetRevenue = BigDecimal.ZERO;
        BigDecimal actualRevenue = BigDecimal.ZERO;
        BigDecimal pendRevenue = BigDecimal.ZERO;
        List<ProjectRevenueOverviewVO.ProjectRevenueOverviewItemVO> projectRevenueTotalVOS = new ArrayList<>();
        for (ProjectContract projectContract : list) {
            if ((projectContract.getStatus() == 130 || projectContract.getStatus() == 107)) {
                ProjectRevenueOverviewVO.ProjectRevenueOverviewItemVO revenueTotalVO = new ProjectRevenueOverviewVO.ProjectRevenueOverviewItemVO();
                revenueTotalVO.setTargetRevenue(projectContract.getContractMoney());
                revenueTotalVO.setContractNumber(projectContract.getNumber());
                targetRevenue = targetRevenue.add(projectContract.getContractMoney());
                if (ObjectUtil.isNotEmpty(map.get(projectContract.getNumber())) && (projectContract.getStatus() == 130 || projectContract.getStatus() == 107)) {
                    revenueTotalVO.setActualRevenue(map.get(projectContract.getNumber()));
                    actualRevenue = actualRevenue.add(map.get(projectContract.getNumber()));
                    revenueTotalVO.setPendRevenue(revenueTotalVO.getTargetRevenue().subtract(revenueTotalVO.getActualRevenue()));
                    pendRevenue = pendRevenue.add(revenueTotalVO.getPendRevenue());
                }
                projectRevenueTotalVOS.add(revenueTotalVO);
            }
        }
        projectRevenueTotalVO.setProjectRevenues(projectRevenueTotalVOS);
        projectRevenueTotalVO.setTargetRevenue(targetRevenue);
        projectRevenueTotalVO.setActualRevenue(actualRevenue);
        projectRevenueTotalVO.setPendRevenue(pendRevenue);
        projectRevenueTotalVO.setContractCount(list.size());
        return projectRevenueTotalVO;
    }


    /**
     * 获取风险统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectRiskCountVO getRiskCount(String projectId) throws Exception {
        Integer completedCode = DeliverStatusEnum.DEAL.getStatus();
        LambdaQueryWrapperX<RiskManagement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(RiskManagement.class);
        String select = "count(*) as total," +
                " ifnull(sum( CASE WHEN `status` = " + completedCode + " THEN 1 ELSE 0 END ),0) AS closeCount ," +
                "ifnull(sum( CASE WHEN `status` != " + completedCode + " THEN 1 ELSE 0 END ),0) AS noCloseCount";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.eqIfPresent(RiskManagement::getProjectId, projectId);
        Map<String, Object> map = riskManagementService.getMap(lambdaQueryWrapperX);
        ProjectRiskCountVO projectRiskCountVO = new ProjectRiskCountVO();
        projectRiskCountVO.setTotal(Integer.parseInt(map.get("total").toString()));
        projectRiskCountVO.setCloseCount(Integer.parseInt(map.get("closeCount").toString()));
        projectRiskCountVO.setNoCloseCount(Integer.parseInt(map.get("noCloseCount").toString()));
        return projectRiskCountVO;
    }

    /**
     * 获取问题统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectProblemCountVO getProblemCount(String projectId) throws Exception {
        Integer completedCode = 160;
        LambdaQueryWrapperX<QuestionManagement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(QuestionManagement.class);
        String select = "count(*) as total," +
                " ifnull(sum( CASE WHEN `status` = " + completedCode + " THEN 1 ELSE 0 END ),0) AS solvedCount ," +
                "ifnull(sum( CASE WHEN `status` != " + completedCode + " THEN 1 ELSE 0 END ),0) AS unSolvedCount";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.eqIfPresent(QuestionManagement::getProjectId, projectId);
        Map<String, Object> map = questionManagementService.getMap(lambdaQueryWrapperX);
        ProjectProblemCountVO projectProblemCountVO = new ProjectProblemCountVO();
        projectProblemCountVO.setTotal(Integer.parseInt(map.get("total").toString()));
        projectProblemCountVO.setSolvedCount(Integer.parseInt(map.get("solvedCount").toString()));
        projectProblemCountVO.setUnSolvedCount(Integer.parseInt(map.get("unSolvedCount").toString()));
        return projectProblemCountVO;
    }


    /**
     * 获取需求统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectDemandCountVO getDemandCount(String projectId) throws Exception {
        Integer completedCode = 101;
        LambdaQueryWrapperX<DemandManagement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(DemandManagement.class);
        String select = "count(*) as total," +
                " ifnull(sum( CASE WHEN `status` != " + completedCode + " THEN 1 ELSE 0 END ),0) AS respondedCount ," +
                "ifnull(sum( CASE WHEN `status` = " + completedCode + " THEN 1 ELSE 0 END ),0) AS noRespondedCount";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.eqIfPresent(DemandManagement::getProjectId, projectId);
        Map map = demandManagementService.getMap(lambdaQueryWrapperX);
        ProjectDemandCountVO projectDemandCountVO = new ProjectDemandCountVO();
        projectDemandCountVO.setTotal(Integer.parseInt(map.get("total").toString()));
        projectDemandCountVO.setRespondedCount(Integer.parseInt(map.get("respondedCount").toString()));
        projectDemandCountVO.setNoRespondedCount(Integer.parseInt(map.get("noRespondedCount").toString()));
        return projectDemandCountVO;
    }

    @Override
    public ProjectQualityCountVO getQualityCount(String projectId) {
        return new ProjectQualityCountVO();
    }

    /**
     * 获取物资统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public GoodsServiceCountVO getGoodsServiceCount(String projectId) throws Exception {
        GoodsServiceCountVO goodsServiceCountVO = new GoodsServiceCountVO();
        LambdaQueryWrapperX<GoodsServicePlan> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(GoodsServicePlan.class);
        lambdaQueryWrapperX.eq(GoodsServicePlan::getProjectId, projectId);
        String select = "ifnull(sum( CASE WHEN `type_code` ='goodsType' THEN 1 ELSE 0 END ),0) as goodsKindTotal," +
                "ifnull(sum( CASE WHEN `type_code` ='serviceType' THEN 1 ELSE 0 END ),0) as serviceKindTotal," +
                "ifnull(sum( CASE WHEN `type_code` ='goodsType' THEN demand_amount ELSE 0 END ),0) as goodsDemandAmount," +
                "ifnull(sum( CASE WHEN `type_code` ='serviceType' THEN demand_amount ELSE 0 END ),0) as serviceDemandAmount," +
                "ifnull(sum( CASE WHEN `type_code` ='goodsType' THEN total_store_amount ELSE 0 END ),0) as goodsStoreAmount," +
                "ifnull(sum( CASE WHEN `type_code` ='serviceType' THEN total_store_amount ELSE 0 END ),0) as serviceStoreAmount";
        lambdaQueryWrapperX.select(select);
        List<Map<String, Object>> list = goodsServicePlanMapper.selectMaps(lambdaQueryWrapperX);
        Map map = list.get(0);
        goodsServiceCountVO.setGoodsKindTotal(Integer.parseInt(map.get("goodsKindTotal").toString()));
        goodsServiceCountVO.setServiceKindTotal(Integer.parseInt(map.get("serviceKindTotal").toString()));
        goodsServiceCountVO.setGoodsDemandAmount((BigDecimal) map.get("goodsDemandAmount"));
        goodsServiceCountVO.setServiceDemandAmount((BigDecimal) map.get("serviceDemandAmount"));
        goodsServiceCountVO.setGoodsStoreAmount((BigDecimal) map.get("goodsStoreAmount"));
        goodsServiceCountVO.setServiceStoreAmount((BigDecimal) map.get("serviceStoreAmount"));
        return goodsServiceCountVO;
    }

    @Override
    public ProjectWorkHourVO getProjectWorkHour(String projectId) throws Exception {
        ProjectWorkHourVO projectWorkHourVO = new ProjectWorkHourVO();
        LambdaQueryWrapperX<WorkHourEstimate> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(WorkHourEstimate.class);
        lambdaQueryWrapperX.eq(WorkHourEstimate::getProjectId, projectId);
        lambdaQueryWrapperX.eq(WorkHourEstimate::getStatus, WorkHourEstimateStatusEnum.AUDITED.getStatus());
        lambdaQueryWrapperX.select("IFNULL(sum(work_hour),0) workHour");
        Map map = workHourEstimateService.getMap(lambdaQueryWrapperX);
        Integer workHour = Integer.parseInt(map.get("workHour").toString());
        LambdaQueryWrapperX<WorkHourFill> fillLambdaQueryWrapperX = new LambdaQueryWrapperX<>(WorkHourFill.class);
        fillLambdaQueryWrapperX.eq(WorkHourFill::getProjectId, projectId);
        fillLambdaQueryWrapperX.eq(WorkHourFill::getStatus, WorkHourFillStatusEnum.AUDITED.getStatus());
        fillLambdaQueryWrapperX.select("IFNULL(sum(work_hour),0) workHour");
        Map fillMap = workHourFillService.getMap(fillLambdaQueryWrapperX);
        Integer fillWorkHour = Integer.parseInt(fillMap.get("workHour").toString());
        projectWorkHourVO.setEstimateWorkHour(workHour);
        projectWorkHourVO.setFillWorkHour(fillWorkHour);
        projectWorkHourVO.setEstimateDeviation(fillWorkHour - workHour);
        projectWorkHourVO.setSurplusWorkHour(workHour - fillWorkHour);
        if (workHour > 0) {
            String fillSchedule = String.format("%.0f", (double) fillWorkHour / workHour * 100);
            String deviationRatio = String.format("%.0f", (double) (fillWorkHour - workHour) / workHour * 100);
            projectWorkHourVO.setFillSchedule(new BigDecimal(fillSchedule));
            projectWorkHourVO.setDeviationRatio(new BigDecimal(deviationRatio));
        }
        return projectWorkHourVO;
    }

    @Override
    public List<WarningSettingMessageRecordVO> getWarningRecords(String projectId) throws Exception {
        LambdaQueryWrapperX<WarningSettingMessageRecord> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(WarningSettingMessageRecord.class);
        lambdaQueryWrapperX.select(WarningSettingMessageRecord::getMessageContent, WarningSettingMessageRecord::getProjectId, WarningSettingMessageRecord::getSenderTime);
        lambdaQueryWrapperX.eqIfPresent(WarningSettingMessageRecord::getProjectId, projectId);
        List<WarningSettingMessageRecord> list = warningSettingMessageRecordService.list(lambdaQueryWrapperX);
        List<WarningSettingMessageRecordVO> warningSettingMessageRecordVOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            warningSettingMessageRecordVOS = BeanCopyUtils.convertListTo(list, WarningSettingMessageRecordVO::new);
        }
        return warningSettingMessageRecordVOS;
    }


    @Override
    public List<ProjectDocumentCountVO> getDocumentCount(String projectId, Integer step) throws Exception {

        List<ProjectDocumentCountVO> result = new ArrayList<>();

        Project project = projectService.getById(projectId);
        if (Objects.isNull(project)) {
            return result;
        }
        //项目关联项目立项
        ProjectInitiation projectInitiation = projectInitiationService.getOne(new LambdaQueryWrapperX<>(ProjectInitiation.class).eq(ProjectInitiation::getProjectNumber, project.getNumber()));
        if (Objects.isNull(projectInitiation)) {
            return result;
        }
        List<String> fileIds = new ArrayList<>();
        // 一、线索与需求管理  1.线索管理-线索管理 2.线索池
        //项目立项关联经营管理模块线索
        if (step == 1) {
            String clueNumbers = projectInitiation.getClueNumbers();
            List<Map<String, Object>> clues = new ArrayList<>();
            if (StrUtil.isNotBlank(clueNumbers)) {
                List<String> clueList = Arrays.asList(clueNumbers.split(","));
                //feign调用pas接口
                ResponseDTO<List<Map<String, Object>>> responseDTO = pasFeignService.listByClueIds(clueList);
                if (Objects.nonNull(responseDTO) && Objects.nonNull(responseDTO.getResult())) {
                    clues = responseDTO.getResult();
                }
            }

            List<String> clueIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(clues)) {
                clueIds.addAll(clues.stream().filter(o -> Objects.nonNull(o.get("id"))).map(o -> (String) o.get("id")).distinct().collect(Collectors.toList()));
                fileIds.addAll(clueIds);
            }
            ProjectDocumentCountVO step1 = new ProjectDocumentCountVO();
            step1.setGroupName("线索管理");
            step1.setFileVOS(new ArrayList<>());
            if (!CollectionUtils.isEmpty(fileIds)) {
                List<FileVO> orionFiles = fileApiService.listMaxFileByDataIds(fileIds);
                step1.setFileVOS(orionFiles);
            }
            result.add(step1);
            return result;
        }

        // 二、市场经营  1.需求管理  2、报价管理  3.合同管理
        if (step == 2) {
            //项目立项关联合同
            String contractNumbers = projectInitiation.getContractNumbers();
            List<MarketContract> marketContractS = new ArrayList<>();
            if (StrUtil.isNotBlank(contractNumbers)) {
                List<String> contractNumberList = Arrays.asList(contractNumbers.split(","));
                //调用合同接口
                marketContractS = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class).select(MarketContract::getId, MarketContract::getQuoteId)
                        .in(MarketContract::getNumber, contractNumberList));
            }

            //合同关联报价
            List<QuotationManagement> quotationManagements = new ArrayList<>();
            List<String> marketContractIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(marketContractS)) {

                marketContractIds.addAll(marketContractS.stream().map(MarketContract::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(marketContractIds);

                List<String> quoteIds = marketContractS.stream().map(MarketContract::getQuoteId).distinct().collect(Collectors.toList());
                quotationManagements = quotationManagementService.listByIds(quoteIds);
            }

            List<RequirementMangement> requirementManagements = new ArrayList<>();
            //报价关联需求
            List<String> quotationManagementIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(quotationManagements)) {
                quotationManagementIds.addAll(quotationManagements.stream().map(QuotationManagement::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(quotationManagementIds);

                List<String> requirementIds = quotationManagements.stream().map(QuotationManagement::getRequirementId).distinct().collect(Collectors.toList());
                requirementManagements = requirementMangementService.listByIds(requirementIds);
            }
            List<String> requirementManagementIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(requirementManagements)) {
                requirementManagementIds.addAll(requirementManagements.stream().map(RequirementMangement::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(requirementManagementIds);
            }

            ProjectDocumentCountVO step2_1 = new ProjectDocumentCountVO();
            step2_1.setGroupName("需求管理");
            step2_1.setFileVOS(new ArrayList<>());


            ProjectDocumentCountVO step2_2 = new ProjectDocumentCountVO();
            step2_2.setGroupName("报价管理");
            step2_2.setFileVOS(new ArrayList<>());

            ProjectDocumentCountVO step2_3 = new ProjectDocumentCountVO();
            step2_3.setGroupName("合同管理");
            step2_3.setFileVOS(new ArrayList<>());

            if (!CollectionUtils.isEmpty(fileIds)) {
                List<FileVO> orionFiles = fileApiService.listMaxFileByDataIds(fileIds);
                List<FileVO> requirementManagementFiles = orionFiles.stream().filter(o -> requirementManagementIds.contains(o.getDataId())).collect(Collectors.toList());
                step2_1.setFileVOS(requirementManagementFiles);
                List<FileVO> quotationManagementFiles = orionFiles.stream().filter(o -> quotationManagementIds.contains(o.getDataId())).collect(Collectors.toList());
                step2_2.setFileVOS(quotationManagementFiles);
                List<FileVO> marketContractFiles = orionFiles.stream().filter(o -> marketContractIds.contains(o.getDataId())).collect(Collectors.toList());
                step2_3.setFileVOS(marketContractFiles);
            }
            result.add(step2_1);
            result.add(step2_2);
            result.add(step2_3);
            return result;
        }

        // 三、采购管理

        if (step == 3) {
            //项目立项关联采购立项
            List<NcfFormpurchaseRequest> purchaseRequests = ncfFormpurchaseRequestService.list(new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class).eq(NcfFormpurchaseRequest::getProjectCode, project.getNumber()));
            //采购立项关联采购实施
            List<String> purchaseRequestIds = new ArrayList<>();
            List<NcfPurchProjectImplementation> purchaseImplementations = new ArrayList<>();
            if (!CollectionUtils.isEmpty(purchaseRequests)) {

                purchaseRequestIds.addAll(purchaseRequests.stream().map(NcfFormpurchaseRequest::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(purchaseRequestIds);

                List<String> purchaseRequestCodes = purchaseRequests.stream().map(NcfFormpurchaseRequest::getCode).collect(Collectors.toList());
                purchaseImplementations = ncfPurchProjectImplementationService.list(new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class).in(NcfPurchProjectImplementation::getPurchReqEcpCode, purchaseRequestCodes));
            }

            //采购实施关联采购合同
            List<ContractInfo> purchaseContractInfos = new ArrayList<>();
            List<String> purchaseImplementationIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(purchaseImplementations)) {

                purchaseImplementationIds.addAll(purchaseImplementations.stream().map(NcfPurchProjectImplementation::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(purchaseImplementationIds);

                List<String> purchaseContractNumbers = purchaseImplementations.stream().map(NcfPurchProjectImplementation::getContractNumber).distinct().collect(Collectors.toList());
                purchaseContractInfos = contractInfoService.list(new LambdaQueryWrapperX<>(ContractInfo.class).in(ContractInfo::getContractNumber, purchaseContractNumbers));

            }
            List<String> purchaseContractInfoIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(purchaseContractInfos)) {
                purchaseContractInfoIds.addAll(purchaseContractInfos.stream().map(ContractInfo::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(purchaseContractInfoIds);
            }


            //项目关联无合同采购
            List<NonContractProc> nonContractProcs = nonContractProcService.list(new LambdaQueryWrapperX<>(NonContractProc.class).eq(NonContractProc::getProjectCode, project.getNumber()));
            if (!CollectionUtils.isEmpty(nonContractProcs)) {
                purchaseContractInfoIds.addAll(nonContractProcs.stream().map(NonContractProc::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(purchaseContractInfoIds);
            }


            ProjectDocumentCountVO step3_1 = new ProjectDocumentCountVO();
            step3_1.setGroupName("采购申请");
            step3_1.setFileVOS(new ArrayList<>());


            ProjectDocumentCountVO step3_2 = new ProjectDocumentCountVO();
            step3_2.setGroupName("采购合同");
            step3_2.setFileVOS(new ArrayList<>());

            ProjectDocumentCountVO step3_3 = new ProjectDocumentCountVO();
            step3_3.setGroupName("采购实施");
            step3_3.setFileVOS(new ArrayList<>());

            if (!CollectionUtils.isEmpty(fileIds)) {
                List<FileVO> orionFiles = fileApiService.listMaxFileByDataIds(fileIds);

                List<FileVO> purchaseRequestFiles = orionFiles.stream().filter(o -> purchaseRequestIds.contains(o.getDataId())).collect(Collectors.toList());
                step3_1.setFileVOS(purchaseRequestFiles);
                List<FileVO> purchaseContractInfoFiles = orionFiles.stream().filter(o -> purchaseContractInfoIds.contains(o.getDataId())).collect(Collectors.toList());
                step3_2.setFileVOS(purchaseContractInfoFiles);
                List<FileVO> purchaseImplementationFiles = orionFiles.stream().filter(o -> purchaseImplementationIds.contains(o.getDataId())).collect(Collectors.toList());
                step3_3.setFileVOS(purchaseImplementationFiles);
            }
            result.add(step3_1);
            result.add(step3_2);
            result.add(step3_3);
            return result;

        }
        if (step == 4) {
            // 四、履约管理

            //项目关联计划
            List<ProjectScheme> projectSchemes = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class).eq(ProjectScheme::getProjectId, projectId));
            List<String> projectSchemeIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(projectSchemes)) {
                projectSchemeIds.addAll(projectSchemes.stream().map(ProjectScheme::getId).distinct().collect(Collectors.toList()));
                fileIds.addAll(projectSchemeIds);

                ProjectDocumentCountVO step4_1 = new ProjectDocumentCountVO();
                step4_1.setGroupName("项目计划");
                step4_1.setFileVOS(new ArrayList<>());

                if (!CollectionUtils.isEmpty(fileIds)) {
                    List<FileVO> orionFiles = fileApiService.listMaxFileByDataIds(fileIds);
                    List<FileVO> projectSchemeFiles = orionFiles.stream().filter(o -> projectSchemeIds.contains(o.getDataId())).collect(Collectors.toList());
                    step4_1.setFileVOS(projectSchemeFiles);
                }
                result.add(step4_1);
                return result;
            }


        }


        // 五、验收交付管理
        //查询项目完工数据 TODO
        if (step == 5) {
            //项目评价
            List<EvaluationProject> evaluationProjects = evaluationProjectService.list(new LambdaQueryWrapperX<>(EvaluationProject.class).eq(EvaluationProject::getProjectId, projectId));
            //项目验收
            List<AcceptanceForm> acceptanceForms = acceptanceFormService.list(new LambdaQueryWrapperX<>(AcceptanceForm.class).eq(AcceptanceForm::getProjectId, projectId));
            ProjectDocumentCountVO step5_1 = new ProjectDocumentCountVO();
            step5_1.setGroupName("项目评价");
            step5_1.setFileVOS(new ArrayList<>());
            ProjectDocumentCountVO step5_2 = new ProjectDocumentCountVO();
            step5_2.setGroupName("项目验收");
            step5_2.setFileVOS(new ArrayList<>());

            List<String> evaluationProjectIds;
            if (!CollectionUtils.isEmpty(evaluationProjects)) {
                evaluationProjectIds = evaluationProjects.stream().map(EvaluationProject::getId).distinct().collect(Collectors.toList());
                fileIds.addAll(evaluationProjectIds);
            } else {
                evaluationProjectIds = new ArrayList<>();
            }
            List<String> acceptanceFormIds;
            if (!CollectionUtils.isEmpty(acceptanceForms)) {
                acceptanceFormIds = acceptanceForms.stream().map(AcceptanceForm::getId).distinct().collect(Collectors.toList());
                fileIds.addAll(acceptanceFormIds);
            } else {
                acceptanceFormIds = new ArrayList<>();
            }
            if (!CollectionUtils.isEmpty(fileIds)) {
                List<FileVO> orionFiles = fileApiService.listMaxFileByDataIds(fileIds);
                //项目评价附件
                List<FileVO> evaluationProjectFiles = orionFiles.stream().filter(o -> evaluationProjectIds.contains(o.getDataId())).collect(Collectors.toList());
                step5_1.setFileVOS(evaluationProjectFiles);
                //项目验收附件
                List<FileVO> acceptanceFormFiles = orionFiles.stream().filter(o -> acceptanceFormIds.contains(o.getDataId())).collect(Collectors.toList());
                step5_2.setFileVOS(acceptanceFormFiles);
            }

            result.add(step5_1);
            result.add(step5_2);
            return result;
        }
        return new ArrayList<>();
    }


    public Map<String, BigDecimal> getMap(List<ProjectFundsReceived> receivedList) {
        Map<String, BigDecimal> map = new HashMap<>();
        for (ProjectFundsReceived projectFundsReceived : receivedList) {
            BigDecimal bigDecimal = map.get(projectFundsReceived.getContractNumber());
            if (ObjectUtil.isNotEmpty(bigDecimal)) {
                bigDecimal.add(projectFundsReceived.getFundsReceived());
                map.put(projectFundsReceived.getContractNumber(), bigDecimal);
            } else {
                map.put(projectFundsReceived.getContractNumber(), projectFundsReceived.getFundsReceived());
            }
        }
        return map;
    }


    /**
     * 获取项目里程碑信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public List<ProjectMilestoneVO> getProjectMilestones(String projectId) throws Exception {
        List<ProjectMilestoneVO> list = new ArrayList<>();
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        lambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectId);
        lambdaQueryWrapperX.eq(ProjectScheme::getNodeType, "milestone");
        lambdaQueryWrapperX.orderByAsc(ProjectScheme::getBeginTime);
        lambdaQueryWrapperX.ne(ProjectScheme::getStatus, Status.PENDING.getCode());
        List<ProjectScheme> schemes = projectSchemeService.list(lambdaQueryWrapperX);

        /**
         *  PENDING(101, "待发布"),
         *     FALLBACK(120, "计划退回"),
         *     PUBLISHED(130, "已下发"),
         *     EXECUTING(140, "执行中"),
         *     FINISHED(111, "已完成"),
         *     SUSPEND(150, "计划暂停"),
         *     TERMINATION(160, "计划终止"),
         *     CONFIRMED(121, "待确认"),
         *     CHANGE(110, "变更中"),
         */
        for (ProjectScheme projectScheme : schemes) {
            ProjectMilestoneVO projectMilestoneViewVO = new ProjectMilestoneVO();
            projectMilestoneViewVO.setName(projectScheme.getName());
            projectMilestoneViewVO.setTypeId(projectScheme.getStatus());

            if (projectScheme.getStatus().equals(Status.PUBLISHED.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.PUBLISHED.getName());
            }
            if (projectScheme.getStatus().equals(Status.EXECUTING.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.EXECUTING.getName());
            }
            if (projectScheme.getStatus().equals(Status.FINISHED.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.FINISHED.getName());
            }
            if (projectScheme.getStatus().equals(Status.SUSPEND.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.SUSPEND.getName());
            }
            if (projectScheme.getStatus().equals(Status.TERMINATION.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.TERMINATION.getName());
            }
            if (projectScheme.getStatus().equals(Status.CONFIRMED.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.CONFIRMED.getName());
            }
            if (projectScheme.getStatus().equals(Status.CHANGE.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.CHANGE.getName());
            }
            projectMilestoneViewVO.setBeginTime(projectScheme.getBeginTime());
            list.add(projectMilestoneViewVO);
        }
        return list;
    }
}
