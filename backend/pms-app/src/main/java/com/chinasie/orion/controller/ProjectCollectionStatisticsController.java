package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProjectCollectionStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/projectCollectionStatistics")
@Api(tags = "项目集统计数据")
public class ProjectCollectionStatisticsController {


    @Autowired
    private ProjectCollectionStatisticsService projectCollectionStatisticsService;


    /**
     * 获取风险统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-风险统计")
    @GetMapping(value = "/riskCount")
    public ResponseDTO<ProjectRiskCountVO> riskCount(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getRiskCount(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    /**
     * 获取问题统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-问题统计")
    @GetMapping(value = "/problemCount")
    public ResponseDTO<ProjectProblemCountVO> problemCount(@RequestParam("projectId") String projectId) throws Exception {
        try {

            return new ResponseDTO(projectCollectionStatisticsService.getProblemCount(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    /**
     * 获取需求统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-需求统计")
    @GetMapping(value = "/demandCount")
    public ResponseDTO<ProjectDemandCountVO> DemandCount(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getDemandCount(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取项目预警信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-预警信息")
    @GetMapping(value = "/warningSettingMessageRecords")
    public ResponseDTO<List<WarningSettingMessageRecordVO>> getWarningSettingMessageRecords(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getwarningSettingMessageRecords(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    /**
     * 获取营收统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-营收信息")
    @GetMapping(value = "/projectRevenueTotal")
    public ResponseDTO<ProjectRevenueTotalVO> getProjectRevenueTotalVO(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getProjectRevenueTotalVO(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取预算统计数据
     *
     * @param projectId
     * @param type
     * @param year
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-预算信息")
    @GetMapping(value = "/projectBudgetTotal")
    public ResponseDTO<ProjectBudgetTotalVO> getProjectBudgetTotalVO(@RequestParam("projectId") String projectId, @RequestParam("type") String type, @RequestParam(value = "year", required = false) String year, @RequestParam(value = "id", required = false) String id) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getBudgetTotalVO(projectId, type, year, id));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取计划统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-计划信息")
    @GetMapping(value = "/planCount")
    public ResponseDTO<ProjectPlanCountVO> getPlanCount(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getPlanCount(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取按月统计的计划数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-计划按月统计信息")
    @GetMapping(value = "/planMonth")
    public ResponseDTO<List<ProjectPlanCountVO>> getPlanMonth(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getPlanMonth(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取物资统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-物资信息")
    @GetMapping(value = "/getGoodsServiceCount")
    public ResponseDTO<GoodsServiceCountVO> getGoodsServiceCount(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getGoodsServiceCount(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取项目详情信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-项目基本信息")
    @GetMapping(value = "/getProjectInfo")
    public ResponseDTO<ProjectInfoVO> getProjectInfo(@RequestParam("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO(projectCollectionStatisticsService.getProjectInfo(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }




    /**
     * 获取项目工时信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取-项目工时信息")
    @GetMapping(value = "/getProjectManhour")
    public ResponseDTO<ProjectWorkHourVO> getProjectManhour(@RequestParam("projectId") String projectId) throws Exception {
        try {
            ProjectWorkHourVO workHourInfo = projectCollectionStatisticsService.getProjectWorkHourInfo(projectId);
            return new ResponseDTO(workHourInfo);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}
