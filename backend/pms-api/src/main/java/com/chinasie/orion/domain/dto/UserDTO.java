package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/12
 */
@Data
@ApiModel(value = "UserDTO", description = "UserDTO对象")
public class UserDTO extends ObjectDTO {

    /**
     * 初始密码校验规则
     */
    public static final String PW_PATTERN = "^(?![A-Za-z0-9]+$)(?![a-z0-9\\W]+$)(?![A-Za-z\\W]+$)(?![A-Z0-9\\W]+$)[a-zA-Z0-9\\W]{10,}$";

    /**
     * 用户登录次数
     */
    public static final int LOGIN_ERROR_COUNT = 5;

    /**
     * 编号
     */
    private String code;
    /**
     * 名称
     */
    @NotBlank(message = "用户名不能为空")
    private String name;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    @Email(message = "email格式错误")
    private String email;
    /**
     * 用户名
     */
    @NotBlank(message = "登录账户不能为空")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "登录账户只能由字母和数字组成")
    private String username;
    /**
     * 加密之后密码
     */
    private String password;
    /**
     * 登录次数
     */
    private Integer count;
    /**
     * 初始密码
     */
    @NotBlank(message = "初始密码不能为空")
    private String initPassword;

    /**
     * 错误次数
     */
    private Integer errors;

    /**
     * 上次登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    /**
     * 锁定原因
     */
    private String lockDesc;

    /**
     * 是否记住密码:true 记住 , false 不记住
     */
    private Boolean keepPassword;


    /**
     * 是否是重置密码:true 是重置 , false 不是重置
     */
    private Boolean resetPassword;

    /**
     * 是否是否锁定/解锁 true 是; false 否
     */
    private Boolean lock;

    /**
     * 工牌号
     */
    private String cardNo;

    /**
     * 员工Key号
     */
    private String keyNo;

    /**
     * 性别
     */
    private String sex;

    /**
     * 删除状态
     */
    private String isDeleted;

    /**
     * 职称
     */
    private String techTitle;

    /**
     * 最高学历
     */
    private String eduexps;

    /**
     * 非密网邮箱
     */
    private String emailb;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 办公电话
     */
    private String officephone;

    /**
     * 资质 A：审定，B：审核，C：校对，D：设计/编写，E：见习
     */
    private String grade;

    /**
     * 员工类型
     */
    private String userType;

    /**
     * 排序
     */
    private String sort;

    /**
     * 管理类型
     */
    private Integer adminType;
    /**
     * 签字图片地址
     */
    private String signatureImage;

    /**
     * 职位id
     */
    @ApiModelProperty("职位id")
    private String position;

    /**
     * 省代号
     */
    @ApiModelProperty("省代号")
    private String provinceId;

    /**
     * 市代号
     */
    @ApiModelProperty("市代号")
    private String cityId;
}
