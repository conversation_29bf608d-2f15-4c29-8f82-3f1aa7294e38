package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/10 15:27
 * @description:
 */
@Data
@ApiModel(value = "ProjectRoleVO对象", description = "项目角色")
public class ProjectRoleVO extends ObjectVO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 业务角色ID
     */
    @ApiModelProperty(value = "业务角色ID")
    private String businessId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用 0:禁用 1：启用")
    private Integer takeEffect;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用")
    private String takeEffectName;

    /**
     * 项目code
     */
    @ApiModelProperty(value = "项目code")
    private String code;
    @ApiModelProperty(value = "用户ID列表")
    private List<String> userIdList;
}
