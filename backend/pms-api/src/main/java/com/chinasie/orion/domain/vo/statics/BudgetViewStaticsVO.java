package com.chinasie.orion.domain.vo.statics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

@Data
@ApiModel(value = "BudgetViewVO对象", description = "预算总览")
public class BudgetViewStaticsVO {
    @ApiModelProperty(value = "项目类型")
    private String type;
    @ApiModelProperty(value = "项目类型名称")
    private String typeName;

    @ApiModelProperty(value = "预算")
    private BigDecimal budget;
    @ApiModelProperty(value = "承诺")
    private BigDecimal promise;
    @ApiModelProperty(value = "实际")
    private BigDecimal practical;
    @ApiModelProperty(value = "执行率==百分比")
    private String percent;


}
