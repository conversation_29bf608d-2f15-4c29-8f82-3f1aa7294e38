package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: lsy
 * @date: 2024/6/12
 * @description:
 */
@Data
public class EcrExecuteVO {

    @ApiModelProperty(value = "流程处理业务数据")
    List<String> dataIdsForWorkflow = new ArrayList<>();

    @ApiModelProperty(value = "消息处理业务数据")
    List<String> dataIdsForMsc = new ArrayList<>();
}
