package com.chinasie.orion.service;


import com.chinasie.orion.domain.vo.DesignProjectSchemeVO;
import com.chinasie.orion.domain.vo.PlmVO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;

@FeignClient(name = "pms", configuration = FeignConfig.class)
@Lazy
public interface ProjectSchemeApiService {


    String API_PREFIX = "/api-pms/projectScheme";

    /**
     * 修改设计计划项目状态
     *
     * @param plmVO
     * @return
     * @throws Exception
     */
    @PostMapping(value = API_PREFIX + "/projectScheme/updateStatus")
   Boolean savePlmBom(@RequestBody PlmVO plmVO) throws Exception;

    /**
     *查询未完成的设计计划项目列表
     */
    @GetMapping(value = API_PREFIX + "/projectScheme/getIncompleteDesignProjectList")
    List<DesignProjectSchemeVO> getIncompleteDesignProjectList() throws Exception;
}
