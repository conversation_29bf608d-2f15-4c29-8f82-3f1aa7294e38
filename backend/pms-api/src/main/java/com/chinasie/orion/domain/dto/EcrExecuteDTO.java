package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/6/12
 * @description:
 */
@Data
public class EcrExecuteDTO {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "变更状态")
    private String ecrStatus;


    @ApiModelProperty(value = "暂停开始时间")
    private Date stopStartTime;
}
