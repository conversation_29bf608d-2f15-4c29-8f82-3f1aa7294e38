// 履约执行模块浏览器Console截图助手
// 使用方法：在浏览器开发者工具的Console中运行此脚本

class ContractExecutionScreenshotHelper {
    constructor() {
        this.screenshots = [
            {
                name: "首页.png",
                desc: "登录成功后的系统首页",
                action: "当前页面直接截图",
                testPoints: [
                    "页面正常加载",
                    "导航菜单显示正确", 
                    "用户信息显示正常",
                    "履约执行菜单可见"
                ]
            },
            {
                name: "合同管理列表.png",
                desc: "合同管理列表页面",
                action: "点击左侧'履约执行' → '合同管理'",
                testPoints: [
                    "合同列表数据加载正常",
                    "表格列显示完整",
                    "筛选和搜索功能可见",
                    "操作按钮显示正确"
                ]
            },
            {
                name: "合同编辑抽屉.png",
                desc: "合同编辑界面",
                action: "点击任意合同的'编辑'按钮",
                testPoints: [
                    "抽屉正常打开",
                    "表单字段完整显示",
                    "数据回填正确",
                    "保存/取消按钮可见"
                ]
            },
            {
                name: "合同创建抽屉.png",
                desc: "合同创建界面",
                action: "点击'创建合同'按钮",
                testPoints: [
                    "抽屉正常打开",
                    "表单字段完整",
                    "必填项标识清晰",
                    "提交/取消按钮可见"
                ]
            },
            {
                name: "合同详情页面.png",
                desc: "合同详情主页面",
                action: "点击合同名称进入详情",
                testPoints: [
                    "页面跳转正常",
                    "合同基本信息显示",
                    "标签页导航显示",
                    "操作按钮区域显示"
                ]
            },
            {
                name: "合同内容信息.png",
                desc: "合同基本信息",
                action: "点击'合同内容信息'标签",
                testPoints: [
                    "标签页切换正常",
                    "合同基本信息完整",
                    "字段格式正确",
                    "编辑功能可用"
                ]
            },
            {
                name: "支付节点信息.png",
                desc: "支付节点管理",
                action: "点击'支付节点信息'标签",
                testPoints: [
                    "支付节点列表显示",
                    "进度条显示正确",
                    "状态标识清晰",
                    "操作按钮可用"
                ]
            },
            {
                name: "支付节点确认弹窗.png",
                desc: "节点确认界面",
                action: "点击'确认'按钮",
                testPoints: [
                    "弹窗正常显示",
                    "确认信息完整",
                    "输入框可用",
                    "确认/取消按钮显示"
                ]
            },
            {
                name: "支付状态确认弹窗.png",
                desc: "状态确认界面",
                action: "点击'状态确认'按钮",
                testPoints: [
                    "弹窗正常显示",
                    "状态选项完整",
                    "备注输入框可用",
                    "提交/取消按钮显示"
                ]
            },
            {
                name: "节点确认记录.png",
                desc: "确认记录列表",
                action: "点击'节点确认记录'标签",
                testPoints: [
                    "确认记录列表显示",
                    "时间排序正确",
                    "操作人信息显示",
                    "查看/审核按钮可用"
                ]
            },
            {
                name: "确认记录详情.png",
                desc: "记录详情界面",
                action: "点击'查看详情'按钮",
                testPoints: [
                    "详情界面显示正常",
                    "记录信息完整",
                    "附件显示正常",
                    "关闭按钮可用"
                ]
            },
            {
                name: "确认记录审核.png",
                desc: "审核界面",
                action: "点击'审核'按钮",
                testPoints: [
                    "审核界面显示正常",
                    "审核选项完整",
                    "审核意见输入框可用",
                    "提交/取消按钮显示"
                ]
            },
            {
                name: "合同附件信息.png",
                desc: "附件管理",
                action: "点击'合同附件信息'标签",
                testPoints: [
                    "附件列表显示",
                    "文件信息完整",
                    "上传/下载/预览按钮可用",
                    "文件大小和类型显示"
                ]
            },
            {
                name: "附件上传界面.png",
                desc: "上传界面",
                action: "点击'上传附件'按钮",
                testPoints: [
                    "上传界面显示正常",
                    "文件选择功能可用",
                    "上传进度显示",
                    "取消/确认按钮显示"
                ]
            },
            {
                name: "附件预览界面.png",
                desc: "预览界面",
                action: "点击'预览'按钮",
                testPoints: [
                    "预览界面显示正常",
                    "文档内容清晰",
                    "缩放功能可用",
                    "下载/关闭按钮显示"
                ]
            },
            {
                name: "合同审批流程.png",
                desc: "审批流程",
                action: "点击'合同审批流程'标签",
                testPoints: [
                    "流程图显示正常",
                    "当前节点高亮",
                    "审批历史完整",
                    "操作按钮有效"
                ]
            }
        ];
        this.currentIndex = 0;
        this.testResults = [];
        this.startTime = new Date();
    }

    start() {
        console.log("🚀 履约执行模块截图助手启动");
        console.log("📋 总共需要截图16个页面（不含登录页面）");
        console.log("💡 每次提示时，请手动截图并进行功能测试");
        console.log("🔧 测试完成后运行 next() 继续下一个");
        console.log("");
        this.showCurrent();
    }

    showCurrent() {
        if (this.currentIndex >= this.screenshots.length) {
            this.showSummary();
            return;
        }

        const shot = this.screenshots[this.currentIndex];
        console.log(`\n📸 [${this.currentIndex + 1}/16] ${shot.name}`);
        console.log(`📝 描述: ${shot.desc}`);
        console.log(`🎯 操作: ${shot.action}`);
        console.log(`💾 保存路径: docs/images/front_system/履约执行/${shot.name}`);
        console.log("");
        console.log("🧪 测试要点:");
        shot.testPoints.forEach((point, index) => {
            console.log(`   ${index + 1}. ${point}`);
        });
        console.log("");
        console.log("⏸️  完成截图和测试后，运行: next()");
        console.log("📊 记录测试结果，运行: recordTest(result)");
        console.log("   例如: recordTest('通过') 或 recordTest('失败', '具体问题描述')");
    }

    next() {
        if (this.currentIndex < this.screenshots.length) {
            this.currentIndex++;
            this.showCurrent();
        }
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.showCurrent();
        }
    }

    recordTest(result, issue = '') {
        const shot = this.screenshots[this.currentIndex];
        this.testResults.push({
            name: shot.name,
            desc: shot.desc,
            result: result,
            issue: issue,
            timestamp: new Date().toLocaleString()
        });
        console.log(`✅ 已记录测试结果: ${shot.name} - ${result}`);
        if (issue) {
            console.log(`⚠️  问题描述: ${issue}`);
        }
    }

    goto(index) {
        if (index >= 1 && index <= this.screenshots.length) {
            this.currentIndex = index - 1;
            this.showCurrent();
        } else {
            console.log(`❌ 请输入1-${this.screenshots.length}之间的数字`);
        }
    }

    list() {
        console.log("📋 截图清单:");
        this.screenshots.forEach((shot, index) => {
            const status = index < this.currentIndex ? "✅" : 
                          index === this.currentIndex ? "👉" : "⏳";
            console.log(`${status} ${index + 1}. ${shot.name} - ${shot.desc}`);
        });
        console.log(`\n当前进度: ${this.currentIndex}/${this.screenshots.length}`);
    }

    showTestResults() {
        console.log("\n📊 测试结果汇总:");
        console.log("=" * 50);
        
        let passCount = 0;
        let failCount = 0;
        
        this.testResults.forEach((result, index) => {
            const status = result.result === '通过' ? '✅' : '❌';
            console.log(`${status} ${index + 1}. ${result.name} - ${result.result}`);
            if (result.issue) {
                console.log(`   问题: ${result.issue}`);
            }
            
            if (result.result === '通过') passCount++;
            else failCount++;
        });
        
        console.log(`\n📈 统计结果:`);
        console.log(`   总计: ${this.testResults.length}`);
        console.log(`   通过: ${passCount}`);
        console.log(`   失败: ${failCount}`);
        console.log(`   通过率: ${(passCount / this.testResults.length * 100).toFixed(1)}%`);
    }

    showSummary() {
        const endTime = new Date();
        const duration = Math.round((endTime - this.startTime) / 1000 / 60);
        
        console.log("🎉 所有截图完成！");
        console.log(`⏱️  总耗时: ${duration} 分钟`);
        console.log("📁 请检查 docs/images/front_system/履约执行/ 目录");
        console.log("");
        
        if (this.testResults.length > 0) {
            this.showTestResults();
        }
        
        console.log("\n🔄 下一步操作:");
        console.log("1. 验证截图质量: cd docs && python3 verify_screenshots.py");
        console.log("2. 更新PRD文档: python3 完整PRD更新脚本.py");
        console.log("3. 生成最终报告");
    }

    help() {
        console.log(`
📖 履约执行模块截图助手使用说明

🚀 基本命令:
- start()           开始截图流程
- next()            下一个截图
- prev()            上一个截图  
- goto(n)           跳转到第n个截图
- list()            查看完整清单
- help()            显示帮助

🧪 测试命令:
- recordTest('通过')                    记录测试通过
- recordTest('失败', '问题描述')         记录测试失败
- showTestResults()                    显示测试结果汇总

📸 截图要求:
- 分辨率: 1920x1080或更高
- 格式: PNG格式
- 范围: 完整浏览器窗口
- 质量: 页面完全加载，内容清晰

🎯 测试要点:
每个页面都有对应的测试要点，请逐一验证：
- 功能是否正常工作
- 界面是否显示完整
- 交互是否响应正常
- 数据是否加载正确

💾 保存位置: docs/images/front_system/履约执行/

🔧 环境信息:
- 测试地址: http://183.136.206.207:44099/login
- 测试账号: heqiang / Abc123456789!
        `);
    }

    // 产品经理视角的评估
    productManagerEvaluation() {
        console.log(`
🎯 产品经理评估清单

📋 用户体验评估:
1. 导航是否直观易懂？
2. 信息展示是否清晰？
3. 操作流程是否顺畅？
4. 错误提示是否友好？

📊 功能完整性评估:
1. 核心业务流程是否完整？
2. 边界情况是否处理得当？
3. 权限控制是否有效？
4. 数据一致性是否良好？

💡 改进建议记录:
请在测试过程中记录发现的问题和改进建议
        `);
    }

    // 测试人员视角的评估
    testerEvaluation() {
        console.log(`
🧪 测试人员评估清单

🔧 功能测试:
1. 正常流程是否工作正常？
2. 异常情况是否处理得当？
3. 边界值是否验证有效？
4. 数据校验是否完善？

⚡ 性能测试:
1. 页面加载速度如何？
2. 操作响应是否及时？
3. 大数据量是否影响性能？

🔒 安全测试:
1. 权限验证是否严格？
2. 输入验证是否完善？
3. 会话管理是否安全？
        `);
    }
}

// 创建全局实例
window.contractHelper = new ContractExecutionScreenshotHelper();

console.log("✅ 履约执行模块截图助手已加载！");
console.log("🚀 运行 contractHelper.start() 开始截图");
console.log("❓ 运行 contractHelper.help() 查看详细帮助");
console.log("🎯 运行 contractHelper.productManagerEvaluation() 查看产品经理评估清单");
console.log("🧪 运行 contractHelper.testerEvaluation() 查看测试人员评估清单");
